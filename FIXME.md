after move code from NooshOne to here, I found some things need to be done before we start create new api.

1. copied a lot base code, we should build common and base api first, or we will have to copy the base code every time we create a new service.
2. code is related to enterprise's ac id, and host, shall we redesign the redirect logic from NooshOne and Noosh React to Enterprise
3. email is not support now

