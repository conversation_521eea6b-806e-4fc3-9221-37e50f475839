<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper">
    <resultMap id="workgroupResultMap" type="com.noosh.app.commons.dto.security.WorkgroupDTO">
        <id property = "id" column = "AC_WORKGROUP_ID"/>
        <result property = "name" column = "NAME"/>
        <result property = "parentWorkgroupId" column = "PARENT_AC_WORKGROUP_ID"/>
        <result property = "companyId" column = "AC_COMPANY_ID"/>
        <result property = "workgroupTypeId" column = "AC_WORKGROUP_TYPE_ID"/>
        <result property = "defaultCurrencyId" column = "DEFAULT_AC_CURRENCY_ID"/>
        <result property = "transactionalCurrencyId" column = "TRANS_AC_CURRENCY_ID"/>
        <result property = "statusId" column = "OC_OBJECT_STATE_ID"/>
        <result property = "portalString" column = "PORTAL"/>
        <result property = "customPropertyId" column = "CUSTOM_PR_PROPERTY_ID"/>
        <result property = "guid" column = "AC_WORKGROUP_GUID"/>
        <result property = "isLocked" column = "IS_LOCKED"/>
        <result property = "authenticationMethodId" column = "AC_AUTH_METHOD_ID"/>
        <result property = "partnerWgId" column = "PARTNER_AC_WORKGROUP_ID"/>
        <result property = "isTrial" column = "IS_TRIAL"/>
        <result property = "sourceTypeId" column = "AC_SOURCE_TYPE_ID"/>
        <result property = "signUpSubdomain" column = "SIGN_UP_SUBDOMAIN"/>
        <result property = "appHome" column = "APP_HOME"/>
        <result property = "decimalPlaces" column = "DECIMAL_PLACES"/>
        <result property = "customReportDS" column = "CUSTOM_REPORT_DS"/>

        <association property="mainAddress" columnPrefix="A_" resultMap="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper.addressResultMap"/>
        <association property="wgsd" columnPrefix="WSD_" resultMap="com.noosh.app.repository.mybatis.security.WorkgroupSecondaryDataMyBatisMapper.workgroupSecondaryDataResultMap"/>
        <association property="workgroupType" columnPrefix="WT_" resultMap="com.noosh.app.repository.mybatis.security.WorkgroupTypeMyBatisMapper.workgroupTypeResultMap"/>
        <association property="company" columnPrefix="C_" resultMap="com.noosh.app.repository.mybatis.security.CompanyMyBatisMapper.companyResultMap"/>
    </resultMap>

    <sql id="workgroupColumns">
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID,
        ${alias}.NAME ${prefix}NAME,
        ${alias}.PARENT_AC_WORKGROUP_ID ${prefix}PARENT_AC_WORKGROUP_ID,
        ${alias}.AC_COMPANY_ID ${prefix}AC_COMPANY_ID,
        ${alias}.AC_WORKGROUP_TYPE_ID ${prefix}AC_WORKGROUP_TYPE_ID,
        ${alias}.DEFAULT_AC_CURRENCY_ID ${prefix}DEFAULT_AC_CURRENCY_ID,
        ${alias}.TRANS_AC_CURRENCY_ID ${prefix}TRANS_AC_CURRENCY_ID,
        ${alias}.OC_OBJECT_STATE_ID ${prefix}OC_OBJECT_STATE_ID,
        ${alias}.PORTAL ${prefix}PORTAL,
        ${alias}.CUSTOM_PR_PROPERTY_ID ${prefix}CUSTOM_PR_PROPERTY_ID,
        ${alias}.AC_WORKGROUP_GUID ${prefix}AC_WORKGROUP_GUID,
        ${alias}.IS_LOCKED ${prefix}IS_LOCKED,
        ${alias}.AC_AUTH_METHOD_ID ${prefix}AC_AUTH_METHOD_ID,
        ${alias}.PARTNER_AC_WORKGROUP_ID ${prefix}PARTNER_AC_WORKGROUP_ID,
        ${alias}.IS_TRIAL ${prefix}IS_TRIAL,
        ${alias}.AC_SOURCE_TYPE_ID ${prefix}AC_SOURCE_TYPE_ID,
        ${alias}.SIGN_UP_SUBDOMAIN ${prefix}SIGN_UP_SUBDOMAIN,
        ${alias}.APP_HOME ${prefix}APP_HOME,
        ${alias}.DECIMAL_PLACES ${prefix}DECIMAL_PLACES,
        ${alias}.CUSTOM_REPORT_DS ${prefix}CUSTOM_REPORT_DS
    </sql>

    <select id="findWorkgroupWithAllData" resultMap="workgroupResultMap">
        SELECT
        <include refid="workgroupColumns">
            <property name="alias" value="WG"/>
            <property name="prefix" value=""/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.WorkgroupTypeMyBatisMapper.workgroupTypeColumns">
            <property name="alias" value="WT"/>
            <property name="prefix" value="WT_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.WorkgroupAddressMyBatisMapper.workgroupAddressColumns">
            <property name="alias" value="WA"/>
            <property name="prefix" value="WA_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.WorkgroupSecondaryDataMyBatisMapper.workgroupSecondaryDataColumns">
            <property name="alias" value="WSD"/>
            <property name="prefix" value="WSD_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper.addressColumns">
            <property name="alias" value="A"/>
            <property name="prefix" value="A_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.CountryMyBatisMapper.countryColumns">
            <property name="alias" value="T"/>
            <property name="prefix" value="A_T_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.CompanyMyBatisMapper.companyColumns">
            <property name="alias" value="C"/>
            <property name="prefix" value="C_"/>
        </include>
        FROM
        AC_WORKGROUP WG,
        AC_WORKGROUP_TYPE WT,
        AC_WORKGROUP_ADDRESS WA,
        AC_ADDRESS A,
        AC_COUNTRY T,
        AC_COMPANY C,
        AC_WORKGROUP_SD WSD
        WHERE
        WG.AC_WORKGROUP_TYPE_ID = WT.AC_WORKGROUP_TYPE_ID
        AND WG.AC_WORKGROUP_ID = WA.AC_WORKGROUP_ID
        AND WG.AC_WORKGROUP_ID = WSD.AC_WORKGROUP_ID
        AND WA.AC_ADDRESS_ID = A.AC_ADDRESS_ID
        AND A.AC_COUNTRY_ID = T.AC_COUNTRY_ID
        AND C.AC_COMPANY_ID= WG.AC_COMPANY_ID
        AND WA.AC_ADDRESS_TYPE_ID=1000000
        AND WG.AC_WORKGROUP_ID=#{workgroupId}

    </select>

    <select id="findWithFirstNameAndLastNameAndDefaultEmail" resultMap="workgroupResultMap">
        SELECT DISTINCT
        <include refid="workgroupColumns">
            <property name="alias" value="WG"/>
            <property name="prefix" value=""/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper.addressColumns">
            <property name="alias" value="A"/>
            <property name="prefix" value="A_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.CountryMyBatisMapper.countryColumns">
            <property name="alias" value="T"/>
            <property name="prefix" value="A_T_"/>
        </include>
        FROM
        ac_account_user       U,
        ac_person             P,
        ac_workgroup          WG,
        ac_person_email       E,
        ac_workgroup_type     WT,
        ac_address            A,
        ac_workgroup_address  WA,
        ac_address_type       AT,
        ac_country            T,
        ac_workgroup_sd       WSD
        WHERE
        U.ac_person_id = P.ac_person_id (+)
        AND E.ac_person_id (+) = P.ac_person_id
        AND E.is_default (+) = 1
        AND U.ac_workgroup_id (+) = WG.ac_workgroup_id
        AND WG.ac_workgroup_type_id = WT.ac_workgroup_type_id
        AND WA.ac_workgroup_id = WG.ac_workgroup_id
        AND WA.ac_address_id = A.ac_address_id
        AND WSD.ac_workgroup_id = WG.ac_workgroup_id
        AND U.oc_object_state_id = 2000056
        AND T.ac_country_id = A.ac_country_id
        AND WA.ac_address_type_id = AT.ac_address_type_id
        AND AT.ac_address_type_id = 1000000
        AND WG.partner_ac_workgroup_id = (
        SELECT
        CAW.partner_ac_workgroup_id
        FROM
        ac_workgroup CAW
        WHERE
        CAW.ac_workgroup_id = #{workgroupId}
        )
        AND ( U.ac_workgroup_id = #{workgroupId} OR P.is_public = 1 )
        AND WG.ac_workgroup_type_id IN ( 1000003, 1000004, 1000001)
        AND WG.ac_workgroup_id != #{workgroupId}
        AND WG.DEFAULT_AC_CURRENCY_ID = #{currencyId}
        <if test="workgroupName != null and workgroupName != ''">
            AND upper(WG.name) LIKE upper(#{workgroupName})
        </if>
        <if test="firstName != null and firstName != ''">
            AND upper(P.first_name) LIKE upper(#{firstName})
        </if>
        <if test="lastName != null and lastName != ''">
            AND upper(P.last_name) LIKE upper(#{lastName})
        </if>
        <if test="defaultEmail != null and defaultEmail != ''">
            AND upper(E.email_address) LIKE upper(#{defaultEmail})
        </if>
        <if test="!isInternal">
            AND ( WSD.is_internal = 0 OR WSD.is_internal IS NULL )
        </if>

    </select>

    <select id="findWorkgroupsBySupplierFilter" resultMap="workgroupResultMap">
        SELECT DISTINCT
        <include refid="workgroupColumns">
            <property name="alias" value="WG"/>
            <property name="prefix" value=""/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.WorkgroupTypeMyBatisMapper.workgroupTypeColumns">
            <property name="alias" value="WT"/>
            <property name="prefix" value="WT_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.WorkgroupSecondaryDataMyBatisMapper.workgroupSecondaryDataColumns">
            <property name="alias" value="WSD"/>
            <property name="prefix" value="WSD_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper.addressColumns">
            <property name="alias" value="A"/>
            <property name="prefix" value="A_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.WorkgroupAddressMyBatisMapper.workgroupAddressColumns">
            <property name="alias" value="WA"/>
            <property name="prefix" value="WA_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.CountryMyBatisMapper.countryColumns">
            <property name="alias" value="T"/>
            <property name="prefix" value="A_T_"/>
        </include>
        FROM
        ac_account_user       U,
        ac_person             P,
        ac_workgroup          WG,
        ac_person_email       E,
        ac_workgroup_type     WT,
        ac_address            A,
        ac_workgroup_address  WA,
        ac_address_type       AT,
        ac_country            T,
        ac_workgroup_sd       WSD
        WHERE
        U.ac_person_id = P.ac_person_id (+)
        AND E.ac_person_id (+) = P.ac_person_id
        AND E.is_default (+) = 1
        AND U.ac_workgroup_id (+) = WG.ac_workgroup_id
        AND WG.ac_workgroup_type_id = WT.ac_workgroup_type_id
        AND WA.ac_workgroup_id = WG.ac_workgroup_id
        AND WA.ac_address_id = A.ac_address_id
        AND WSD.ac_workgroup_id = WG.ac_workgroup_id
        AND ( U.ac_workgroup_id = #{workgroupId} OR P.is_public = 1 )
        <if test="!isInternal">
            AND ( WSD.is_internal = 0 OR WSD.is_internal IS NULL )
        </if>
        AND U.oc_object_state_id = 2000056
        AND T.ac_country_id = A.ac_country_id
        AND WA.ac_address_type_id = AT.ac_address_type_id
        AND AT.ac_address_type_id = 1000000
        AND WG.ac_workgroup_type_id IN ( 1000003, 1000004, 1000002, -1)
        AND WG.partner_ac_workgroup_id = #{partnerWgId}
        AND WG.ac_workgroup_id != #{workgroupId}
        ${filter.filterSQL}
    </select>


    <select id="findWorkgroup" resultMap="workgroupResultMap">
        SELECT
        <include refid="workgroupColumns">
            <property name="alias" value="WG"/>
            <property name="prefix" value=""/>
        </include>

        FROM
        AC_WORKGROUP WG
        WHERE
        WG.AC_WORKGROUP_ID=#{workgroupId}
    </select>

    <select id="findWorkgroupByGuid" resultMap="workgroupResultMap">
        SELECT
        <include refid="workgroupColumns">
            <property name="alias" value="WG"/>
            <property name="prefix" value=""/>
        </include>

        FROM
        AC_WORKGROUP WG
        WHERE
        WG.AC_WORKGROUP_GUID=#{guid}
    </select>
</mapper>