<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.security.AccountUserMyBatisMapper">

    <resultMap id="accountUserResultMap" type="com.noosh.app.commons.dto.security.AccountUserDTO">
        <id property="id" column="USER_ID"/>
        <result property="personId" column="AC_PERSON_ID"/>
        <result property="workgroupId" column="AC_WORKGROUP_ID"/>
        <result property="isDefault" column="IS_DEFAULT"/>
        <result property="isLocked" column="IS_LOCKED"/>
        <result property="statusId" column="OC_OBJECT_STATE_ID"/>
        <result property="invitationDate" column="INVITATION_DATE"/>
        <result property="activationDate" column="ACTIVATION_DATE"/>
        <result property="deactivationDate" column="DEACTIVATION_DATE"/>
        <result property="customPropertyId" column="CUSTOM_PR_PROPERTY_ID"/>
        <association property="person" columnPrefix="P_" resultMap="com.noosh.app.repository.mybatis.security.PersonMyBatisMapper.personResultMap"/>
        <association property="workgroupRole" columnPrefix="R_" resultMap="com.noosh.app.repository.mybatis.security.RoleMyBatisMapper.roleResultMap"/>
    </resultMap>

    <sql id="accountUserColumns">
        ${alias}.USER_ID ${prefix}USER_ID,
        ${alias}.AC_PERSON_ID ${prefix}AC_PERSON_ID,
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID,
        ${alias}.IS_DEFAULT ${prefix}IS_DEFAULT,
        ${alias}.IS_LOCKED ${prefix}IS_LOCKED,
        ${alias}.OC_OBJECT_STATE_ID ${prefix}OC_OBJECT_STATE_ID,
        ${alias}.INVITATION_DATE ${prefix}INVITATION_DATE,
        ${alias}.ACTIVATION_DATE ${prefix}ACTIVATION_DATE,
        ${alias}.DEACTIVATION_DATE ${prefix}DEACTIVATION_DATE,
        ${alias}.CUSTOM_PR_PROPERTY_ID ${prefix}CUSTOM_PR_PROPERTY_ID
    </sql>

    <select id="findWithAllData" resultMap="accountUserResultMap">
        SELECT
        <include refid="accountUserColumns">
            <property name="alias" value="U"/>
            <property name="prefix" value=""/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.PersonMyBatisMapper.personColumns">
            <property name="alias" value="P"/>
            <property name="prefix" value="P_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.PersonEmailMyBatisMapper.personEmailColumns">
            <property name="alias" value="E"/>
            <property name="prefix" value="P_E_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.PersonSecondaryDataMyBatisMapper.personSecondaryDataColumns">
            <property name="alias" value="PS"/>
            <property name="prefix" value="P_PS_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper.addressColumns">
            <property name="alias" value="A"/>
            <property name="prefix" value="P_A_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.RoleMyBatisMapper.roleColumns">
            <property name="alias" value="R"/>
            <property name="prefix" value="R_"/>
        </include>
        FROM
        AC_ACCOUNT_USER U,
        AC_PERSON P,
        AC_PERSON_EMAIL E,
        AC_ADDRESS A,
        AC_PERSON_SD PS,
        TM_TEAM_OBJECT T,
        TM_TEAM_MEMBER TM,
        RE_ROLE R
        WHERE
        U.AC_PERSON_ID = P.AC_PERSON_ID
        AND P.AC_PERSON_ID = PS.AC_PERSON_ID
        AND P.AC_PERSON_ID = E.AC_PERSON_ID
        AND A.AC_ADDRESS_ID(+) = P.AC_ADDRESS_ID
        AND U.USER_ID = TM.USER_ID
        AND TM.RE_ROLE_ID = R.RE_ROLE_ID
        AND TM.TM_TEAM_ID = T.TM_TEAM_ID
        AND TM.CURRENT_RECORD = 1
        AND T.OBJECT_ID  = U.AC_WORKGROUP_ID
        AND T.OC_OBJECT_CLASS_ID=1000100
        AND E.IS_DEFAULT=1
        AND U.AC_WORKGROUP_ID=#{workgroupId}
        AND U.OC_OBJECT_STATE_ID = #{userStatusId}
    </select>

    <select id="findByPersonIdAndWorkgroupId" resultMap="accountUserResultMap">
        SELECT
        <include refid="accountUserColumns">
            <property name="alias" value="U"/>
            <property name="prefix" value=""/>
        </include>
        FROM
        AC_ACCOUNT_USER U
        WHERE
        U.AC_PERSON_ID = #{personId}
        AND U.AC_WORKGROUP_ID=#{workgroupId}
    </select>

    <select id="findWithPersonAndWorkgroupByUserId" resultMap="accountUserResultMap">
        SELECT
        <include refid="accountUserColumns">
            <property name="alias" value="U"/>
            <property name="prefix" value=""/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.PersonMyBatisMapper.personColumns">
            <property name="alias" value="P"/>
            <property name="prefix" value="P_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.PersonEmailMyBatisMapper.personEmailColumns">
            <property name="alias" value="E"/>
            <property name="prefix" value="P_E_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.PersonSecondaryDataMyBatisMapper.personSecondaryDataColumns">
            <property name="alias" value="PS"/>
            <property name="prefix" value="P_PS_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper.addressColumns">
            <property name="alias" value="A"/>
            <property name="prefix" value="P_A_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.CountryMyBatisMapper.countryColumns">
            <property name="alias" value="T"/>
            <property name="prefix" value="P_A_T_"/>
        </include>
        FROM
        AC_ACCOUNT_USER U,
        AC_PERSON P,
        AC_PERSON_SD PS,
        AC_ADDRESS A,
        AC_COUNTRY T,
        AC_WORKGROUP WG,
        AC_PERSON_EMAIL E,
        AC_WORKGROUP_SD WS
        WHERE
        U.AC_PERSON_ID = P.AC_PERSON_ID
        AND P.AC_ADDRESS_ID = A.AC_ADDRESS_ID ( + )
        AND P.AC_PERSON_ID = E.AC_PERSON_ID
        AND U.AC_WORKGROUP_ID = WG.AC_WORKGROUP_ID
        AND WG.AC_WORKGROUP_ID = WS.AC_WORKGROUP_ID
        AND P.AC_PERSON_ID = PS.AC_PERSON_ID
        AND E.IS_DEFAULT = 1
        AND T.AC_COUNTRY_ID = A.AC_COUNTRY_ID
        AND U.USER_ID IN #{userId}
    </select>

</mapper>
