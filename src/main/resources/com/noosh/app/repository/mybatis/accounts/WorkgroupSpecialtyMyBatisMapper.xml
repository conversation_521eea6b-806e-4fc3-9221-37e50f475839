<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.accounts.WorkgroupSpecialtyMyBatisMapper">

    <resultMap id="workgroupSpecialtyResultMap" type="com.noosh.app.commons.dto.accounts.WorkgroupSpecialtyDTO">
        <id property="id" column="AC_WG_SPECIALTY_ID"/>
        <result property="workgroupId" column="AC_WORKGROUP_ID"/>
        <result property="specialtyId" column="AC_SPECIALTY_ID"/>
        <result property="ordinalNumber" column="ORDINAL_NUMBER"/>
        <result property="searchContent" column="SEARCH_CONTENT"/>
        <result property="searchContent" column="SEARCH_CONTENT"/>
        <collection property="specialty" columnPrefix="S_" resultMap="com.noosh.app.repository.mybatis.accounts.SpecialtyMyBatisMapper.specialtyResultMap"/>
    </resultMap>

    <sql id="workgroupSpecialtyColumns">
        ${alias}.AC_WG_SPECIALTY_ID ${prefix}AC_WG_SPECIALTY_ID,
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID,
        ${alias}.AC_SPECIALTY_ID ${prefix}AC_SPECIALTY_ID,
        ${alias}.ORDINAL_NUMBER ${prefix}ORDINAL_NUMBER,
        ${alias}.SEARCH_CONTENT ${prefix}SEARCH_CONTENT
    </sql>

    <select id="findByWorkgroupId" resultMap="workgroupSpecialtyResultMap">
        SELECT
        <include refid="workgroupSpecialtyColumns">
            <property name="alias" value="WS"/>
            <property name="prefix" value=""/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.accounts.SpecialtyMyBatisMapper.specialtyColumns">
            <property name="alias" value="S"/>
            <property name="prefix" value="S_"/>
        </include>
        FROM AC_WG_SPECIALTY WS, AC_SPECIALTY S
        WHERE WS.AC_WORKGROUP_ID = #{workgroupId}
        AND WS.AC_SPECIALTY_ID = S.AC_SPECIALTY_ID
        ORDER BY WS.ORDINAL_NUMBER
    </select>
</mapper>
