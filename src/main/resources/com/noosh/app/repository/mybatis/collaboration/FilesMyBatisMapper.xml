<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.collaboration.FilesMyBatisMapper">

    <resultMap id="fileTagsResultMap" type="com.noosh.app.commons.dto.document.TgTagDTO">
        <id property="id" column="TG_TAG_ID"/>
        <result property="name" column="NAME"/>
        <result property="ownerWorkgroupId" column="OWNER_AC_WG_ID"/>
        <result property="objectClassId" column="OBJECT_CLASS_ID"/>
        <result property="isSystemGenerated" column="IS_SYSTEM_GENERATED"/>
        <result property="isActive" column="IS_ACTIVE"/>
        <result property="createDate" column="CREATE_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="modDate" column="MOD_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
    </resultMap>

    <sql id="fileTagsColumns">
        ${alias}.TG_TAG_ID ${prefix}TG_TAG_ID,
        ${alias}.NAME ${prefix}NAME,
        ${alias}.OWNER_AC_WG_ID ${prefix}OWNER_AC_WG_ID,
        ${alias}.OBJECT_CLASS_ID ${prefix}OBJECT_CLASS_ID,
        ${alias}.IS_SYSTEM_GENERATED ${prefix}IS_SYSTEM_GENERATED,
        ${alias}.IS_ACTIVE ${prefix}IS_ACTIVE,
        ${alias}.CREATE_DATE ${prefix}CREATE_DATE,
        ${alias}.MOD_DATE ${prefix}MOD_DATE
    </sql>

    <select id="findByOwnerWgIdAndObjectClassId" resultMap="fileTagsResultMap">
        SELECT
        <include refid="fileTagsColumns">
            <property name="alias" value="T"/>
            <property name="prefix" value=""/>
        </include>
        FROM TG_TAG T
        WHERE  T.OWNER_AC_WG_ID = #{ownerWgId}
        AND  T.OBJECT_CLASS_ID = #{objectClassId}
        <if test="isActive == true">
            AND  T.IS_ACTIVE = 1
        </if>
    </select>

</mapper>
