<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.noosh.app.repository.mybatis.order.OrderMyDeskMyBatisMapper">

    <resultMap id="orderStatusResultMap" type="com.noosh.app.commons.dto.order.OrderStatusMyDeskDTO">
        <result property="orderId" column="OR_ORDER_ID"/>
        <result property="orderStateId" column="OC_OBJECT_STATE_ID"/>
        <result property="projectId" column="PARENT_OBJECT_ID"/>
        <result property="projectName" column="X_PARENT_TITLE"/>
        <result property="orderName" column="orderTitle"/>
        <result property="orderTypeId" column="OR_ORDER_TYPE_ID"/>
        <result property="parentOrderId" column="PARENT_OR_ORDER_ID"/>
        <result property="buyerWorkgroupId" column="BUYER_AC_WORKGROUP_ID"/>
        <result property="supplierWorkgroupId" column="SUPPLIER_AC_WORKGROUP_ID"/>
        <association property="changeOrderCount" column="OR_ORDER_ID" javaType="long" select="getChangeOrderCount"/>
    </resultMap>

    <resultMap id="orderSupplierResultMap" type="com.noosh.app.commons.dto.order.OrderSupplierMyDeskDTO">
        <result property="orderId" column="OR_ORDER_ID"/>
        <result property="orderStateId" column="OC_OBJECT_STATE_ID"/>
        <result property="projectId" column="PARENT_OBJECT_ID"/>
        <result property="projectName" column="X_PARENT_TITLE"/>
        <result property="orderName" column="orderTitle"/>
        <result property="orderTypeId" column="OR_ORDER_TYPE_ID"/>
        <result property="parentOrderId" column="PARENT_OR_ORDER_ID"/>
        <result property="buyerWorkgroupId" column="BUYER_AC_WORKGROUP_ID"/>
        <result property="supplierWorkgroupId" column="SUPPLIER_AC_WORKGROUP_ID"/>
        <result property="supplierWorkgroupName" column="supplierWorkgroupName"/>
        <association property="amount" column="OR_ORDER_ID" javaType="bigDecimal" select="getTotalPriceByOrderId"/>
        <association property="taxAndShipping" column="OR_ORDER_ID" javaType="bigDecimal" select="getTotalTaxAndShippingByOrderId"/>
        <association property="changeOrderCount" column="OR_ORDER_ID" javaType="long" select="getChangeOrderCount"/>
    </resultMap>

    <resultMap id="orderInvoiceResultMap" type="com.noosh.app.commons.dto.order.OrderInvoiceMyDeskDTO">
        <result property="orderId" column="OR_ORDER_ID"/>
        <result property="projectId" column="PARENT_OBJECT_ID"/>
        <result property="projectName" column="X_PARENT_TITLE"/>
        <result property="orderName" column="orderTitle"/>
        <result property="orderTypeId" column="OR_ORDER_TYPE_ID"/>
        <result property="parentOrderId" column="PARENT_OR_ORDER_ID"/>
        <result property="invoiceId" column="PC_INVOICE_ID"/>
        <result property="buyerWorkgroupId" column="BUYER_AC_WORKGROUP_ID"/>
        <result property="supplierWorkgroupId" column="SUPPLIER_AC_WORKGROUP_ID"/>
        <association property="orderAmount" column="OR_ORDER_ID" javaType="bigDecimal" select="getTotalPriceByOrderId"/>
        <association property="orderTaxAndShipping" column="OR_ORDER_ID" javaType="bigDecimal" select="getTotalTaxAndShippingByOrderId"/>
        <association property="invoiceAmount" column="PC_INVOICE_ID" javaType="bigDecimal" select="getTotalPriceByInvoiceId"/>
        <association property="invoiceTaxAndShipping" column="PC_INVOICE_ID" javaType="bigDecimal" select="getTotalTaxAndShippingByInvoiceId"/>
        <association property="changeOrderCount" column="OR_ORDER_ID" javaType="long" select="getChangeOrderCount"/>
        <association property="invoiceQuantity" column="PC_INVOICE_ID" javaType="bigDecimal" select="getTotalQuantityByInvoiceId"/>
        <association property="orderQuantity" column="OR_ORDER_ID" javaType="bigDecimal" select="getTotalQuantityByOrderId"/>
    </resultMap>

    <resultMap id="orderPendingResultMap" type="com.noosh.app.commons.dto.order.OrderPendingMyDeskDTO">
        <result property="orderId" column="OR_ORDER_ID"/>
        <result property="orderStateId" column="OC_OBJECT_STATE_ID"/>
        <result property="projectId" column="PARENT_OBJECT_ID"/>
        <result property="projectName" column="X_PARENT_TITLE"/>
        <result property="orderName" column="orderTitle"/>
        <result property="orderTypeId" column="OR_ORDER_TYPE_ID"/>
        <result property="parentOrderId" column="PARENT_OR_ORDER_ID"/>
        <result property="buyerWorkgroupId" column="BUYER_AC_WORKGROUP_ID"/>
        <result property="supplierWorkgroupId" column="SUPPLIER_AC_WORKGROUP_ID"/>
        <result property="orderStatusStrId" column="DESCRIPTION_STR_ID"/>
        <association property="changeOrderCount" column="OR_ORDER_ID" javaType="long" select="getChangeOrderCount"/>
    </resultMap>

    <resultMap id="invoicePendingResultMap" type="com.noosh.app.commons.dto.invoice.InvoicePendingMyDeskDTO">
        <result property="projectId" column="PARENT_OBJECT_ID"/>
        <result property="projectName" column="X_PARENT_TITLE"/>
        <result property="invoiceId" column="PC_INVOICE_ID"/>
        <result property="buyerWorkgroupId" column="BUYER_AC_WORKGROUP_ID"/>
        <result property="supplierWorkgroupId" column="SUPPLIER_AC_WORKGROUP_ID"/>
    </resultMap>

    <select id="findOrderWithStatus" resultMap="orderStatusResultMap">
        SELECT oos.OC_OBJECT_STATE_ID, oo.OR_ORDER_ID, sc.PARENT_OBJECT_ID, sc.X_PARENT_TITLE,
            DECODE(oov.TITLE, NULL, oov.REFERENCE,oov.TITLE) AS orderTitle, oov.OR_ORDER_TYPE_ID, oo.PARENT_OR_ORDER_ID,
        oov.BUYER_AC_WORKGROUP_ID, oov.SUPPLIER_AC_WORKGROUP_ID
        FROM OR_ORDER oo, OR_ORDER_VERSION oov, OR_ORDER_STATE oos, SY_CONTAINABLE sc
        <if test="isMyProject">
            , (
            SELECT
            TM_TEAM_OBJECT.OBJECT_ID , TM_TEAM_MEMBER.RE_ROLE_ID
            FROM
            TM_TEAM_MEMBER,
            TM_TEAM_OBJECT
            WHERE
            TM_TEAM_MEMBER.TM_TEAM_ID = TM_TEAM_OBJECT.TM_TEAM_ID
            AND TM_TEAM_MEMBER.USER_ID = #{userId}
            AND TM_TEAM_MEMBER.CURRENT_RECORD = 1
            AND TM_TEAM_OBJECT.OC_OBJECT_CLASS_ID = 1000000
            ) ACCESS_SQL
        </if>
        <if test="!isMyProject">
            , PM_PROJECT PM
        </if>
            WHERE oo.OR_ORDER_ID = oov.OR_ORDER_ID AND oov.IS_CURRENT = 1 AND oo.OR_ORDER_ID = oos.OR_ORDER_ID
            AND oos.IS_CURRENT = 1 AND sc.object_id = oov.or_order_id AND sc.object_class_id in (1000117, 2500785)
            AND sc.PARENT_OBJECT_CLASS_ID = 1000000
        <if test="isMyProject">
            AND sc.PARENT_OBJECT_ID =  ACCESS_SQL.OBJECT_ID
            AND EXISTS (SELECT 'x' FROM RE_ACCESS_RULE_REL    WHERE ACCESS_SQL.RE_ROLE_ID = RE_ROLE_ID
            AND RE_ACCESS_RULE_ID = 1001120)
        </if>
        <if test="!isMyProject">
            AND sc.PARENT_OBJECT_ID =  PM.PM_PROJECT_ID AND PM.OWNER_AC_WORKGROUP_ID = #{workgroupId}
        </if>
            AND oov.ORDER_CREATE_DATE &gt;= SYSDATE - 90
    </select>

    <select id="findOrderWithSupplier" resultMap="orderSupplierResultMap">
        SELECT oos.OC_OBJECT_STATE_ID, oo.OR_ORDER_ID, sc.PARENT_OBJECT_ID, sc.X_PARENT_TITLE,
        DECODE(oov.TITLE, NULL, oov.REFERENCE,oov.TITLE) AS orderTitle, oov.OR_ORDER_TYPE_ID, oo.PARENT_OR_ORDER_ID,
        oov.BUYER_AC_WORKGROUP_ID, oov.SUPPLIER_AC_WORKGROUP_ID, aw.NAME as supplierWorkgroupName, oov.OR_ORDER_VERSION_ID
        FROM OR_ORDER oo, OR_ORDER_VERSION oov, OR_ORDER_STATE oos, SY_CONTAINABLE sc, AC_WORKGROUP aw
        <if test="isMyProject">
            , (
            SELECT
            TM_TEAM_OBJECT.OBJECT_ID , TM_TEAM_MEMBER.RE_ROLE_ID
            FROM
            TM_TEAM_MEMBER,
            TM_TEAM_OBJECT
            WHERE
            TM_TEAM_MEMBER.TM_TEAM_ID = TM_TEAM_OBJECT.TM_TEAM_ID
            AND TM_TEAM_MEMBER.USER_ID = #{userId}
            AND TM_TEAM_MEMBER.CURRENT_RECORD = 1
            AND TM_TEAM_OBJECT.OC_OBJECT_CLASS_ID = 1000000
            ) ACCESS_SQL
        </if>
        <if test="!isMyProject">
            , PM_PROJECT PM
        </if>
        WHERE oo.OR_ORDER_ID = oov.OR_ORDER_ID AND oov.IS_CURRENT = 1 AND oo.OR_ORDER_ID = oos.OR_ORDER_ID
        AND oos.IS_CURRENT = 1 AND sc.object_id = oov.or_order_id AND sc.object_class_id in (1000117, 2500785)
        AND sc.PARENT_OBJECT_CLASS_ID = 1000000 AND aw.AC_WORKGROUP_ID = oov.SUPPLIER_AC_WORKGROUP_ID
        AND oov.BUYER_AC_WORKGROUP_ID = #{workgroupId} AND oov.SUPPLIER_AC_WORKGROUP_ID != #{workgroupId}
        <if test="isMyProject">
            AND sc.PARENT_OBJECT_ID =  ACCESS_SQL.OBJECT_ID
            AND EXISTS (SELECT 'x' FROM RE_ACCESS_RULE_REL    WHERE ACCESS_SQL.RE_ROLE_ID = RE_ROLE_ID
            AND RE_ACCESS_RULE_ID = 1001120)
        </if>
        <if test="!isMyProject">
            AND sc.PARENT_OBJECT_ID =  PM.PM_PROJECT_ID AND PM.OWNER_AC_WORKGROUP_ID = #{workgroupId}
        </if>
        <if test='dateType.equals("0")'>
            AND oov.ACCEPT_DATE IS NOT NULL AND oov.ACCEPT_DATE &gt;= SYSDATE - #{dateRange}
        </if>
        <if test='dateType.equals("1")'>
            AND oos.MOD_DATE &gt;= SYSDATE - #{dateRange} AND oos.OC_OBJECT_STATE_ID = 2500045
        </if>
        ORDER BY oos.OC_OBJECT_STATE_ID, oo.OR_ORDER_ID ASC
    </select>

    <select id="getTotalPriceByOrderId" parameterType="long" resultType="bigDecimal">
        SELECT  sum(oi.value) value
        FROM OR_ORDER_ITEM oi, OR_ORDER oo , OR_ORDER_STATE oos
        WHERE oi.or_order_version_id = oo.OR_ORDER_VERSION_ID AND oo.PARENT_OR_ORDER_ID = #{orderId} AND oos.OR_ORDER_ID = oo.OR_ORDER_ID AND oos.IS_CURRENT= 1
        AND ((oos.OC_OBJECT_STATE_ID IN (2000078, 2500064, 2500043, 2500044, 2500065, 2500045, 2500074)  AND oo.PARENT_OR_ORDER_ID != oo.OR_ORDER_ID )
        OR oo.PARENT_OR_ORDER_ID = oo.OR_ORDER_ID)
    </select>

    <select id="getTotalTaxAndShippingByOrderId" parameterType="long" resultType="bigDecimal">
        SELECT  sum(DECODE(oov.tax, NULL, 0, oov.tax) + DECODE(oov.shipping, NULL, 0, oov.shipping)) value
        FROM OR_ORDER oo , OR_ORDER_STATE oos, OR_ORDER_VERSION oov
        WHERE oo.PARENT_OR_ORDER_ID = #{orderId} AND oos.OR_ORDER_ID = oo.OR_ORDER_ID AND oov.or_order_version_id = oo.or_order_version_id AND oos.IS_CURRENT= 1
        AND ((oos.OC_OBJECT_STATE_ID IN (2000078, 2500064, 2500043, 2500044, 2500065, 2500045, 2500074)  AND oo.PARENT_OR_ORDER_ID != oo.OR_ORDER_ID )
        OR oo.PARENT_OR_ORDER_ID = oo.OR_ORDER_ID)
    </select>

    <select id="getChangeOrderCount" parameterType="long" resultType="long">
        SELECT COUNT(*) FROM OR_ORDER oo WHERE oo.PARENT_OR_ORDER_ID = #{orderId} AND oo.OR_ORDER_ID  != #{orderId}
    </select>

    <select id="getTotalPriceByInvoiceId" parameterType="long" resultType="bigDecimal">
        SELECT  sum(oi.AMOUNT) value
        FROM PC_INVOICE_ITEM oi
        WHERE oi.PC_INVOICE_ID = #{invoiceId}
    </select>

    <select id="getTotalQuantityByInvoiceId" parameterType="long" resultType="bigDecimal">
        SELECT  sum(oi.QUANTITY) QUANTITY
        FROM PC_INVOICE_ITEM oi
        WHERE oi.PC_INVOICE_ID = #{invoiceId}
    </select>

    <select id="getTotalQuantityByOrderId" parameterType="long" resultType="bigDecimal">
        SELECT  SUM(ooi.QUANTITY) quantity
        FROM OR_ORDER_ITEM ooi, OR_ORDER oo
        WHERE oo.OR_ORDER_VERSION_ID =  ooi.OR_ORDER_VERSION_ID AND  oo.OR_ORDER_ID = #{orderId}
    </select>

    <select id="getTotalTaxAndShippingByInvoiceId" parameterType="long" resultType="bigDecimal">
        SELECT  sum(DECODE(oi.TAX, NULL, 0, oi.TAX) + DECODE(oi.SHIPPING, NULL, 0, oi.SHIPPING)) value
        FROM PC_INVOICE oi
        WHERE oi.PC_INVOICE_ID = #{invoiceId}
    </select>

    <select id="findOrderWithInvoice" resultMap="orderInvoiceResultMap">
        SELECT O.OR_ORDER_ID, O.PARENT_OBJECT_ID, O.X_PARENT_TITLE, O.BUYER_AC_WORKGROUP_ID, O.SUPPLIER_AC_WORKGROUP_ID, O.OR_ORDER_VERSION_ID,
            O.orderTitle, O.OR_ORDER_TYPE_ID, O.PARENT_OR_ORDER_ID, PCI.PC_INVOICE_ID FROM (
            SELECT oos.OC_OBJECT_STATE_ID, oo.OR_ORDER_ID, sc.PARENT_OBJECT_ID, sc.X_PARENT_TITLE,
            DECODE(oov.TITLE, NULL, oov.REFERENCE,oov.TITLE) AS orderTitle, oov.OR_ORDER_TYPE_ID, oo.PARENT_OR_ORDER_ID,
        oov.BUYER_AC_WORKGROUP_ID, oov.SUPPLIER_AC_WORKGROUP_ID, oov.OR_ORDER_VERSION_ID
        FROM OR_ORDER oo, OR_ORDER_VERSION oov, OR_ORDER_STATE oos, SY_CONTAINABLE sc
        <if test="isMyProject">
            , (
            SELECT
            TM_TEAM_OBJECT.OBJECT_ID,TM_TEAM_MEMBER.RE_ROLE_ID
            FROM
            TM_TEAM_MEMBER,
            TM_TEAM_OBJECT
            WHERE
            TM_TEAM_MEMBER.TM_TEAM_ID = TM_TEAM_OBJECT.TM_TEAM_ID
            AND TM_TEAM_MEMBER.USER_ID = #{userId}
            AND TM_TEAM_MEMBER.CURRENT_RECORD = 1
            AND TM_TEAM_OBJECT.OC_OBJECT_CLASS_ID = 1000000
            ) ACCESS_SQL
        </if>
        <if test="!isMyProject">
            , PM_PROJECT PM
        </if>
            WHERE oo.OR_ORDER_ID = oov.OR_ORDER_ID AND oov.IS_CURRENT = 1 AND oo.OR_ORDER_ID = oos.OR_ORDER_ID
            AND oos.IS_CURRENT = 1 AND sc.object_id = oov.or_order_id AND sc.object_class_id in (1000117, 2500785)
            AND sc.PARENT_OBJECT_CLASS_ID = 1000000
        <if test="isMyProject">
            AND sc.PARENT_OBJECT_ID =  ACCESS_SQL.OBJECT_ID
            AND EXISTS (SELECT 'x' FROM RE_ACCESS_RULE_REL    WHERE ACCESS_SQL.RE_ROLE_ID = RE_ROLE_ID
            AND RE_ACCESS_RULE_ID = 1001120)
        </if>
        <if test="!isMyProject">
            AND sc.PARENT_OBJECT_ID =  PM.PM_PROJECT_ID AND PM.OWNER_AC_WORKGROUP_ID = #{workgroupId}
            AND EXISTS (SELECT 'x' FROM TM_TEAM_MEMBER ttm, TM_TEAM_OBJECT tto, RE_ACCESS_RULE_REL rar
            WHERE tto.OC_OBJECT_CLASS_ID = 1000100 AND tto.OBJECT_ID = #{workgroupId} AND tto.TM_TEAM_ID = ttm.TM_TEAM_ID
            AND ttm.USER_ID = #{userId} AND ttm.RE_ROLE_ID = rar.RE_ROLE_ID AND rar.RE_ACCESS_RULE_ID = 1001120)
        </if>
        <if test="isBuy">
            AND oov.BUYER_AC_WORKGROUP_ID = #{workgroupId}
        </if>
        <if test="isSell">
            AND oov.SUPPLIER_AC_WORKGROUP_ID = #{workgroupId}
        </if>
            AND oov.ACCEPT_DATE IS NOT NULL AND oov.ACCEPT_DATE >= SYSDATE - #{dateRange}
            ORDER BY oos.OC_OBJECT_STATE_ID, oo.OR_ORDER_ID ASC) O
            LEFT JOIN
            (SELECT pi2.PC_INVOICE_ID, sc2.PARENT_OBJECT_ID, pi2.OR_ORDER_ID FROM PC_INVOICE pi2, SY_CONTAINABLE sc2 WHERE pi2.IS_TEMPLATE = 0
             AND sc2.PARENT_OBJECT_CLASS_ID = 1000000 AND sc2.OBJECT_ID = pi2.PC_INVOICE_ID AND sc2.OBJECT_CLASS_ID = 1000119
             AND pi2.OC_OBJECT_STATE_ID = 2500090) PCI
            ON PCI.PARENT_OBJECT_ID = O.PARENT_OBJECT_ID AND PCI.OR_ORDER_ID = O.OR_ORDER_ID
            ORDER BY PCI.PC_INVOICE_ID NULLS FIRST
    </select>

    <select id="findOrderPending" resultMap="orderPendingResultMap">
        SELECT oos.OC_OBJECT_STATE_ID, oo.OR_ORDER_ID, sc.PARENT_OBJECT_ID, sc.X_PARENT_TITLE,
        DECODE(oov.TITLE, NULL, oov.REFERENCE,oov.TITLE) AS orderTitle, oov.OR_ORDER_TYPE_ID, oo.PARENT_OR_ORDER_ID,
        oov.BUYER_AC_WORKGROUP_ID, oov.SUPPLIER_AC_WORKGROUP_ID, OS.DESCRIPTION_STR_ID
        FROM OR_ORDER oo, OR_ORDER_VERSION oov, OR_ORDER_STATE oos, SY_CONTAINABLE sc, OC_OBJECT_STATE OS
        <if test="isMyProject">
            , (
            SELECT
            TM_TEAM_OBJECT.OBJECT_ID , TM_TEAM_MEMBER.RE_ROLE_ID
            FROM
            TM_TEAM_MEMBER,
            TM_TEAM_OBJECT
            WHERE
            TM_TEAM_MEMBER.TM_TEAM_ID = TM_TEAM_OBJECT.TM_TEAM_ID
            AND TM_TEAM_MEMBER.USER_ID = #{userId}
            AND TM_TEAM_MEMBER.CURRENT_RECORD = 1
            AND TM_TEAM_OBJECT.OC_OBJECT_CLASS_ID = 1000000
            ) ACCESS_SQL
        </if>
        <if test="!isMyProject">
            , PM_PROJECT PM
        </if>
        WHERE oo.OR_ORDER_ID = oov.OR_ORDER_ID AND oov.IS_CURRENT = 1 AND oo.OR_ORDER_ID = oos.OR_ORDER_ID
        AND oos.IS_CURRENT = 1 AND sc.object_id = oov.or_order_id AND sc.object_class_id in (1000117, 2500785)
        AND sc.PARENT_OBJECT_CLASS_ID = 1000000 AND oos.OC_OBJECT_STATE_ID = OS.OC_OBJECT_STATE_ID
        <if test="isMyProject">
            AND sc.PARENT_OBJECT_ID =  ACCESS_SQL.OBJECT_ID
            AND EXISTS (SELECT 'x' FROM RE_ACCESS_RULE_REL    WHERE ACCESS_SQL.RE_ROLE_ID = RE_ROLE_ID
            AND RE_ACCESS_RULE_ID = 1001120)
        </if>
        <if test="!isMyProject">
            AND sc.PARENT_OBJECT_ID =  PM.PM_PROJECT_ID AND PM.OWNER_AC_WORKGROUP_ID = #{workgroupId}
        </if>
        <if test="isOverdue or isDue">
            AND oov.ORDER_CREATE_DATE &gt;= SYSDATE - 90
        </if>
        <if test="startDate != null and startDate.length > 0">
            <if test="isOverdue or isDue">
                AND oov.COMPLETION_DATE &gt;= TO_date(#{startDate}, 'MM/dd/yyyy HH24:MI')
            </if>
            <if test="isPending">
                AND oov.ORDER_CREATE_DATE &gt;= TO_date(#{startDate}, 'MM/dd/yyyy HH24:MI')
            </if>
        </if>
        <if test="endDate != null and endDate.length > 0">
            <if test="isOverdue or isDue">
                AND oov.COMPLETION_DATE &lt;= TO_date(#{endDate}, 'MM/dd/yyyy HH24:MI')
            </if>
            <if test="isPending">
                AND oov.ORDER_CREATE_DATE &lt;= TO_date(#{endDate}, 'MM/dd/yyyy HH24:MI')
            </if>
        </if>
        <if test="isOverdue">
            AND oos.OC_OBJECT_STATE_ID IN (2000078, 2000081, 2000082, 2000085, 2000086, 2000079)
            AND oo.OR_ORDER_ID NOT IN (
            SELECT OBJECT_ID FROM MY_OVERDUE_EXCLUDE
            WHERE OBJECT_CLASS_ID = 1000117
            AND CREATE_USER_ID = #{userId}
            AND CREATE_DATE &gt;= (SYSDATE - 90)
            )        
		</if>
        <if test="isDue">
            AND oos.OC_OBJECT_STATE_ID IN (2000081, 2000082, 2000085, 2000086, 2000079)
        </if>
        <if test="isPending">
            AND oos.OC_OBJECT_STATE_ID IN (2000030, 2000081, 2000082, 2000085, 2000086, 2000079)
        </if>
        ORDER BY oo.OR_ORDER_ID ASC
    </select>

    <select id="findChangeOrderPending" resultMap="orderPendingResultMap">
        SELECT oos.OC_OBJECT_STATE_ID, oo.OR_ORDER_ID, sc.PARENT_OBJECT_ID, sc.X_PARENT_TITLE,
        DECODE(oov.TITLE, NULL, oov.REFERENCE,oov.TITLE) AS orderTitle, oov.OR_ORDER_TYPE_ID, oo.PARENT_OR_ORDER_ID,
        oov.BUYER_AC_WORKGROUP_ID, oov.SUPPLIER_AC_WORKGROUP_ID, OS.DESCRIPTION_STR_ID
        FROM OR_ORDER oo, OR_ORDER_VERSION oov, OR_ORDER_STATE oos, SY_CONTAINABLE sc, OC_OBJECT_STATE OS
        <if test="isMyProject">
            , (
            SELECT
            TM_TEAM_OBJECT.OBJECT_ID , TM_TEAM_MEMBER.RE_ROLE_ID
            FROM
            TM_TEAM_MEMBER,
            TM_TEAM_OBJECT
            WHERE
            TM_TEAM_MEMBER.TM_TEAM_ID = TM_TEAM_OBJECT.TM_TEAM_ID
            AND TM_TEAM_MEMBER.USER_ID = #{userId}
            AND TM_TEAM_MEMBER.CURRENT_RECORD = 1
            AND TM_TEAM_OBJECT.OC_OBJECT_CLASS_ID = 1000000
            ) ACCESS_SQL
        </if>
        <if test="!isMyProject">
            , PM_PROJECT PM
        </if>
        WHERE oo.OR_ORDER_ID = oov.OR_ORDER_ID AND oov.IS_CURRENT = 1 AND oo.OR_ORDER_ID = oos.OR_ORDER_ID
        AND oos.IS_CURRENT = 1 AND sc.object_id = oo.parent_or_order_id AND sc.object_class_id in (1000117, 2500785)
        AND sc.PARENT_OBJECT_CLASS_ID = 1000000  AND oov.OR_ORDER_TYPE_ID = 1000002 AND oos.OC_OBJECT_STATE_ID = OS.OC_OBJECT_STATE_ID
        <if test="isMyProject">
            AND sc.PARENT_OBJECT_ID =  ACCESS_SQL.OBJECT_ID
            AND EXISTS (SELECT 'x' FROM RE_ACCESS_RULE_REL    WHERE ACCESS_SQL.RE_ROLE_ID = RE_ROLE_ID
            AND RE_ACCESS_RULE_ID = 1001120)
        </if>
        <if test="!isMyProject">
            AND sc.PARENT_OBJECT_ID =  PM.PM_PROJECT_ID AND PM.OWNER_AC_WORKGROUP_ID = #{workgroupId}
        </if>
        <if test="isOverdue or isDue">
            AND oov.ORDER_CREATE_DATE &gt;= SYSDATE - 90
        </if>
        <if test="startDate != null and startDate.length > 0">
            <if test="isOverdue or isDue">
                AND oov.COMPLETION_DATE &gt;= TO_date(#{startDate}, 'MM/dd/yyyy HH24:MI')
            </if>
            <if test="isPending">
                AND oov.ORDER_CREATE_DATE &gt;= TO_date(#{startDate}, 'MM/dd/yyyy HH24:MI')
            </if>
        </if>
        <if test="endDate != null and endDate.length > 0">
            <if test="isOverdue or isDue">
                AND oov.COMPLETION_DATE &lt;= TO_date(#{endDate}, 'MM/dd/yyyy HH24:MI')
            </if>
            <if test="isPending">
                AND oov.ORDER_CREATE_DATE &lt;= TO_date(#{endDate}, 'MM/dd/yyyy HH24:MI')
            </if>
        </if>
        <if test="isOverdue">
            AND (oos.OC_OBJECT_STATE_ID IN (2000078, 2000081, 2000082, 2000085, 2000086) OR
            (oos.OC_OBJECT_STATE_ID = 2000079 AND EXISTS (SELECT 'X' from AC_ACCOUNT_USER aau where aau.USER_ID = oov.CREATE_USER_ID AND aau.AC_WORKGROUP_ID = #{workgroupId})))
            AND oo.OR_ORDER_ID NOT IN (
            SELECT OBJECT_ID FROM MY_OVERDUE_EXCLUDE
            WHERE OBJECT_CLASS_ID = 1000117
            AND CREATE_USER_ID = #{userId}
            AND CREATE_DATE &gt;= (SYSDATE - 90)
            )        
		</if>
        <if test="isDue">
            AND (oos.OC_OBJECT_STATE_ID IN (2000081, 2000082, 2000085, 2000086) OR
            (oos.OC_OBJECT_STATE_ID = 2000079 AND EXISTS (SELECT 'X' from AC_ACCOUNT_USER aau where aau.USER_ID = oov.CREATE_USER_ID AND aau.AC_WORKGROUP_ID = #{workgroupId})))
        </if>
        <if test="isPending">
            AND (oos.OC_OBJECT_STATE_ID IN (2000030, 2000081, 2000082, 2000085, 2000086) OR
            (oos.OC_OBJECT_STATE_ID = 2000079 AND EXISTS (SELECT 'X' from AC_ACCOUNT_USER aau where aau.USER_ID = oov.CREATE_USER_ID AND aau.AC_WORKGROUP_ID = #{workgroupId})))
        </if>
        ORDER BY oo.OR_ORDER_ID ASC
    </select>

    <select id="findInvoicePending" resultMap="invoicePendingResultMap">
        SELECT pi.PC_INVOICE_ID, sc.PARENT_OBJECT_ID, sc.X_PARENT_TITLE,
        pi.BUYER_AC_WORKGROUP_ID, pi.SUPPLIER_AC_WORKGROUP_ID
        FROM PC_INVOICE pi, SY_CONTAINABLE sc
        <if test="isMyProject">
            , (
            SELECT
            TM_TEAM_OBJECT.OBJECT_ID , TM_TEAM_MEMBER.RE_ROLE_ID
            FROM
            TM_TEAM_MEMBER,
            TM_TEAM_OBJECT
            WHERE
            TM_TEAM_MEMBER.TM_TEAM_ID = TM_TEAM_OBJECT.TM_TEAM_ID
            AND TM_TEAM_MEMBER.USER_ID = #{userId}
            AND TM_TEAM_MEMBER.CURRENT_RECORD = 1
            AND TM_TEAM_OBJECT.OC_OBJECT_CLASS_ID = 1000000
            ) ACCESS_SQL
        </if>
        <if test="!isMyProject">
            , PM_PROJECT PM
        </if>
        WHERE sc.object_id = pi.PC_INVOICE_ID AND sc.object_class_id = 1000119
        AND sc.PARENT_OBJECT_CLASS_ID = 1000000 AND pi.IS_TEMPLATE = 0
        <if test="isMyProject">
            AND sc.PARENT_OBJECT_ID =  ACCESS_SQL.OBJECT_ID
            AND EXISTS (SELECT 'x' FROM RE_ACCESS_RULE_REL    WHERE ACCESS_SQL.RE_ROLE_ID = RE_ROLE_ID
            AND RE_ACCESS_RULE_ID = 2501334)
        </if>
        <if test="!isMyProject">
            AND sc.PARENT_OBJECT_ID =  PM.PM_PROJECT_ID AND PM.OWNER_AC_WORKGROUP_ID = #{workgroupId}
        </if>
        AND pi.CREATE_DATE is not null
        <if test="isOverdue or isDue">
            AND pi.CREATE_DATE &gt;= SYSDATE - 90
        </if>
        <if test="startDate != null and startDate.length > 0">
            <if test="isOverdue or isDue">
                AND pi.DUE_DATE is not null and pi.DUE_DATE &gt;= TO_date(#{startDate}, 'MM/dd/yyyy HH24:MI')
            </if>
            <if test="isPending">
                AND pi.CREATE_DATE &gt;= TO_date(#{startDate}, 'MM/dd/yyyy HH24:MI')
            </if>
        </if>
        <if test="endDate != null and endDate.length > 0">
            <if test="isOverdue or isDue">
                AND pi.DUE_DATE is not null and pi.DUE_DATE &lt;= TO_date(#{endDate}, 'MM/dd/yyyy HH24:MI')
            </if>
            <if test="isPending">
                AND pi.CREATE_DATE &lt;= TO_date(#{endDate}, 'MM/dd/yyyy HH24:MI')
            </if>

        </if>
        <if test="isOverdue">
            AND pi.PC_INVOICE_ID NOT IN (
            SELECT OBJECT_ID FROM MY_OVERDUE_EXCLUDE
            WHERE OBJECT_CLASS_ID = 1000119
            AND CREATE_USER_ID = #{userId}
            AND CREATE_DATE &gt;= (SYSDATE - 90)
            )
        </if>		
        AND pi.OC_OBJECT_STATE_ID in (2500089, 2500088)
        ORDER BY pi.PC_INVOICE_ID ASC
    </select>





</mapper>