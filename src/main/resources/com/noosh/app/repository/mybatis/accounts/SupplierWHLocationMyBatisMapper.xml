<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.accounts.SupplierWHLocationMyBatisMapper">

    <resultMap id="supplierWHLocationResultMap" type="com.noosh.app.commons.dto.accounts.SupplierWHLocationDTO">
        <result property="id" column="BU_SUPPLIER_WHLOCATION_ID"/>
        <result property="idCode" column="ID_CODE"/>
        <result property="supplierId" column="BU_SUPPLIER_WORKGROUP_ID"/>
        <result property="warehouseLocationId" column="AC_WHLOCATION_ID"/>
        <association property="warehouseLocation" columnPrefix="WH_" resultMap="com.noosh.app.repository.mybatis.accounts.WarehouseLocationMyBatisMapper.warehouseLocationResultMap"/>
    </resultMap>

    <sql id="supplierWHLocationColumns">
        ${alias}.BU_SUPPLIER_WHLOCATION_ID ${prefix}BU_SUPPLIER_WHLOCATION_ID,
        ${alias}.ID_CODE ${prefix}ID_CODE,
        ${alias}.BU_SUPPLIER_WORKGROUP_ID ${prefix}BU_SUPPLIER_WORKGROUP_ID,
        ${alias}.AC_WHLOCATION_ID ${prefix}AC_WHLOCATION_ID
    </sql>

    <select id="findAll" resultMap="supplierWHLocationResultMap">
        SELECT
            <include refid="supplierWHLocationColumns">
                <property name="alias" value="SWH"/>
                <property name="prefix" value=""/>
            </include>,
            <include refid="com.noosh.app.repository.mybatis.accounts.WarehouseLocationMyBatisMapper.warehouseLocationColumns">
                <property name="alias" value="WH"/>
                <property name="prefix" value="WH_"/>
            </include>,
            <include refid="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper.addressColumns">
                <property name="alias" value="ADDR"/>
                <property name="prefix" value="WH_ADDR_"/>
            </include>,
            <include refid="com.noosh.app.repository.mybatis.security.CountryMyBatisMapper.countryColumns">
                <property name="alias" value="T"/>
                <property name="prefix" value="WH_ADDR_T_"/>
            </include>,
            <include refid="com.noosh.app.repository.mybatis.security.AccountUserMyBatisMapper.accountUserColumns">
                <property name="alias" value="U"/>
                <property name="prefix" value="WH_U_"/>
            </include>,
            <include refid="com.noosh.app.repository.mybatis.security.PersonMyBatisMapper.personColumns">
                <property name="alias" value="P"/>
                <property name="prefix" value="WH_U_P_"/>
            </include>
        FROM
            BU_SUPPLIER_WHLOCATION SWH,
            AC_WHLOCATION WH,
            AC_ADDRESS ADDR,
            AC_ACCOUNT_USER U,
            AC_PERSON P,
            AC_COUNTRY T
        WHERE
            WH.AC_WORKGROUP_ID = #{supplierWorkgroupId}
            AND WH.OC_OBJECT_STATE_ID = 2000121
            AND WH.AC_WHLOCATION_ID = SWH.AC_WHLOCATION_ID ( + )
            AND WH.AC_ADDRESS_ID = ADDR.AC_ADDRESS_ID
            AND SWH.BU_SUPPLIER_WORKGROUP_ID ( + ) = #{supplierId}
            AND U.AC_PERSON_ID = P.AC_PERSON_ID ( + )
            AND WH.CONTACT_USER_ID = U.USER_ID ( + )
            AND ADDR.AC_COUNTRY_ID = T.AC_COUNTRY_ID ( + )
        ORDER BY
	        WH.NAME
    </select>

</mapper>
