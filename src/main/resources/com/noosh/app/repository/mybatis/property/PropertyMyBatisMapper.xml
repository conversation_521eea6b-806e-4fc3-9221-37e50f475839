<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.property.PropertyMyBatisMapper">

    <resultMap id="propertyResultMap" type="com.noosh.app.commons.dto.property.PropertyAttributeDTO">
        <result property="paramName" column = "PARAM_NAME" jdbcType="VARCHAR" />
        <result property="numberValue" column = "NUMBER_VALUE" jdbcType="DECIMAL" />
        <result property="dateValue" column = "DATE_VALUE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler" />
        <result property="stringValue" column = "STRING_VALUE" jdbcType="VARCHAR" />
        <result property="prDataTypeId" column = "PR_DATA_TYPE_ID" jdbcType="DECIMAL" />
    </resultMap>

    <resultMap id="propertyAttributeDtoResultMap" type="com.noosh.app.commons.dto.property.PropertyAttributeDTO">
        <result property="prPropertyAttributeId" column="pr_property_attribute_id" jdbcType="DECIMAL" />
        <result property="paramName" column="param_name" jdbcType="VARCHAR" />
        <result property="numberValue" column="number_value" jdbcType="DECIMAL" />
        <result property="stringValue" column="string_value" jdbcType="VARCHAR" />
        <result property="dateValue" column="date_value"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="prDataTypeId" column="pr_data_type_id"/>
    </resultMap>

    <select id="getPaperDetailProperty" resultMap="propertyAttributeDtoResultMap">
        SELECT
            PP.PARAM_NAME,
            PA.NUMBER_VALUE,
            PA.STRING_VALUE,
            PA.DATE_VALUE
        FROM
            OR_ORDER_ITEM OOI,
            PR_PROPERTY_PARAM PP,
            PR_PROPERTY_ATTRIBUTE PA
        WHERE
            OOI.CUSTOM_PR_PROPERTY_ID = PA.PR_PROPERTY_ID
            AND PP.PR_PROPERTY_PARAM_id = PA.PR_PROPERTY_PARAM_id
            AND OOI.CUSTOM_PR_PROPERTY_ID = #{propertyId}
    </select>

    <select id="getEstItemPaperDetailProperty" resultMap="propertyAttributeDtoResultMap">
        SELECT
            PP.PARAM_NAME,
            PA.NUMBER_VALUE,
            PA.STRING_VALUE,
            PA.DATE_VALUE
        FROM
            EM_ESTIMATE_ITEM EEI,
            PR_PROPERTY_PARAM PP,
            PR_PROPERTY_ATTRIBUTE PA
        WHERE
            EEI.CUSTOM_PR_PROPERTY_ID = PA.PR_PROPERTY_ID
          AND PP.PR_PROPERTY_PARAM_id = PA.PR_PROPERTY_PARAM_id
          AND EEI.CUSTOM_PR_PROPERTY_ID = #{propertyId}
    </select>

    <select id="getPaperDetailCount" resultType="java.lang.Long">
        SELECT
            DISTINCT PR.PR_PROPERTY_ID
        FROM
            SP_SPEC_TYPE SPT,
            SP_SPEC SP,
            PR_PROPERTY PR,
            PR_PROPERTY_ATTRIBUTE PA,
            PR_PROPERTY_PARAM PM
        WHERE
            SP.SP_SPEC_TYPE_ID = SPT.SP_SPEC_TYPE_ID
          AND SP.PR_PROPERTY_ID = PR.PARENT_PROPERTY_ID
          AND PR.PR_PROPERTY_ID = PA.PR_PROPERTY_ID
          AND PA.PR_PROPERTY_PARAM_ID = PM.PR_PROPERTY_PARAM_ID
          AND SP.SP_SPEC_ID = #{specId}
          AND PR.PROPERTY_NAME = #{name}
          AND (
            ( SPT.AC_SOURCE_TYPE_ID = 1000000 AND PM.PARAM_NAME = 'STOCK_USED_AS' AND PA.STRING_VALUE IS NOT NULL )
                OR ( SPT.AC_SOURCE_TYPE_ID != 1000000 )
            )
    </select>

    <select id="getChildrenProperty" resultMap="propertyAttributeDtoResultMap">
        SELECT
            PA.pr_property_id,
            PA.pr_property_attribute_id,
            PP.param_name,
            PP.pr_data_type_id,
            PA.number_value,
            PA.string_value,
            PA.date_value
        FROM
            PR_PROPERTY_PARAM PP,
            PR_PROPERTY_ATTRIBUTE PA,
            PR_PROPERTY PR
        WHERE
            PR.PR_PROPERTY_ID = PA.PR_PROPERTY_ID
          AND PP.PR_PROPERTY_PARAM_id = PA.PR_PROPERTY_PARAM_id
          AND PR.PARENT_PROPERTY_ID = #{parentPropertyId}
          AND PROPERTY_NAME = #{name}
    </select>

    <select id="findBillToByPropertyId" resultMap="propertyResultMap">
        SELECT
            pp.PARAM_NAME,
            pa.STRING_VALUE
        FROM
            pr_property_attribute pa,
            pr_property_param pp
        WHERE
            pa.pr_property_id = #{propertyId}
            AND pp.pr_property_param_id = pa.pr_property_param_id
            AND pp.param_name in
            ('O_BILL_TO_str',
            'O_BILL_TO_LINE1_str',
            'O_BILL_TO_LINE2_str',
            'O_BILL_TO_LINE3_str',
            'O_BILL_TO_CITY_str',
            'O_BILL_TO_STATE_str',
            'O_BILL_TO_POSTAL_str',
            'O_BILL_TO_COUNTRY_str')
    </select>

    <select id="findSpecPropertyByPropertyId" resultMap="propertyResultMap">
        SELECT
            pp.PARAM_NAME,
            pa.NUMBER_VALUE,
            pa.DATE_VALUE,
            pa.STRING_VALUE,
            pp.PR_DATA_TYPE_ID
        FROM
            pr_property_attribute pa,
            pr_property_param pp
        WHERE
            pp.pr_property_param_id = pa.pr_property_param_id
            and pa.pr_property_id = #{propertyId}
            AND (PA.STRING_VALUE is not null
            OR PA.NUMBER_VALUE is not null
            OR PA.DATE_VALUE is not null)
    </select>

    <select id="findPropertyAttributes" resultMap="propertyAttributeDtoResultMap">
        SELECT
            pa.pr_property_id,
            pa.pr_property_attribute_id,
            pm.param_name,
            pm.pr_data_type_id,
            pa.number_value,
            pa.string_value,
            pa.date_value
        FROM
            pr_property_param pm,
            pr_property_attribute pa

        WHERE
            pm.PR_PROPERTY_PARAM_ID = pa.PR_PROPERTY_PARAM_ID
            AND pa.pr_property_id = #{propertyId}

    </select>

    <select id="getSpecPaperFields" resultMap="propertyAttributeDtoResultMap">
        SELECT
            PA.pr_property_attribute_id,
            PP.param_name,
            PP.pr_data_type_id,
            PA.number_value,
            PA.string_value,
            PA.date_value
        FROM
            PR_PROPERTY_PARAM PP,
            PR_PROPERTY_ATTRIBUTE PA,
            PR_PROPERTY PR
        WHERE
            PR.PR_PROPERTY_ID = PA.PR_PROPERTY_ID
          AND PP.PR_PROPERTY_PARAM_id = PA.PR_PROPERTY_PARAM_id
          <choose>
            <when test="paperPropertyIdList != null and !paperPropertyIdList.isEmpty()">
                AND PR.PR_PROPERTY_ID IN
                <foreach item="id" collection="paperPropertyIdList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </when>
            <otherwise>
                AND 1=0
            </otherwise>
          </choose>
          AND PROPERTY_NAME = #{name}
          AND PP.PARAM_NAME IN ('STOCK_USED_AS', 'BRAND', 'PAPER_TYPE', 'FINISH', 'WEIGHT', 'USED_AS', 'USED_AS_SUB')
        ORDER BY PR.PR_PROPERTY_ID ASC
    </select>

    <select id="deletePropertyWithPropertyIds">
        DELETE FROM pr_property WHERE pr_property_id in
        <foreach item="item" index="index" collection="propertyIds" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="deleteAttributeWithPropertyIds">
        DELETE FROM pr_property_attribute WHERE pr_property_id in
        <foreach item="item" index="index" collection="propertyIds" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>