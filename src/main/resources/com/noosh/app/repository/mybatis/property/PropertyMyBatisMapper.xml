<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.property.PropertyMyBatisMapper">

    <resultMap id="propertyAttributeDtoResultMap" type="com.noosh.app.commons.dto.property.PropertyAttributeDTO">
        <result property="prPropertyId" column="pr_property_id"/>
        <result property="prPropertyAttributeId" column="pr_property_attribute_id"/>
        <result property="paramName" column="param_name"/>
        <result property="prDataTypeId" column="pr_data_type_id"/>
        <result property="numberValue" column="number_value"/>
        <result property="stringValue" column="string_value"/>
        <result property="dateValue" column="date_value"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
    </resultMap>

    <select id="findPropertyAttributes" resultMap="propertyAttributeDtoResultMap">
        SELECT
            pa.pr_property_id,
            pa.pr_property_attribute_id,
            pm.param_name,
            pm.pr_data_type_id,
            pa.number_value,
            pa.string_value,
            pa.date_value
        FROM
            pr_property_param pm,
            pr_property_attribute pa

        WHERE
            pm.PR_PROPERTY_PARAM_ID = pa.PR_PROPERTY_PARAM_ID
            AND pa.pr_property_id = #{propertyId}

    </select>

    <select id="findPropertyAttributesByIdsAndParamNames"
            resultMap="propertyAttributeDtoResultMap">
        SELECT
        pa.pr_property_id,
        pa.pr_property_attribute_id,
        pm.param_name,
        pm.pr_data_type_id,
        pa.number_value,
        pa.string_value,
        pa.date_value
        FROM
        pr_property_param pm,
        pr_property_attribute pa

        WHERE
        pm.PR_PROPERTY_PARAM_ID = pa.PR_PROPERTY_PARAM_ID

        <if test="paramNames != null">
        AND pm.param_name IN
        <foreach collection="paramNames" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        </if>

        AND (
        pa.pr_property_id IN
        <trim suffixOverrides=" OR pa.pr_property_id IN ()">
            <foreach item="item" index="index" collection="propertyIds"
                     open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 999">) OR pa.pr_property_id IN (</when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
        </trim>
        )
    </select>

</mapper>