<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.shipment.ShipmentMyBatisMapper">

    <select id= "findRequestIdsByJobs" resultType="java.lang.Long">
        SELECT sr.SH_REQUEST_ID FROM SH_SHIPMENT ss, SH_REQUEST sr WHERE ss.SH_SHIPMENT_ID = sr.SH_SHIPMENT_ID AND ss.PC_JOB_ID in
        <foreach collection="jobIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>