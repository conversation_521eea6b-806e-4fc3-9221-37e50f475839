<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.noosh.app.repository.mybatis.estimate.EstimateMyBatisMapper">

    <resultMap id="estimateDTOResult" type="com.noosh.app.commons.dto.estimate.EstimateDTO">
        <result property="id" column="EM_ESTIMATE_ID"/>
        <result property="rfeId" column="EM_RFE_ID"/>
        <result property="reference" column="reference"/>
        <result property="ownerReference" column="ownerReference"/>
        <result property="title" column="TITLE"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="comments" column="COMMENTS"/>
        <result property="expirationDate" column="EXPIRATION_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="submitDate" column="SUBMIT_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="submitUserId" column="SUBMIT_USER_ID"/>
        <result property="customPropertyId" column="CUSTOM_PR_PROPERTY_ID"/>
        <result property="ownerWorkgroupId" column="OWNER_AC_WORKGROUP_ID"/>
        <result property="ownerUserId" column="OWNER_USER_ID"/>
        <result property="termsId" column="AC_TERMS_ID"/>
        <result property="stateId" column="OC_OBJECT_STATE_ID"/>
        <result property="isAllOrNone" column="IS_ALL_OR_NONE"/>
        <result property="autoGenerated" column="AUTO_GENERATED"/>
        <result property="behalfUserId" column="BEHALF_USER_ID"/>
        <result property="stateChangeComments" column="STATE_CHANGE_COMMENTS"/>
        <result property="objectAttr" column="OBJECT_ATTR"/>
        <result property="itemizedTaxAndShipping" column="AC_SOITEMIZED_TNSURCE_TYPE_ID"/>
        <result property="sourceTypeId" column="AC_SOURCE_TYPE_ID"/>

    </resultMap>




    <select id="findAwardedEstimates" resultMap="estimateDTOResult">
        SELECT E.EM_ESTIMATE_ID,E.EM_RFE_ID,E.REFERENCE,E.OWNER_REFERENCE,E.TITLE,E.DESCRIPTION,E.COMMENTS,E.EXPIRATION_DATE,
        E.SUBMIT_DATE,E.SUBMIT_USER_ID,E.CUSTOM_PR_PROPERTY_ID,E.OWNER_AC_WORKGROUP_ID,E.OWNER_USER_ID,E.AC_TERMS_ID,
        E.OC_OBJECT_STATE_ID,E.IS_ALL_OR_NONE,E.AUTO_GENERATED,E.BEHALF_USER_ID,E.STATE_CHANGE_COMMENTS,
        E.OBJECT_ATTR,E.ITEMIZED_TNS,E.AC_SOURCE_TYPE_ID
        FROM EM_ESTIMATE E, SY_CONTAINABLE C
        WHERE E.EM_RFE_ID = #{rfeId}
            AND E.EM_RFE_ID = C.OBJECT_ID
            AND C.OBJECT_CLASS_ID = 1000115
            AND C.PARENT_OBJECT_ID = #{projectId}
            AND C.PARENT_OBJECT_CLASS_ID = 1000000
            AND EXISTS (
                SELECT 'x' FROM
                OR_ORDER_VERSION OV , OR_ORDER_STATE OS ,OR_ORDER_ITEM OI ,EM_ESTIMATE_ITEM EI ,EM_ESTIMATE_ITEM_PRICE EIP
                WHERE OV.IS_CURRENT = 1  AND OV.OR_ORDER_ID = OS.OR_ORDER_ID
                AND OS.IS_CURRENT = 1
                AND OS.OC_OBJECT_STATE_ID not in (2000080,2000083,2000084)
                AND OV.OR_ORDER_TYPE_ID != 1000002
                AND OV.OR_ORDER_VERSION_ID = OI.OR_ORDER_VERSION_ID
                AND OI.EM_ESTIMATE_ITEM_PRICE_ID = EIP.EM_ESTIMATE_ITEM_PRICE_ID
                AND EIP.EM_ESTIMATE_ITEM_ID = EI.EM_ESTIMATE_ITEM_ID
                AND EI.EM_ESTIMATE_ID = E.EM_ESTIMATE_ID )

    </select>


</mapper>