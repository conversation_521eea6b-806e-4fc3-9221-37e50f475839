<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.collaboration.ProjectStatusMyBatisMapper">
    <resultMap id="projectStatusResultMap" type="com.noosh.app.commons.dto.collaboration.ProjectStatusDTO">
        <id property="id" column="PM_PROJECT_STATUS_ID"/>
        <result property="nameStr" column="NAME_STR"/>
        <result property="nameStrId" column="NAME_STR_ID"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="workgroupId" column="AC_WORKGROUP_ID"/>
        <result property="isDefault" column="IS_DEFAULT"/>
        <result property="statusOrder" column="STATUS_ORDER"/>
    </resultMap>

    <sql id="projectStatusColumns">
        ${alias}.PM_PROJECT_STATUS_ID ${prefix}PM_PROJECT_STATUS_ID,
        ${alias}.NAME_STR ${prefix}NAME_STR,
        ${alias}.NAME_STR_ID ${prefix}NAME_STR_ID,
        ${alias}.DESCRIPTION ${prefix}DESCRIPTION,
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID,
        ${alias}.IS_DEFAULT ${prefix}IS_DEFAULT,
        ${alias}.STATUS_ORDER ${prefix}STATUS_ORDER
    </sql>

    <select id="findProjectStatus" resultMap="projectStatusResultMap">
        <include refid="projectStatusColumns">
            <property name="alias" value="P"/>
            <property name="prefix" value="P_"/>
        </include>
    </select>

    <select id="getTotalProjectStatusInUsed" resultType="java.lang.Long">
        SELECT count(*) FROM PM_PROJECT pp WHERE pp.PM_PROJECT_STATUS_ID = #{projectStatusId}
    </select>
    
    <select id="updateProjectStatus">
        update PM_PROJECT set PM_PROJECT_STATUS_ID = #{newStatusId} where PM_PROJECT_STATUS_ID = #{oldStatusId}
    </select>


</mapper>