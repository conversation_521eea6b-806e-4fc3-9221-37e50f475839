<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.security.ObjectCounterMyBatisMapper">

    <resultMap id="objectCounterResultMap" type="com.noosh.app.commons.dto.security.ObjectCounterDTO">
        <result property="id" column="AC_OBJECT_COUNTER_ID"/>
        <result property="workgroupId" column="AC_WORKGROUP_ID"/>
        <result property="counterTypeId" column="AC_OBJECT_COUNTER_TYPE_ID"/>
        <result property="isActive" column="IS_ACTIVE"/>
        <result property="prefix" column="PREFIX"/>
        <result property="nextValue" column="NEXT_VALUE"/>
        <result property="numberOfDigits" column="NUMBER_OF_DIGITS"/>
    </resultMap>

    <sql id="objectCounterColumns">
        ${alias}.AC_OBJECT_COUNTER_ID ${prefix}AC_OBJECT_COUNTER_ID,
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID,
        ${alias}.AC_OBJECT_COUNTER_TYPE_ID ${prefix}AC_OBJECT_COUNTER_TYPE_ID,
        ${alias}.IS_ACTIVE ${prefix}IS_ACTIVE,
        ${alias}.PREFIX ${prefix}PREFIX,
        ${alias}.NEXT_VALUE ${prefix}NEXT_VALUE,
        ${alias}.NUMBER_OF_DIGITS ${prefix}NUMBER_OF_DIGITS
    </sql>

    <select id="findObjectCounter" resultMap="objectCounterResultMap">
        SELECT
        <include refid="objectCounterColumns">
            <property name="alias" value="O"/>
            <property name="prefix" value=""/>
        </include>
        FROM AC_OBJECT_COUNTER O
        WHERE O.AC_OBJECT_COUNTER_TYPE_ID = #{counterTypeId}
        AND O.AC_WORKGROUP_ID = #{workgroupId}
    </select>
</mapper>
