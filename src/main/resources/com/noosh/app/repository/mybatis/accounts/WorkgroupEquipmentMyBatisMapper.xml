<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.accounts.WorkgroupEquipmentMyBatisMapper">

    <resultMap id="workgroupEquipmentResultMap" type="com.noosh.app.commons.dto.accounts.WorkgroupEquipmentDTO">
        <id property="id" column="AC_WG_EQUIPMENT_ID"/>
        <result property="workgroupId" column="AC_WORKGROUP_ID"/>
        <result property="typeId" column="AC_EQUIPMENT_TYPE_ID"/>
        <result property="ordinalNumber" column="ORDINAL_NUMBER"/>
        <result property="quantity" column="QUANTITY"/>
        <result property="maker" column="MAKER"/>
        <result property="model" column="MODEL"/>
        <result property="width" column="WIDTH"/>
        <result property="length" column="LENGTH"/>
        <result property="uofm" column="UOFM"/>
        <result property="numberOfColor" column="COLOR_UNIT_NO"/>
        <result property="isUVCoating" column="IS_UV_COATING"/>
        <result property="isInlineFinishing" column="IS_INLINE_FINISHING"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="searchContent" column="SEARCH_CONTENT"/>
        <collection property="type" columnPrefix="P_" resultMap="com.noosh.app.repository.mybatis.accounts.EquipmentTypeMyBatisMapper.equipmentTypeResultMap"/>
        <collection property="equipmentClass" columnPrefix="S_" resultMap="com.noosh.app.repository.mybatis.accounts.EquipmentClassMyBatisMapper.equipmentClassResultMap"/>
    </resultMap>

    <sql id="workgroupEquipmentColumns">
        ${alias}.AC_WG_EQUIPMENT_ID ${prefix}AC_WG_EQUIPMENT_ID,
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID,
        ${alias}.AC_EQUIPMENT_TYPE_ID ${prefix}AC_EQUIPMENT_TYPE_ID,
        ${alias}.ORDINAL_NUMBER ${prefix}ORDINAL_NUMBER,
        ${alias}.QUANTITY ${prefix}QUANTITY,
        ${alias}.MAKER ${prefix}MAKER,
        ${alias}.MODEL ${prefix}MODEL,
        ${alias}.WIDTH ${prefix}WIDTH,
        ${alias}.LENGTH ${prefix}LENGTH,
        ${alias}.UOFM ${prefix}UOFM,
        ${alias}.COLOR_UNIT_NO ${prefix}COLOR_UNIT_NO,
        ${alias}.IS_UV_COATING ${prefix}IS_UV_COATING,
        ${alias}.IS_INLINE_FINISHING ${prefix}IS_INLINE_FINISHING,
        ${alias}.DESCRIPTION ${prefix}DESCRIPTION,
        ${alias}.SEARCH_CONTENT ${prefix}SEARCH_CONTENT
    </sql>

    <select id="findByWorkgroupId" resultMap="workgroupEquipmentResultMap">
        SELECT
        <include refid="workgroupEquipmentColumns">
            <property name="alias" value="WE"/>
            <property name="prefix" value=""/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.accounts.EquipmentClassMyBatisMapper.equipmentClassColumns">
            <property name="alias" value="S"/>
            <property name="prefix" value="S_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.accounts.EquipmentTypeMyBatisMapper.equipmentTypeColumns">
            <property name="alias" value="P"/>
            <property name="prefix" value="P_"/>
        </include>
        FROM
            AC_WG_EQUIPMENT WE,
            AC_EQUIPMENT_CLASS S,
            AC_EQUIPMENT_TYPE P
        WHERE
            WE.AC_WORKGROUP_ID = #{workgroupId}
            AND WE.AC_EQUIPMENT_TYPE_ID = P.AC_EQUIPMENT_TYPE_ID
            AND S.AC_EQUIPMENT_CLASS_ID = P.AC_EQUIPMENT_CLASS_ID
        ORDER BY
            WE.ORDINAL_NUMBER
    </select>
</mapper>
