<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.spec.SpecTypeMyBatisMapper">

    <resultMap id="specTypeResultMap" type="com.noosh.app.commons.dto.spec.SpecTypeDTO">
        <result property="id" column="SP_SPEC_TYPE_ID"/>
        <result property="labelStrId" column="LABEL_STR_ID"/>
        <result property="labelStr" column="LABEL_STR"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="templateBaseName" column="TEMPLATE_BASE_NAME"/>
        <result property="isDeprecated" column="IS_DEPRECATED"/>
        <result property="deprecationReason" column="DEPRECATION_REASON"/>
        <result property="deprecationDate" column="DEPRECATION_DATE"/>
        <result property="replacedBySpecTypeId" column="REPLACED_BY_SPEC_TYPE_ID"/>
        <result property="icon" column="ICON"/>
        <result property="image" column="IMAGE"/>
        <result property="isDirectMail" column="IS_DIRECT_MAIL"/>
        <result property="isVisible" column="IS_VISIBLE"/>
        <result property="originalSpecTypeId" column="ORIGINAL_SPEC_TYPE_ID"/>
        <result property="parentSpecTypeId" column="PARENT_SPEC_TYPE_ID"/>
        <result property="versionNumber" column="VERSION_NUMBER"/>
        <result property="isCurrent" column="IS_CURRENT"/>
        <result property="isCampaign" column="IS_CAMPAIGN"/>
    </resultMap>

    <sql id="specTypeColumns">
        ${alias}.SP_SPEC_TYPE_ID ${prefix}SP_SPEC_TYPE_ID,
        ${alias}.LABEL_STR_ID ${prefix}LABEL_STR_ID,
        ${alias}.LABEL_STR ${prefix}LABEL_STR,
        ${alias}.DESCRIPTION ${prefix}DESCRIPTION,
        ${alias}.CO_SERVICE_ID ${prefix}CO_SERVICE_ID,
        ${alias}.PR_PROPERTY_TYPE_ID ${prefix}PR_PROPERTY_TYPE_ID,
        ${alias}.TEMPLATE_BASE_NAME ${prefix}TEMPLATE_BASE_NAME,
        ${alias}.IS_DEPRECATED ${prefix}IS_DEPRECATED,
        ${alias}.DEPRECATION_REASON ${prefix}DEPRECATION_REASON,
        ${alias}.DEPRECATION_DATE ${prefix}DEPRECATION_DATE,
        ${alias}.REPLACED_BY_SPEC_TYPE_ID ${prefix}REPLACED_BY_SPEC_TYPE_ID,
        ${alias}.ICON ${prefix}ICON,
        ${alias}.IMAGE ${prefix}IMAGE,
        ${alias}.IS_DIRECT_MAIL ${prefix}IS_DIRECT_MAIL,
        ${alias}.IS_VISIBLE ${prefix}IS_VISIBLE,
        ${alias}.IS_IM_ENABLED ${prefix}IS_IM_ENABLED,
        ${alias}.AC_CUSTOM_FORM_ID ${prefix}AC_CUSTOM_FORM_ID,
        ${alias}.ORIGINAL_SPEC_TYPE_ID ${prefix}ORIGINAL_SPEC_TYPE_ID,
        ${alias}.PARENT_SPEC_TYPE_ID ${prefix}PARENT_SPEC_TYPE_ID,
        ${alias}.VERSION_NUMBER ${prefix}VERSION_NUMBER,
        ${alias}.IS_CURRENT ${prefix}IS_CURRENT,
        ${alias}.IS_CAMPAIGN ${prefix}IS_CAMPAIGN,
        ${alias}.AC_SOURCE_TYPE_ID ${prefix}AC_SOURCE_TYPE_ID
    </sql>

</mapper>
