<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.accounts.RatingQuestionMyBatisMapper">

    <resultMap id="ratingQuestionResultMap" type="com.noosh.app.commons.dto.accounts.RatingQuestionDTO">
        <id property="id" column="SR_QUESTION_ID"/>
        <result property="ratingQuestionnaireId" column="SR_QUESTIONNAIRE_ID"/>
        <result property="ratingSectionId" column="SR_SECTION_ID"/>
        <result property="text" column="TEXT"/>
        <result property="weight" column="WEIGHT"/>
        <result property="ordinal" column="ORDINAL"/>
    </resultMap>

    <sql id="ratingQuestionColumns">
        ${alias}.SR_QUESTION_ID ${prefix}SR_QUESTION_ID,
        ${alias}.SR_QUESTIONNAIRE_ID ${prefix}SR_QUESTIONNAIRE_ID,
        ${alias}.SR_SECTION_ID ${prefix}SR_SECTION_ID,
        ${alias}.TEXT ${prefix}TEXT,
        ${alias}.WEIGHT ${prefix}WEIGHT,
        ${alias}.ORDINAL ${prefix}ORDINAL
    </sql>
</mapper>
