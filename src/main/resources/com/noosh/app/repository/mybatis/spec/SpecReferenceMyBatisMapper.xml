<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.spec.SpecReferenceMyBatisMapper">

    <resultMap id="specReferenceResultMap" type="com.noosh.app.commons.dto.spec.SpecReferenceDTO">
        <result property="spSpecReferenceId" column="SP_SPEC_REFERENCE_ID"/>
        <result property="specName" column="SPEC_NAME"/>
        <result property="refNumber" column="REF_NUMBER"/>
        <result property="ownerWorkgroupId" column="OWNER_WORKGROUP_ID"/>
        <result property="preferredSpSpecId" column="PREFERRED_SP_SPEC_ID"/>
        <result property="spSpecTypeId" column="SP_SPEC_TYPE_ID"/>
        <result property="sku" column="SKU"/>
        <result property="isMaster" column="IS_MASTER"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="buClientWorkgroupId" column="BU_CLIENT_WORKGROUP_ID"/>
        <result property="buUofmId" column="BU_UOFM_ID"/>
        <result property="ownerDesc" column="OWNER_DESC"/>
        <result property="customPrPropertyId" column="CUSTOM_PR_PROPERTY_ID"/>
        <result property="isObsolete" column="IS_OBSOLETE"/>
    </resultMap>

    <sql id="specReferenceColumns">
        ${alias}.SP_SPEC_REFERENCE_ID ${prefix}SP_SPEC_REFERENCE_ID,
        ${alias}.SPEC_NAME ${prefix}SPEC_NAME,
        ${alias}.REF_NUMBER ${prefix}REF_NUMBER,
        ${alias}.OWNER_WORKGROUP_ID ${prefix}OWNER_WORKGROUP_ID,
        ${alias}.PREFERRED_SP_SPEC_ID ${prefix}PREFERRED_SP_SPEC_ID,
        ${alias}.SP_SPEC_TYPE_ID ${prefix}SP_SPEC_TYPE_ID,
        ${alias}.SKU ${prefix}SKU,
        ${alias}.IS_MASTER ${prefix}IS_MASTER,
        ${alias}.DESCRIPTION ${prefix}DESCRIPTION,
        ${alias}.BU_CLIENT_WORKGROUP_ID ${prefix}BU_CLIENT_WORKGROUP_ID,
        ${alias}.BU_UOFM_ID ${prefix}BU_UOFM_ID,
        ${alias}.OWNER_DESC ${prefix}OWNER_DESC,
        ${alias}.CUSTOM_PR_PROPERTY_ID ${prefix}CUSTOM_PR_PROPERTY_ID,
        ${alias}.IS_OBSOLETE ${prefix}IS_OBSOLETE
    </sql>

</mapper>
