<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.messages.MessageMyBatisMapper">

    <resultMap id="messageResultMap" type="com.noosh.app.commons.dto.messages.MessageDTO">
        <id property="id" column="ME_MESSAGE_ID"/>
        <result property="subject" column="SUBJECT"/>
        <result property="rootSubject" column="ROOT_SUBJECT"/>
        <result property="content" column="CONTENT"/>
        <result property="authorUserId" column="AUTHOR_USER_ID"/>
        <result property="datePosted" column="DATE_POSTED"/>
        <result property="replyForMeMessageId" column="REPLY_FOR_ME_MESSAGE_ID"/>
        <result property="rootMessageId" column="ROOT_ME_MESSAGE_ID"/>
        <result property="priorityStrId" column="PRIORITY_STR_ID"/>
        <result property="originatingEmailAddress" column="ORIGINATING_EMAIL_ADDRESS"/>
        <result property="acWorkgroupId" column="AC_WORKGROUP_ID"/>
        <result property="postToGroup" column="POST_TO_GROUP"/>
        <association property="person" columnPrefix="P_" resultMap="com.noosh.app.repository.mybatis.security.PersonMyBatisMapper.personResultMap"/>
        <association property="messageStatus" columnPrefix="MES_" resultMap="com.noosh.app.repository.mybatis.messages.MessageStatusMyBatisMapper.messageStatusResultMap"/>
    </resultMap>

    <sql id="messageColumns">
        ${alias}.ME_MESSAGE_ID ${prefix}ME_MESSAGE_ID,
        ${alias}.SUBJECT ${prefix}SUBJECT,
        ${alias}.ROOT_SUBJECT ${prefix}ROOT_SUBJECT,
        ${alias}.CONTENT ${prefix}CONTENT,
        ${alias}.AUTHOR_USER_ID ${prefix}AUTHOR_USER_ID,
        ${alias}.DATE_POSTED ${prefix}DATE_POSTED,
        ${alias}.REPLY_FOR_ME_MESSAGE_ID ${prefix}REPLY_FOR_ME_MESSAGE_ID,
        ${alias}.ROOT_ME_MESSAGE_ID ${prefix}ROOT_ME_MESSAGE_ID,
        ${alias}.PRIORITY_STR_ID ${prefix}PRIORITY_STR_ID,
        ${alias}.ORIGINATING_EMAIL_ADDRESS ${prefix}ORIGINATING_EMAIL_ADDRESS,
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID,
        ${alias}.POST_TO_GROUP ${prefix}POST_TO_GROUP
    </sql>

    <select id="findBroadcastMessagesByWorkgroupId" resultMap="messageResultMap">
        SELECT
        <include refid="messageColumns">
            <property name="alias" value="ME"/>
            <property name="prefix" value=""/>
        </include>,
        MES.ME_MESSAGE_STATUS_ID MES_ME_MESSAGE_STATUS_ID,
        MES.ME_MESSAGE_ID MES_ME_MESSAGE_ID,
        MES.SENT_TO_USER_ID MES_SENT_TO_USER_ID,
        MES.OC_OBJECT_STATE_ID MES_OC_OBJECT_STATE_ID,
        DECODE( MES.OC_OBJECT_STATE_ID, NULL, 2000100, MES.OC_OBJECT_STATE_ID ) AS MES_STATE_SORTABLE_ID,
        P.AC_PERSON_ID P_AC_PERSON_ID,
        P.FIRST_NAME P_FIRST_NAME,
        P.LAST_NAME P_LAST_NAME,
        P.MIDDLE_NAME P_MIDDLE_NAME
        FROM
            ME_MESSAGE ME,
            ME_MESSAGE_STATUS MES,
            AC_ACCOUNT_USER U,
            AC_PERSON P
        WHERE
            ME.AC_WORKGROUP_ID = #{workgroupId}
            AND ME.ME_MESSAGE_ID = MES.ME_MESSAGE_ID ( + )
            AND ME.AUTHOR_USER_ID = U.USER_ID
            AND U.AC_PERSON_ID = P.AC_PERSON_ID
    </select>
</mapper>
