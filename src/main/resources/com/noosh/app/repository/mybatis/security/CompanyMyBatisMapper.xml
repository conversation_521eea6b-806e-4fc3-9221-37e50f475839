<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.security.CompanyMyBatisMapper">

    <resultMap id="companyResultMap" type="com.noosh.app.commons.dto.security.CompanyDTO">
        <id property="id" column="AC_COMPANY_ID"/>
        <result property="name" column="NAME"/>
        <result property="phoneNumber" column="PHONE_NUMBER"/>
        <result property="faxNumber" column="FAX_NUMBER"/>
        <result property="emailAddress" column="EMAIL_ADDRESS"/>
        <result property="partnerWgId" column="PARTNER_AC_WORKGROUP_ID"/>
    </resultMap>

    <sql id="companyColumns">
        ${alias}.AC_COMPANY_ID ${prefix}AC_COMPANY_ID,
        ${alias}.NAME ${prefix}NAME,
        ${alias}.PHONE_NUMBER ${prefix}PHONE_NUMBER,
        ${alias}.FAX_NUMBER ${prefix}FAX_NUMBER,
        ${alias}.EMAIL_ADDRESS ${prefix}EMAIL_ADDRESS,
        ${alias}.PARTNER_AC_WORKGROUP_ID ${prefix}PARTNER_AC_WORKGROUP_ID
    </sql>

</mapper>
