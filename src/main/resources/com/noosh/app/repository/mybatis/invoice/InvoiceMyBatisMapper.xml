<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.invoice.InvoiceMyBatisMapper">

    <resultMap id="invoiceResultMap" type="com.noosh.app.commons.dto.invoice.InvoiceDTO">
        <result property="id" column = "PC_INVOICE_ID" />
        <result property="orderId" column = "OR_ORDER_ID" />
        <result property="reference" column = "REFERENCE" />
        <result property="ownerReference" column = "OWNER_REFERENCE" />
        <result property="buyerUserId" column = "BUYER_USER_ID" />
        <result property="buyerWorkgroupId" column = "BUYER_AC_WORKGROUP_ID" />
        <result property="supplierUserId" column = "SUPPLIER_USER_ID" />
        <result property="supplierWorkgroupId" column = "SUPPLIER_AC_WORKGROUP_ID" />
        <result property="invoiceDate" column = "INVOICE_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler" />
        <result property="dueDate" column = "DUE_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="submitDate" column = "SUBMIT_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="acceptedDate" column = "ACCEPTED_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="modDate" column = "MOD_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="comments" column = "COMMENTS" />
        <result property="tax" column = "TAX" />
        <result property="taxCurrencyId" column = "TAX_AC_CURRENCY_ID" />
        <result property="shipping" column = "SHIPPING"/>
        <result property="shippingCurrencyId" column = "SHIPPING_AC_CURRENCY_ID" />
        <result property="miscCost" column = "MISC_COST"/>
        <result property="miscCostCurrencyId" column = "MISC_COST_AC_CURRENCY_ID" />
        <result property="stateId" column = "OC_OBJECT_STATE_ID" />
        <result property="stateChangeComment" column = "STATE_CHANGE_COMMENT" />
        <result property="customPropertyId" column = "CUSTOM_PR_PROPERTY_ID" />
        <result property="prepareUserId" column = "PREPARE_USER_ID" />
        <result property="invoiceCMContactId" column = "INVOICE_CM_CONTACT_ID" />
        <result property="isNonBillable" column = "IS_NON_BILLABLE" />
        <result property="nonBillableReasonId" column = "NON_BILLABLE_REASON_ID" />
        <result property="isApproved" column = "IS_APPROVED" />
        <result property="isFinal" column = "IS_FINAL" />
        <result property="discountOrSurcharge" column = "D_OR_S"/>
        <result property="discountOrSurchargeCurrencyId" column = "D_OR_S_AC_CURRENCY_ID" />
        <result property="isTemplate" column = "IS_TEMPLATE" />
        <result property="sendOnClosedOrder" column = "SEND_ON_CLOSED_ORDER" />
        <result property="termsId" column = "AC_TERMS_ID" />

        <result property="rate" column = "RATE" />
        <result property="exCurrencyId" column = "EX_CURRENCYID" />
        <result property="exTax" column = "EXTAX" />
        <result property="exTaxCurrencyId" column = "EXTAX_AC_CURRENCY_ID" />
        <result property="exShipping" column = "EXSHIPPING" />
        <result property="exShippingCurrencyId" column = "EXSHIPPING_AC_CURRENCY_ID" />
        <result property="exMiscCost" column = "EXMISC_COST" />
        <result property="exMiscCostCurrencyId" column = "EXMISC_COST_AC_CURRENCY_ID" />
        <result property="exDiscountOrSurcharge" column = "EX_D_OR_S" />
        <result property="exDiscountOrSurchargeCurrencyId" column = "EX_D_OR_S_AC_CURRENCY_ID" />
    </resultMap>

    <resultMap id="invoiceItemResultMap" type="com.noosh.app.commons.dto.invoice.InvoiceItemDTO">
        <result property="invoiceItemId" column = "PC_INVOICE_ITEM_ID" />
        <result property="invoiceId" column = "PC_INVOICE_ID" />
        <result property="specId" column = "SP_SPEC_ID" />
        <result property="jobId" column = "PC_JOB_ID" />
        <result property="quantity" column = "QUANTITY" />
        <result property="amount" column = "AMOUNT" />
        <result property="amountCurrencyId" column = "AMOUNT_AC_CURRENCY_ID" />
        <result property="includeBreakouts" column = "INCLUDE_BREAKOUTS" />
        <result property="breakoutTypeId" column = "PC_BREAKOUT_TYPE_ID" />
        <result property="itemIndex" column = "ITEM_INDEX" />
        <result property="tax" column = "TAX" />
        <result property="taxCurrencyId" column = "TAX_AC_CURRENCY_ID" />
        <result property="shipping" column = "SHIPPING"/>
        <result property="shippingCurrencyId" column = "SHIPPING_AC_CURRENCY_ID" />
        <result property="discountOrSurcharge" column = "D_OR_S" />
        <result property="discountOrSurchargeCurrencyId" column = "D_OR_S_AC_CURRENCY_ID" />

        <result property="exAmount" column = "EXAMOUNT" />
        <result property="exAmountCurrencyId" column = "EXAMOUNT_AC_CURRENCY_ID" />
        <result property="exTax" column = "EXTAX" />
        <result property="exTaxCurrencyId" column = "EXTAX_AC_CURRENCY_ID" />
        <result property="exShipping" column = "EXSHIPPING"/>
        <result property="exShippingCurrencyId" column = "EXSHIPPING_AC_CURRENCY_ID" />
        <result property="exDiscountOrSurcharge" column = "EX_D_OR_S" />
        <result property="exDiscountOrSurchargeCurrencyId" column = "EX_D_OR_S_AC_CURRENCY_ID" />

        <result property="customPropertyId" column = "CUSTOM_PR_PROPERTY_ID" />
    </resultMap>

    <resultMap id="breakOutResultMap" type="com.noosh.app.commons.dto.breakout.BreakoutDTO">
        <result property="id" column = "PC_BREAKOUT_ID" />
        <result property="breakoutTypeId" column = "PC_BREAKOUT_TYPE_ID" />
        <result property="nestingLevel" column = "NESTING_LEVEL" />
        <result property="isLeafNode" column = "IS_LEAF_NODE" />
        <result property="hasQuantity" column = "HAS_QUANTITY" />
        <result property="value" column = "VALUE" />
        <result property="price" column = "PRICE" />
        <result property="priceCurrencyId" column = "PRICE_AC_CURRENCY_ID" />
        <result property="objectId" column = "OBJECT_ID" />
        <result property="objectClassId" column = "OC_OBJECT_CLASS_ID" />
        <result property="custom1" column = "CUSTOM1" />
        <result property="preMarkup" column = "PRE_MARKUP" />
        <result property="preMarkupCurrencyId" column = "PRE_MARKUP_AC_CURRENCY_ID" />
        <result property="markupPercent" column = "MARKUP_PERCENT"/>
        <result property="markupFixed" column = "MARKUP_FIXED" />
        <result property="markupCurrencyId" column = "MARKUP_FIXED_AC_CURRENCY_ID" />
        <result property="units" column = "UNITS" />
        <result property="rates" column = "RATES" />
        <result property="ratesCurrencyId" column = "RATES_AC_CURRENCY_ID" />
        <result property="exPrice" column = "EXPRICE" />
        <result property="exPriceCurrencyId" column = "EXPRICE_AC_CURRENCY_ID" />
        <result property="exPreMarkup" column = "EXPRE_MARKUP" />
        <result property="exPreMarkupCurrencyId" column = "EXPRE_MARKUP_AC_CURRENCY_ID" />
        <result property="exMarkupFixed" column = "EXMARKUP_FIX" />
        <result property="exMarkupFixedCurrencyId" column = "EXMARKUP_FIX_AC_CURRENCY_ID" />
        <association property="breakoutType" javaType="com.noosh.app.commons.dto.breakout.BreakoutTypeDTO">
            <result property="id" column = "PC_BREAKOUT_TYPE_ID" />
            <result property="workgroupId" column = "AC_WORKGROUP_ID" />
            <result property="parentTypeId" column = "PARENT_PC_BREAKOUT_TYPE_ID" />
            <result property="rootTypeId" column = "ROOT_PC_BREAKOUT_TYPE_ID" />
            <result property="isLocked" column = "IS_LOCKED" />
            <result property="isCurrent" column = "IS_CURRENT" />
            <result property="name" column = "NAME" />
            <result property="description" column = "DESCRIPTION" />
            <result property="isRequired" column = "IS_REQUIRED" />
            <result property="isQuantity" column = "IS_QUANTITY" />
            <result property="allSuppliers" column = "ALL_SUPPLIERS" />
            <result property="sortOrder" column = "SORT_ORDER" />
            <result property="custom1" column = "CUSTOM1" />
            <result property="cloneOf" column = "CLONE_OF" />
            <result property="isIncluded" column = "IS_INCLUDED" />
            <result property="pricePer" column = "PRICE_PER"/>
            <result property="propertyId" column = "CUSTOM_PR_PROPERTY_ID" />
            <result property="code" column = "CODE" />
            <result property="allClients" column = "ALL_CLIENTS" />
            <result property="level" column = "LEVEL" />
        </association>
    </resultMap>

    <resultMap id="breakOutTypeResultMap" type="com.noosh.app.commons.dto.breakout.BreakoutTypeDTO">
        <result property="id" column = "PC_BREAKOUT_TYPE_ID" />
        <result property="workgroupId" column = "AC_WORKGROUP_ID" />
        <result property="parentTypeId" column = "PARENT_PC_BREAKOUT_TYPE_ID" />
        <result property="rootTypeId" column = "ROOT_PC_BREAKOUT_TYPE_ID" />
        <result property="isLocked" column = "IS_LOCKED" />
        <result property="isCurrent" column = "IS_CURRENT" />
        <result property="name" column = "NAME" />
        <result property="description" column = "DESCRIPTION" />
        <result property="isRequired" column = "IS_REQUIRED" />
        <result property="isQuantity" column = "IS_QUANTITY" />
        <result property="allSuppliers" column = "ALL_SUPPLIERS" />
        <result property="sortOrder" column = "SORT_ORDER" />
        <result property="custom1" column = "CUSTOM1" />
        <result property="cloneOf" column = "CLONE_OF" />
        <result property="isIncluded" column = "IS_INCLUDED" />
        <result property="pricePer" column = "PRICE_PER"/>
        <result property="propertyId" column = "CUSTOM_PR_PROPERTY_ID" />
        <result property="code" column = "CODE" />
        <result property="allClients" column = "ALL_CLIENTS" />
        <result property="level" column = "LEVEL" />
    </resultMap>

    <resultMap id="invoiceListResultMap" type="com.noosh.app.commons.dto.invoice.InvoiceListDTO">
        <result property="orderId" column = "OR_ORDER_ID" />
        <result property="orderTitle" column = "OV_TITLE" />
        <result property="orderReference" column = "OV_REFERENCE" />
        <result property="orderAcceptDate" column = "OV_ACCEPT_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="orderTypeId" column = "OV_ORDER_TYPE_ID" />
        <result property="orderBuyerWorkgroupId" column = "OV_BUYER_AC_WORKGROUP_ID" />
        <result property="orderSupplierWorkgroupId" column = "OV_SUPPLIER_AC_WORKGROUP_ID" />
        <result property="orderObjectStateId" column = "OV_OBJECT_STATE_ID" />
        <result property="orderBuyerWorkgroupName" column = "ORDER_BUYER_WG_NAME" />
        <result property="orderSupplierWorkgroupName" column = "ORDER_SUPPLIER_WG_NAME" />
        <result property="orderClassificationId" column = "OR_ORDER_CLASSIFICATION_ID" />
        <result property="buClientId" column = "OR_BU_CLIENT_WORKGROUP_ID" />
        <result property="parentOrderId" column="PARENT_OR_ORDER_ID" jdbcType="DECIMAL"/>
        <result property="orderVersionId" column="or_order_version_id" jdbcType="DECIMAL"/>

        <result property="id" column = "I_INVOICE_ID" />
        <result property="ownerReference" column = "I_OWNER_REFERENCE" />
        <result property="stateId" column = "I_OC_OBJECT_STATE_ID" />
        <result property="acceptedDate" column = "I_ACCEPTED_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="dueDate" column = "I_DUE_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="modDate" column = "I_MOD_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="isNonBillable" column = "I_IS_NON_BILLABLE" />
        <result property="supplierWorkgroupId" column = "I_SUPPLIER_AC_WORKGROUP_ID" />
        <result property="isApproved" column = "I_IS_APPROVED" />
        <result property="isFinal" column = "I_IS_FINAL" />
        <result property="tax" column = "I_TAX"/>
        <result property="taxCurrencyId" column = "I_TAX_AC_CURRENCY_ID" />
        <result property="shipping" column = "I_SHIPPING"/>
        <result property="shippingCurrencyId" column = "I_SHIPPING_AC_CURRENCY_ID" />
        <result property="miscCost" column = "I_MISC_COST"/>
        <result property="miscCostCurrencyId" column = "I_MISC_COST_AC_CURRENCY_ID" />
        <result property="discountOrSurcharge" column = "I_D_OR_S"/>
        <result property="discountOrSurchargeCurrencyId" column = "I_D_OR_S_AC_CURRENCY_ID" />
        <result property="exTax" column = "I_EXTAX"/>
        <result property="exTaxCurrencyId" column = "I_EXTAX_AC_CURRENCY_ID" />
        <result property="exShipping" column = "I_EXSHIPPING"/>
        <result property="exShippingCurrencyId" column = "I_EXSHIPPING_AC_CURRENCY_ID" />
        <result property="exMiscCost" column = "I_EXMISC_COST"/>
        <result property="exMiscCostCurrencyId" column = "I_EXMISC_COST_AC_CURRENCY_ID" />
        <result property="exDiscountOrSurcharge" column = "I_EX_D_OR_S"/>
        <result property="exDiscountOrSurchargeCurrencyId" column = "I_EX_D_OR_S_AC_CURRENCY_ID" />
        <result property="rate" column = "I_RATE" />
        <result property="exCurrencyId" column = "I_EX_CURRENCYID" />
    </resultMap>

    <resultMap id="requestResultMap" type="com.noosh.app.commons.dto.invoice.RequestDTO">
        <result property="id" column = "SH_REQUEST_ID" />
        <result property="shipmentRecord" column = "SHIPMENT_RECORD" />
        <result property="shippedDate" column = "D_SHIPPED_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler" />
        <result property="invoiceItemId" column = "PC_INVOICE_ITEM_ID" />
        <result property="quantity" column = "QUANTITY" />
    </resultMap>

    <select id="findInvoiceList" resultMap="invoiceListResultMap">
        SELECT
        OREF.OR_ORDER_ID OR_ORDER_ID
        , OREF.PARENT_OR_ORDER_ID
        , OV.TITLE OV_TITLE
        , OV.REFERENCE OV_REFERENCE
        , OV.ACCEPT_DATE OV_ACCEPT_DATE
        , OV.OR_ORDER_TYPE_ID OV_ORDER_TYPE_ID
        , OV.BUYER_AC_WORKGROUP_ID OV_BUYER_AC_WORKGROUP_ID
        , OV.SUPPLIER_AC_WORKGROUP_ID OV_SUPPLIER_AC_WORKGROUP_ID
        , OV.OR_ORDER_CLASSIFICATION_ID OR_ORDER_CLASSIFICATION_ID
        , OV.BU_CLIENT_WORKGROUP_ID OR_BU_CLIENT_WORKGROUP_ID
        , OV.OR_ORDER_VERSION_ID
        , OS.OC_OBJECT_STATE_ID OV_OBJECT_STATE_ID
        , BUYER_WORKGROUP.NAME ORDER_BUYER_WG_NAME
        , SUPPLIER_WORKGROUP.NAME ORDER_SUPPLIER_WG_NAME
        , I.PC_INVOICE_ID I_INVOICE_ID
        , I.OWNER_REFERENCE I_OWNER_REFERENCE
        , I.OC_OBJECT_STATE_ID I_OC_OBJECT_STATE_ID
        , I.ACCEPTED_DATE I_ACCEPTED_DATE
        , I.DUE_DATE I_DUE_DATE
        , I.MOD_DATE I_MOD_DATE
        , I.IS_NON_BILLABLE I_IS_NON_BILLABLE
        , I.SUPPLIER_AC_WORKGROUP_ID I_SUPPLIER_AC_WORKGROUP_ID
        , I.IS_APPROVED I_IS_APPROVED
        , I.IS_FINAL I_IS_FINAL
        , I.TAX I_TAX
        , I.TAX_AC_CURRENCY_ID I_TAX_AC_CURRENCY_ID
        , I.SHIPPING I_SHIPPING
        , I.SHIPPING_AC_CURRENCY_ID I_SHIPPING_AC_CURRENCY_ID
        , I.MISC_COST I_MISC_COST
        , I.MISC_COST_AC_CURRENCY_ID I_MISC_COST_AC_CURRENCY_ID
        , I.D_OR_S I_D_OR_S
        , I.D_OR_S_AC_CURRENCY_ID I_D_OR_S_AC_CURRENCY_ID
        , I.EXTAX I_EXTAX
        , I.EXTAX_AC_CURRENCY_ID I_EXTAX_AC_CURRENCY_ID
        , I.EXSHIPPING I_EXSHIPPING
        , I.EXSHIPPING_AC_CURRENCY_ID I_EXSHIPPING_AC_CURRENCY_ID
        , I.EXMISC_COST I_EXMISC_COST
        , I.EXMISC_COST_AC_CURRENCY_ID I_EXMISC_COST_AC_CURRENCY_ID
        , I.EX_D_OR_S I_EX_D_OR_S
        , I.EX_D_OR_S_AC_CURRENCY_ID I_EX_D_OR_S_AC_CURRENCY_ID
        , I.RATE I_RATE
        , I.EX_CURRENCYID I_EX_CURRENCYID
        FROM
        PC_INVOICE I
        , SY_CONTAINABLE C
        , OR_ORDER_VERSION OV
        , OR_ORDER OREF
        , OR_ORDER_STATE OS
        , AC_WORKGROUP BUYER_WORKGROUP
        , AC_WORKGROUP SUPPLIER_WORKGROUP
        WHERE
        I.IS_TEMPLATE = 0
        AND I.PC_INVOICE_ID=C.OBJECT_ID
        AND C.OBJECT_CLASS_ID=1000119
        AND C.PARENT_OBJECT_ID=#{projectId}
        AND C.PARENT_OBJECT_CLASS_ID=1000000
        AND C.ITEM_OC_OBJECT_STATE_ID in  ( 2500040, 2500042)
        AND I.OR_ORDER_ID = OREF.OR_ORDER_ID
        AND OREF.OR_ORDER_VERSION_ID = OV.OR_ORDER_VERSION_ID
        AND OREF.OR_ORDER_ID = OS.OR_ORDER_ID
        AND OS.IS_CURRENT = 1
        AND BUYER_WORKGROUP.ac_workgroup_id = OV.BUYER_AC_WORKGROUP_ID
        AND SUPPLIER_WORKGROUP.ac_workgroup_id = OV.SUPPLIER_AC_WORKGROUP_ID

        <if test="includeBuySide and !includeSellSide">
            AND OV.BUYER_AC_WORKGROUP_ID=#{ownerGroupId}
            <if test="isClientNotOnNoosh">
                AND OV.BU_CLIENT_WORKGROUP_ID is null
            </if>
        </if>
        <if test="!includeBuySide and includeSellSide">
            AND OV.SUPPLIER_AC_WORKGROUP_ID=#{ownerGroupId}
            <if test="isClientNotOnNoosh">
                AND OV.BU_CLIENT_WORKGROUP_ID is not null
            </if>
        </if>

        ORDER BY OREF.PARENT_OR_ORDER_ID, I.CREATE_DATE
    </select>

    <select id="getDraftAndPendingInvoiceCountForOrderId" resultType="java.lang.Long">
        SELECT count(*) as MY_COUNT
        FROM
        PC_INVOICE I
        WHERE I.OR_ORDER_ID = #{orderId}
        AND I.IS_TEMPLATE = 0
        AND I.OC_OBJECT_STATE_ID in (2500088, 2500089)

    </select>

    <select id="findInvoiceDetail" resultMap="invoiceResultMap">
        SELECT
            I.PC_INVOICE_ID
            , I.OR_ORDER_ID
            , I.REFERENCE
            , I.OWNER_REFERENCE
            , I.BUYER_USER_ID
            , I.BUYER_AC_WORKGROUP_ID
            , I.SUPPLIER_USER_ID
            , I.SUPPLIER_AC_WORKGROUP_ID
            , I.INVOICE_DATE
            , I.DUE_DATE
            , I.SUBMIT_DATE
            , I.ACCEPTED_DATE
            , I.COMMENTS
            , I.TAX
            , I.TAX_AC_CURRENCY_ID
            , I.SHIPPING
            , I.SHIPPING_AC_CURRENCY_ID
            , I.MISC_COST
            , I.MISC_COST_AC_CURRENCY_ID
            , I.OC_OBJECT_STATE_ID
            , I.STATE_CHANGE_COMMENT
            , I.CUSTOM_PR_PROPERTY_ID
            , I.MOD_DATE
            , I.PREPARE_USER_ID
            , I.INVOICE_CM_CONTACT_ID
            , I.IS_NON_BILLABLE
            , I.NON_BILLABLE_REASON_ID
            , I.IS_APPROVED
            , I.IS_FINAL
            , I.D_OR_S
            , I.D_OR_S_AC_CURRENCY_ID
            , I.IS_TEMPLATE
            , I.SEND_ON_CLOSED_ORDER
            , I.RATE
            , I.EX_CURRENCYID
            , I.EXTAX
            , I.EXTAX_AC_CURRENCY_ID
            , I.EXSHIPPING
            , I.EXSHIPPING_AC_CURRENCY_ID
            , I.EXMISC_COST
            , I.EXMISC_COST_AC_CURRENCY_ID
            , I.EX_D_OR_S
            , I.EX_D_OR_S_AC_CURRENCY_ID
            , I.AC_TERMS_ID
        FROM PC_INVOICE I
            , SY_CONTAINABLE C
        WHERE I.PC_INVOICE_ID = #{invoiceId}
        AND I.IS_TEMPLATE = 0
        AND I.PC_INVOICE_ID =C.OBJECT_ID
        AND C.OBJECT_CLASS_ID = 1000119
        AND C.PARENT_OBJECT_ID = #{projectId}
        AND C.PARENT_OBJECT_CLASS_ID = 1000000
        AND C.ITEM_OC_OBJECT_STATE_ID IN (2500040, 2500042)
    </select>

    <select id="findInvoiceForOrder" resultMap="invoiceResultMap">
        SELECT I.PC_INVOICE_ID
            , I.OR_ORDER_ID
            , I.REFERENCE
            , I.OWNER_REFERENCE
            , I.BUYER_USER_ID
            , I.BUYER_AC_WORKGROUP_ID
            , I.SUPPLIER_USER_ID
            , I.SUPPLIER_AC_WORKGROUP_ID
            , I.INVOICE_DATE
            , I.DUE_DATE
            , I.SUBMIT_DATE
            , I.ACCEPTED_DATE
            , I.COMMENTS
            , I.TAX
            , I.TAX_AC_CURRENCY_ID
            , I.SHIPPING
            , I.SHIPPING_AC_CURRENCY_ID
            , I.MISC_COST
            , I.MISC_COST_AC_CURRENCY_ID
            , I.OC_OBJECT_STATE_ID
            , I.STATE_CHANGE_COMMENT
            , I.CUSTOM_PR_PROPERTY_ID
            , I.MOD_DATE
            , I.PREPARE_USER_ID
            , I.INVOICE_CM_CONTACT_ID
            , I.IS_NON_BILLABLE
            , I.NON_BILLABLE_REASON_ID
            , I.IS_APPROVED
            , I.IS_FINAL
            , I.D_OR_S
            , I.D_OR_S_AC_CURRENCY_ID
            , I.IS_TEMPLATE
            , I.SEND_ON_CLOSED_ORDER
            , I.RATE
            , I.EX_CURRENCYID
            , I.EXTAX
            , I.EXTAX_AC_CURRENCY_ID
            , I.EXSHIPPING
            , I.EXSHIPPING_AC_CURRENCY_ID
            , I.EXMISC_COST
            , I.EXMISC_COST_AC_CURRENCY_ID
            , I.EX_D_OR_S
            , I.EX_D_OR_S_AC_CURRENCY_ID
        FROM PC_INVOICE I,
        SY_CONTAINABLE C
        WHERE I.OR_ORDER_ID = #{orderId}
        AND I.IS_TEMPLATE = 0
        AND I.PC_INVOICE_ID = C.OBJECT_ID
        AND C.OBJECT_CLASS_ID = 1000119
        AND C.PARENT_OBJECT_ID = #{projectId}
        AND C.PARENT_OBJECT_CLASS_ID = 1000000
        AND C.ITEM_OC_OBJECT_STATE_ID in(2500040, 2500042)
        ORDER BY I.PC_INVOICE_ID
    </select>

    <select id="findInvoiceItem" resultMap="invoiceItemResultMap">
        SELECT
            II.PC_INVOICE_ITEM_ID
            , II.PC_INVOICE_ID
            , II.SP_SPEC_ID
            , II.PC_JOB_ID
            , II.QUANTITY
            , II.AMOUNT
            , II.AMOUNT_AC_CURRENCY_ID
            , II.INCLUDE_BREAKOUTS
            , II.PC_BREAKOUT_TYPE_ID
            , II.ITEM_INDEX
            , II.TAX
            , II.TAX_AC_CURRENCY_ID
            , II.SHIPPING
            , II.SHIPPING_AC_CURRENCY_ID
            , II.D_OR_S
            , II.D_OR_S_AC_CURRENCY_ID
            , II.CUSTOM_PR_PROPERTY_ID
            , II.EXAMOUNT
            , II.EXAMOUNT_AC_CURRENCY_ID
            , II.EXTAX
            , II.EXTAX_AC_CURRENCY_ID
            , II.EXSHIPPING
            , II.EXSHIPPING_AC_CURRENCY_ID
            , II.EX_D_OR_S
            , II.EX_D_OR_S_AC_CURRENCY_ID
        FROM PC_INVOICE_ITEM II
        WHERE II.PC_INVOICE_ID = #{invoiceId}
        ORDER BY II.PC_INVOICE_ID, II.ITEM_INDEX
    </select>

    <select id="findBreakoutByInvoiceItemIds" resultMap="breakOutResultMap">
        SELECT B.PC_BREAKOUT_ID
        , B.PC_BREAKOUT_TYPE_ID
        , B.NESTING_LEVEL
        , B.IS_LEAF_NODE
        , B.HAS_QUANTITY
        , B.VALUE
        , B.PRICE
        , B.PRICE_AC_CURRENCY_ID
        , B.OBJECT_ID
        , B.OC_OBJECT_CLASS_ID
        , B.CUSTOM1
        , B.PRE_MARKUP
        , B.PRE_MARKUP_AC_CURRENCY_ID
        , B.MARKUP_PERCENT
        , B.MARKUP_FIXED
        , B.MARKUP_FIXED_AC_CURRENCY_ID
        , B.UNITS
        , B.RATES
        , B.RATES_AC_CURRENCY_ID
        , BT.PC_BREAKOUT_TYPE_ID
        , BT.AC_WORKGROUP_ID
        , BT.PARENT_PC_BREAKOUT_TYPE_ID
        , BT.ROOT_PC_BREAKOUT_TYPE_ID
        , BT.IS_LOCKED
        , BT.IS_CURRENT
        , BT.NAME
        , BT.DESCRIPTION
        , BT.IS_REQUIRED
        , BT.IS_QUANTITY
        , BT.ALL_SUPPLIERS
        , BT.SORT_ORDER
        , BT.CUSTOM1
        , BT.CLONE_OF
        , BT.IS_INCLUDED
        , BT.PRICE_PER
        , BT.CUSTOM_PR_PROPERTY_ID
        , BT.CODE
        , BT.ALL_CLIENTS
        , B.EXPRICE
        , B.EXPRICE_AC_CURRENCY_ID
        , B.EXPRE_MARKUP
        , B.EXPRE_MARKUP_AC_CURRENCY_ID
        , B.EXMARKUP_FIX
        , B.EXMARKUP_FIX_AC_CURRENCY_ID
        FROM PC_BREAKOUT B,
        PC_BREAKOUT_TYPE BT
        WHERE B.OBJECT_ID IN
        <foreach item="item" index="index" collection="invoiceItemIds" open="(" close=")" separator=",">
            #{item}
        </foreach>
        AND B.OC_OBJECT_CLASS_ID = 1000120
        AND BT.PC_BREAKOUT_TYPE_ID = B.PC_BREAKOUT_TYPE_ID
        ORDER BY B.OBJECT_ID, B.PC_BREAKOUT_ID
    </select>

    <select id="findAllWithRootBreakoutTypeIdAndSpecTypeIdAndSupplierGroupId" resultMap="breakOutTypeResultMap">
        SELECT
            *
        FROM
        (
            SELECT
            LEVEL
            , BT.PC_BREAKOUT_TYPE_ID
            , BT.AC_WORKGROUP_ID
            , BT.PARENT_PC_BREAKOUT_TYPE_ID
            , BT.ROOT_PC_BREAKOUT_TYPE_ID
            , BT.IS_LOCKED
            , BT.IS_CURRENT
            , BT.NAME
            , BT.DESCRIPTION
            , BT.IS_REQUIRED
            , BT.IS_QUANTITY
            , BT.ALL_SUPPLIERS
            , BT.SORT_ORDER
            , BT.CUSTOM1
            , BT.CLONE_OF
            , BT.IS_INCLUDED
            , BT.PRICE_PER
            , BT.CUSTOM_PR_PROPERTY_ID
            , BT.CODE
            , BT.ALL_CLIENTS
        FROM PC_BREAKOUT_TYPE BT
        WHERE BT.ROOT_PC_BREAKOUT_TYPE_ID = #{breakoutTypeId, jdbcType=NUMERIC}
            AND BT.IS_INCLUDED = 1
        CONNECT BY
        PRIOR BT.PC_BREAKOUT_TYPE_ID = BT.PARENT_PC_BREAKOUT_TYPE_ID
        START WITH
            BT.PARENT_PC_BREAKOUT_TYPE_ID IS NULL
            AND BT.ROOT_PC_BREAKOUT_TYPE_ID = #{breakoutTypeId, jdbcType=NUMERIC} ORDER SIBLINGS BY SORT_ORDER)
        WHERE (PC_BREAKOUT_TYPE_ID IN
                (SELECT PC_BREAKOUT_TYPE_ID
                FROM PC_BREAKOUT_TYPE_SPEC
                WHERE SP_SPEC_TYPE_ID  = #{specTypeId, jdbcType=NUMERIC})
            <if test="supplierWorkgroupId &gt; 0">
                AND (PC_BREAKOUT_TYPE_ID IN
                    (SELECT  PC_BREAKOUT_TYPE_ID
                    FROM PC_BREAKOUT_TYPE_SUPPLIER
                    WHERE AC_WORKGROUP_ID = #{supplierWorkgroupId, jdbcType=NUMERIC}) OR ALL_SUPPLIERS = 1)
            </if>
            ) OR PARENT_PC_BREAKOUT_TYPE_ID is null
    </select>

    <select id="findShipmentRecordsByInvoiceItemIdAndJobId" resultMap="requestResultMap">
        SELECT
            RECIPIENT_NAME || ', ' || CITY || ', ' || STATE || ', ' || REQUEST_TYPE_STR || ', ' || D_SHIPPED_QUANTITY AS SHIPMENT_RECORD,
            D_SHIPPED_DATE ,
            R.SH_REQUEST_ID,
            R.SH_SHIPMENT_ID,
            R.PC_INVOICE_ITEM_ID
        FROM
            (
            SELECT
                DISTINCT R.SH_REQUEST_ID ,
            CASE
                    WHEN R.FIRST_NAME IS NOT NULL
                    OR R.MIDDLE_NAME IS NOT NULL
                    OR R.LAST_NAME IS NOT NULL THEN R.FIRST_NAME || ' ' || R.MIDDLE_NAME || ' ' || R.LAST_NAME
                    WHEN R.DESCRIPTION IS NOT NULL THEN R.DESCRIPTION
                    WHEN R.WORKGROUP_NAME IS NOT NULL THEN R.WORKGROUP_NAME
                    ELSE R.COMPANY_NAME
            END AS RECIPIENT_NAME ,
                A.CITY,
                A.STATE ,
                GET_I18N_LABEL(T.NAME_STR_ID,
                'default',
                'en_US') REQUEST_TYPE_STR ,
                D.SHIPPED_DATE AS D_SHIPPED_DATE,
                D.SHIPPED_QUANTITY D_SHIPPED_QUANTITY
            FROM
                SH_SHIPMENT SH,
                PC_JOB J,
                SH_REQUEST R,
                SH_REQUEST_TYPE T,
                AC_ADDRESS A,
                SH_DELIVERY D,
                PC_INVOICE_ITEM II
            WHERE
                SH.PC_JOB_ID = J.PC_JOB_ID
                AND R.SH_SHIPMENT_ID = SH.SH_SHIPMENT_ID
                AND D.SH_REQUEST_ID = R.SH_REQUEST_ID
                AND R.AC_ADDRESS_ID = A.AC_ADDRESS_ID
                AND R.SH_REQUEST_TYPE_ID = 1000000
                AND (R.PC_INVOICE_ITEM_ID IS NULL
                OR EXISTS (
                SELECT
                    PC_INVOICE_ITEM_ID
                FROM
                    PC_INVOICE_ITEM
                WHERE
                    R.PC_INVOICE_ITEM_ID = PC_INVOICE_ITEM.PC_INVOICE_ITEM_ID
                    AND PC_INVOICE_ITEM.PC_INVOICE_ITEM_ID IN #{invoiceItemId})
                OR NOT EXISTS (
                SELECT
                    PC_INVOICE_ITEM_ID
                FROM
                    PC_INVOICE_ITEM
                WHERE
                    R.PC_INVOICE_ITEM_ID = PC_INVOICE_ITEM.PC_INVOICE_ITEM_ID ) )
                AND J.PC_JOB_ID = II.PC_JOB_ID(+)
                AND R.SH_REQUEST_TYPE_ID = T.SH_REQUEST_TYPE_ID(+)
                AND J.PC_JOB_ID = #{jobId} ) SRECORDS,
            SH_REQUEST R
        WHERE
            SRECORDS.SH_REQUEST_ID = R.SH_REQUEST_ID
        ORDER BY
            RECIPIENT_NAME || ', ' || REQUEST_TYPE_STR || ', ' || D_SHIPPED_QUANTITY || ', ' || D_SHIPPED_DATE
    </select>

    <select id="findShipmentRecordsByJobIdAndRequestIds" resultMap="requestResultMap">
        SELECT
                RECIPIENT_NAME || ', ' || CITY || ', ' || STATE || ', ' || REQUEST_TYPE_STR || ', ' || D_SHIPPED_QUANTITY AS SHIPMENT_RECORD,
                D_SHIPPED_DATE ,
                R.SH_REQUEST_ID,
                R.SH_SHIPMENT_ID,
                R.PC_INVOICE_ITEM_ID,
                R.QUANTITY
        FROM
            (
                SELECT
                    DISTINCT R.SH_REQUEST_ID ,
                             CASE
                                 WHEN R.FIRST_NAME IS NOT NULL
                                     OR R.MIDDLE_NAME IS NOT NULL
                                     OR R.LAST_NAME IS NOT NULL THEN R.FIRST_NAME || ' ' || R.MIDDLE_NAME || ' ' || R.LAST_NAME
                                 WHEN R.DESCRIPTION IS NOT NULL THEN R.DESCRIPTION
                                 WHEN R.WORKGROUP_NAME IS NOT NULL THEN R.WORKGROUP_NAME
                                 ELSE R.COMPANY_NAME
                                 END AS RECIPIENT_NAME ,
                             R.QUANTITY,
                             A.CITY,
                             A.STATE ,
                             GET_I18N_LABEL(T.NAME_STR_ID,
                                            'default',
                                            'en_US') REQUEST_TYPE_STR ,
                             D.SHIPPED_DATE AS D_SHIPPED_DATE,
                             D.SHIPPED_QUANTITY D_SHIPPED_QUANTITY
                FROM
                    SH_SHIPMENT SH,
                    PC_JOB J,
                    SH_REQUEST R,
                    SH_REQUEST_TYPE T,
                    AC_ADDRESS A,
                    SH_DELIVERY D,
                    PC_INVOICE_ITEM II
                WHERE
                    SH.PC_JOB_ID = J.PC_JOB_ID
                  AND R.SH_SHIPMENT_ID = SH.SH_SHIPMENT_ID
                  AND D.SH_REQUEST_ID = R.SH_REQUEST_ID
                  AND R.AC_ADDRESS_ID = A.AC_ADDRESS_ID
                  AND R.SH_REQUEST_TYPE_ID = 1000000
                  <if test="requestIds != null and requestIds.size() > 0">
                  AND (R.SH_REQUEST_ID in
                    <foreach item="item" index="index" collection="requestIds" open="(" separator="," close=")">
                        #{item}
                    </foreach> )
                  </if>
                  <if test="requestIds != null and requestIds.size() == 0">
                  AND R.SH_REQUEST_ID = -1
                  </if>
                  AND J.PC_JOB_ID = II.PC_JOB_ID(+)
                  AND R.SH_REQUEST_TYPE_ID = T.SH_REQUEST_TYPE_ID(+)
                  AND J.PC_JOB_ID = #{jobId} ) SRECORDS,
            SH_REQUEST R
        WHERE
            SRECORDS.SH_REQUEST_ID = R.SH_REQUEST_ID
        ORDER BY
                RECIPIENT_NAME || ', ' || REQUEST_TYPE_STR || ', ' || D_SHIPPED_QUANTITY || ', ' || D_SHIPPED_DATE
    </select>
</mapper>