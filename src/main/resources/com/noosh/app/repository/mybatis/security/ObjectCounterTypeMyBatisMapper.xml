<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.security.ObjectCounterTypeMyBatisMapper">

    <resultMap id="objectCounterTypeResultMap" type="com.noosh.app.commons.dto.security.ObjectCounterTypeDTO">
        <id property="id" column="AC_OBJECT_COUNTER_TYPE_ID"/>
        <result property="constantToken" column="CONSTANT_TOKEN"/>
        <result property="labelStr" column="LABEL"/>
        <result property="labelStrId" column="LABEL_STR_ID"/>
        <result property="workgroupId" column="AC_WORKGROUP_ID"/>
        <result property="systemManaged" column="SYSTEM_MANAGED"/>
    </resultMap>

    <sql id="objectCounterTypeColumns">
        ${alias}.AC_OBJECT_COUNTER_TYPE_ID ${prefix}AC_OBJECT_COUNTER_TYPE_ID,
        ${alias}.CONSTANT_TOKEN ${prefix}CONSTANT_TOKEN,
        ${alias}.LABEL ${prefix}LABEL,
        ${alias}.LABEL_STR_ID ${prefix}LABEL_STR_ID,
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID,
        ${alias}.SYSTEM_MANAGED ${prefix}SYSTEM_MANAGED
    </sql>

    <select id="findAllByWorkgroupId" resultMap="objectCounterTypeResultMap">
        SELECT
        <include refid="objectCounterTypeColumns">
            <property name="alias" value="OCT"/>
            <property name="prefix" value=""/>
        </include>
        FROM AC_OBJECT_COUNTER_TYPE OCT
        WHERE (OCT.AC_WORKGROUP_ID = #{workgroupId} OR OCT.AC_WORKGROUP_ID is NULL)
        <if test="excludeSystemManaged">
            AND (OCT.SYSTEM_MANAGED != 1 OR OCT.SYSTEM_MANAGED IS NULL )
        </if>
        ORDER BY OCT.AC_OBJECT_COUNTER_TYPE_ID ASC
    </select>
</mapper>
