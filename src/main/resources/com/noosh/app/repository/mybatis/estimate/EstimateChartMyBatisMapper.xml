<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.noosh.app.repository.mybatis.estimate.EstimateChartMyBatisMapper">

    <resultMap id="estimateChartResultMap" type="com.noosh.app.commons.vo.chart.EstimateChartItemVO">
        <result property="avgType" column="avg_type"/>
        <result property="days" column="days"/>
        <result property="totalCount" column="totalCount"/>
    </resultMap>

    <select id="findAverageTurnAroundTimeByDefault" resultMap="estimateChartResultMap">
        <if test="filter == 'workgroup'">
            select 'all_first_estimate_receive_30days' avg_type, sum(delta)/count(EM_ESTIMATE_ID) days, count(EM_ESTIMATE_ID) totalCount FROM (
            SELECT * FROM (
            SELECT m.EM_ESTIMATE_ID, m.a1 - m.a4 delta, m.em_rfe_id, row_number() over(partition by m.em_rfe_id order by m.a1 asc) rn
            FROM (select e.EM_ESTIMATE_ID, e.submit_date a1, r.submit_date a4, r.em_rfe_id
            from em_estimate e, em_rfe r
            where e.em_rfe_id = r.em_rfe_id
            and e.oc_object_state_id = 2000075
            and ADD_MONTHS(e.submit_date, 1) &gt;= sysdate
            and r.owner_ac_workgroup_id = #{workgroupId}) m) WHERE rn=1) avg_x

            union all

            select 'all_last_30days' as avg_type,
            sum(e.submit_date - r.submit_date)/count(e.em_estimate_id) days, count(e.em_estimate_id) totalCount
            from em_estimate e, em_rfe r
            where e.em_rfe_id = r.em_rfe_id
            and e.oc_object_state_id = 2000075
            and ADD_MONTHS(e.submit_date, 1) &gt;= sysdate
            and r.owner_ac_workgroup_id = #{workgroupId}

            union all

            select 'all_first_estimate_receive_6months' avg_type, sum(delta)/count(EM_ESTIMATE_ID) days, count(EM_ESTIMATE_ID) totalCount FROM (
            SELECT * FROM (
            SELECT m.EM_ESTIMATE_ID, m.a1 - m.a4 delta, m.em_rfe_id, row_number() over(partition by m.em_rfe_id order by m.a1 asc) rn
            FROM (select e.EM_ESTIMATE_ID, e.submit_date a1, r.submit_date a4, r.em_rfe_id
            from em_estimate e, em_rfe r
            where e.em_rfe_id = r.em_rfe_id
            and e.oc_object_state_id = 2000075
            and ADD_MONTHS(e.submit_date, 6) &gt;= sysdate
            and r.owner_ac_workgroup_id = #{workgroupId}) m) WHERE rn=1) avg_x

            union all

            select 'all_last_6months' as avg_type,
            sum(e.submit_date - r.submit_date)/count(e.em_estimate_id) days, count(e.em_estimate_id) totalCount
            from em_estimate e, em_rfe r
            where e.em_rfe_id = r.em_rfe_id
            and e.oc_object_state_id = 2000075
            and ADD_MONTHS(e.submit_date, 6) &gt;= sysdate
            and r.owner_ac_workgroup_id = #{workgroupId}

            union all

            select 'all_first_estimate_receive_12months' avg_type, sum(delta)/count(EM_ESTIMATE_ID) days, count(EM_ESTIMATE_ID) totalCount FROM (
            SELECT * FROM (
            SELECT m.EM_ESTIMATE_ID, m.a1 - m.a4 delta, m.em_rfe_id, row_number() over(partition by m.em_rfe_id order by m.a1 asc) rn
            FROM (select e.EM_ESTIMATE_ID, e.submit_date a1, r.submit_date a4, r.em_rfe_id
            from em_estimate e, em_rfe r
            where e.em_rfe_id = r.em_rfe_id
            and e.oc_object_state_id = 2000075
            and ADD_MONTHS(e.submit_date, 12) &gt;= sysdate
            and r.owner_ac_workgroup_id = #{workgroupId}) m) WHERE rn=1) avg_x

        union all

        select 'all_last_12months' as avg_type,
        sum(e.submit_date - r.submit_date)/count(e.em_estimate_id) days, count(e.em_estimate_id) totalCount
        from em_estimate e, em_rfe r
        where e.em_rfe_id = r.em_rfe_id
        and e.oc_object_state_id = 2000075
        and ADD_MONTHS(e.submit_date, 12) &gt;= sysdate
        and r.owner_ac_workgroup_id = #{workgroupId}
        </if>

        <if test="filter == 'my'">
            select 'my_first_estimate_receive_30days' avg_type, sum(delta)/count(EM_ESTIMATE_ID) days, count(EM_ESTIMATE_ID) totalCount FROM (
            SELECT * FROM (
            SELECT m.EM_ESTIMATE_ID, m.a1 - m.a4 delta, m.em_rfe_id, row_number() over(partition by m.em_rfe_id order by m.a1 asc) rn
            FROM (
            SELECT e.submit_date a1, r.submit_date a4, e.em_estimate_id, r.em_rfe_id
            from (select C.OBJECT_ID as ESTIMATE_ID, USER_ID
            FROM
            PM_PROJECT PRJ
            , SY_CONTAINABLE C
            , (SELECT TM_TEAM_OBJECT.OBJECT_ID, USER_ID
            FROM TM_TEAM_OBJECT, TM_TEAM_MEMBER,
            RE_ACCESS_RULE_REL
            WHERE USER_ID = #{userId} AND
            TM_TEAM_MEMBER.CURRENT_RECORD = 1 AND
            TM_TEAM_MEMBER.TM_TEAM_ID = TM_TEAM_OBJECT.TM_TEAM_ID AND
            TM_TEAM_OBJECT.OC_OBJECT_CLASS_ID = 1000000 AND
            TM_TEAM_MEMBER.RE_ROLE_ID = RE_ACCESS_RULE_REL.RE_ROLE_ID AND
            RE_ACCESS_RULE_REL.RE_ACCESS_RULE_ID = 1001090) ACCESS_SQL
            WHERE
            C.OBJECT_CLASS_ID = 1000116
            AND C.PARENT_OBJECT_CLASS_ID = 1000000
            AND C.ITEM_OC_OBJECT_STATE_ID != 2500069
            AND C.PARENT_OBJECT_ID = PRJ.PM_PROJECT_ID
            AND ACCESS_SQL.OBJECT_ID = PRJ.PM_PROJECT_ID) x,
            em_estimate e,
            em_rfe r
            where
            x.ESTIMATE_ID = e.EM_ESTIMATE_ID
            and e.em_rfe_id = r.em_rfe_id
            and e.oc_object_state_id = 2000075
            and ADD_MONTHS(e.submit_date, 1) &gt;= sysdate) m) WHERE rn=1) avg_x

            union all

            select 'my_last_30days' as avg_type,
            sum(e.submit_date - r.submit_date)/count(e.em_estimate_id) days, count(e.em_estimate_id) totalCount
            from (select C.OBJECT_ID as ESTIMATE_ID, USER_ID
            FROM
            PM_PROJECT PRJ
            , SY_CONTAINABLE C
            , (SELECT TM_TEAM_OBJECT.OBJECT_ID, USER_ID
            FROM TM_TEAM_OBJECT, TM_TEAM_MEMBER,
            RE_ACCESS_RULE_REL
            WHERE USER_ID = #{userId} AND
            TM_TEAM_MEMBER.CURRENT_RECORD = 1 AND
            TM_TEAM_MEMBER.TM_TEAM_ID = TM_TEAM_OBJECT.TM_TEAM_ID AND
            TM_TEAM_OBJECT.OC_OBJECT_CLASS_ID = 1000000 AND
            TM_TEAM_MEMBER.RE_ROLE_ID = RE_ACCESS_RULE_REL.RE_ROLE_ID AND
            RE_ACCESS_RULE_REL.RE_ACCESS_RULE_ID = 1001090) ACCESS_SQL
            WHERE
            C.OBJECT_CLASS_ID = 1000116
            AND C.PARENT_OBJECT_CLASS_ID = 1000000
            AND C.ITEM_OC_OBJECT_STATE_ID != 2500069
            AND C.PARENT_OBJECT_ID = PRJ.PM_PROJECT_ID
            AND ACCESS_SQL.OBJECT_ID = PRJ.PM_PROJECT_ID) x,
            em_estimate e,
            em_rfe r
            where
            x.ESTIMATE_ID = e.EM_ESTIMATE_ID
            and e.em_rfe_id = r.em_rfe_id
            and e.oc_object_state_id = 2000075
            and ADD_MONTHS(e.submit_date, 1) &gt;= sysdate

            union all

            select 'my_first_estimate_receive_6months' avg_type, sum(delta)/count(EM_ESTIMATE_ID) days, count(EM_ESTIMATE_ID) totalCount FROM (
            SELECT * FROM (
            SELECT m.EM_ESTIMATE_ID, m.a1 - m.a4 delta, m.em_rfe_id, row_number() over(partition by m.em_rfe_id order by m.a1 asc) rn
            FROM (
            SELECT e.submit_date a1, r.submit_date a4, e.em_estimate_id, r.em_rfe_id
            from (select C.OBJECT_ID as ESTIMATE_ID, USER_ID
            FROM
            PM_PROJECT PRJ
            , SY_CONTAINABLE C
            , (SELECT TM_TEAM_OBJECT.OBJECT_ID, USER_ID
            FROM TM_TEAM_OBJECT, TM_TEAM_MEMBER,
            RE_ACCESS_RULE_REL
            WHERE USER_ID = #{userId} AND
            TM_TEAM_MEMBER.CURRENT_RECORD = 1 AND
            TM_TEAM_MEMBER.TM_TEAM_ID = TM_TEAM_OBJECT.TM_TEAM_ID AND
            TM_TEAM_OBJECT.OC_OBJECT_CLASS_ID = 1000000 AND
            TM_TEAM_MEMBER.RE_ROLE_ID = RE_ACCESS_RULE_REL.RE_ROLE_ID AND
            RE_ACCESS_RULE_REL.RE_ACCESS_RULE_ID = 1001090) ACCESS_SQL
            WHERE
            C.OBJECT_CLASS_ID = 1000116
            AND C.PARENT_OBJECT_CLASS_ID = 1000000
            AND C.ITEM_OC_OBJECT_STATE_ID != 2500069
            AND C.PARENT_OBJECT_ID = PRJ.PM_PROJECT_ID
            AND ACCESS_SQL.OBJECT_ID = PRJ.PM_PROJECT_ID) x,
            em_estimate e,
            em_rfe r
            where
            x.ESTIMATE_ID = e.EM_ESTIMATE_ID
            and e.em_rfe_id = r.em_rfe_id
            and e.oc_object_state_id = 2000075
            and ADD_MONTHS(e.submit_date, 6) &gt;= sysdate) m) WHERE rn=1) avg_x

            union all

            select 'my_last_6months' as avg_type,
            sum(e.submit_date - r.submit_date)/count(e.em_estimate_id) days, count(e.em_estimate_id) totalCount
            from (select C.OBJECT_ID as ESTIMATE_ID, USER_ID
            FROM
            PM_PROJECT PRJ
            , SY_CONTAINABLE C
            , (SELECT TM_TEAM_OBJECT.OBJECT_ID, USER_ID
            FROM TM_TEAM_OBJECT, TM_TEAM_MEMBER,
            RE_ACCESS_RULE_REL
            WHERE USER_ID = #{userId} AND
            TM_TEAM_MEMBER.CURRENT_RECORD = 1 AND
            TM_TEAM_MEMBER.TM_TEAM_ID = TM_TEAM_OBJECT.TM_TEAM_ID AND
            TM_TEAM_OBJECT.OC_OBJECT_CLASS_ID = 1000000 AND
            TM_TEAM_MEMBER.RE_ROLE_ID = RE_ACCESS_RULE_REL.RE_ROLE_ID AND
            RE_ACCESS_RULE_REL.RE_ACCESS_RULE_ID = 1001090) ACCESS_SQL
            WHERE
            C.OBJECT_CLASS_ID = 1000116
            AND C.PARENT_OBJECT_CLASS_ID = 1000000
            AND C.ITEM_OC_OBJECT_STATE_ID != 2500069
            AND C.PARENT_OBJECT_ID = PRJ.PM_PROJECT_ID
            AND ACCESS_SQL.OBJECT_ID = PRJ.PM_PROJECT_ID) x,
            em_estimate e,
            em_rfe r
            where
            x.ESTIMATE_ID = e.EM_ESTIMATE_ID
            and e.em_rfe_id = r.em_rfe_id
            and e.oc_object_state_id = 2000075
            and ADD_MONTHS(e.submit_date, 6) &gt;= sysdate

            union all

            select 'my_first_estimate_receive_12months' avg_type, sum(delta)/count(EM_ESTIMATE_ID) days, count(EM_ESTIMATE_ID) totalCount FROM (
            SELECT * FROM (
            SELECT m.EM_ESTIMATE_ID, m.a1 - m.a4 delta, m.em_rfe_id, row_number() over(partition by m.em_rfe_id order by m.a1 asc) rn
            FROM (
            SELECT e.submit_date a1, r.submit_date a4, e.em_estimate_id, r.em_rfe_id
            from (select C.OBJECT_ID as ESTIMATE_ID, USER_ID
            FROM
            PM_PROJECT PRJ
            , SY_CONTAINABLE C
            , (SELECT TM_TEAM_OBJECT.OBJECT_ID, USER_ID
            FROM TM_TEAM_OBJECT, TM_TEAM_MEMBER,
            RE_ACCESS_RULE_REL
            WHERE USER_ID = #{userId} AND
            TM_TEAM_MEMBER.CURRENT_RECORD = 1 AND
            TM_TEAM_MEMBER.TM_TEAM_ID = TM_TEAM_OBJECT.TM_TEAM_ID AND
            TM_TEAM_OBJECT.OC_OBJECT_CLASS_ID = 1000000 AND
            TM_TEAM_MEMBER.RE_ROLE_ID = RE_ACCESS_RULE_REL.RE_ROLE_ID AND
            RE_ACCESS_RULE_REL.RE_ACCESS_RULE_ID = 1001090) ACCESS_SQL
            WHERE
            C.OBJECT_CLASS_ID = 1000116
            AND C.PARENT_OBJECT_CLASS_ID = 1000000
            AND C.ITEM_OC_OBJECT_STATE_ID != 2500069
            AND C.PARENT_OBJECT_ID = PRJ.PM_PROJECT_ID
            AND ACCESS_SQL.OBJECT_ID = PRJ.PM_PROJECT_ID) x,
            em_estimate e,
            em_rfe r
            where
            x.ESTIMATE_ID = e.EM_ESTIMATE_ID
            and e.em_rfe_id = r.em_rfe_id
            and e.oc_object_state_id = 2000075
            and ADD_MONTHS(e.submit_date, 12) &gt;= sysdate) m) WHERE rn=1) avg_x

        union all

        select 'my_last_12months' as avg_type,
        sum(e.submit_date - r.submit_date)/count(e.em_estimate_id) days, count(e.em_estimate_id) totalCount
        from (select C.OBJECT_ID as ESTIMATE_ID, USER_ID
            FROM
              PM_PROJECT PRJ
              , SY_CONTAINABLE C
              , (SELECT TM_TEAM_OBJECT.OBJECT_ID, USER_ID
              FROM TM_TEAM_OBJECT, TM_TEAM_MEMBER,
              RE_ACCESS_RULE_REL
              WHERE USER_ID = #{userId} AND
              TM_TEAM_MEMBER.CURRENT_RECORD = 1 AND
              TM_TEAM_MEMBER.TM_TEAM_ID = TM_TEAM_OBJECT.TM_TEAM_ID AND
              TM_TEAM_OBJECT.OC_OBJECT_CLASS_ID = 1000000 AND
              TM_TEAM_MEMBER.RE_ROLE_ID = RE_ACCESS_RULE_REL.RE_ROLE_ID AND
              RE_ACCESS_RULE_REL.RE_ACCESS_RULE_ID = 1001090) ACCESS_SQL
            WHERE
              C.OBJECT_CLASS_ID = 1000116
              AND C.PARENT_OBJECT_CLASS_ID = 1000000
              AND C.ITEM_OC_OBJECT_STATE_ID != 2500069
              AND C.PARENT_OBJECT_ID = PRJ.PM_PROJECT_ID
              AND ACCESS_SQL.OBJECT_ID = PRJ.PM_PROJECT_ID) x,
        em_estimate e,
        em_rfe r
        where
        x.ESTIMATE_ID = e.EM_ESTIMATE_ID
        and e.em_rfe_id = r.em_rfe_id
        and e.oc_object_state_id = 2000075
        and ADD_MONTHS(e.submit_date, 12) &gt;= sysdate
        </if>

        <if test="filter == 'supplier30'">
            select  w.name as avg_type, x.days, x.totalCount FROM
            (select e.OWNER_AC_WORKGROUP_ID,
            sum(e.submit_date - r.submit_date)/count(e.em_estimate_id) days, count(e.em_estimate_id) totalCount
            from em_estimate e, em_rfe r
            where e.em_rfe_id = r.em_rfe_id
            and e.oc_object_state_id = 2000075
            and r.owner_ac_workgroup_id = #{workgroupId}
            and ADD_MONTHS(e.submit_date, 1) &gt;= sysdate
            GROUP BY e.OWNER_AC_WORKGROUP_ID) x, ac_workgroup w
            WHERE x.OWNER_AC_WORKGROUP_ID = w.ac_workgroup_id
        </if>

        <if test="filter == 'supplier6Month'">
            select  w.name as avg_type, x.days, x.totalCount FROM
            (select e.OWNER_AC_WORKGROUP_ID,
            sum(e.submit_date - r.submit_date)/count(e.em_estimate_id) days, count(e.em_estimate_id) totalCount
            from em_estimate e, em_rfe r
            where e.em_rfe_id = r.em_rfe_id
            and e.oc_object_state_id = 2000075
            and r.owner_ac_workgroup_id = #{workgroupId}
            and ADD_MONTHS(e.submit_date, 6) &gt;= sysdate
            GROUP BY e.OWNER_AC_WORKGROUP_ID) x, ac_workgroup w
            WHERE x.OWNER_AC_WORKGROUP_ID = w.ac_workgroup_id
        </if>

        <if test="filter == 'supplier12Month'">
            select  w.name as avg_type, x.days, x.totalCount FROM
            (select e.OWNER_AC_WORKGROUP_ID,
            sum(e.submit_date - r.submit_date)/count(e.em_estimate_id) days, count(e.em_estimate_id) totalCount
            from em_estimate e, em_rfe r
            where e.em_rfe_id = r.em_rfe_id
            and e.oc_object_state_id = 2000075
            and r.owner_ac_workgroup_id = #{workgroupId}
            and ADD_MONTHS(e.submit_date, 12) &gt;= sysdate
            GROUP BY e.OWNER_AC_WORKGROUP_ID) x, ac_workgroup w
            WHERE x.OWNER_AC_WORKGROUP_ID = w.ac_workgroup_id
        </if>
    </select>

    <select id="findAverageTurnAroundTimeBySupplier" resultMap="estimateChartResultMap">
        select
        w.name as avg_type, days
        from
        (
            select distinct sw.supplier_ac_workgroup_id
            from BU_SUPPLIER_WORKGROUP sw
            where sw.owner_ac_workgroup_id = #{workgroupId}
        ) swx,

        ac_workgroup w,
        (
            select e.owner_ac_workgroup_id,
            sum(e.submit_date - r.submit_date)/count(e.em_estimate_id) days
            from em_estimate e, em_rfe r
            where e.em_rfe_id = r.em_rfe_id
            and e.oc_object_state_id = 2000075
            group by e.owner_ac_workgroup_id
        ) x
        where w.ac_workgroup_id = x.owner_ac_workgroup_id
        and swx.supplier_ac_workgroup_id = w.ac_workgroup_id
        order by w.name
    </select>
</mapper>