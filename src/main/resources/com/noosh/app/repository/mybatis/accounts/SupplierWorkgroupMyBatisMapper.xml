<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.accounts.SupplierWorkgroupMyBatisMapper">

    <resultMap id="supplierWorkgroupResultMap" type="com.noosh.app.commons.dto.accounts.SupplierWorkgroupDTO">
        <result property="id" column="BU_SUPPLIER_WORKGROUP_ID"/>
        <result property="ownerWorkgroupId" column="OWNER_AC_WORKGROUP_ID"/>
        <result property="supplierWorkgroupId" column="SUPPLIER_AC_WORKGROUP_ID"/>
        <result property="isApproved" column="IS_APPROVED"/>
        <result property="clientWorkgroupId" column="CLIENT_AC_WORKGROUP_ID"/>
        <result property="alias" column="ALIAS"/>
        <result property="acceptQuote" column="ACCEPT_QUOTE"/>
        <result property="acceptChangeOrder" column="ACCEPT_CHANGE_ORDER"/>
        <result property="supplierCode" column="SUPPLIER_CODE"/>
        <result property="defaultSupplierUserId" column="DEFAULT_SUPPLIER_USER_ID"/>
        <result property="customPropertyId" column="CUSTOM_PR_PROPERTY_ID"/>
        <result property="acceptPartialInvoice" column="ACCEPT_PARTIAL_INVOICE"/>
        <result property="acceptChangeOrderNoChange" column="ACCEPT_CO_NO_CHANGE"/>
        <association property="supplierWorkgroup" columnPrefix="SW_" resultMap="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper.workgroupResultMap"/>
        <association property="clientWorkgroup" columnPrefix="CW_" resultMap="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper.workgroupResultMap"/>
        <association property="defaultSupplierUser" columnPrefix="AU_" resultMap="com.noosh.app.repository.mybatis.security.AccountUserMyBatisMapper.accountUserResultMap"/>
        <collection property="supplierWHLocations" columnPrefix="SL_" resultMap="com.noosh.app.repository.mybatis.accounts.SupplierWHLocationMyBatisMapper.supplierWHLocationResultMap"/>
    </resultMap>

    <sql id="supplierWorkgroupColumns">
        ${alias}.BU_SUPPLIER_WORKGROUP_ID ${prefix}BU_SUPPLIER_WORKGROUP_ID,
        ${alias}.OWNER_AC_WORKGROUP_ID ${prefix}OWNER_AC_WORKGROUP_ID,
        ${alias}.SUPPLIER_AC_WORKGROUP_ID ${prefix}SUPPLIER_AC_WORKGROUP_ID,
        ${alias}.IS_APPROVED ${prefix}IS_APPROVED,
        ${alias}.CLIENT_AC_WORKGROUP_ID ${prefix}CLIENT_AC_WORKGROUP_ID,
        ${alias}.ALIAS ${prefix}ALIAS,
        ${alias}.ACCEPT_QUOTE ${prefix}ACCEPT_QUOTE,
        ${alias}.ACCEPT_CHANGE_ORDER ${prefix}ACCEPT_CHANGE_ORDER,
        ${alias}.SUPPLIER_CODE ${prefix}SUPPLIER_CODE,
        ${alias}.DEFAULT_SUPPLIER_USER_ID ${prefix}DEFAULT_SUPPLIER_USER_ID,
        ${alias}.CUSTOM_PR_PROPERTY_ID ${prefix}CUSTOM_PR_PROPERTY_ID,
        ${alias}.ACCEPT_PARTIAL_INVOICE ${prefix}ACCEPT_PARTIAL_INVOICE,
        ${alias}.ACCEPT_CO_NO_CHANGE ${prefix}ACCEPT_CO_NO_CHANGE
    </sql>

    <select id="find" resultMap="supplierWorkgroupResultMap">
        SELECT
        <include refid="supplierWorkgroupColumns">
            <property name="alias" value="S"/>
            <property name="prefix" value=""/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper.workgroupColumns">
            <property name="alias" value="SW"/>
            <property name="prefix" value="SW_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper.workgroupColumns">
            <property name="alias" value="CW"/>
            <property name="prefix" value="CW_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.AccountUserMyBatisMapper.accountUserColumns">
            <property name="alias" value="AU"/>
            <property name="prefix" value="AU_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.PersonMyBatisMapper.personColumns">
            <property name="alias" value="P"/>
            <property name="prefix" value="AU_P_"/>
        </include>,
        decode(CW.name, NULL, OW.name, '[Client] ' || CW.name) AS display_name
        FROM
        bu_supplier_workgroup  S,
        ac_workgroup           CW,
        ac_workgroup           OW,
        ac_account_user        AU,
        ac_person              P,
        ac_person_email        PE,
        ac_person_sd           PSD,
        ac_workgroup           SW
        WHERE
        S.client_ac_workgroup_id = CW.ac_workgroup_id (+)
        AND S.supplier_ac_workgroup_id = SW.ac_workgroup_id (+)
        AND S.owner_ac_workgroup_id = OW.ac_workgroup_id (+)
        AND S.default_supplier_user_id = AU.user_id (+)
        AND AU.ac_person_id = P.ac_person_id (+)
        AND P.ac_person_id = PE.ac_person_id (+)
        AND PE.is_default (+) = 1
        AND P.ac_person_id = PSD.ac_person_id (+)
        AND S.BU_SUPPLIER_WORKGROUP_ID = #{supplierId}
    </select>

    <select id="findSupplierWorkgroups" resultMap="supplierWorkgroupResultMap">
        SELECT
            <include refid="supplierWorkgroupColumns">
                <property name="alias" value="S"/>
                <property name="prefix" value=""/>
            </include>,
            SW.NAME AS SW_NAME,
            CW.NAME AS CW_NAME,
            AU.USER_ID AS AU_USER_ID,
            P.FIRST_NAME AS AU_P_FIRST_NAME,
            P.LAST_NAME AS AU_P_LAST_NAME,
            P.MIDDLE_NAME AS AU_P_MIDDLE_NAME,
            <include refid="com.noosh.app.repository.mybatis.accounts.SupplierWHLocationMyBatisMapper.supplierWHLocationColumns">
                <property name="alias" value="SL"/>
                <property name="prefix" value="SL_"/>
            </include>,
            <include refid="com.noosh.app.repository.mybatis.accounts.WarehouseLocationMyBatisMapper.warehouseLocationColumns">
                <property name="alias" value="WH"/>
                <property name="prefix" value="SL_WH_"/>
            </include>,
            decode(CW.name, NULL, OW.name, '[Client] ' || CW.name) AS display_name
        FROM
            bu_supplier_workgroup  S,
            ac_workgroup           CW,
            ac_workgroup           OW,
            ac_account_user        AU,
            ac_person              P,
            ac_person_email        PE,
            ac_person_sd           PSD,
            ac_workgroup           SW,
            bu_supplier_whlocation SL,
            ac_whlocation          WH
        WHERE
                S.client_ac_workgroup_id = CW.ac_workgroup_id (+)
            AND S.supplier_ac_workgroup_id = SW.ac_workgroup_id (+)
            AND S.owner_ac_workgroup_id = OW.ac_workgroup_id (+)
            AND S.default_supplier_user_id = AU.user_id (+)
            AND AU.ac_person_id = P.ac_person_id (+)
            AND P.ac_person_id = PE.ac_person_id (+)
            AND PE.is_default (+) = 1
            AND P.ac_person_id = PSD.ac_person_id (+)
            AND S.BU_SUPPLIER_WORKGROUP_ID = SL.BU_SUPPLIER_WORKGROUP_ID (+)
            AND SL.AC_WHLOCATION_ID = WH.AC_WHLOCATION_ID (+)
            AND S.owner_ac_workgroup_id = #{ownerWorkgroupId}
            <if test="supplierWorkgroupName != null and supplierWorkgroupName != ''">
                AND UPPER(SW.NAME) like #{supplierWorkgroupName} ESCAPE '/'
            </if>
            <if test="clientWorkgroupFilterSql != null and clientWorkgroupFilterSql != ''">
                ${clientWorkgroupFilterSql}
            </if>
            <if test="hHCertFilterSql != null and hHCertFilterSql != ''">
                ${hHCertFilterSql}
            </if>
    </select>

    <select id="findSupplierWorkgroupsByName" resultMap="supplierWorkgroupResultMap">
        SELECT
        <include refid="supplierWorkgroupColumns">
            <property name="alias" value="S"/>
            <property name="prefix" value=""/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper.workgroupColumns">
            <property name="alias" value="SW"/>
            <property name="prefix" value="SW_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper.workgroupColumns">
            <property name="alias" value="OW"/>
            <property name="prefix" value="OW_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.AccountUserMyBatisMapper.accountUserColumns">
            <property name="alias" value="AU"/>
            <property name="prefix" value="AU_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.PersonMyBatisMapper.personColumns">
            <property name="alias" value="P"/>
            <property name="prefix" value="AU_P_"/>
        </include>
        FROM
        bu_supplier_workgroup  S,
        ac_workgroup           CW,
        ac_workgroup           OW,
        ac_account_user        AU,
        ac_person              P,
        ac_person_email        PE,
        ac_person_sd           PSD,
        ac_workgroup           SW
        WHERE
        S.client_ac_workgroup_id = CW.ac_workgroup_id (+)
        AND S.supplier_ac_workgroup_id = SW.ac_workgroup_id (+)
        AND S.owner_ac_workgroup_id = OW.ac_workgroup_id (+)
        AND S.default_supplier_user_id = AU.user_id (+)
        AND AU.ac_person_id = P.ac_person_id (+)
        AND P.ac_person_id = PE.ac_person_id (+)
        AND PE.is_default (+) = 1
        AND P.ac_person_id = PSD.ac_person_id (+)
        AND S.owner_ac_workgroup_id = #{ownerWorkgroupId}
        AND UPPER(SW.NAME) = UPPER(#{supplierWorkgroupName})
        AND S.CLIENT_AC_WORKGROUP_ID IS NULL
    </select>
    <select id="findByOwnerAndSupplierWorkgroupId" resultMap="supplierWorkgroupResultMap">
        SELECT
        <include refid="supplierWorkgroupColumns">
            <property name="alias" value="S"/>
            <property name="prefix" value=""/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper.workgroupColumns">
            <property name="alias" value="SW"/>
            <property name="prefix" value="SW_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper.workgroupColumns">
            <property name="alias" value="CW"/>
            <property name="prefix" value="CW_"/>
        </include>
        FROM
        bu_supplier_workgroup  S,
        ac_workgroup           CW,
        ac_workgroup           OW,
        ac_workgroup           SW
        WHERE
        S.client_ac_workgroup_id = CW.ac_workgroup_id (+)
        AND S.supplier_ac_workgroup_id = SW.ac_workgroup_id (+)
        AND S.owner_ac_workgroup_id = OW.ac_workgroup_id (+)
        AND S.owner_ac_workgroup_id = #{ownerWorkgroupId}
        AND S.supplier_ac_workgroup_id = #{supplierWorkgroupId}
        AND S.BU_SUPPLIER_WORKGROUP_ID != #{supplierId}
    </select>

    <select id="findOutsourcerSuppliers" resultMap="supplierWorkgroupResultMap">
        SELECT
        <include refid="supplierWorkgroupColumns">
            <property name="alias" value="S"/>
            <property name="prefix" value=""/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper.workgroupColumns">
            <property name="alias" value="SW"/>
            <property name="prefix" value="SW_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper.workgroupColumns">
            <property name="alias" value="CW"/>
            <property name="prefix" value="CW_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.AccountUserMyBatisMapper.accountUserColumns">
            <property name="alias" value="AU"/>
            <property name="prefix" value="AU_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.PersonMyBatisMapper.personColumns">
            <property name="alias" value="P"/>
            <property name="prefix" value="AU_P_"/>
        </include>,
        decode(CW.name, NULL, OW.name, '[Client] ' || CW.name) AS display_name
        FROM
            bu_supplier_workgroup  S,
            ac_workgroup           CW,
            ac_workgroup           OW,
            ac_account_user        AU,
            ac_person              P,
            ac_person_email        PE,
            ac_person_sd           PSD,
            ac_workgroup           SW
        WHERE
                S.client_ac_workgroup_id = CW.ac_workgroup_id (+)
            AND S.supplier_ac_workgroup_id = SW.ac_workgroup_id (+)
            AND S.owner_ac_workgroup_id = OW.ac_workgroup_id (+)
            AND S.default_supplier_user_id = AU.user_id (+)
            AND AU.ac_person_id = P.ac_person_id (+)
            AND P.ac_person_id = PE.ac_person_id (+)
            AND PE.is_default (+) = 1
            AND P.ac_person_id = PSD.ac_person_id (+)
            AND S.owner_ac_workgroup_id = #{ownerWorkgroupId}
            AND ( S.client_ac_workgroup_id IS NULL OR S.client_ac_workgroup_id = 0 )
            AND S.supplier_ac_workgroup_id NOT IN (
                SELECT
                    S.supplier_ac_workgroup_id
                FROM
                    bu_supplier_workgroup S
                WHERE
                    S.owner_ac_workgroup_id = #{ownerWorkgroupId}
                    AND S.client_ac_workgroup_id = #{clientWorkgroupId}
            )
            <if test="hHCertFilterSql != null and hHCertFilterSql != ''">
                ${hHCertFilterSql}
            </if>
    </select>

    <select id="findApprovedClients" resultMap="supplierWorkgroupResultMap">
        SELECT
            <include refid="supplierWorkgroupColumns">
                <property name="alias" value="S"/>
                <property name="prefix" value=""/>
            </include>,
            <include refid="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper.workgroupColumns">
                <property name="alias" value="SW"/>
                <property name="prefix" value="SW_"/>
            </include>,
            <include refid="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper.workgroupColumns">
                <property name="alias" value="CW"/>
                <property name="prefix" value="CW_"/>
            </include>
        FROM
            bu_supplier_workgroup  S,
            ac_workgroup           CW,
            ac_workgroup           SW
        WHERE
            S.CLIENT_AC_WORKGROUP_ID = CW.AC_WORKGROUP_ID ( + )
            AND S.SUPPLIER_AC_WORKGROUP_ID = SW.AC_WORKGROUP_ID ( + )
            AND S.OWNER_AC_WORKGROUP_ID = #{ownerWorkgroupId}
            AND S.SUPPLIER_AC_WORKGROUP_ID = #{supplierWorkgroupId}
            AND S.CLIENT_AC_WORKGROUP_ID IS NOT NULL
            <if test="searchStr != null and searchStr != ''">
                AND (UPPER(CW.NAME) like #{searchStr} ESCAPE '/')
            </if>
    </select>

</mapper>
