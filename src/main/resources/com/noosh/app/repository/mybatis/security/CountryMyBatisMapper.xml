<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.security.CountryMyBatisMapper">

    <resultMap id="countryResultMap" type="com.noosh.app.commons.dto.security.CountryDTO">
        <id property="id" column="AC_COUNTRY_ID"/>
        <result property="constantToken" column="CONSTANT_TOKEN"/>
        <result property="nameStrId" column="NAME_STR_ID"/>
    </resultMap>

    <sql id="countryColumns">
        ${alias}.AC_COUNTRY_ID ${prefix}AC_COUNTRY_ID,
        ${alias}.CONSTANT_TOKEN ${prefix}CONSTANT_TOKEN,
        ${alias}.NAME_STR_ID ${prefix}NAME_STR_ID
    </sql>

    <select id="findAll" resultMap="countryResultMap">
        SELECT
        <include refid="countryColumns">
            <property name="alias" value="C"/>
            <property name="prefix" value=""/>
        </include>
        FROM
        AC_COUNTRY C
    </select>
</mapper>
