<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.accounts.WorkgroupProfileMyBatisMapper">

    <resultMap id="workgroupProfileResultMap" type="com.noosh.app.commons.dto.accounts.WorkgroupProfileDTO">
        <id property="id" column="AC_WG_PROFILE_ID"/>
        <result property="workgroupId" column="AC_WORKGROUP_ID"/>
        <result property="contactUserId" column="CONTACT_USER_ID"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="noOfEmployees" column="NO_OF_EMPLOYEES"/>
        <result property="isSecured" column="IS_SECURED"/>
        <result property="isSmall" column="IS_SMALL"/>
        <result property="isDisadvantaged" column="IS_DISADVANTAGED"/>
        <result property="isWomanOwned" column="IS_WOMAN_OWNED"/>
        <result property="isMinority" column="IS_MINORITY"/>
        <result property="isHZSB" column="IS_HZSB"/>
        <result property="isSDB" column="IS_SDB"/>
        <result property="isVetOwned" column="IS_VET_OWNED"/>
        <result property="isDisabledVetOwned" column="IS_DISABLED_VET_OWNED"/>
        <result property="logoId" column="AC_WG_LOGO_ID"/>
        <association property="logo" columnPrefix="WL_" resultMap="com.noosh.app.repository.mybatis.accounts.WorkgroupLogoMyBatisMapper.workgroupLogoResultMap"/>
    </resultMap>

    <sql id="workgroupProfileColumns">
        ${alias}.AC_WG_PROFILE_ID ${prefix}AC_WG_PROFILE_ID,
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID,
        ${alias}.CONTACT_USER_ID ${prefix}CONTACT_USER_ID,
        ${alias}.DESCRIPTION ${prefix}DESCRIPTION,
        ${alias}.NO_OF_EMPLOYEES ${prefix}NO_OF_EMPLOYEES,
        ${alias}.IS_SECURED ${prefix}IS_SECURED,
        ${alias}.IS_SMALL ${prefix}IS_SMALL,
        ${alias}.IS_DISADVANTAGED ${prefix}IS_DISADVANTAGED,
        ${alias}.IS_WOMAN_OWNED ${prefix}IS_WOMAN_OWNED,
        ${alias}.IS_MINORITY ${prefix}IS_MINORITY,
        ${alias}.IS_HZSB ${prefix}IS_HZSB,
        ${alias}.IS_SDB ${prefix}IS_SDB,
        ${alias}.IS_VET_OWNED ${prefix}IS_VET_OWNED,
        ${alias}.IS_DISABLED_VET_OWNED ${prefix}IS_DISABLED_VET_OWNED,
        ${alias}.AC_WG_LOGO_ID ${prefix}AC_WG_LOGO_ID
    </sql>

    <select id="findByWorkgroupId" resultMap="workgroupProfileResultMap">
        SELECT
        <include refid="workgroupProfileColumns">
            <property name="alias" value="WP"/>
            <property name="prefix" value=""/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.accounts.WorkgroupLogoMyBatisMapper.workgroupLogoColumns">
            <property name="alias" value="WL"/>
            <property name="prefix" value="WL_"/>
        </include>
        FROM
            AC_WG_PROFILE WP,
            AC_WG_LOGO WL
        WHERE
            WP.AC_WORKGROUP_ID = #{workgroupId}
            AND WP.AC_WG_LOGO_ID = WL.AC_WG_LOGO_ID ( + )
    </select>
</mapper>
