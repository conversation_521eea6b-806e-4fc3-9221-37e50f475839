<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.address.AddressMyBatisMapper">

    <resultMap id="addressResultMap" type="com.noosh.app.commons.dto.address.AddressDTO">
        <result property = "id" column = "AC_ADDRESS_ID"/>
        <result property = "description" column = "DESCRIPTION"/>
        <result property = "line1" column = "LINE_1"/>
        <result property = "line2" column = "LINE_2"/>
        <result property = "line3" column = "LINE_3"/>
        <result property = "city" column = "CITY"/>
        <result property = "state" column = "STATE"/>
        <result property = "postal" column = "POSTAL"/>
        <result property = "countryId" column = "AC_COUNTRY_ID"/>
        <result property = "countryStrId" column = "NAME_STR_ID"/>
    </resultMap>


    <select id="findAddressByIds" resultMap="addressResultMap">
        SELECT aa.AC_ADDRESS_ID, aa.AC_COUNTRY_ID , aa.CITY , aa.DESCRIPTION , aa.LINE_1 ,
        aa.LINE_2 , aa.LINE_3 , aa.POSTAL , aa.STATE , ac.NAME_STR_ID FROM AC_ADDRESS aa,
        AC_COUNTRY ac WHERE aa.AC_COUNTRY_ID = ac.AC_COUNTRY_ID
        and aa.AC_ADDRESS_ID IN
        <foreach item="item" index="index" collection="addressIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findWorkgroupMainAddress" resultMap="addressResultMap">
        SELECT aa.AC_ADDRESS_ID, aa.AC_COUNTRY_ID , aa.CITY , aa.DESCRIPTION , aa.LINE_1 ,
        aa.LINE_2 , aa.LINE_3 , aa.POSTAL , aa.STATE , ac.NAME_STR_ID FROM AC_WORKGROUP_ADDRESS awa, AC_ADDRESS aa , AC_COUNTRY ac
        WHERE awa.AC_ADDRESS_ID = aa.AC_ADDRESS_ID AND aa.AC_COUNTRY_ID = ac.AC_COUNTRY_ID
        AND awa.AC_ADDRESS_TYPE_ID = 1000000 AND awa.AC_WORKGROUP_ID = #{workgroupId}
        <if test="isIncludingWarehouse">
            UNION
            SELECT aa.AC_ADDRESS_ID, aa.AC_COUNTRY_ID , aa.CITY , aa.DESCRIPTION , aa.LINE_1 ,
            aa.LINE_2 , aa.LINE_3 , aa.POSTAL , aa.STATE , ac.NAME_STR_ID FROM AC_WHLOCATION aw, AC_ADDRESS aa , AC_COUNTRY ac
            WHERE aw.AC_ADDRESS_ID = aa.AC_ADDRESS_ID AND aa.AC_COUNTRY_ID = ac.AC_COUNTRY_ID
            AND aw.OC_OBJECT_STATE_ID = 2000121 AND aw.AC_WORKGROUP_ID = #{workgroupId}
        </if>

    </select>


</mapper>