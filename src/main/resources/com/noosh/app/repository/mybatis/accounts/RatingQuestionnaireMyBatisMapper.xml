<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.accounts.RatingQuestionnaireMyBatisMapper">

    <resultMap id="ratingQuestionnaireResultMap" type="com.noosh.app.commons.dto.accounts.RatingQuestionnaireDTO">
        <id property="id" column="SR_QUESTIONNAIRE_ID"/>
        <result property="title" column="TITLE"/>
        <result property="workGroupId" column="AC_WORKGROUP_ID"/>
        <result property="isCurrent" column="IS_CURRENT"/>
        <result property="createDate" column="CREATE_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="modDate" column="MOD_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="createUserId" column="CREATE_USER_ID"/>
        <result property="modUserId" column="MOD_USER_ID"/>
        <collection property="questions" columnPrefix="Q_" resultMap="com.noosh.app.repository.mybatis.accounts.RatingQuestionMyBatisMapper.ratingQuestionResultMap"/>
    </resultMap>

    <sql id="ratingQuestionnaireColumns">
        ${alias}.SR_QUESTIONNAIRE_ID ${prefix}SR_QUESTIONNAIRE_ID,
        ${alias}.TITLE ${prefix}TITLE,
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID,
        ${alias}.IS_CURRENT ${prefix}IS_CURRENT,
        ${alias}.CREATE_DATE ${prefix}CREATE_DATE,
        ${alias}.MOD_DATE ${prefix}MOD_DATE,
        ${alias}.CREATE_USER_ID ${prefix}CREATE_USER_ID,
        ${alias}.MOD_USER_ID ${prefix}MOD_USER_ID
    </sql>

    <select id="findByWorkGroupId" resultMap="ratingQuestionnaireResultMap">
        SELECT
        <include refid="ratingQuestionnaireColumns">
            <property name="alias" value="R"/>
            <property name="prefix" value=""/>
        </include>
        FROM SR_QUESTIONNAIRE R
        WHERE R.AC_WORKGROUP_ID = #{WorkGroupId}
        AND R.IS_CURRENT = 1
        ORDER BY R.SR_QUESTIONNAIRE_ID
    </select>

    <select id="findWithQuestions" resultMap="ratingQuestionnaireResultMap">
        SELECT
            <include refid="ratingQuestionnaireColumns">
                <property name="alias" value="R"/>
                <property name="prefix" value=""/>
            </include>,
            <include refid="com.noosh.app.repository.mybatis.accounts.RatingQuestionMyBatisMapper.ratingQuestionColumns">
                <property name="alias" value="Q"/>
                <property name="prefix" value="Q_"/>
            </include>
        FROM
            SR_QUESTIONNAIRE R,
            SR_QUESTION Q
        WHERE
            R.SR_QUESTIONNAIRE_ID = #{questionnaireId}
          AND Q.SR_QUESTIONNAIRE_ID = R.SR_QUESTIONNAIRE_ID
        ORDER BY
            SR_QUESTIONNAIRE_ID,
            Q_SR_SECTION_ID,
            Q_ORDINAL
    </select>

</mapper>
