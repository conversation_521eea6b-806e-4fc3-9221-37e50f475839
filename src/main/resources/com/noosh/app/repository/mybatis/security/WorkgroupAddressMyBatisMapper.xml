<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.security.WorkgroupAddressMyBatisMapper">

    <sql id="workgroupAddressColumns">
        ${alias}.AC_WORKGROUP_ADDRESS_ID ${prefix}AC_WORKGROUP_ADDRESS_ID,
        ${alias}.AC_ADDRESS_TYPE_ID ${prefix}AC_ADDRESS_TYPE_ID,
        ${alias}.AC_ADDRESS_ID ${prefix}AC_ADDRESS_ID,
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID
    </sql>

</mapper>
