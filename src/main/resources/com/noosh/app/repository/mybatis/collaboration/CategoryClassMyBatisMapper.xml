<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.collaboration.CategoryClassMyBatisMapper">

    <resultMap id="categoryClassResultMap" type="com.noosh.app.commons.dto.collaboration.CategoryClassDTO">
        <id property="id" column="CO_CATEGORY_CLASS_ID"/>
        <result property="categoryId" column="CO_CATEGORY_ID"/>
        <result property="objectClassId" column="OC_OBJECT_CLASS_ID"/>
        <result property="ordinalNumber" column="ORDINAL_NUMBER"/>
        <result property="isDefault" column="IS_DEFAULT"/>
    </resultMap>

    <sql id="categoryClassColumns">
        ${alias}.CO_CATEGORY_CLASS_ID ${prefix}CO_CATEGORY_CLASS_ID,
        ${alias}.CO_CATEGORY_ID ${prefix}CO_CATEGORY_ID,
        ${alias}.OC_OBJECT_CLASS_ID ${prefix}OC_OBJECT_CLASS_ID,
        ${alias}.ORDINAL_NUMBER ${prefix}ORDINAL_NUMBER,
        ${alias}.IS_DEFAULT ${prefix}IS_DEFAULT
    </sql>

</mapper>
