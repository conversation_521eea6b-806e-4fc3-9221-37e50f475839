<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.security.PersonMyBatisMapper">

    <resultMap id="personResultMap" type="com.noosh.app.commons.dto.security.PersonDTO">
        <id property="id" column="AC_PERSON_ID"/>
        <result property="firstName" column="FIRST_NAME"/>
        <result property="lastName" column="LAST_NAME"/>
        <result property="middleName" column="MIDDLE_NAME"/>
        <result property="addressId" column="AC_ADDRESS_ID"/>
        <result property="timeZoneId" column="AC_TIME_ZONE_ID"/>
        <result property="localeId" column="AC_LOCALE_ID"/>
        <result property="isPublic" column="IS_PUBLIC"/>
        <result property="invitedByUserId" column="INVITED_BY_USER_ID"/>
        <result property="activationDate" column="ACTIVATION_DATE"/>
        <result property="personalCompanyName" column="PERSONAL_COMPANY_NAME"/>
        <result property="customPropertyId" column="CUSTOM_PR_PROPERTY_ID"/>
        <result property="profileImg" column="AC_PERSON_PROFILE_IMG"/>
        <association property="address" columnPrefix="A_"
                     resultMap="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper.addressResultMap"/>
        <association property="psd" columnPrefix="PS_"
                     resultMap="com.noosh.app.repository.mybatis.security.PersonSecondaryDataMyBatisMapper.personSecondaryDataResultMap"/>
        <association property="defaultPersonEmail" columnPrefix="E_"
                     resultMap="com.noosh.app.repository.mybatis.security.PersonEmailMyBatisMapper.personEmailResultMap"/>
    </resultMap>

    <sql id="personColumns">
        ${alias}.AC_PERSON_ID ${prefix}AC_PERSON_ID,
        ${alias}.FIRST_NAME ${prefix}FIRST_NAME,
        ${alias}.LAST_NAME ${prefix}LAST_NAME,
        ${alias}.MIDDLE_NAME ${prefix}MIDDLE_NAME,
        ${alias}.AC_ADDRESS_ID ${prefix}AC_ADDRESS_ID,
        ${alias}.AC_TIME_ZONE_ID ${prefix}AC_TIME_ZONE_ID,
        ${alias}.AC_LOCALE_ID ${prefix}AC_LOCALE_ID,
        ${alias}.IS_PUBLIC ${prefix}IS_PUBLIC,
        ${alias}.INVITED_BY_USER_ID ${prefix}INVITED_BY_USER_ID,
        ${alias}.ACTIVATION_DATE ${prefix}ACTIVATION_DATE,
        ${alias}.PERSONAL_COMPANY_NAME ${prefix}PERSONAL_COMPANY_NAME,
        ${alias}.CUSTOM_PR_PROPERTY_ID ${prefix}CUSTOM_PR_PROPERTY_ID,
        ${alias}.AC_PERSON_PROFILE_IMG ${prefix}AC_PERSON_PROFILE_IMG
    </sql>
    <select id="findAllPersonsInCompanyNotInWorkgroup"
            resultMap="personResultMap">
        SELECT * FROM (
            SELECT DISTINCT
            <include refid="personColumns">
                <property name="alias" value="P"/>
                <property name="prefix" value=""/>
            </include>
            FROM AC_PERSON P
            , AC_WORKGROUP W
            , AC_ACCOUNT_USER U
            WHERE P.AC_PERSON_ID = U.AC_PERSON_ID
            AND U.AC_WORKGROUP_ID = W.AC_WORKGROUP_ID
            AND U.OC_OBJECT_STATE_ID != 2000120
            AND U.OC_OBJECT_STATE_ID != 2000057
            AND W.AC_COMPANY_ID = #{companyId}
            MINUS
            SELECT DISTINCT
            <include refid="personColumns">
                <property name="alias" value="P"/>
                <property name="prefix" value=""/>
            </include>
            FROM AC_PERSON P
            , AC_WORKGROUP W
            , AC_ACCOUNT_USER U
            WHERE P.AC_PERSON_ID = U.AC_PERSON_ID
            AND U.AC_WORKGROUP_ID = W.AC_WORKGROUP_ID
            AND W.AC_WORKGROUP_ID = #{workgroupId}
            AND W.AC_COMPANY_ID = #{companyId}
        )
        ORDER BY FIRST_NAME, LAST_NAME
    </select>
    
    <select id="findByEmailAddress" resultMap="personResultMap">
        SELECT DISTINCT
        <include refid="personColumns">
            <property name="alias" value="P"/>
            <property name="prefix" value=""/>
        </include>
        FROM AC_PERSON P, AC_PERSON_EMAIL PE, AC_PERSON_SD PSD
        , AC_ACCOUNT_USER AU
        WHERE P.AC_PERSON_ID = PE.AC_PERSON_ID
        AND P.AC_PERSON_ID = PSD.AC_PERSON_ID
        AND TRIM(UPPER(PE.EMAIL_ADDRESS)) = TRIM(UPPER(#{email}))
        AND P.AC_PERSON_ID = AU.AC_PERSON_ID
        AND AU.AC_WORKGROUP_ID != 2
    </select>

</mapper>
