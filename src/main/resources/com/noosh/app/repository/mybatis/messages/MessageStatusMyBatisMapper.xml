<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.messages.MessageStatusMyBatisMapper">

    <resultMap id="messageStatusResultMap" type="com.noosh.app.commons.dto.messages.MessageStatusDTO">
        <id property="id" column="ME_MESSAGE_STATUS_ID"/>
        <result property="messageId" column="ME_MESSAGE_ID"/>
        <result property="sendToUserId" column="SENT_TO_USER_ID"/>
        <result property="objectStateId" column="OC_OBJECT_STATE_ID"/>
    </resultMap>

    <sql id="messageStatusColumns">
        ${alias}.ME_MESSAGE_STATUS_ID ${prefix}ME_MESSAGE_STATUS_ID,
        ${alias}.ME_MESSAGE_ID ${prefix}ME_MESSAGE_ID,
        ${alias}.SENT_TO_USER_ID ${prefix}SENT_TO_USER_ID,
        ${alias}.OC_OBJECT_STATE_ID ${prefix}OC_OBJECT_STATE_ID
    </sql>
</mapper>
