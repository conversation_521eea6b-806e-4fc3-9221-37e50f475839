<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.spec.SpecMyBatisMapper">

    <resultMap id="specResultMap" type="com.noosh.app.commons.dto.spec.SpecDTO">
        <result property="id" column="SP_SPEC_ID"/>
        <result property="name" column="SPEC_NAME"/>
        <result property="spSpecTypeId" column="SP_SPEC_TYPE_ID"/>
        <result property="originalSpecId" column="ORIGINAL_SPEC_ID"/>
        <result property="propertyId" column="PR_PROPERTY_ID"/>
        <result property="isLocked" column="IS_LOCKED"/>
        <result property="includeAup" column="INCLUDE_AUP"/>
        <result property="lockCount" column="LOCK_COUNT"/>
        <result property="isItemVersion" column="IS_ITEM_VERSION"/>
        <result property="versionNumber" column="VERSION_NUMBER"/>
        <result property="specUserStateId" column="SPEC_USER_STATE_ID"/>
        <result property="quantity1" column="QUANTITY1"/>
        <association property="specNode" columnPrefix="SN_" resultMap="com.noosh.app.repository.mybatis.spec.SpecNodeMyBatisMapper.specNodeResultMap"/>
        <association property="specType" columnPrefix="ST_" resultMap="com.noosh.app.repository.mybatis.spec.SpecTypeMyBatisMapper.specTypeResultMap"/>
        <association property="specReference" columnPrefix="SR_" resultMap="com.noosh.app.repository.mybatis.spec.SpecReferenceMyBatisMapper.specReferenceResultMap"/>
    </resultMap>

    <sql id="specColumns">
        ${alias}.SP_SPEC_ID ${prefix}SP_SPEC_ID,
        ${alias}.SPEC_NAME ${prefix}SPEC_NAME,
        ${alias}.SP_SPEC_TYPE_ID ${prefix}SP_SPEC_TYPE_ID,
        ${alias}.CLONE_OF ${prefix}CLONE_OF,
        ${alias}.ORIGINAL_SPEC_ID ${prefix}ORIGINAL_SPEC_ID,
        ${alias}.PR_PROPERTY_ID ${prefix}PR_PROPERTY_ID,
        ${alias}.CONTENTS_MOD_DATE ${prefix}CONTENTS_MOD_DATE,
        ${alias}.SPEC_CREATE_DATE ${prefix}SPEC_CREATE_DATE,
        ${alias}.SPEC_CREATE_USER_ID ${prefix}SPEC_CREATE_USER_ID,
        ${alias}.IS_TEMPLATE ${prefix}IS_TEMPLATE,
        ${alias}.IS_LOCKED ${prefix}IS_LOCKED,
        ${alias}.IS_OBSOLETE ${prefix}IS_OBSOLETE,
        ${alias}.TEMPLATE_SP_SPEC_ID ${prefix}TEMPLATE_SP_SPEC_ID,
        ${alias}.IS_ACTIVE ${prefix}IS_ACTIVE,
        ${alias}.INCLUDE_AUP ${prefix}INCLUDE_AUP,
        ${alias}.SP_SPEC_REFERENCE_ID ${prefix}SP_SPEC_REFERENCE_ID,
        ${alias}.IS_PSF_SPEC_TEMPLATE ${prefix}IS_PSF_SPEC_TEMPLATE,
        ${alias}.AC_ATTR_PRODUCT_TYPE_ID ${prefix}AC_ATTR_PRODUCT_TYPE_ID,
        ${alias}.AC_ATTR_UNSPSC_TYPE_ID ${prefix}AC_ATTR_UNSPSC_TYPE_ID,
        ${alias}.QUANTITY1 ${prefix}QUANTITY1,
        ${alias}.QUANTITY2 ${prefix}QUANTITY2,
        ${alias}.QUANTITY3 ${prefix}QUANTITY3,
        ${alias}.QUANTITY4 ${prefix}QUANTITY4,
        ${alias}.QUANTITY5 ${prefix}QUANTITY5,
        ${alias}.LOCK_COUNT ${prefix}LOCK_COUNT,
        ${alias}.SPEC_WEIGHT ${prefix}SPEC_WEIGHT,
        ${alias}.IS_ITEM_VERSION ${prefix}IS_ITEM_VERSION,
        ${alias}.IS_CURRENT_VERSION ${prefix}IS_CURRENT_VERSION,
        ${alias}.VERSION_NUMBER ${prefix}VERSION_NUMBER,
        ${alias}.CUSTOM1 ${prefix}CUSTOM1,
        ${alias}.REVISION_REASON ${prefix}REVISION_REASON,
        ${alias}.OC_OBJECT_STATE_ID ${prefix}OC_OBJECT_STATE_ID,
        ${alias}.SPEC_USER_STATE_ID ${prefix}SPEC_USER_STATE_ID,
        ${alias}.NUMBER_OF_KINDS ${prefix}NUMBER_OF_KINDS,
        ${alias}.DEFAULT_SPEC_TYPE_ID ${prefix}DEFAULT_SPEC_TYPE_ID,
        ${alias}.AC_SOURCE_TYPE_ID ${prefix}AC_SOURCE_TYPE_ID
    </sql>

    <select id="findSpecsBySpecRefId" resultMap="specResultMap">
        SELECT
        <include refid="specColumns">
            <property name="alias" value="S"/>
            <property name="prefix" value=""/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.spec.SpecNodeMyBatisMapper.specNodeColumns">
            <property name="alias" value="SN"/>
            <property name="prefix" value="SN_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.spec.SpecTypeMyBatisMapper.specTypeColumns">
            <property name="alias" value="ST"/>
            <property name="prefix" value="ST_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.spec.SpecReferenceMyBatisMapper.specReferenceColumns">
            <property name="alias" value="SR"/>
            <property name="prefix" value="SR_"/>
        </include>
        FROM SP_SPEC_REFERENCE SR
        , SP_SPEC S
        , SP_SPEC_TYPE ST
        , SP_SPEC_NODE SN
        , SP_SPEC_TREE TREE
        WHERE SR.SP_SPEC_REFERENCE_ID = #{specRefId}
        AND SR.SP_SPEC_REFERENCE_ID = S.SP_SPEC_REFERENCE_ID
        AND S.SP_SPEC_ID = SN.SP_SPEC_ID
        AND SN.SP_SPEC_TREE_ID = TREE.SP_SPEC_TREE_ID
        AND TREE.IS_HIDDEN = 0
        AND S.SP_SPEC_TYPE_ID = ST.SP_SPEC_TYPE_ID
        AND EXISTS (SELECT 'x' FROM SY_CONTAINABLE C
        WHERE C.OBJECT_ID = S.SP_SPEC_ID
        AND C.OBJECT_CLASS_ID = 1000114
        AND C.PARENT_OBJECT_ID = #{parentObjectId}
        AND C.PARENT_OBJECT_CLASS_ID = 1000000
        )
        ORDER BY SR.SP_SPEC_REFERENCE_ID,S.SP_SPEC_ID
    </select>

    <select id="findTotalSpecReferenceCountByProjectIds" resultType="java.lang.Integer">
        SELECT COUNT(*) SR_COUNT
        FROM (
        SELECT DISTINCT
        SR.SP_SPEC_REFERENCE_ID
        , C.PARENT_OBJECT_ID
        FROM SP_SPEC_REFERENCE SR
        , SP_SPEC S
        , SP_SPEC_TYPE ST
        , SY_CONTAINABLE C
        , SP_SPEC_NODE N
        , SP_SPEC_TREE TREE
        WHERE
        SR.SP_SPEC_REFERENCE_ID = S.SP_SPEC_REFERENCE_ID
        AND S.SP_SPEC_ID = N.SP_SPEC_ID
        AND N.SP_SPEC_TREE_ID = TREE.SP_SPEC_TREE_ID
        AND TREE.IS_HIDDEN = 0
        AND S.SP_SPEC_TYPE_ID = ST.SP_SPEC_TYPE_ID
        AND ST.IS_VISIBLE = 1
        AND C.OBJECT_ID = S.SP_SPEC_ID
        AND C.OBJECT_CLASS_ID = 1000114
        AND C.PARENT_OBJECT_ID in
        <foreach item="item" index="index" collection="projectIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND C.PARENT_OBJECT_CLASS_ID = 1000000
        )
    </select>
</mapper>