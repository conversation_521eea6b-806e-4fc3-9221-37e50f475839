<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.spec.SpecMyBatisMapper">

    <resultMap id="specReferenceListResultMap" type="com.noosh.app.commons.dto.spec.SpecWithJobDTO">
        <result property = "specReferenceId" column = "SP_SPEC_REFERENCE_ID"/>
        <result property = "specReferenceCustomPropertyId" column = "CUSTOM_PR_PROPERTY_ID"/>
        <result property = "preferredSpecId" column = "PREFERRED_SP_SPEC_ID"/>
        <result property = "specReferenceName" column = "referenceName" />
        <result property = "iconHtml" column = "ICON"/>
        <result property = "refNumber" column = "REF_NUMBER"/>
        <result property = "isMaster" column = "IS_MASTER"/>
        <result property = "specTypeName" column = "LABEL_STR"/>
        <association property="specType" column="SP_SPEC_TYPE_ID" resultMap="specTypeResultMap" />
        <collection property="jobDTOs" ofType="com.noosh.app.commons.dto.spec.JobDTO" resultMap="jobResultMap" javaType="ArrayList" />
        <collection property="specDetailList" column="SP_SPEC_ID" ofType="com.noosh.app.commons.dto.spec.SpecDTO" javaType="ArrayList" resultMap="specListResultMap"/>
    </resultMap>

    <resultMap id="specListResultMap" type="com.noosh.app.commons.dto.spec.SpecDTO">
        <result property = "id" column = "SP_SPEC_ID"/>
        <result property = "name" column = "SPEC_NAME"/>
        <result property = "originalSpecId" column = "ORIGINAL_SPEC_ID"/>
        <result property = "isLocked" column = "IS_LOCKED"/>
        <result property = "isItemVersion" column = "IS_ITEM_VERSION"/>
        <result property = "nodeId" column = "SP_SPEC_NODE_ID"/>
        <result property = "specUserStateId" column = "SPEC_USER_STATE_ID"/>
        <result property = "createDate" column = "SPEC_CREATE_DATE" typeHandler = "com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property = "modDate" column = "CONTENTS_MOD_DATE" typeHandler = "com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property = "createUserId" column = "CREATE_USER_ID"/>
        <result property = "creator" column = "createUserFullName"/>
        <result property = "creatorAliasName" column = "createUserAliasName"/>
        <result property = "lockCount" column = "LOCK_COUNT"/>
        <result property = "versionNumber" column = "VERSION_NUMBER"/>
        <result property = "quantity1" column = "QUANTITY1"/>
        <result property = "quantity2" column = "QUANTITY2"/>
        <result property = "quantity3" column = "QUANTITY3"/>
        <result property = "quantity4" column = "QUANTITY4"/>
        <result property = "quantity5" column = "QUANTITY5"/>
        <result property = "propertyId" column = "PR_PROPERTY_ID"/>
        <result property = "specTypeId" column = "SP_SPEC_TYPE_ID"/>
        <result property = "productTypeId" column = "AC_ATTR_PRODUCT_TYPE_ID"/>
    </resultMap>

    <resultMap id="jobResultMap" type="com.noosh.app.commons.dto.spec.JobDTO">
        <result property = "jobId" column = "PC_JOB_ID"/>
        <result property = "jobUserStateId" column = "jobUserStateId"/>
        <result property = "currentJobSpecId" column = "currentSpecId"/>
    </resultMap>

    <resultMap id="jobStatusResultMap" type="com.noosh.app.commons.dto.spec.JobStatusDTO">
        <result property = "pcJobStatusId" column = "PC_JOB_STATUS_ID"/>
        <result property = "pcJobId" column = "PC_JOB_ID"/>
        <result property = "pmProjectId" column = "PM_PROJECT_ID"/>
        <result property = "buClientWorkgroupId" column = "BU_CLIENT_WORKGROUP_ID"/>
        <result property = "buyerAcWorkgroupId" column = "BUYER_AC_WORKGROUP_ID"/>
        <result property = "ownerAcWorkgroupId" column = "OWNER_AC_WORKGROUP_ID"/>
        <result property = "ocObjectStateId" column = "OC_OBJECT_STATE_ID"/>
        <result property = "rfeCount" column = "RFE_COUNT"/>
        <result property = "estimateCount" column = "ESTIMATE_COUNT"/>
        <result property = "rfqCount" column = "RFQ_COUNT"/>
        <result property = "quoteCount" column = "QUOTE_COUNT"/>
        <result property = "prCount" column = "PR_COUNT"/>
        <result property = "orderPendingCount" column = "ORDER_PENDING_COUNT"/>
        <result property = "orderAcceptedCount" column = "ORDER_ACCEPTED_COUNT"/>
        <result property = "userStateId" column = "USER_STATE_ID"/>
        <result property = "totalOrderCount" column = "TOTAL_ORDER_COUNTER"/>
        <result property = "orderCompletedCount" column = "ORDER_COMPLETED_COUNT"/>
    </resultMap>

    <resultMap id="specTypeResultMap" type="com.noosh.app.commons.dto.spec.SpecTypeDTO">
        <result property="id" column="SP_SPEC_TYPE_ID"/>
        <result property="labelStrId" column="LABEL_STR_ID"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="coServiceId" column="CO_SERVICE_ID"/>
        <result property="prPropertyTypeId" column="PR_PROPERTY_TYPE_ID"/>
        <result property="templateBaseName" column="TEMPLATE_BASE_NAME"/>
        <result property="isDeprecated" column="IS_DEPRECATED"/>
        <result property="deprecationReason" column="DEPRECATION_REASON"/>
        <result property="deprecationDate" column="DEPRECATION_DATE" typeHandler = "com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="replacedBySpecTypeId" column="REPLACED_BY_SPEC_TYPE_ID"/>
        <result property="labelStr" column="LABEL_STR"/>
        <result property="icon" column="ICON"/>
        <result property="isDirectMail" column="IS_DIRECT_MAIL"/>
        <result property="isVisible" column="IS_VISIBLE"/>
        <result property="image" column="IMAGE"/>
        <result property="isImEnabled" column="IS_IM_ENABLED"/>
        <result property="isCampaign" column="IS_CAMPAIGN"/>
        <result property="acCustomFormId" column="AC_CUSTOM_FORM_ID"/>
        <result property="originalSpecTypeId" column="ORIGINAL_SPEC_TYPE_ID"/>
        <result property="parentSpecTypeId" column="PARENT_SPEC_TYPE_ID"/>
        <result property="versionNumber" column="VERSION_NUMBER_ST"/>
        <result property="isCurrent" column="IS_CURRENT"/>
        <result property="iconUrl" column="ICON_URL"/>
        <result property="sourceTypeId" column="AC_SOURCE_TYPE_ID"/>
        <result property="coServiceDescStrId" column="CO_SERVICE_DESCRIPTION_STR_ID"/>
        <result property="isNlp" column="IS_NLP"/>
    </resultMap>

    <resultMap id="specTemplateResultMap" type="com.noosh.app.commons.dto.spec.SpecTemplateDTO">
        <result property = "id" column = "SP_SPEC_ID"/>
        <result property = "name" column = "SPEC_NAME"/>
        <result property = "specTypeId" column = "SP_SPEC_TYPE_ID"/>
    </resultMap>

    <resultMap id="simpleSpecResultMap" type="com.noosh.app.commons.dto.component.DropdownDTO">
        <result property = "id" column = "SP_SPEC_ID"/>
        <result property = "label" column = "SPEC_NAME"/>
    </resultMap>

    <resultMap id="specProcurementItemPriceMap" type="com.noosh.app.commons.dto.spec.SpecProcurementItemPriceDTO">
        <result property="specId" column="SPEC_ID"/>
        <result property="price" column="PRICE"/>
        <result property="quantity" column="QUANTITY"/>
        <result property="priceCurrencyId" column="PRICE_AC_CURRENCY_ID"/>
        <result property="supplierWorkgroupName" column="SUPPLIERWORKGROUPNAME"/>
    </resultMap>
	
    <resultMap id="specInvoiceMap" type="com.noosh.app.commons.dto.spec.SpecInvoiceDTO">
        <result property="specId" column="SP_SPEC_ID"/>
        <result property="stateStrId" column="DESCRIPTION_STR_ID"/>
        <result property="isFinal" column="IS_FINAL"/>
    </resultMap>

    <resultMap id="specOrderMap" type="com.noosh.app.commons.dto.spec.SpecOrderDTO">
        <result property="orderId" column="OR_ORDER_ID"/>
        <result property="orderCount" column="orderCount"/>
    </resultMap>

    <resultMap id="quantityMap" type="com.noosh.app.commons.vo.spec.QuantityVO">
        <result property = "quantityIndex" column = "OPTION_INDEX"/>
        <result property = "quantity" column = "VALUE"/>
        <result property = "price" column = "PRICE"/>
        <result property = "estimateId" column = "EM_ESTIMATE_ID"/>
        <result property = "estimateItemPriceId" column = "EM_ESTIMATE_ITEM_PRICE_ID"/>
    </resultMap>

    <resultMap id="specWithProjectMap" type="com.noosh.app.commons.dto.spec.SpecWithProjectDTO">
        <result property = "projectId" column = "PRJ_PM_PROJECT_ID"/>
        <result property = "projectReference" column = "PRJ_REFERENCE"/>
        <result property = "projectName" column = "PRJ_NAME"/>
        <result property = "projectNumber" column = "PRJ_PROJECT_NUMBER"/>
        <result property = "specId" column = "S_SP_SPEC_ID"/>
        <result property = "specName" column = "S_SPEC_NAME"/>
        <result property = "specTypeId" column = "S_SP_SPEC_TYPE_ID"/>
        <result property = "specReferenceNumber" column = "SR_REF_NUMBER"/>
        <result property = "specNodeId" column = "SN_SP_SPEC_NODE_ID"/>
        <result property = "iconPath" column = "ST_SP_SPEC_TYPE_ICON"/>
    </resultMap>

    <resultMap id="specTypeResultMapForStaples" type="com.noosh.app.commons.dto.spec.SpecTypeDTO">
        <result property="id" column="SP_SPEC_TYPE_ID"/>
        <result property="labelStrId" column="LABEL_STR_ID"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="coServiceId" column="CO_SERVICE_ID"/>
        <result property="prPropertyTypeId" column="PR_PROPERTY_TYPE_ID"/>
        <result property="templateBaseName" column="TEMPLATE_BASE_NAME"/>
        <result property="isDeprecated" column="IS_DEPRECATED"/>
        <result property="deprecationReason" column="DEPRECATION_REASON"/>
        <result property="deprecationDate" column="DEPRECATION_DATE" typeHandler = "com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="replacedBySpecTypeId" column="REPLACED_BY_SPEC_TYPE_ID"/>
        <result property="labelStr" column="LABEL_STR"/>
        <result property="icon" column="ICON"/>
        <result property="isDirectMail" column="IS_DIRECT_MAIL"/>
        <result property="isVisible" column="IS_VISIBLE"/>
        <result property="image" column="IMAGE"/>
        <result property="isImEnabled" column="IS_IM_ENABLED"/>
        <result property="isCampaign" column="IS_CAMPAIGN"/>
        <result property="acCustomFormId" column="AC_CUSTOM_FORM_ID"/>
        <result property="originalSpecTypeId" column="ORIGINAL_SPEC_TYPE_ID"/>
        <result property="parentSpecTypeId" column="PARENT_SPEC_TYPE_ID"/>
        <result property="versionNumber" column="VERSION_NUMBER_ST"/>
        <result property="isCurrent" column="IS_CURRENT"/>
        <result property="iconUrl" column="ICON_URL"/>
        <result property="sourceTypeId" column="AC_SOURCE_TYPE_ID"/>
        <result property="coServiceDescStrId" column="CO_SERVICE_DESCRIPTION_STR_ID"/>
        <result property="isNlp" column="IS_NLP"/>
        <result property="categoryName" column="CATEGORY_NAME"/>
    </resultMap>

    <select id="getSpecReferenceIdsOnly" resultType="java.lang.Long">

        select distinct SP_SPEC_REFERENCE_ID, referenceName from (
            select
            a.*,
            ap.first_name || ' ' || ap.last_name createUserFullName,
            decode(ap.alias_name, null, ap.first_name || ' ' || ap.last_name, ap.alias_name) createUserAliasName,
            pj.PC_JOB_ID,
            pj.OC_OBJECT_STATE_ID as jobUserStateId,
            pj.CURRENT_SP_SPEC_ID as  currentSpecId

            from (select sf.SP_SPEC_REFERENCE_ID,
                    sf.PREFERRED_SP_SPEC_ID,
                    sf.CUSTOM_PR_PROPERTY_ID,
                    sf.SPEC_NAME as referenceName,
                    s.SP_SPEC_ID,
                    s.SPEC_NAME,
                    s.ORIGINAL_SPEC_ID,
                    s.IS_LOCKED,
                    s.IS_ITEM_VERSION,
                    sn.SP_SPEC_NODE_ID,
                    s.SPEC_USER_STATE_ID,
                    s.SPEC_CREATE_DATE,
                    case when s.CONTENTS_MOD_DATE is null then s.SPEC_CREATE_DATE else s.CONTENTS_MOD_DATE end CONTENTS_MOD_DATE,
                    s.CREATE_USER_ID,
                    s.LOCK_COUNT,
                    sf.REF_NUMBER,
                    sf.SKU,
                    st.ICON,
                    sf.IS_MASTER,
                    st.LABEL_STR,
                    st.CO_SERVICE_ID,
                    s.VERSION_NUMBER,
                    s.PR_PROPERTY_ID,
                    s.QUANTITY1,
                    s.QUANTITY2,
                    s.QUANTITY3,
                    s.QUANTITY4,
                    s.QUANTITY5
                from sp_spec s, sp_spec_node sn, sy_containable sc, sp_spec_reference sf, sp_spec_type st
                where sc.OBJECT_CLASS_ID = 1000114
                and sc.PARENT_OBJECT_CLASS_ID = 1000000
                and sn.SP_SPEC_ID = s.SP_SPEC_ID
                and sc.PARENT_OBJECT_ID = #{projectId}
                and sc.OBJECT_ID = s.SP_SPEC_ID
                and sf.sp_spec_type_id = st.sp_spec_type_id
                and s.SP_SPEC_REFERENCE_ID = sf.SP_SPEC_REFERENCE_ID
                <if test="specTypeId != null">
                    and st.SP_SPEC_TYPE_ID = #{specTypeId}
                </if>
                order by sf.SP_SPEC_REFERENCE_ID, s.SP_SPEC_ID desc) a

            left join (select pc.*
            from pc_job pc, sy_containable sc
            where sc.parent_object_id = #{projectId}
            and sc.parent_object_class_id = 1000000
            and sc.object_class_id = 1000020
            and sc.object_id = pc.pc_job_id) pj

            on pj.SP_SPEC_REFERENCE_ID = a.SP_SPEC_REFERENCE_ID

            left join ac_account_user au
            on au.user_id = a.CREATE_USER_ID
            left join ac_person ap
            on ap.ac_person_id = au.ac_person_id

            where 1 = 1
            <if test="searchString != null">
                and (
                upper(a.SPEC_NAME) like '%' || #{searchString} || '%' ESCAPE '\'
                or upper(a.REF_NUMBER) like '%' || #{searchString} || '%' ESCAPE '\'
                or upper(a.SKU) like '%' || #{searchString} || '%' ESCAPE '\'
                or pj.PC_JOB_ID like '%' || #{searchNum} || '%' ESCAPE '\'
                )
            </if>

            <if test="createStartDate != null">
                and a.SPEC_CREATE_DATE &gt;=to_date(#{createStartDate}, 'MM/DD/YYYY HH24:MI:SS')
            </if>
            <if test="createEndDate != null">
                and a.SPEC_CREATE_DATE &lt;=to_date(#{createEndDate}, 'MM/DD/YYYY HH24:MI:SS')
            </if>
            <if test="lastUpdateStartDate != null">
                and nvl(a.CONTENTS_MOD_DATE, a.SPEC_CREATE_DATE) &gt;=to_date(#{lastUpdateStartDate}, 'MM/DD/YYYY HH24:MI:SS')
            </if>
            <if test="lastUpdateEndDate != null">
                and nvl(a.CONTENTS_MOD_DATE, a.SPEC_CREATE_DATE) &lt;=to_date(#{lastUpdateEndDate}, 'MM/DD/YYYY HH24:MI:SS')
            </if>

            <if test="createUserId != null and createUserId.contains(-1L) == false">
                and a.CREATE_USER_ID in
                <foreach item="item" index="index" collection="createUserId" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="stateId != null and stateId == 2000130">
                and (pj.OC_OBJECT_STATE_ID = #{stateId} OR pj.PC_JOB_ID IS NULL)
            </if>
            <if test="stateId != null and stateId == 2000131">
                and pj.OC_OBJECT_STATE_ID = #{stateId}
            </if>
            <if test="orderBy != null">
                order by ${orderBy}
            </if>

        )

    </select>

    <select id="getAllSpecsWithJobByReferenceIds" resultMap="specReferenceListResultMap">
        select
        a.*,
        ap.first_name || ' ' || ap.last_name createUserFullName,
        decode(ap.alias_name, null, ap.first_name || ' ' || ap.last_name, ap.alias_name) createUserAliasName,
        pj.PC_JOB_ID,
        pj.OC_OBJECT_STATE_ID as jobUserStateId,
        pj.CURRENT_SP_SPEC_ID as  currentSpecId

        from (select sf.SP_SPEC_REFERENCE_ID,
                sf.PREFERRED_SP_SPEC_ID,
                sf.CUSTOM_PR_PROPERTY_ID,
                sf.SPEC_NAME as referenceName,
                s.SP_SPEC_ID,
                s.SPEC_NAME,
                s.ORIGINAL_SPEC_ID,
                s.IS_LOCKED,
                s.IS_ITEM_VERSION,
                sn.SP_SPEC_NODE_ID,
                s.SPEC_USER_STATE_ID,
                s.SPEC_CREATE_DATE,
                case when s.CONTENTS_MOD_DATE is null then s.SPEC_CREATE_DATE else s.CONTENTS_MOD_DATE end CONTENTS_MOD_DATE,
                s.CREATE_USER_ID,
                s.LOCK_COUNT,
                sf.REF_NUMBER,
                sf.SKU,
                st.ICON,
                sf.IS_MASTER,
                st.LABEL_STR,
                st.CO_SERVICE_ID,
                s.VERSION_NUMBER,
                s.PR_PROPERTY_ID,
                s.QUANTITY1,
                s.QUANTITY2,
                s.QUANTITY3,
                s.QUANTITY4,
                s.QUANTITY5
            from sp_spec s, sp_spec_node sn, sy_containable sc, sp_spec_reference sf, sp_spec_type st
            where sc.OBJECT_CLASS_ID = 1000114
            and sc.PARENT_OBJECT_CLASS_ID = 1000000
            and sn.SP_SPEC_ID = s.SP_SPEC_ID
            and sc.PARENT_OBJECT_ID = #{projectId}
            and sc.OBJECT_ID = s.SP_SPEC_ID
            and sf.sp_spec_type_id = st.sp_spec_type_id
            and s.SP_SPEC_REFERENCE_ID = sf.SP_SPEC_REFERENCE_ID
            <if test="specTypeId != null">
                and st.SP_SPEC_TYPE_ID = #{specTypeId}
            </if>
            and sf.SP_SPEC_REFERENCE_ID in
            <foreach item="item" index="index" collection="specReferenceIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            order by sf.SP_SPEC_REFERENCE_ID, s.SP_SPEC_ID desc) a

        left join (select pc.*
        from pc_job pc, sy_containable sc
        where sc.parent_object_id = #{projectId}
        and sc.parent_object_class_id = 1000000
        and sc.object_class_id = 1000020
        and sc.object_id = pc.pc_job_id) pj

        on pj.SP_SPEC_REFERENCE_ID = a.SP_SPEC_REFERENCE_ID

        left join ac_account_user au
        on au.user_id = a.CREATE_USER_ID
        left join ac_person ap
        on ap.ac_person_id = au.ac_person_id

        where 1 = 1
        <if test="searchString != null">
            and (
            upper(a.SPEC_NAME) like '%' || #{searchString} || '%' ESCAPE '\'
            or upper(a.REF_NUMBER) like '%' || #{searchString} || '%' ESCAPE '\'
            or upper(a.SKU) like '%' || #{searchString} || '%' ESCAPE '\'
            or pj.PC_JOB_ID like '%' || #{searchNum} || '%' ESCAPE '\'
            )
        </if>

        <if test="createStartDate != null">
            and a.SPEC_CREATE_DATE &gt;=to_date(#{createStartDate}, 'MM/DD/YYYY HH24:MI:SS')
        </if>
        <if test="createEndDate != null">
            and a.SPEC_CREATE_DATE &lt;=to_date(#{createEndDate}, 'MM/DD/YYYY HH24:MI:SS')
        </if>
        <if test="lastUpdateStartDate != null">
            and nvl(a.CONTENTS_MOD_DATE, a.SPEC_CREATE_DATE) &gt;=to_date(#{lastUpdateStartDate}, 'MM/DD/YYYY HH24:MI:SS')
        </if>
        <if test="lastUpdateEndDate != null">
            and nvl(a.CONTENTS_MOD_DATE, a.SPEC_CREATE_DATE) &lt;=to_date(#{lastUpdateEndDate}, 'MM/DD/YYYY HH24:MI:SS')
        </if>

        <if test="createUserId != null and createUserId.contains(-1L) == false">
            and a.CREATE_USER_ID in
            <foreach item="item" index="index" collection="createUserId" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="stateId != null and stateId == 2000130">
            and (pj.OC_OBJECT_STATE_ID = #{stateId} OR pj.PC_JOB_ID IS NULL)
        </if>
        <if test="stateId != null and stateId == 2000131">
            and pj.OC_OBJECT_STATE_ID = #{stateId}
        </if>
        <if test="orderBy != null">
            order by ${orderBy}
        </if>
    </select>

    <select id="getAllSpecsWithJob" resultMap="specReferenceListResultMap">
        select
        a.*,
        ap.first_name || ' ' || ap.last_name createUserFullName,
        decode(ap.alias_name, null, ap.first_name || ' ' || ap.last_name, ap.alias_name) createUserAliasName,
        pj.PC_JOB_ID,
        pj.OC_OBJECT_STATE_ID as jobUserStateId,
        pj.CURRENT_SP_SPEC_ID as  currentSpecId

        from (select sf.SP_SPEC_REFERENCE_ID,
                sf.PREFERRED_SP_SPEC_ID,
                sf.CUSTOM_PR_PROPERTY_ID,
                sf.SPEC_NAME as referenceName,
                s.SP_SPEC_ID,
                s.SPEC_NAME,
                s.ORIGINAL_SPEC_ID,
                s.IS_LOCKED,
                s.IS_ITEM_VERSION,
                sn.SP_SPEC_NODE_ID,
                s.SPEC_USER_STATE_ID,
                s.SPEC_CREATE_DATE,
                case when s.CONTENTS_MOD_DATE is null then s.SPEC_CREATE_DATE else s.CONTENTS_MOD_DATE end CONTENTS_MOD_DATE,
                s.CREATE_USER_ID,
                s.LOCK_COUNT,
                sf.REF_NUMBER,
                sf.SKU,
                st.ICON,
                sf.IS_MASTER,
                st.LABEL_STR,
                st.CO_SERVICE_ID,
                s.VERSION_NUMBER,
                s.PR_PROPERTY_ID,
                s.QUANTITY1,
                s.QUANTITY2,
                s.QUANTITY3,
                s.QUANTITY4,
                s.QUANTITY5
            from sp_spec s, sp_spec_node sn, sy_containable sc, sp_spec_reference sf, sp_spec_type st
            where sc.OBJECT_CLASS_ID = 1000114
            and sc.PARENT_OBJECT_CLASS_ID = 1000000
            and sn.SP_SPEC_ID = s.SP_SPEC_ID
            and sc.PARENT_OBJECT_ID = #{projectId}
            and sc.OBJECT_ID = s.SP_SPEC_ID
            and sf.sp_spec_type_id = st.sp_spec_type_id
            and s.SP_SPEC_REFERENCE_ID = sf.SP_SPEC_REFERENCE_ID
            <if test="specTypeId != null">
                and st.SP_SPEC_TYPE_ID = #{specTypeId}
            </if>
            order by sf.SP_SPEC_REFERENCE_ID, s.SP_SPEC_ID desc) a

        left join (select pc.*
        from pc_job pc, sy_containable sc
        where sc.parent_object_id = #{projectId}
        and sc.parent_object_class_id = 1000000
        and sc.object_class_id = 1000020
        and sc.object_id = pc.pc_job_id) pj

        on pj.SP_SPEC_REFERENCE_ID = a.SP_SPEC_REFERENCE_ID

        left join ac_account_user au
        on au.user_id = a.CREATE_USER_ID
        left join ac_person ap
        on ap.ac_person_id = au.ac_person_id

        where 1 = 1
        <if test="searchString != null">
            and (
            upper(a.SPEC_NAME) like '%' || #{searchString} || '%' ESCAPE '\'
            or upper(a.REF_NUMBER) like '%' || #{searchString} || '%' ESCAPE '\'
            or upper(a.SKU) like '%' || #{searchString} || '%' ESCAPE '\'
            or pj.PC_JOB_ID like '%' || #{searchNum} || '%' ESCAPE '\'
            )
        </if>

        <if test="createStartDate != null">
            and a.SPEC_CREATE_DATE &gt;=to_date(#{createStartDate}, 'MM/DD/YYYY HH24:MI:SS')
        </if>
        <if test="createEndDate != null">
            and a.SPEC_CREATE_DATE &lt;=to_date(#{createEndDate}, 'MM/DD/YYYY HH24:MI:SS')
        </if>
        <if test="lastUpdateStartDate != null">
            and nvl(a.CONTENTS_MOD_DATE, a.SPEC_CREATE_DATE) &gt;=to_date(#{lastUpdateStartDate}, 'MM/DD/YYYY HH24:MI:SS')
        </if>
        <if test="lastUpdateEndDate != null">
            and nvl(a.CONTENTS_MOD_DATE, a.SPEC_CREATE_DATE) &lt;=to_date(#{lastUpdateEndDate}, 'MM/DD/YYYY HH24:MI:SS')
        </if>

        <if test="createUserId != null and createUserId.contains(-1L) == false">
            and a.CREATE_USER_ID in
            <foreach item="item" index="index" collection="createUserId" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="stateId != null and stateId == 2000130">
            and (pj.OC_OBJECT_STATE_ID = #{stateId} OR pj.PC_JOB_ID IS NULL)
        </if>
        <if test="stateId != null and stateId == 2000131">
            and pj.OC_OBJECT_STATE_ID = #{stateId}
        </if>

    </select>

    <select id="getAllJobStatusByIdAndWorkgroupId" resultMap="jobStatusResultMap">
        SELECT
        JS.PC_JOB_STATUS_ID,
        JS.PC_JOB_ID,
        JS.PM_PROJECT_ID,
        JS.BU_CLIENT_WORKGROUP_ID,
        JS.BUYER_AC_WORKGROUP_ID,
        JS.OWNER_AC_WORKGROUP_ID,
        JS.OC_OBJECT_STATE_ID,
        JS.RFE_COUNT,
        JS.ESTIMATE_COUNT,
        JS.RFQ_COUNT,
        JS.QUOTE_COUNT,
        JS.PR_COUNT,
        JS.ORDER_PENDING_COUNT,
        JS.ORDER_ACCEPTED_COUNT,
        JS.ORDER_COMPLETED_COUNT,
        DECODE(OC_OBJECT_STATE_ID, 2000024,(SELECT DISTINCT OC_OBJECT_STATE_ID FROM SH_SHIPMENT SH WHERE SH.PC_JOB_ID = JS.PC_JOB_ID), NULL) AS USER_STATE_ID ,
        (
        SELECT
        SUM(JS2.ORDER_PENDING_COUNT + JS2.ORDER_ACCEPTED_COUNT + JS2.ORDER_COMPLETED_COUNT)
        FROM
        PC_JOB_STATUS JS2
        WHERE
        JS2.PC_JOB_ID = JS.PC_JOB_ID
        AND JS2.BUYER_AC_WORKGROUP_ID = JS.BUYER_AC_WORKGROUP_ID
        AND JS2.BU_CLIENT_WORKGROUP_ID IS NULL) AS TOTAL_ORDER_COUNTER
        FROM
        PC_JOB_STATUS JS
        WHERE
        JS.PC_JOB_ID =#{jobId}
        AND JS.OWNER_AC_WORKGROUP_ID = #{workgroupId}
    </select>

    <select id="getAllJobStatusByIdsAndWorkgroupId" resultMap="jobStatusResultMap">
        SELECT
        JS.PC_JOB_STATUS_ID,
        JS.PC_JOB_ID,
        JS.PM_PROJECT_ID,
        JS.BU_CLIENT_WORKGROUP_ID,
        JS.BUYER_AC_WORKGROUP_ID,
        JS.OWNER_AC_WORKGROUP_ID,
        JS.OC_OBJECT_STATE_ID,
        JS.RFE_COUNT,
        JS.ESTIMATE_COUNT,
        JS.RFQ_COUNT,
        JS.QUOTE_COUNT,
        JS.PR_COUNT,
        JS.ORDER_PENDING_COUNT,
        JS.ORDER_ACCEPTED_COUNT,
        JS.ORDER_COMPLETED_COUNT,
        DECODE(OC_OBJECT_STATE_ID, 2000024,(SELECT DISTINCT OC_OBJECT_STATE_ID FROM SH_SHIPMENT SH WHERE SH.PC_JOB_ID = JS.PC_JOB_ID), NULL) AS USER_STATE_ID ,
        (
        SELECT
        SUM(JS2.ORDER_PENDING_COUNT + JS2.ORDER_ACCEPTED_COUNT + JS2.ORDER_COMPLETED_COUNT)
        FROM
        PC_JOB_STATUS JS2
        WHERE
        JS2.PC_JOB_ID = JS.PC_JOB_ID
        AND JS2.BUYER_AC_WORKGROUP_ID = JS.BUYER_AC_WORKGROUP_ID
        AND JS2.BU_CLIENT_WORKGROUP_ID IS NULL) AS TOTAL_ORDER_COUNTER
        FROM
        PC_JOB_STATUS JS
        WHERE
        JS.PC_JOB_ID IN
        <foreach item="item" index="index" collection="jobIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND JS.OWNER_AC_WORKGROUP_ID = #{workgroupId}
    </select>

    <select id="findTypesByWorkgroupId" resultMap="specTypeResultMap">
        select S.*, SER.DESCRIPTION_STR_ID as CO_SERVICE_DESCRIPTION_STR_ID
        from 
        (
            select
            distinct T.SP_SPEC_TYPE_ID, T.LABEL_STR_ID, T.DESCRIPTION, T.CREATE_DATE, T.MOD_DATE, T.CREATE_USER_ID,
            T.MOD_USER_ID, T.CO_SERVICE_ID, T.PR_PROPERTY_TYPE_ID, T.TEMPLATE_BASE_NAME, T.IS_DEPRECATED, T.DEPRECATION_REASON,
            T.DEPRECATION_DATE, T.REPLACED_BY_SPEC_TYPE_ID, T.LABEL_STR, T.ICON, T.IS_DIRECT_MAIL, T.IS_VISIBLE, T.IMAGE,
            T.IS_IM_ENABLED, T.IS_CAMPAIGN, T.AC_CUSTOM_FORM_ID, T.ORIGINAL_SPEC_TYPE_ID, T.PARENT_SPEC_TYPE_ID,
            T.VERSION_NUMBER as VERSION_NUMBER_ST, T.IS_CURRENT, T.ICON_URL, T.AC_SOURCE_TYPE_ID, T.IS_INVOICE_ADJUSTMENT, T.IS_TIME_MATERIALS, T.IS_NLP
            from
            SP_SPEC_TYPE T,
            SP_SPEC_REGISTRATION R
            where
            T.IS_DEPRECATED = 0 OR T.IS_DEPRECATED is null
            AND R.SP_SPEC_TYPE_ID = T.SP_SPEC_TYPE_ID
            AND R.AC_WORKGROUP_ID = #{workgroupId}
            AND T.IS_VISIBLE = 1
        ) S
        left join CO_SERVICE SER on S.CO_SERVICE_ID = SER.CO_SERVICE_ID
        order by S.CO_SERVICE_ID asc
    </select>

    <select id="findTemplateByWorkgroupId" resultMap="specTemplateResultMap">
        select S.* from sy_containable C, sp_spec S, sp_spec_type T, ac_account_user A, ac_Person P where
        C.OBJECT_ID = S.SP_SPEC_ID AND (S.IS_OBSOLETE is null or
        S.IS_OBSOLETE != 1) AND S.IS_ACTIVE = 1
        AND S.IS_TEMPLATE = 1 AND  C.OBJECT_CLASS_ID = 1000114
        AND T.SP_SPEC_TYPE_ID = S.SP_SPEC_TYPE_ID AND C.PARENT_OBJECT_ID = #{workgroupId}
        AND  C.PARENT_OBJECT_CLASS_ID = 1000100 AND A.USER_ID = S.SPEC_CREATE_USER_ID
        AND A.AC_PERSON_ID = P.AC_PERSON_ID AND T.IS_VISIBLE =1 order by S.SPEC_NAME asc
    </select>

    <select id="getAllSpecsCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        from
        (select sf.SP_SPEC_REFERENCE_ID,
        sf.PREFERRED_SP_SPEC_ID,
        sf.SPEC_NAME as referenceName,
        s.SP_SPEC_ID,
        s.SPEC_NAME,
        s.ORIGINAL_SPEC_ID,
        s.IS_LOCKED,
        s.IS_ITEM_VERSION,
        sn.SP_SPEC_NODE_ID,
        s.SPEC_USER_STATE_ID,
        s.SPEC_CREATE_DATE,
        s.CONTENTS_MOD_DATE,
        s.CREATE_USER_ID,
        s.LOCK_COUNT,
        sf.REF_NUMBER,
        sf.SKU,
        st.ICON,
        sf.IS_MASTER,
        st.LABEL_STR,
        s.VERSION_NUMBER,
        s.PR_PROPERTY_ID
        from sp_spec s,
        sp_spec_node sn,
        sy_containable sc,
        sp_spec_reference sf,
        sp_spec_type st
        where sc.OBJECT_CLASS_ID = 1000114
        and sc.PARENT_OBJECT_CLASS_ID = 1000000
        and sn.SP_SPEC_ID = s.SP_SPEC_ID
        and sc.PARENT_OBJECT_ID = #{projectId}
        and sc.OBJECT_ID = s.SP_SPEC_ID
        and sf.SP_SPEC_TYPE_ID = st.SP_SPEC_TYPE_ID
        and s.SP_SPEC_REFERENCE_ID = sf.SP_SPEC_REFERENCE_ID
        order by sf.SP_SPEC_REFERENCE_ID, s.SP_SPEC_ID desc) a

        left join (select pc.*
        from pc_job pc, sy_containable sc
        where sc.parent_object_id = #{projectId}
        and sc.parent_object_class_id = 1000000
        and sc.object_class_id = 1000020
        and sc.object_id = pc.pc_job_id) pj
        on pj.SP_SPEC_REFERENCE_ID = a.SP_SPEC_REFERENCE_ID
        where (
            (a.IS_MASTER = 1 and (pj.OC_OBJECT_STATE_ID = 2000130 or pj.OC_OBJECT_STATE_ID is null))
            or ((a.IS_ITEM_VERSION = 0 or a.IS_ITEM_VERSION is null) and (a.SPEC_USER_STATE_ID = 2000130 or a.SPEC_USER_STATE_ID is null))
        )
    </select>

    <select id="getAllSpecsList" resultMap="specReferenceListResultMap">
        SELECT a.*,
        pj.PC_JOB_ID,
        pj.OC_OBJECT_STATE_ID as jobUserStateId,
        pj.CURRENT_SP_SPEC_ID as  currentSpecId
        from
        (
        select
        sf.SP_SPEC_REFERENCE_ID,
        sf.PREFERRED_SP_SPEC_ID,
        sf.SPEC_NAME as referenceName,
        sf.REF_NUMBER,
        sf.SKU,
        sf.IS_MASTER,

        s.SP_SPEC_ID,
        s.SPEC_NAME,
        s.ORIGINAL_SPEC_ID,
        s.IS_LOCKED,
        s.IS_ITEM_VERSION,
        s.SPEC_USER_STATE_ID,
        s.SPEC_CREATE_DATE,
        s.CONTENTS_MOD_DATE,
        s.CREATE_USER_ID,
        s.LOCK_COUNT,
        s.VERSION_NUMBER,
        s.PR_PROPERTY_ID,
        s.AC_ATTR_PRODUCT_TYPE_ID,

        sn.SP_SPEC_NODE_ID,

        st.SP_SPEC_TYPE_ID,
        st.ICON,
        st.LABEL_STR,
        st.AC_SOURCE_TYPE_ID

        from sp_spec s,
        sp_spec_node sn,
        sy_containable sc,
        sp_spec_reference sf,
        sp_spec_type st
        where sc.OBJECT_CLASS_ID = 1000114
        and sc.PARENT_OBJECT_CLASS_ID = 1000000
        and sn.SP_SPEC_ID = s.SP_SPEC_ID
        and sc.PARENT_OBJECT_ID IN
        <foreach item="item" index="index" collection="projectIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and sc.OBJECT_ID = s.SP_SPEC_ID
        and sf.SP_SPEC_TYPE_ID = st.SP_SPEC_TYPE_ID
        and s.SP_SPEC_REFERENCE_ID = sf.SP_SPEC_REFERENCE_ID
        <if test="specsFilter.specTypeId != null">
            and st.SP_SPEC_TYPE_ID = #{specsFilter.specTypeId}
        </if>
        order by sf.SP_SPEC_REFERENCE_ID, s.SP_SPEC_ID) a

        left join (select pc.*
        from pc_job pc, sy_containable sc
        where sc.parent_object_id IN
        <foreach item="item" index="index" collection="projectIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and sc.parent_object_class_id = 1000000
        and sc.object_class_id = 1000020
        and sc.object_id = pc.pc_job_id) pj
        on pj.SP_SPEC_REFERENCE_ID = a.SP_SPEC_REFERENCE_ID
        where (
        (a.IS_MASTER = 1 and (pj.OC_OBJECT_STATE_ID = 2000130 or pj.OC_OBJECT_STATE_ID is null))
        or ((a.IS_ITEM_VERSION = 0 or a.IS_ITEM_VERSION is null) and (a.SPEC_USER_STATE_ID = 2000130 or a.SPEC_USER_STATE_ID is null))
        )
        <if test="specsFilter.searchString != null or specsFilter.startDate != null
            or specsFilter.endDate != null or specsFilter.createUserId != null ">
            <if test="specsFilter.searchString != null">
                and (
                upper(a.SPEC_NAME) like '%' || #{specsFilter.searchString} || '%' ESCAPE '\'
                or upper(a.REF_NUMBER) like '%' || #{specsFilter.searchString} || '%' ESCAPE '\'
                or upper(a.SKU) like '%' || #{specsFilter.searchString} || '%' ESCAPE '\'
                or pj.PC_JOB_ID like '%' || #{specsFilter.searchNum} || '%' ESCAPE '\'
                )
            </if>
            <if test="specsFilter.startDate != null and specsFilter.dateType == 1">
                and a.SPEC_CREATE_DATE &gt;=to_date(#{specsFilter.startDate}, 'MM/DD/YYYY HH24:MI:SS')
            </if>
            <if test="specsFilter.endDate != null and specsFilter.dateType == 1">
                and a.SPEC_CREATE_DATE &lt;=to_date(#{specsFilter.endDate}, 'MM/DD/YYYY HH24:MI:SS')
            </if>
            <if test="specsFilter.startDate != null and specsFilter.dateType == 0">
                and a.CONTENTS_MOD_DATE &gt;=to_date(#{specsFilter.startDate}, 'MM/DD/YYYY HH24:MI:SS')
            </if>
            <if test="specsFilter.endDate != null and specsFilter.dateType == 0">
                and a.CONTENTS_MOD_DATE &lt;=to_date(#{specsFilter.endDate}, 'MM/DD/YYYY HH24:MI:SS')
            </if>
            <if test="specsFilter.createUserId != null">
                and a.CREATE_USER_ID in
                <foreach item="item" index="index" collection="specsFilter.createUserId" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
    </select>
    
    <select id="findSimpleSpecListByProjectId" resultMap="simpleSpecResultMap">
        SELECT ss.SP_SPEC_ID, ss.SPEC_NAME FROM SY_CONTAINABLE sc, SP_SPEC ss WHERE sc.PARENT_OBJECT_ID = #{projectId} AND sc.OBJECT_CLASS_ID = 1000114 AND sc.PARENT_OBJECT_CLASS_ID = 1000000 AND sc.OBJECT_ID = ss.SP_SPEC_ID
    </select>

    <select id="findUofmIdsBySpecType" resultType="java.lang.Long">
        SELECT /*+INDEX (U BU_UOFM_SPEC_TYPE_SPTYPEID_X) */ bust.BU_UOFM_ID FROM BU_UOFM_SPEC_TYPE bust WHERE bust.SP_SPEC_TYPE_ID =#{specTypeId}
	</select>
	
    <select id="deleteBaselineAttributesByMarkId">
        delete from NO_BENCHMARK_ATTRIBUTE where NO_BENCHMARK_ID = #{benchmarkId}
    </select>

    <select id="findMatchedOrderItemPrice" resultMap="specProcurementItemPriceMap">
        select TEMP.SPEC_ID,
            OI.QUANTITY,
            TEMP.PRICE,
            OI.VALUE_AC_CURRENCY_ID as PRICE_AC_CURRENCY_ID,
            TEMP.SUPPLIERWORKGROUPNAME
        from
            OR_ORDER_ITEM OI,
            (select OI.SP_SPEC_ID        as SPEC_ID,
                Sum(OI.VALUE)            as PRICE,
                Max(OI.OR_ORDER_ITEM_ID) as MAX_ORDER_ITEM_ID,
                MAX(aw.NAME) AS  SUPPLIERWORKGROUPNAME
            from OR_ORDER OO,
                OR_ORDER_VERSION OV,
                OR_ORDER_ITEM OI,
                OR_ORDER_STATE OS,
                AC_WORKGROUP aw
        where OI.OR_ORDER_VERSION_ID = OV.OR_ORDER_VERSION_ID
                and OO.OR_ORDER_ID = OV.OR_ORDER_ID
                and OS.OR_ORDER_ID = OO.OR_ORDER_ID
                and OS.IS_CURRENT = 1
                and OV.IS_CURRENT = 1
                and OS.OC_OBJECT_STATE_ID in ( 2000078, 2500043, 2500044, 2500045, 2500064, 2500065, 2500074, 2000081, 2000082, 2000085, 2000086)
                AND aw.AC_WORKGROUP_ID = OV.SUPPLIER_AC_WORKGROUP_ID
                and OI.SP_SPEC_ID in
                <foreach collection="specIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <choose>
                    <when test="isSellOrder">
                        and OV.SUPPLIER_AC_WORKGROUP_ID = #{workgroupId}
                    </when>
                    <otherwise>
                        and OV.BUYER_AC_WORKGROUP_ID = #{workgroupId}
                        and OV.SUPPLIER_AC_WORKGROUP_ID != #{workgroupId}
                    </otherwise>
                </choose>
                group by OI.SP_SPEC_ID) TEMP
        where OI.OR_ORDER_ITEM_ID = TEMP.MAX_ORDER_ITEM_ID
    </select>

    <select id="findMatchedInvoice" resultMap="specInvoiceMap">
        SELECT oos.DESCRIPTION_STR_ID, pii.SP_SPEC_ID, pi2.IS_FINAL
        FROM PC_INVOICE_ITEM pii, PC_INVOICE pi2, OC_OBJECT_STATE oos
        WHERE oos.OC_OBJECT_STATE_ID = pi2.OC_OBJECT_STATE_ID AND pii.PC_INVOICE_ID = pi2.PC_INVOICE_ID AND pii.SP_SPEC_ID IN
        <foreach collection="specIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
            <choose>
                <when test="isSell">
                    and pi2.SUPPLIER_AC_WORKGROUP_ID = #{workgroupId}
                </when>
                <otherwise>
                    and pi2.BUYER_AC_WORKGROUP_ID = #{workgroupId}
                    and pi2.SUPPLIER_AC_WORKGROUP_ID != #{workgroupId}
                </otherwise>
            </choose>
        order by pi2.PC_INVOICE_ID desc
    </select>

    <select id="findMatchedRfeEstimateItemPrice" resultMap="specProcurementItemPriceMap">
        SELECT EI.SP_SPEC_ID SPEC_ID,
            min(EIP.PRICE) PRICE,
            max(EIP.PRICE_AC_CURRENCY_ID) PRICE_AC_CURRENCY_ID
        FROM
            EM_RFE ER,
            EM_ESTIMATE EE,
            EM_ESTIMATE_ITEM EI,
            EM_ITEM_OPTION EIO,
            EM_ESTIMATE_ITEM_PRICE EIP
        where  EIP.EM_ESTIMATE_ITEM_ID = EI.EM_ESTIMATE_ITEM_ID
            and EI.EM_ESTIMATE_ID = EE.EM_ESTIMATE_ID
            and EE.EM_RFE_ID = ER.EM_RFE_ID
            and EIO.EM_ITEM_OPTION_ID = EIP.EM_ITEM_OPTION_ID and EE.OC_OBJECT_STATE_ID not in ( 2000074 ,2000077, 2500095, 2500094)
            and EI.SP_SPEC_ID in
            <foreach collection="specIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            GROUP BY EI.SP_SPEC_ID
    </select>

    <select id="findMatchedRfeEstimateItemPriceList" resultMap="specProcurementItemPriceMap">
        Select SPEC_ID, PRICE, PRICE_AC_CURRENCY_ID, SUPPLIERWORKGROUPNAME from (SELECT EI.SP_SPEC_ID SPEC_ID,
        EIP.PRICE PRICE,
        EIP.PRICE_AC_CURRENCY_ID PRICE_AC_CURRENCY_ID,
        aw.NAME SUPPLIERWORKGROUPNAME
        FROM
        EM_RFE ER,
        EM_ESTIMATE EE,
        EM_ESTIMATE_ITEM EI,
        EM_ITEM_OPTION EIO,
        EM_ESTIMATE_ITEM_PRICE EIP,
        AC_WORKGROUP aw
        where  EIP.EM_ESTIMATE_ITEM_ID = EI.EM_ESTIMATE_ITEM_ID
        and EI.EM_ESTIMATE_ID = EE.EM_ESTIMATE_ID
        and EE.EM_RFE_ID = ER.EM_RFE_ID
        and EIO.EM_ITEM_OPTION_ID = EIP.EM_ITEM_OPTION_ID and EE.OC_OBJECT_STATE_ID not in ( 2000074 ,2000077, 2500095, 2500094)
        AND aw.AC_WORKGROUP_ID = EE.OWNER_AC_WORKGROUP_ID order BY EI.SP_SPEC_ID, EIP.PRICE ASC)
        where SPEC_ID in
        <foreach collection="specIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    
    <select id="findMatchedQuoteItemPrice" resultMap="specProcurementItemPriceMap">
        select  pqi.SP_SPEC_ID SPEC_ID,
            sum(pqr.PRICE) PRICE,
            max(pqr.PRICE_AC_CURRENCY_ID) PRICE_AC_CURRENCY_ID
        from pc_quote pq,
            pc_quote_item pqi,
            PC_QUOTE_PRICE pqr
        where pq.pc_quote_id = pqi.pc_quote_id
            and pqi.CHOSEN_PC_QUOTE_PRICE_ID = pqr.PC_QUOTE_PRICE_id
            and pqi.sp_spec_id in
            <foreach collection="specIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            group by pqi.sp_spec_id
    </select>
	
	<select id="findLowestEstimateId" resultType="hashmap">
		SELECT EM_ESTIMATE_ID, EM_RFE_ID FROM (
        SELECT eeip.price AS price, eei.EM_ESTIMATE_ID, ee.EM_RFE_ID  FROM EM_ESTIMATE_ITEM eei, EM_ESTIMATE_ITEM_PRICE eeip, EM_ESTIMATE ee
        WHERE eei.EM_ESTIMATE_ITEM_ID = eeip.EM_ESTIMATE_ITEM_ID AND eei.EM_ESTIMATE_ID = ee.EM_ESTIMATE_ID and ee.OC_OBJECT_STATE_ID not in ( 2000074 ,2000077, 2500095, 2500094)
        AND eei.SP_SPEC_ID = #{specId} ORDER BY price ASC) WHERE rownum = 1	
	</select>

    <select id="findRfeIdByEstimateId" resultType="java.lang.Long">
        SELECT EM_RFE_ID FROM EM_ESTIMATE ee WHERE ee.EM_ESTIMATE_ID = #{estimateId}
    </select>
	
	<select id="findOriginalOrderId" resultMap="specOrderMap">
        SELECT DISTINCT oo.OR_ORDER_ID,
        (SELECT count(*) FROM OR_ORDER o, OR_ORDER_VERSION oov, OR_ORDER_STATE oos
        WHERE o.parent_or_order_id = oo.OR_ORDER_ID AND
        o.OR_ORDER_VERSION_ID = oov.OR_ORDER_VERSION_ID AND oov.OR_ORDER_TYPE_ID = 1000002 AND oov.IS_CURRENT = 1
        AND oos.OR_ORDER_ID = o.OR_ORDER_ID AND oos.IS_CURRENT = 1
        AND oos.OC_OBJECT_STATE_ID in ( 2000078, 2500043, 2500044, 2500045, 2500064, 2500065,
        2500074)) AS orderCount FROM OR_ORDER oo, OR_ORDER_VERSION oov, OR_ORDER_ITEM ooi, OR_ORDER_STATE oos
        WHERE oo.OR_ORDER_VERSION_ID = oov.OR_ORDER_VERSION_ID 
        AND oov.OR_ORDER_VERSION_ID = ooi.OR_ORDER_VERSION_ID
        AND oos.OC_OBJECT_STATE_ID in ( 2000078, 2500043, 2500044, 2500045, 2500064, 2500065, 2500074, 2000081, 2000082, 2000085, 2000086) AND oos.OR_ORDER_ID = oo.OR_ORDER_ID AND oos.IS_CURRENT = 1
        AND oov.IS_CURRENT = 1 AND ooi.SP_SPEC_ID = #{specId} AND oo.OR_ORDER_ID = oo.PARENT_OR_ORDER_ID 
			<choose>
                <when test="isSell">
                    and oov.SUPPLIER_AC_WORKGROUP_ID = #{workgroupId}
                </when>
                <otherwise>
                    and oov.BUYER_AC_WORKGROUP_ID = #{workgroupId}
                    and oov.SUPPLIER_AC_WORKGROUP_ID != #{workgroupId}
                </otherwise>
            </choose>
        FETCH FIRST ROW ONLY
    </select>

    <select id="findRfeQuantityByRfeId" resultMap="quantityMap">
       SELECT eio.OPTION_INDEX, eio.VALUE, eeip.PRICE, ee.EM_ESTIMATE_ID, eeip.EM_ESTIMATE_ITEM_PRICE_ID FROM EM_RFE er, EM_RFE_ITEM eri, EM_ITEM_OPTION eio, EM_ITEM_OPTION eio2 ,
       EM_ESTIMATE_ITEM_PRICE eeip , EM_ESTIMATE_ITEM eei, EM_ESTIMATE ee
       WHERE er.EM_RFE_ID = ee.EM_RFE_ID AND er.EM_RFE_ID = eri.EM_RFE_ID
       AND eri.EM_RFE_ITEM_ID = eio.ITEM_OBJECT_ID AND eio.OC_OBJECT_CLASS_ID = 1000115
       AND eio.OPTION_INDEX = eio2.OPTION_INDEX AND eio2.OC_OBJECT_CLASS_ID = 1000116
       AND ee.EM_ESTIMATE_ID = eei.EM_ESTIMATE_ID AND eei.EM_ESTIMATE_ITEM_ID = eeip.EM_ESTIMATE_ITEM_ID
       AND eeip.EM_ITEM_OPTION_ID = eio2.EM_ITEM_OPTION_ID AND ee.EM_RFE_ID = #{rfeId} AND eei.SP_SPEC_ID = #{specId}
              AND eri.SP_SPEC_ID = eei.SP_SPEC_ID
       ORDER BY eio.OPTION_INDEX ASC, eeip.PRICE ASC
    </select>

    <select id="findRfeQuantityBySpecId" resultMap="quantityMap">
        SELECT * FROM (SELECT eio.OPTION_INDEX, eio.VALUE FROM EM_RFE_ITEM eri, EM_ITEM_OPTION eio WHERE eri.EM_RFE_ITEM_ID = eio.ITEM_OBJECT_ID AND eio.OC_OBJECT_CLASS_ID = 1000115
        AND eri.SP_SPEC_ID = #{specId} ORDER BY eio.CREATE_DATE DESC, eio.OPTION_INDEX ASC ) WHERE rownum &lt;= 5 ORDER BY OPTION_INDEX ASC
    </select>

    <select id="findTotalCountBySpecId" resultType="java.lang.Long">
        SELECT count(DISTINCT eri.em_rfe_id) AS rfeCount FROM EM_RFE_ITEM eri, EM_RFE er
        WHERE eri.SP_SPEC_ID = #{specId} AND eri.EM_RFE_ID = er.EM_RFE_ID AND er.OC_OBJECT_STATE_ID NOT IN (2000067, 2000070, 2000071, 2500093)

        UNION ALL

        SELECT count(DISTINCT eei.em_estimate_id) AS estimateCount FROM EM_ESTIMATE_ITEM eei, EM_ESTIMATE ee
        WHERE eei.SP_SPEC_ID = #{specId} AND eei.EM_ESTIMATE_ID = ee.EM_ESTIMATE_ID AND ee.OC_OBJECT_STATE_ID not in ( 2000074 ,2000077, 2500095, 2500094)

        UNION ALL

        SELECT count(DISTINCT pqi.PC_QUOTE_ID) AS quoteCount FROM PC_QUOTE_ITEM pqi, PC_QUOTE pq
        WHERE pqi.SP_SPEC_ID = #{specId} AND pqi.PC_QUOTE_ID = pq.PC_QUOTE_ID AND pq.OC_OBJECT_STATE_ID NOT IN (2000007, 2000009, 2000018)

        UNION ALL

        SELECT count(DISTINCT ooi.OR_ORDER_VERSION_ID) AS buyOrderCount FROM OR_ORDER_ITEM ooi, OR_ORDER_VERSION oov, OR_ORDER_STATE OS, OR_ORDER oo
        WHERE ooi.SP_SPEC_ID = #{specId} AND ooi.OR_ORDER_VERSION_ID = oov.OR_ORDER_VERSION_ID  and OS.OR_ORDER_ID = oov.OR_ORDER_ID
        and OS.IS_CURRENT = 1  AND oo.or_order_id = oov.OR_ORDER_ID AND oo.PARENT_OR_ORDER_ID = oo.or_order_id
        and oov.IS_CURRENT = 1
        and OS.OC_OBJECT_STATE_ID in ( 2000078, 2500043, 2500044, 2500045, 2500064, 2500065, 2500074, 2000081, 2000082, 2000085, 2000086)
        and oov.BUYER_AC_WORKGROUP_ID = #{workgroupId}
        and oov.SUPPLIER_AC_WORKGROUP_ID != #{workgroupId}

        UNION ALL

        SELECT count(DISTINCT ooi.OR_ORDER_VERSION_ID) AS sellOrderCount FROM OR_ORDER_ITEM ooi, OR_ORDER_VERSION oov, OR_ORDER_STATE OS, OR_ORDER oo
        WHERE ooi.SP_SPEC_ID = #{specId} AND ooi.OR_ORDER_VERSION_ID = oov.OR_ORDER_VERSION_ID  and OS.OR_ORDER_ID = oov.OR_ORDER_ID
        and OS.IS_CURRENT = 1  AND oo.or_order_id = oov.OR_ORDER_ID AND oo.PARENT_OR_ORDER_ID = oo.or_order_id
        and oov.IS_CURRENT = 1
        and OS.OC_OBJECT_STATE_ID in ( 2000078, 2500043, 2500044, 2500045, 2500064, 2500065, 2500074, 2000081, 2000082, 2000085, 2000086)
        and oov.SUPPLIER_AC_WORKGROUP_ID = #{workgroupId}

        UNION ALL

        SELECT count(DISTINCT pii.PC_INVOICE_ID) AS buyInvoiceCount
        FROM PC_INVOICE_ITEM pii, PC_INVOICE pi2, OC_OBJECT_STATE oos
        WHERE oos.OC_OBJECT_STATE_ID = pi2.OC_OBJECT_STATE_ID
        AND pii.PC_INVOICE_ID = pi2.PC_INVOICE_ID AND pii.SP_SPEC_ID = #{specId}
        AND oos.OC_OBJECT_STATE_ID NOT IN (2500088, 2500092)
        AND pi2.BUYER_AC_WORKGROUP_ID = #{workgroupId}
        AND pi2.SUPPLIER_AC_WORKGROUP_ID != #{workgroupId}

        UNION ALL

        SELECT count(DISTINCT pii.PC_INVOICE_ID) AS sellInvoiceCount
        FROM PC_INVOICE_ITEM pii, PC_INVOICE pi2, OC_OBJECT_STATE oos
        WHERE oos.OC_OBJECT_STATE_ID = pi2.OC_OBJECT_STATE_ID
        AND pii.PC_INVOICE_ID = pi2.PC_INVOICE_ID AND pii.SP_SPEC_ID = #{specId}
        AND oos.OC_OBJECT_STATE_ID NOT IN (2500088, 2500092)
        AND pi2.SUPPLIER_AC_WORKGROUP_ID = #{workgroupId}
    </select>
    
    <select id="getSpecWithProjectInfoList" resultMap="specWithProjectMap">
        select PRJ.PM_PROJECT_ID PRJ_PM_PROJECT_ID,PRJ.REFERENCE PRJ_REFERENCE,PRJ.NAME PRJ_NAME,PRJ.PROJECT_NUMBER PRJ_PROJECT_NUMBER,
        S.SP_SPEC_ID S_SP_SPEC_ID,S.SPEC_NAME S_SPEC_NAME,S.SP_SPEC_TYPE_ID S_SP_SPEC_TYPE_ID,SR.REF_NUMBER SR_REF_NUMBER,
        SN.SP_SPEC_NODE_ID SN_SP_SPEC_NODE_ID, st.ICON ST_SP_SPEC_TYPE_ICON from PM_PROJECT PRJ,SP_SPEC S,SP_SPEC_TYPE ST,SP_SPEC_REFERENCE SR,SP_SPEC_NODE SN,
        SP_SPEC_TREE TREE,SY_CONTAINABLE CO,SP_SPEC_REGISTRATION SREGIS, (
        select TM_TEAM_OBJECT.OBJECT_ID
        from TM_TEAM_MEMBER, TM_TEAM_OBJECT, RE_ACCESS_RULE_REL
        where TM_TEAM_MEMBER.TM_TEAM_ID = TM_TEAM_OBJECT.TM_TEAM_ID and
              TM_TEAM_MEMBER.USER_ID = #{userId} and
              TM_TEAM_MEMBER.CURRENT_RECORD = 1 and
              TM_TEAM_OBJECT.OC_OBJECT_CLASS_ID = 1000000 AND
              TM_TEAM_MEMBER.RE_ROLE_ID = RE_ACCESS_RULE_REL.RE_ROLE_ID AND
              RE_ACCESS_RULE_REL.RE_ACCESS_RULE_ID = 1001078

        <if test="isAllProjects == true">
          UNION SELECT PRJ.PM_PROJECT_ID as OBJECT_ID FROM PM_PROJECT PRJ WHERE PRJ.OWNER_AC_WORKGROUP_ID=#{workgroupId}
        </if>

         ) SPEC_PROJECT_SQL
        <if test="projectCategoryIds != null and projectCategoryIds.size > 0 and projectCategoryIds.contains(-1L) == false">
          , CO_CATEGORY_OBJECT PCO,CO_CATEGORY PC
        </if>
        <if test="statusNameSql != null and statusNameSql.length > 0">
          , PM_PROJECT_STATUS PMS
        </if>
        where
         (PRJ.PM_PROJECT_ID=SPEC_PROJECT_SQL.OBJECT_ID) and (PRJ.OC_OBJECT_STATE_ID != 2000065)
        <if test="statusNameSql != null and statusNameSql.length > 0">
            AND PRJ.PM_PROJECT_STATUS_ID = PMS.PM_PROJECT_STATUS_ID AND
             ${statusNameSql}
        </if>
        <if test="projectCategoryIds != null and projectCategoryIds.size > 0 and projectCategoryIds.contains(-1L) == false">
         and (PC.CO_CATEGORY_ID IN
            <foreach collection="projectCategoryIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>) and (PC.CO_CATEGORY_ID = PCO.CO_CATEGORY_ID) and (PCO.OC_OBJECT_CLASS_ID = 1000000)
            and (PCO.OBJECT_ID = PRJ.PM_PROJECT_ID)
        </if>
       and (PRJ.PM_PROJECT_ID=CO.PARENT_OBJECT_ID) and (CO.PARENT_OBJECT_CLASS_ID = 1000000)
         and (CO.OBJECT_CLASS_ID = 1000114) and (S.SP_SPEC_ID= CO.OBJECT_ID) and (SR.SP_SPEC_REFERENCE_ID=S.SP_SPEC_REFERENCE_ID)
         and (ST.SP_SPEC_TYPE_ID=S.SP_SPEC_TYPE_ID) and (SN.SP_SPEC_ID=S.SP_SPEC_ID) and (SN.SP_SPEC_TREE_ID=TREE.SP_SPEC_TREE_ID)
         and (TREE.IS_HIDDEN = 0) and (ST.IS_VISIBLE=1) and (ST.SP_SPEC_TYPE_ID = SREGIS.SP_SPEC_TYPE_ID)
         and (SREGIS.AC_WORKGROUP_ID = #{workgroupId})
        <if test="specTypeId != null and specTypeId != -1">
          and ST.SP_SPEC_TYPE_ID= #{specTypeId}
        </if>
        <if test="searchStr != null">
            and (UPPER(SR.SPEC_NAME) LIKE '%' || UPPER(#{searchStr}) || '%'  OR  UPPER(SR.REF_NUMBER) LIKE '%' || UPPER(#{searchStr}) || '%'
            <if test="isSearchProjectName == true">
              OR UPPER(PRJ.NAME) LIKE '%' || UPPER(#{searchStr}) || '%'
              OR  UPPER(PRJ.REFERENCE) LIKE '%' || UPPER(#{searchStr}) || '%' OR  UPPER(PRJ.PROJECT_NUMBER) LIKE '%' || UPPER(#{searchStr}) || '%'
            </if>
            )
         </if>
    </select>


    <select id="findTypesForStaples" resultMap="specTypeResultMapForStaples">
        SELECT S.*, SER.DESCRIPTION_STR_ID AS CO_SERVICE_DESCRIPTION_STR_ID
        FROM
            (
                SELECT
                    DISTINCT T.SP_SPEC_TYPE_ID, T.LABEL_STR_ID, T.DESCRIPTION, T.CREATE_DATE, T.MOD_DATE, T.CREATE_USER_ID,
                             T.MOD_USER_ID, T.CO_SERVICE_ID, T.PR_PROPERTY_TYPE_ID, T.TEMPLATE_BASE_NAME, T.IS_DEPRECATED, T.DEPRECATION_REASON,
                             T.DEPRECATION_DATE, T.REPLACED_BY_SPEC_TYPE_ID, T.LABEL_STR, T.ICON, T.IS_DIRECT_MAIL, T.IS_VISIBLE, T.IMAGE,
                             T.IS_IM_ENABLED, T.IS_CAMPAIGN, T.AC_CUSTOM_FORM_ID, T.ORIGINAL_SPEC_TYPE_ID, T.PARENT_SPEC_TYPE_ID,
                             T.VERSION_NUMBER as VERSION_NUMBER_ST, T.IS_CURRENT, T.ICON_URL, T.AC_SOURCE_TYPE_ID, T.IS_INVOICE_ADJUSTMENT, T.IS_TIME_MATERIALS, T.IS_NLP, TAG.NAME AS CATEGORY_NAME
                FROM SP_SPEC_TYPE T
                JOIN SP_SPEC_REGISTRATION R ON R.SP_SPEC_TYPE_ID = T.SP_SPEC_TYPE_ID
                LEFT JOIN SP_PRODUCT P ON P.SP_SPEC_TYPE_ID = R.SP_SPEC_TYPE_ID
                LEFT JOIN NL_PRODUCT_TAG PTAG ON PTAG.SP_PRODUCT_ID = P.SP_PRODUCT_ID
                LEFT JOIN NL_TAG TAG ON TAG.NL_TAG_ID = PTAG.NL_TAG_ID
                WHERE
                    T.IS_DEPRECATED = 0 OR T.IS_DEPRECATED IS NULL
                    AND R.AC_WORKGROUP_ID = #{workgroupId}
                    AND T.IS_VISIBLE = 1
            ) S
        LEFT JOIN CO_SERVICE SER on S.CO_SERVICE_ID = SER.CO_SERVICE_ID
        ORDER BY S.CO_SERVICE_ID ASC
    </select>

    <select id="findAISpecCreationTypesByWorkgroupIds" resultMap="specTypeResultMap">
        SELECT DISTINCT
            sst.sp_spec_type_id,
            sst.label_str_id,
            sst.description,
            sst.create_date,
            sst.mod_date,
            sst.create_user_id,
            sst.mod_user_id,
            sst.co_service_id,
            sst.pr_property_type_id,
            sst.template_base_name,
            sst.is_deprecated,
            sst.deprecation_reason,
            sst.deprecation_date,
            sst.replaced_by_spec_type_id,
            sst.label_str,
            sst.icon,
            sst.is_direct_mail,
            sst.is_visible,
            sst.image,
            sst.is_im_enabled,
            sst.is_campaign,
            sst.ac_custom_form_id,
            sst.original_spec_type_id,
            sst.parent_spec_type_id,
            sst.version_number   AS version_number_st,
            sst.is_current,
            sst.icon_url,
            sst.ac_source_type_id,
            sst.is_invoice_adjustment,
            sst.is_time_materials,
            sst.is_nlp
        FROM
        sp_spec_type sst,
        sp_spec_registration ssr,
        (
            SELECT
                WG.AC_WORKGROUP_ID, attr.string_value
            FROM
                pf_preference pref,
                pr_property pro,
                pr_property_attribute attr,
                pr_property_param para,
                ac_workgroup wg
            WHERE
                pref.pr_property_id = pro.pr_property_id
                AND pro.pr_property_id = attr.pr_property_id
                AND attr.pr_property_param_id = para.pr_property_param_id
                AND wg.ac_workgroup_id = pref.owner_object_id
                AND wg.oc_object_state_id = 2000054
                AND pro.pr_property_type_id = 1000048
                AND pro.object_class_id = 2000200
                AND pref.owner_oc_object_class_id = 1000100
                AND para.param_name = 'WORKGROUP_OPTION_AI_SPEC_CREATION_NEW'
                AND attr.string_value = '1'
                AND wg.ac_workgroup_id IN
                    <foreach item="item" index="index" collection="workgroupIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
        ) ascn
        WHERE
        sst.is_deprecated = 0
        OR sst.is_deprecated IS NULL
        AND ssr.sp_spec_type_id = sst.sp_spec_type_id
        AND ssr.ac_workgroup_id  = ascn.AC_WORKGROUP_ID
        AND sst.is_visible = 1
        AND sst.is_nlp = 1
    </select>
</mapper>