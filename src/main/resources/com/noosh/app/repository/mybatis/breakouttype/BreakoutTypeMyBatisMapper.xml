<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.breakouttype.BreakoutTypeMyBatisMapper">

    <resultMap id="breakOutTypeResultMap" type="com.noosh.app.commons.dto.breakout.BreakoutTypeDTO">
        <result property="id" column = "PC_BREAKOUT_TYPE_ID" />
        <result property="workgroupId" column = "AC_WORKGROUP_ID" />
        <result property="parentTypeId" column = "PARENT_PC_BREAKOUT_TYPE_ID" />
        <result property="rootTypeId" column = "ROOT_PC_BREAKOUT_TYPE_ID" />
        <result property="isLocked" column = "IS_LOCKED" />
        <result property="isCurrent" column = "IS_CURRENT" />
        <result property="name" column = "NAME" />
        <result property="description" column = "DESCRIPTION" />
        <result property="isRequired" column = "IS_REQUIRED" />
        <result property="isQuantity" column = "IS_QUANTITY" />
        <result property="allSuppliers" column = "ALL_SUPPLIERS" />
        <result property="sortOrder" column = "SORT_ORDER" />
        <result property="custom1" column = "CUSTOM1" />
        <result property="cloneOf" column = "CLONE_OF" />
        <result property="isIncluded" column = "IS_INCLUDED" />
        <result property="pricePer" column = "PRICE_PER"/>
        <result property="propertyId" column = "CUSTOM_PR_PROPERTY_ID" />
        <result property="code" column = "CODE" />
        <result property="allClients" column = "ALL_CLIENTS" />
        <result property="level" column = "LEVEL" />
    </resultMap>

    <select id="findAllWithRootBreakoutTypeIdAndSpecTypeIdAndSupplierGroupId" resultMap="breakOutTypeResultMap">
        SELECT
            *
        FROM
        (
            SELECT
            LEVEL, BT.*
        FROM PC_BREAKOUT_TYPE BT
        WHERE BT.ROOT_PC_BREAKOUT_TYPE_ID = #{breakoutTypeId}
            AND BT.IS_INCLUDED = 1
        CONNECT BY PRIOR BT.PC_BREAKOUT_TYPE_ID = BT.PARENT_PC_BREAKOUT_TYPE_ID
        START WITH
            BT.PARENT_PC_BREAKOUT_TYPE_ID IS NULL
            AND BT.ROOT_PC_BREAKOUT_TYPE_ID = #{breakoutTypeId} ORDER SIBLINGS BY SORT_ORDER)
        WHERE (PC_BREAKOUT_TYPE_ID IN
                (SELECT PC_BREAKOUT_TYPE_ID
                FROM PC_BREAKOUT_TYPE_SPEC
                WHERE SP_SPEC_TYPE_ID  = #{specTypeId})
            <if test="supplierWorkgroupId &gt; 0">
                AND (PC_BREAKOUT_TYPE_ID IN
                    (SELECT  PC_BREAKOUT_TYPE_ID
                    FROM PC_BREAKOUT_TYPE_SUPPLIER
                    WHERE AC_WORKGROUP_ID = #{supplierWorkgroupId}) OR ALL_SUPPLIERS = 1)
            </if>
            ) OR PARENT_PC_BREAKOUT_TYPE_ID is null
    </select>

    <select id="findByWorkgroupAndSpecTypeId" resultMap="breakOutTypeResultMap">
        SELECT
            LEVEL, BT.*
        FROM PC_BREAKOUT_TYPE BT
        WHERE
            (EXISTS
                 (SELECT 'x' FROM PC_BREAKOUT_TYPE_SPEC SP
                  WHERE BT.PC_BREAKOUT_TYPE_ID = SP.PC_BREAKOUT_TYPE_ID
                    AND SP.SP_SPEC_TYPE_ID = #{specTypeId})
                OR BT.PARENT_PC_BREAKOUT_TYPE_ID IS NULL
                )
            CONNECT BY PRIOR
             BT.PC_BREAKOUT_TYPE_ID = BT.PARENT_PC_BREAKOUT_TYPE_ID
        START WITH (
            BT.PARENT_PC_BREAKOUT_TYPE_ID is null
               AND BT.AC_WORKGROUP_ID = #{workgroupId}
               AND BT.IS_CURRENT = 1)
        ORDER SIBLINGS BY SORT_ORDER
    </select>


    <select id="findAllByRootBreakoutTypeId" resultMap="breakOutTypeResultMap">
        SELECT
            LEVEL,
            BT.*
        FROM PC_BREAKOUT_TYPE BT
        WHERE
            BT.ROOT_PC_BREAKOUT_TYPE_ID = #{rootBreakoutTypeId}
            CONNECT BY PRIOR
             BT.PC_BREAKOUT_TYPE_ID = BT.PARENT_PC_BREAKOUT_TYPE_ID
        START WITH
                 (BT.PARENT_PC_BREAKOUT_TYPE_ID is null
               AND BT.ROOT_PC_BREAKOUT_TYPE_ID = #{rootBreakoutTypeId}
            )
        ORDER SIBLINGS BY SORT_ORDER
    </select>

</mapper>