<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.order.OrderItemMyBatisMapper">

    <resultMap id="orderItemsMap" type="com.noosh.app.commons.dto.order.OrderItemDTO">
        <result property="orderId" column="or_order_id"/>
        <result property="orderVersionId" column="or_order_version_id"/>
        <result property="value" column="value"/>
        <result property="valueCurrencyId" column="VALUE_AC_CURRENCY_ID"/>
        <result property="exValue" column="EXVALUE"/>
        <result property="dors" column="D_OR_S"/>
        <result property="exDors" column="EX_D_OR_S"/>
    </resultMap>


    <select id="findByOrderVersionId" parameterType="long" resultMap="orderItemsMap">
        SELECT  or_order_version_id, VALUE, VALUE_AC_CURRENCY_ID, EXVALUE, D_OR_S, EX_D_OR_S
        FROM OR_ORDER_ITEM
        WHERE or_order_version_id = #{orderVersionId}
    </select>

    <select id="findByJobId" parameterType="long" resultMap="orderItemsMap">
        SELECT  or_order_version_id, VALUE, D_OR_S
        FROM OR_ORDER_ITEM
        WHERE PC_JOB_ID = #{jobId}
    </select>

</mapper>