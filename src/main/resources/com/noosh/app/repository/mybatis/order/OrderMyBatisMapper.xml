<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.noosh.app.repository.mybatis.order.OrderMyBatisMapper">

    <resultMap id="projectOrderResultMap" type="com.noosh.app.commons.dto.order.ProjectOrderWidgetDTO">
        <result property="orderId" column="or_order_id" jdbcType="DECIMAL"/>
        <result property="orderVersionId" column="or_order_version_id" jdbcType="DECIMAL"/>
        <result property="reference" column="REFERENCE" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="stateId" column="oc_object_state_id" jdbcType="DECIMAL"/>
        <result property="stateCreateUserId" column="STATE_CREATE_USER_ID"/>
        <result property="completionDate" column="completion_date"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="lastModDate" column="LAST_CHANGE_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="supplierWorkgroupId" column="SUPPLIER_AC_WORKGROUP_ID" jdbcType="DECIMAL"/>
        <result property="buyerWorkgroupId" column="BUYER_AC_WORKGROUP_ID" jdbcType="DECIMAL"/>
        <result property="orderTypeId" column="OR_ORDER_TYPE_ID" jdbcType="DECIMAL"/>
        <result property="paymentReference" column="PAYMENT_REFERENCE" jdbcType="VARCHAR"/>
        <result property="parentOrderId" column="PARENT_OR_ORDER_ID" jdbcType="DECIMAL"/>
        <result property="customPropertyId" column="CUSTOM_PR_PROPERTY_ID" jdbcType="DECIMAL"/>
        <result property="isClosing" column="is_closing"/>
        <result property="versionNumber" column="VERSION_NUMBER"/>
        <result property="buClientId" column="BU_CLIENT_WORKGROUP_ID"/>
        <result property="tax" column="tax"/>
        <result property="taxCurrencyId" column="TAX_AC_CURRENCY_ID"/>
        <result property="shipping" column="SHIPPING"/>
        <result property="shippingCurrencyId" column="SHIPPING_AC_CURRENCY_ID"/>
        <result property="discountOrSurcharge" column="D_OR_S"/>
        <result property="discountOrSurchargeCurrencyId" column="D_OR_S_AC_CURRENCY_ID"/>
        <result property="rate" column="RATE"/>
        <result property="exCurrencyId" column="EX_CURRENCYID"/>
        <result property="exTax" column="EXTAX"/>
        <result property="exTaxCurrencyId" column="EXTAX_AC_CURRENCY_ID"/>
        <result property="exShipping" column="EXSHIPPING"/>
        <result property="exShippingCurrencyId" column="EXSHIPPING_AC_CURRENCY_ID"/>
        <result property="exDiscountOrSurcharge" column="EX_D_OR_S"/>
        <result property="exDiscountOrSurchargeCurrencyId" column="EX_D_OR_S_AC_CURRENCY_ID"/>
    </resultMap>

    <resultMap id="orderResultItemMap" type="com.noosh.app.commons.dto.order.OrderDTO">
        <result property="orderVersionId" column="OR_ORDER_VERSION_ID"/>
        <result property="orderId" column="OR_ORDER_ID"/>
        <result property="reference" column="REFERENCE"/>
        <result property="title" column="TITLE" />
        <result property="orderClassificationId" column = "OR_ORDER_CLASSIFICATION_ID" />
        <result property="stateId" column="OC_OBJECT_STATE_ID" />
        <result property="versionNumber" column="VERSION_NUMBER" />
        <result property="completionDate" column="COMPLETION_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="lastModDate" column="LAST_CHANGE_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="buyerWorkgroupId" column="BUYER_AC_WORKGROUP_ID"/>
        <result property="supplierWorkgroupId" column="SUPPLIER_AC_WORKGROUP_ID"/>
        <result property="orderTypeId" column="OR_ORDER_TYPE_ID"/>
        <result property="paymentReference" column="PAYMENT_REFERENCE"/>
        <result property="parentOrderId" column="PARENT_OR_ORDER_ID"/>
        <result property="isClosing" column="IS_CLOSING"/>
        <result property="buClientId" column="BU_CLIENT_WORKGROUP_ID"/>
        <result property="rate" column="RATE"/>
        <result property="exCurrencyId" column="EX_CURRENCYID"/>
        <result property="tax" column="TAX"/>
        <result property="taxCurrencyId" column="TAX_AC_CURRENCY_ID"/>
        <result property="exTax" column="EXTAX"/>
        <result property="exTaxCurrencyId" column="EXTAX_AC_CURRENCY_ID"/>
        <result property="shipping" column="SHIPPING"/>
        <result property="shippingCurrencyId" column="SHIPPING_AC_CURRENCY_ID"/>
        <result property="exShipping" column="EXSHIPPING"/>
        <result property="exShippingCurrencyId" column="EXSHIPPING_AC_CURRENCY_ID"/>
        <result property="stateCreateUserId" column="STATE_CREATE_USER_ID"/>
        <result property="discountOrSurcharge" column="D_OR_S"/>
        <result property="discountOrSurchargeCurrencyId" column="D_OR_S_AC_CURRENCY_ID"/>
        <result property="exDiscountOrSurcharge" column="EX_D_OR_S"/>
        <result property="exDiscountOrSurchargeCurrencyId" column="EX_D_OR_S_AC_CURRENCY_ID"/>
        <result property="customPropertyId" column="CUSTOM_PR_PROPERTY_ID"/>
    </resultMap>

    <resultMap id="orderResultMap" type="com.noosh.app.commons.dto.order.OrderDTO">
        <result property="orderVersionId" column="OR_ORDER_VERSION_ID"/>
        <result property="orderId" column="OR_ORDER_ID"/>
        <result property="reference" column="REFERENCE"/>
        <result property="title" column="TITLE" />
        <result property="stateId" column="OC_OBJECT_STATE_ID" />
        <result property="completionDate" column="COMPLETION_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="lastModDate" column="LAST_CHANGE_DATE"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="buyerWorkgroupId" column="BUYER_AC_WORKGROUP_ID"/>
        <result property="supplierWorkgroupId" column="SUPPLIER_AC_WORKGROUP_ID"/>
        <result property="orderTypeId" column="OR_ORDER_TYPE_ID"/>
        <result property="paymentReference" column="PAYMENT_REFERENCE"/>
        <result property="parentOrderId" column="PARENT_OR_ORDER_ID"/>
        <result property="isClosing" column="IS_CLOSING"/>
        <result property="buClientId" column="BU_CLIENT_WORKGROUP_ID"/>
        <result property="tax" column="TAX"/>
        <result property="shipping" column="SHIPPING"/>
        <result property="descriptionStrId" column="DESCRIPTION_STR_ID"/>
    </resultMap>

    <select id="findSoldOrdersCount" resultType="java.lang.Long">
        SELECT
        count(*)
        FROM
        OR_ORDER_VERSION OV
        , OR_ORDER OREF
        , SY_CONTAINABLE C
        , OR_ORDER_STATE OS
        WHERE
        OV.OR_ORDER_VERSION_ID=OREF.OR_ORDER_VERSION_ID
        AND OREF.OR_ORDER_ID=OS.OR_ORDER_ID
        AND OS.IS_CURRENT=1
        AND OREF.OR_ORDER_ID=C.OBJECT_ID
        AND C.OBJECT_CLASS_ID=1000117
        AND OV.OR_ORDER_TYPE_ID != 1000002
        AND C.PARENT_OBJECT_ID=#{projectId}
        AND C.PARENT_OBJECT_CLASS_ID=1000000

        AND OV.SUPPLIER_AC_WORKGROUP_ID=#{ownerGroupId}
        <if test="isClientNotOnNoosh">
            AND OV.BU_CLIENT_WORKGROUP_ID is not null
        </if>
    </select>

    <select id="findChangeOrderIds" resultType="java.lang.Long" parameterType="long">
        select or_order_id from or_order where parent_or_order_id = #{orderId} and or_order_id != parent_or_order_id
    </select>

    <select id="getOriginalOrderList" resultMap="orderResultItemMap">
        SELECT
            ov.OR_ORDER_VERSION_ID
            , ov.REFERENCE
            , o.OR_ORDER_ID
            , ov.TITLE
            , ov.OR_ORDER_CLASSIFICATION_ID
            , os.OC_OBJECT_STATE_ID
            , ov.VERSION_NUMBER
            , ov.COMPLETION_DATE
            , ov.MOD_DATE
            , os.STATE_CREATE_DATE
            , os.LAST_CHANGE_DATE
            , os.STATE_CREATE_USER_ID
            , ov.SUPPLIER_AC_WORKGROUP_ID
            , ov.BUYER_AC_WORKGROUP_ID
            , ov.OR_ORDER_TYPE_ID
            , ov.PAYMENT_REFERENCE
            , o.PARENT_OR_ORDER_ID
            , ov.CUSTOM_PR_PROPERTY_ID
            , o.IS_CLOSING
            , ov.BU_CLIENT_WORKGROUP_ID
            , ov.RATE
            , ov.EX_CURRENCYID
            , ov.TAX
            , ov.TAX_AC_CURRENCY_ID
            , ov.EXTAX
            , ov.EXTAX_AC_CURRENCY_ID
            , ov.SHIPPING
            , ov.SHIPPING_AC_CURRENCY_ID
            , ov.EXSHIPPING
            , ov.EXSHIPPING_AC_CURRENCY_ID
        FROM
            OR_ORDER_VERSION ov
            , OR_ORDER o
            , OR_ORDER_STATE os
            , SY_CONTAINABLE cont
            , PM_PROJECT p
        WHERE ov.OR_ORDER_VERSION_ID = o.OR_ORDER_VERSION_ID
        AND o.PARENT_OR_ORDER_ID = cont.OBJECT_ID
        AND o.OR_ORDER_ID = os.OR_ORDER_ID
        AND cont.OBJECT_CLASS_ID = 1000117
        AND cont.PARENT_OBJECT_CLASS_ID = 1000000
        AND cont.PARENT_OBJECT_ID = #{projectId}
        AND p.PM_PROJECT_ID = cont.PARENT_OBJECT_ID
        AND cont.ITEM_OC_OBJECT_STATE_ID != 2500069
        AND os.IS_CURRENT = 1
        AND o.PARENT_OR_ORDER_ID = o.OR_ORDER_ID
        AND os.OC_OBJECT_STATE_ID not in
        <foreach item="item" index="index" collection="cogFilter" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="orderType == 'buy'">
            and  ov.BUYER_AC_WORKGROUP_ID = p.OWNER_AC_WORKGROUP_ID
            <if test="isClientNotOnNoosh == true">
                and ov.BU_CLIENT_WORKGROUP_ID is null
            </if>
        </if>
        <if test="orderType == 'sell'">
            and  ov.SUPPLIER_AC_WORKGROUP_ID = p.OWNER_AC_WORKGROUP_ID
            <if test="isClientNotOnNoosh == true">
                and ov.BU_CLIENT_WORKGROUP_ID is not null
            </if>
        </if>
    </select>

    <select id="getChangeOrderListByParent" resultMap="orderResultItemMap">
        SELECT
            ov.OR_ORDER_VERSION_ID
            , ov.REFERENCE
            , o.OR_ORDER_ID
            , ov.TITLE
            , os.OC_OBJECT_STATE_ID
            , ov.VERSION_NUMBER
            , ov.COMPLETION_DATE
            , ov.MOD_DATE
            , os.STATE_CREATE_DATE
            , os.LAST_CHANGE_DATE
            , os.STATE_CREATE_USER_ID
            , ov.SUPPLIER_AC_WORKGROUP_ID
            , ov.BUYER_AC_WORKGROUP_ID
            , ov.OR_ORDER_TYPE_ID
            , ov.PAYMENT_REFERENCE
            , o.PARENT_OR_ORDER_ID
            , ov.CUSTOM_PR_PROPERTY_ID
            , o.IS_CLOSING
            , ov.BU_CLIENT_WORKGROUP_ID
            , ov.RATE
            , ov.EX_CURRENCYID
            , ov.TAX
            , ov.TAX_AC_CURRENCY_ID
            , ov.EXTAX
            , ov.EXTAX_AC_CURRENCY_ID
            , ov.SHIPPING
            , ov.SHIPPING_AC_CURRENCY_ID
            , ov.EXSHIPPING
            , ov.EXSHIPPING_AC_CURRENCY_ID
            , ov.D_OR_S
            , ov.D_OR_S_AC_CURRENCY_ID
            , ov.EX_D_OR_S
            , ov.EX_D_OR_S_AC_CURRENCY_ID
        FROM
            OR_ORDER_VERSION ov
            , OR_ORDER o
            , OR_ORDER_STATE os
            , SY_CONTAINABLE cont
            , PM_PROJECT p
        WHERE ov.OR_ORDER_VERSION_ID = o.OR_ORDER_VERSION_ID
            AND o.PARENT_OR_ORDER_ID = cont.OBJECT_ID
            AND o.OR_ORDER_ID = os.OR_ORDER_ID
            AND cont.OBJECT_CLASS_ID = 1000117
            AND cont.PARENT_OBJECT_CLASS_ID = 1000000
            AND cont.PARENT_OBJECT_ID = #{projectId}
            AND cont.ITEM_OC_OBJECT_STATE_ID != 2500069
            AND os.IS_CURRENT = 1
            AND o.PARENT_OR_ORDER_ID IN
            <foreach collection="orderIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND ov.OR_ORDER_TYPE_ID = 1000002
            AND cont.PARENT_OBJECT_ID = p.PM_PROJECT_ID
            <if test="includePendingSubmissionCO == false">
                AND ((os.OC_OBJECT_STATE_ID = 2000030
                AND p.OWNER_AC_WORKGROUP_ID = ov.BUYER_AC_WORKGROUP_ID) or os.OC_OBJECT_STATE_ID != 2000030)
            </if>
            AND os.OC_OBJECT_STATE_ID not in
            <foreach item="item" index="index" collection="cogFilter" open="(" close=")" separator=",">
                #{item}
            </foreach>
        ORDER BY o.OR_ORDER_ID
    </select>

    <select id="getChangeOrderListByOriginalOrderId" resultMap="orderResultItemMap">
        SELECT
            ov.OR_ORDER_VERSION_ID
            , ov.REFERENCE
            , o.OR_ORDER_ID
            , ov.TITLE
            , os.OC_OBJECT_STATE_ID
            , ov.VERSION_NUMBER
            , ov.COMPLETION_DATE
            , ov.MOD_DATE
            , os.STATE_CREATE_DATE
            , os.LAST_CHANGE_DATE
            , os.STATE_CREATE_USER_ID
            , ov.SUPPLIER_AC_WORKGROUP_ID
            , ov.BUYER_AC_WORKGROUP_ID
            , ov.OR_ORDER_TYPE_ID
            , ov.PAYMENT_REFERENCE
            , o.PARENT_OR_ORDER_ID
            , ov.CUSTOM_PR_PROPERTY_ID
            , o.IS_CLOSING
            , ov.BU_CLIENT_WORKGROUP_ID
            , ov.TAX
            , ov.SHIPPING
            , ov.D_OR_S
        FROM
            OR_ORDER_VERSION ov
            , OR_ORDER o
            , OR_ORDER_STATE os
            , SY_CONTAINABLE cont
            , PM_PROJECT p
        WHERE ov.OR_ORDER_VERSION_ID = o.OR_ORDER_VERSION_ID
            AND o.PARENT_OR_ORDER_ID = cont.OBJECT_ID
            AND o.OR_ORDER_ID = os.OR_ORDER_ID
            AND cont.OBJECT_CLASS_ID = 1000117
            AND cont.PARENT_OBJECT_CLASS_ID = 1000000
            AND cont.PARENT_OBJECT_ID = #{projectId}
            AND cont.ITEM_OC_OBJECT_STATE_ID != 2500069
            AND os.IS_CURRENT = 1
            AND o.PARENT_OR_ORDER_ID = #{originalOrderId}
            AND ov.OR_ORDER_TYPE_ID = 1000002
            AND cont.PARENT_OBJECT_ID = p.PM_PROJECT_ID
            AND ((os.OC_OBJECT_STATE_ID = 2000030
            AND p.OWNER_AC_WORKGROUP_ID = ov.BUYER_AC_WORKGROUP_ID) or os.OC_OBJECT_STATE_ID != 2000030)

        ORDER BY o.OR_ORDER_ID
    </select>

    <select id="findOrderById" resultMap="orderResultMap">
        SELECT
            OV.OR_ORDER_TYPE_ID,
            OV.OR_ORDER_ID,
            OV.REFERENCE,
            OV.PAYMENT_REFERENCE,
            OV.TITLE,
            OV.SUPPLIER_AC_WORKGROUP_ID,
            OV.BUYER_AC_WORKGROUP_ID,
            OS.OC_OBJECT_STATE_ID,
            OREF.PARENT_OR_ORDER_ID,
            BPM.DESCRIPTION_STR_ID
        FROM OR_ORDER_VERSION OV,
        OR_ORDER OREF,
        SY_CONTAINABLE CONT,
        OR_ORDER_STATE OS,
        BU_PAYMENT_METHOD BPM
        WHERE OV.OR_ORDER_VERSION_ID = OREF.OR_ORDER_VERSION_ID
        AND OREF.PARENT_OR_ORDER_ID = CONT.OBJECT_ID
        AND OREF.OR_ORDER_ID = OS.OR_ORDER_ID
        AND OS.IS_CURRENT = 1
        AND OREF.OR_ORDER_ID = #{orderId}
        AND CONT.OBJECT_CLASS_ID = 1000117
        AND CONT.PARENT_OBJECT_ID = #{projectId}
        AND CONT.PARENT_OBJECT_CLASS_ID = 1000000
        AND OV.BU_PAYMENT_METHOD_ID = BPM.BU_PAYMENT_METHOD_ID
    </select>

    <select id="findOriginalOrderByProjectId" resultMap="projectOrderResultMap">
        SELECT
        ov.OR_ORDER_VERSION_ID,
        ov.REFERENCE,
        o.OR_ORDER_ID,
        ov.TITLE,
        os.OC_OBJECT_STATE_ID,
        ov.VERSION_NUMBER,
        ov.COMPLETION_DATE,
        os.STATE_CREATE_DATE as OS_CREATE_DATE,
        os.LAST_CHANGE_DATE,
        ov.SUPPLIER_AC_WORKGROUP_ID,
        ov.BUYER_AC_WORKGROUP_ID,
        ov.OR_ORDER_TYPE_ID,
        ov.PAYMENT_REFERENCE,
        o.PARENT_OR_ORDER_ID,
        ov.CUSTOM_PR_PROPERTY_ID,
        o.is_closing,
        ov.BU_CLIENT_WORKGROUP_ID,
        ov.tax,
        ov.SHIPPING,
        ov.D_OR_S
        FROM
        or_order_version ov,
        or_order o,
        or_order_state os,
        PM_PROJECT prj,
        sy_containable cont
        WHERE
        ov.or_order_version_id = o.or_order_version_id
        AND o.parent_or_order_id = cont.object_id
        AND o.or_order_id = os.or_order_id
        AND ov.OR_ORDER_TYPE_ID != 1000002
        AND os.OC_OBJECT_STATE_ID != 2000079
        AND cont.object_class_id = 1000117
        AND cont.parent_object_class_id = 1000000
        AND cont.parent_object_id = #{projectId}
        AND cont.item_oc_object_state_id != 2500069
        AND cont.PARENT_OBJECT_ID = prj.PM_PROJECT_ID
        AND os.is_current = 1
        /* buy order */
        <if test="isBuy">
            AND ov.BUYER_AC_WORKGROUP_ID = prj.OWNER_AC_WORKGROUP_ID
            AND ov.BUYER_AC_WORKGROUP_ID != ov.SUPPLIER_AC_WORKGROUP_ID
        </if>
        /* sell order */
        <if test="isSell">
            AND  ov.SUPPLIER_AC_WORKGROUP_ID = prj.OWNER_AC_WORKGROUP_ID
        </if>
    </select>

    <select id="findChangeOrderByParentOrderId" resultMap="projectOrderResultMap">
        SELECT
        OV.OR_ORDER_VERSION_ID
        , OV.REFERENCE
        , O.OR_ORDER_ID
        , OV.TITLE
        , OS.OC_OBJECT_STATE_ID
        , OV.VERSION_NUMBER
        , OV.COMPLETION_DATE
        , OV.SUPPLIER_AC_WORKGROUP_ID
        , OV.BUYER_AC_WORKGROUP_ID
        , OV.OR_ORDER_TYPE_ID
        , OV.PAYMENT_REFERENCE
        , O.PARENT_OR_ORDER_ID
        , OV.CUSTOM_PR_PROPERTY_ID
        , O.IS_CLOSING
        , OV.BU_CLIENT_WORKGROUP_ID
        , OV.TAX
        , OV.SHIPPING
        , OS.STATE_CREATE_DATE as OS_CREATE_DATE,
        OS.LAST_CHANGE_DATE,
        ov.D_OR_S
        FROM OR_ORDER_VERSION OV,
        OR_ORDER O,
        OR_ORDER_STATE OS
        WHERE OV.OR_ORDER_VERSION_ID = O.OR_ORDER_VERSION_ID
        AND O.OR_ORDER_ID = OS.OR_ORDER_ID
        AND OS.IS_CURRENT = 1
        AND OV.OR_ORDER_TYPE_ID = 1000002
        AND O.PARENT_OR_ORDER_ID = #{orderId}
        ORDER BY OV.OR_ORDER_ID
    </select>

    <select id="getProjectOrderListWithBuyCheck" resultMap="projectOrderResultMap">
        select
        ov.OR_ORDER_VERSION_ID,
        ov.REFERENCE, o.OR_ORDER_ID,
        ov.TITLE,
        os.OC_OBJECT_STATE_ID,
        OS.STATE_CREATE_USER_ID,
        ov.VERSION_NUMBER,
        ov.COMPLETION_DATE,
        os.STATE_CREATE_DATE as OS_CREATE_DATE,
        os.LAST_CHANGE_DATE,
        ov.SUPPLIER_AC_WORKGROUP_ID,
        ov.BUYER_AC_WORKGROUP_ID,
        ov.OR_ORDER_TYPE_ID,
        ov.PAYMENT_REFERENCE,
        o.PARENT_OR_ORDER_ID,
        ov.CUSTOM_PR_PROPERTY_ID,
        o.is_closing,
        ov.BU_CLIENT_WORKGROUP_ID,
        ov.tax,
        ov.TAX_AC_CURRENCY_ID,
        ov.SHIPPING,
        ov.SHIPPING_AC_CURRENCY_ID,
        ov.D_OR_S,
        ov.D_OR_S_AC_CURRENCY_ID,
        ov.RATE,
        ov.EX_CURRENCYID,
        ov.EXTAX,
        ov.EXTAX_AC_CURRENCY_ID,
        ov.EXSHIPPING,
        ov.EXSHIPPING_AC_CURRENCY_ID,
        ov.EX_D_OR_S,
        ov.EX_D_OR_S_AC_CURRENCY_ID
        from or_order_version ov,
        or_order o,
        or_order_state os,
        sy_containable cont,
        pm_project pm
        where ov.or_order_version_id = o.or_order_version_id
        and o.parent_or_order_id = cont.object_id
        and o.or_order_id = os.or_order_id
        and cont.object_class_id = 1000117
        and cont.parent_object_class_id = 1000000
        and cont.parent_object_id = #{projectId}
        and cont.item_oc_object_state_id != 2500069
        and os.is_current = 1
        and pm.pm_project_id = cont.parent_object_id
        /* all order */
        <if test="type == 'all'">
        and ((os.OC_OBJECT_STATE_ID = 2000030 and pm.owner_ac_workgroup_id = ov.BUYER_AC_WORKGROUP_ID)
        or os.OC_OBJECT_STATE_ID != 2000030)
        </if>
        /* buy order */
        <if test="type == 'buy'">
            AND ov.BUYER_AC_WORKGROUP_ID = pm.OWNER_AC_WORKGROUP_ID
            <if test="isClientNotOnNoosh == true">
                and ov.BU_CLIENT_WORKGROUP_ID is null
            </if>
        </if>
        /* sell order */
        <if test="type == 'sell'">
            AND ov.SUPPLIER_AC_WORKGROUP_ID = pm.OWNER_AC_WORKGROUP_ID
            <if test="isClientNotOnNoosh == true">
                and ov.BU_CLIENT_WORKGROUP_ID is not null
            </if>
        </if>
        AND os.OC_OBJECT_STATE_ID not in
        <foreach item="item" index="index" collection="cogFilter" open="(" close=")" separator=",">
            #{item}
        </foreach>
        order by o.create_date desc
    </select>

    <select id="countTotalProjectOrderListWithBuyCheck" resultType="java.lang.Long">
        select
        count(*)
        from or_order_version ov,
        or_order o,
        or_order_state os,
        sy_containable cont,
        pm_project pm
        where ov.or_order_version_id = o.or_order_version_id
        and ov.or_order_type_id != 1000002
        and o.parent_or_order_id = cont.object_id
        and o.or_order_id = os.or_order_id
        and cont.object_class_id = 1000117
        and cont.parent_object_class_id = 1000000
        and cont.parent_object_id = #{projectId}
        and cont.item_oc_object_state_id != 2500069
        and os.is_current = 1
        and pm.pm_project_id = cont.parent_object_id
        /* all order */
        <if test="type == 'all'">
        and ((os.OC_OBJECT_STATE_ID = 2000030 and pm.owner_ac_workgroup_id = ov.BUYER_AC_WORKGROUP_ID)
        or os.OC_OBJECT_STATE_ID != 2000030)
        </if>
        /* buy order */
        <if test="type == 'buy'">
            AND ov.BUYER_AC_WORKGROUP_ID = pm.OWNER_AC_WORKGROUP_ID
            <if test="isClientNotOnNoosh == true">
                and ov.BU_CLIENT_WORKGROUP_ID is null
            </if>
        </if>
        /* sell order */
        <if test="type == 'sell'">
            AND ov.SUPPLIER_AC_WORKGROUP_ID = pm.OWNER_AC_WORKGROUP_ID
            <if test="isClientNotOnNoosh == true">
                and ov.BU_CLIENT_WORKGROUP_ID is not null
            </if>
        </if>
        
    </select>

    <select id="findOrdersByProjectId" resultMap="orderResultMap">
        SELECT
        OV.OR_ORDER_ID
        , OV.REFERENCE
        , OV.TITLE
        , OS.OC_OBJECT_STATE_ID
        , OBS.DESCRIPTION_STR_ID
        FROM OR_ORDER_VERSION OV,
        OR_ORDER OREF,
        SY_CONTAINABLE C,
        OR_ORDER_STATE OS,
        OC_OBJECT_STATE OBS
        WHERE
        OV.OR_ORDER_VERSION_ID = OREF.OR_ORDER_VERSION_ID
        AND OREF.OR_ORDER_ID = OS.OR_ORDER_ID
        AND OS.IS_CURRENT = 1
        AND OREF.OR_ORDER_ID = C.OBJECT_ID
        AND C.OBJECT_CLASS_ID = 1000117
        AND OV.OR_ORDER_TYPE_ID != 1000002
        AND C.PARENT_OBJECT_ID = #{projectId}
        AND C.PARENT_OBJECT_CLASS_ID = 1000000
        AND OV.SUPPLIER_AC_WORKGROUP_ID = #{ownerWorkgroupId}
        AND OS.OC_OBJECT_STATE_ID = OBS.OC_OBJECT_STATE_ID
        <if test="isClientNotOnNoosh">
            AND OV.BU_CLIENT_WORKGROUP_ID is not null
        </if>
        <if test="isForCreateInvoice">
            AND OS.OC_OBJECT_STATE_ID not in (2000080, 2000084)
        </if>
    </select>
    
    <select id="checkOrderTitleExists" resultType="java.lang.Long">
        select count(*) as cnt
        from
        or_order o
        inner join or_order_version a on o.or_order_id = a.or_order_id
        inner join sy_containable c
        on o.parent_or_order_id = c.object_id
        and (c.object_class_id = 1000117
        or c.object_class_id = 1000118)
        and c.parent_object_class_id = 1000000
        inner join pm_project p on c.parent_object_id = p.pm_project_id
        where
        a.is_current = 1
        and a.title is not null
        and a.title = #{title}
        and o.parent_or_order_id != #{parentObjectId}
        and p.pm_project_id = #{projectId}
    </select>

    <select id="getOriginalOrderItemIdList" resultType="java.lang.Long">
        SELECT DISTINCT OOI.OR_ORDER_ITEM_ID
        FROM
        OR_ORDER_VERSION ov
        , OR_ORDER o
        , OR_ORDER_STATE os
        , SY_CONTAINABLE cont
        , PM_PROJECT p
        , OR_ORDER_ITEM OOI
        WHERE ov.OR_ORDER_VERSION_ID = o.OR_ORDER_VERSION_ID
        AND o.PARENT_OR_ORDER_ID = cont.OBJECT_ID
        AND o.OR_ORDER_ID = os.OR_ORDER_ID
        AND cont.OBJECT_CLASS_ID = 1000117
        AND cont.PARENT_OBJECT_CLASS_ID = 1000000
        AND cont.PARENT_OBJECT_ID = #{projectId}
        AND p.PM_PROJECT_ID = cont.PARENT_OBJECT_ID
        AND cont.ITEM_OC_OBJECT_STATE_ID != 2500069
        AND os.IS_CURRENT = 1
        AND o.PARENT_OR_ORDER_ID = o.OR_ORDER_ID
        AND OOI.OR_ORDER_VERSION_ID = ov.OR_ORDER_VERSION_ID
        AND os.OC_OBJECT_STATE_ID not in
        <foreach item="item" index="index" collection="cogFilter" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="orderType == 'buy'">
            and  ov.BUYER_AC_WORKGROUP_ID = p.OWNER_AC_WORKGROUP_ID
            <if test="isClientNotOnNoosh == true">
                and ov.BU_CLIENT_WORKGROUP_ID is null
            </if>
        </if>
        <if test="orderType == 'sell'">
            and  ov.SUPPLIER_AC_WORKGROUP_ID = p.OWNER_AC_WORKGROUP_ID
            <if test="isClientNotOnNoosh == true">
                and ov.BU_CLIENT_WORKGROUP_ID is not null
            </if>
        </if>
    </select>

</mapper>