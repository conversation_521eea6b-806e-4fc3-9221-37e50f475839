<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.localization.LocalizationMyBatisMapper">
    <resultMap id="localizationResultMap" type="com.noosh.app.commons.dto.localization.LocalizationDTO">
        <id property="id" column="NO_LOCALIZATION_ID"/>
        <result property="portal" column="PORTAL" />
        <result property="language" column="LANGUAGE" />
        <result property="resourceName" column="RESOURCE_NAME" />
        <result property="resourceValue" column="RESOURCE_VALUE" />
    </resultMap>

    <resultMap id="portalResultMap" type="com.noosh.app.commons.dto.localization.PortalDTO">
        <id property="id" column="NO_PORTAL_ID"/>
        <result property="name" column="NAME" />
    </resultMap>

    <select id="getLocalizationList" resultMap="localizationResultMap" >
        SELECT  NO_LOCALIZATION_ID, PORTAL, LANGUAGE, RESOURCE_NAME , RESOURCE_VALUE FROM NO_LOCALIZATION
        where 1 = 1
        <if test="language != null and language.length > 0">
            AND upper(LANGUAGE) = upper(#{language})
        </if>
        <if test="portal != null and portal.length > 0">
            AND upper(portal) = UPPER(#{portal})
        </if>
        <if test="resourceName != null and resourceName.length > 0">
            AND upper(RESOURCE_NAME) LIKE '%' || UPPER(#{resourceName}) || '%'
        </if>
        <if test="resourceValue != null and resourceValue.length > 0">
            AND upper(RESOURCE_VALUE) LIKE '%' || UPPER(#{resourceValue}) || '%'
        </if>
        <if test="ids != null and ids.size() > 0">
            AND NO_LOCALIZATION_ID in
            <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="deleteByPortal">
        delete from NO_LOCALIZATION WHERE portal= #{portal}
    </select>

    <select id="updateByPortal">
        update NO_LOCALIZATION set portal = #{newPortal} WHERE portal= #{oldPortal}
    </select>

    <select id="getFullLocalization" resultMap="localizationResultMap">
        select NO_LOCALIZATION_ID, PORTAL, LANGUAGE, RESOURCE_NAME , RESOURCE_VALUE FROM (
        select NO_LOCALIZATION_ID, PORTAL, LANGUAGE, RESOURCE_NAME , RESOURCE_VALUE from (SELECT  NO_LOCALIZATION_ID, PORTAL, LANGUAGE, RESOURCE_NAME , RESOURCE_VALUE FROM NO_LOCALIZATION
        WHERE 1= 1
        <if test="language != null">
            AND upper(LANGUAGE) = upper(#{language})
        </if>
        <if test="portal != null">
            AND upper(portal) = UPPER(#{portal})
        </if>
            ORDER BY RESOURCE_NAME) full_data
        <if test="language != null and (!language.equalsIgnoreCase('en_US'))">
            UNION ALL
            SELECT n1.NO_LOCALIZATION_ID, n1.PORTAL, n1.LANGUAGE, n1.RESOURCE_NAME , n1.RESOURCE_VALUE FROM NO_localization n1
            WHERE upper(n1.LANGUAGE) = upper('en_US')
            <if test="portal != null">
                AND n1.portal = #{portal}
            </if>
            AND NOT EXISTS (
            SELECT 'x' FROM NO_LOCALIZATION n2
            WHERE upper(n2.LANGUAGE) = upper(#{language})
            <if test="portal != null">
                AND upper(n2.portal) = UPPER(#{portal})
            </if>
            AND n2.RESOURCE_NAME = n1.RESOURCE_NAME)
        </if>
        <if test="isNeedDefault">
            UNION ALL
            SELECT n1.NO_LOCALIZATION_ID, n1.PORTAL, n1.LANGUAGE, n1.RESOURCE_NAME , n1.RESOURCE_VALUE
            FROM NO_localization n1
            WHERE n1.portal = 'default'
            <if test="language != null">
                AND upper(n1.LANGUAGE) = upper(#{language})
            </if>
            AND NOT EXISTS (
            SELECT 'x' FROM NO_LOCALIZATION n2 WHERE 1= 1
            <if test="language != null">
                AND upper(n2.LANGUAGE) = upper(#{language})
            </if>
            <if test="portal != null">
                AND upper(n2.portal) = UPPER(#{portal})
            </if>
            AND n2.RESOURCE_NAME = n1.RESOURCE_NAME)
        </if>
        <if test="language != null and (!language.equalsIgnoreCase('en_US')) and isNeedDefault">
            UNION ALL
            select NO_LOCALIZATION_ID, PORTAL, LANGUAGE, RESOURCE_NAME , RESOURCE_VALUE from (
            SELECT n1.NO_LOCALIZATION_ID, n1.PORTAL, n1.LANGUAGE, n1.RESOURCE_NAME , n1.RESOURCE_VALUE
            FROM NO_localization n1 WHERE n1.portal = 'default' AND n1.LANGUAGE = 'en_US' AND NOT EXISTS (
            SELECT 'x' FROM NO_LOCALIZATION n2 WHERE
            (upper(n2.PORTAL) = UPPER('default')
            <if test="portal != null">
                OR upper(n2.portal) = UPPER(#{portal})
            </if>)
            AND upper(n2.LANGUAGE) = upper(#{language}) AND n2.RESOURCE_NAME = n1.RESOURCE_NAME)
            ORDER BY n1.RESOURCE_NAME) full_data_1
        </if>
        )  full_data_3 where 1 = 1
        <if test="ids != null and ids.size() > 0">
            AND NO_LOCALIZATION_ID in
            <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="deleteByIds">
        delete from NO_LOCALIZATION WHERE 1= 1
        <if test="ids != null and ids.size() > 0">
            AND NO_LOCALIZATION_ID in
            <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="getPortalList" resultMap="portalResultMap">
        select NO_PORTAL_ID, NAME from NO_PORTAL where 1 = 1
        <if test="name != null and name.length > 0 ">
            and upper(name) like '%' || upper(#{name}) || '%'
        </if>
    </select>
</mapper>