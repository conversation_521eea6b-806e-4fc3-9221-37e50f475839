<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.BaseUtilMyBatisMapper">

    <resultMap id="propertyAttributeDtoResultMap" type="com.noosh.app.commons.dto.custom.CustomAttributeDTO">
        <result property="propertyId" column="pr_property_id"/>
        <result property="propertyAttributeId" column="pr_property_attribute_id"/>
        <result property="paramName" column="param_name"/>
        <result property="dataTypeId" column="pr_data_type_id"/>
        <result property="numberValue" column="number_value"/>
        <result property="stringValue" column="string_value"/>
        <result property="dateValue" column="date_value"
                typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
    </resultMap>

    <sql id="idsInSQL">
        (
        ${idFieldName} IN
        <trim suffixOverrides=" OR ${idFieldName} IN ()">
            <foreach item="item" index="index" collection="${ids}"
                     open="(" close=")">
                <if test="index != 0">
                    <choose>
                        <when test="index % 1000 == 999">) OR ${idFieldName} IN (</when>
                        <otherwise>,</otherwise>
                    </choose>
                </if>
                #{item}
            </foreach>
        </trim>
        )
    </sql>

</mapper>
