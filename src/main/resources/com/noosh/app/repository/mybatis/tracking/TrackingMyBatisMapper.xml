<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.noosh.app.repository.mybatis.tracking.TrackingMyBatisMapper">

    <resultMap id="TrackingListResultMap" type="com.noosh.app.commons.dto.tracking.WGTrackingDTO" >
        <result property="id" column="NO_WG_TRACKING_ID" jdbcType="DECIMAL" />
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="trackingTypeId" column="TR_TRACKING_TYPE_ID" jdbcType="DECIMAL" />
        <result property="createDate" column="CREATE_DATE" typeHandler = "com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="i18nData" column="I18N_DATA" jdbcType="VARCHAR"/>
        <result property="comments" column="COMMENTS" jdbcType="VARCHAR"/>
        <result property="nameStrId" column="NAME_STR_ID" jdbcType="DECIMAL"/>
        <result property="descStrId" column="DESCRIPTION_STR_ID" jdbcType="DECIMAL"/>
        <result property="customNameStr" column="CUSTOM_NAME_STR" jdbcType="VARCHAR"/>
        <result property="constantToken" column="CONSTANT_TOKEN" jdbcType="VARCHAR"/>
        <result property="isUserPosted" column="IS_USER_POSTABLE" jdbcType="DECIMAL"/>
        <result property="objectClassId" column="TRACKING_OBJECT_CLASS_ID" jdbcType="DECIMAL"/>
        <result property="objectId" column="TRACKING_OBJECT_ID" jdbcType="DECIMAL"/>
    </resultMap>

    <select id="getTrackingByWorkgroup" resultMap="TrackingListResultMap">
        select TR.TR_TRACKING_TYPE_ID, TR.NO_WG_TRACKING_ID, TR.CREATE_DATE, TR.I18N_DATA, TR.COMMENTS, TRTYPE.CONSTANT_TOKEN,
        TRTYPE.NAME_STR_ID, TRTYPE.DESCRIPTION_STR_ID, TRTYPE.CUSTOM_NAME_STR, PERSON.FIRST_NAME || ' ' || PERSON.LAST_NAME as creator,
        TRTYPE.IS_USER_POSTABLE, TR.TRACKING_OBJECT_CLASS_ID, TR.TRACKING_OBJECT_ID
        FROM NO_WG_TRACKING TR
        left join TR_TRACKING_TYPE TRTYPE ON  TR.TR_TRACKING_TYPE_ID = TRTYPE.TR_TRACKING_TYPE_ID
        left join AC_ACCOUNT_USER U ON U.USER_ID = TR.CREATE_USER_ID
        left join AC_PERSON PERSON ON PERSON.AC_PERSON_ID = U.AC_PERSON_ID
        WHERE TR.TRACKING_OBJECT_CLASS_ID = 1000100 AND TR.TRACKING_OBJECT_ID = #{workgroupId}
        <if test="startDate != null">
           and TR.CREATE_DATE  &gt;=to_date(#{startDate}, 'MM/DD/YYYY HH24:MI:SS')
        </if>
        <if test="endDate != null">
            and   TR.CREATE_DATE  &lt;=to_date(#{endDate}, 'MM/DD/YYYY HH24:MI:SS')
        </if>
        <if test="typeIds != null and typeIds.size > 0 and (!typeIds.contains(-1L))">
            AND TR.TR_TRACKING_TYPE_ID in
            <foreach item="item" index="index" collection="typeIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="order != null and order == 'desc'">
            ORDER BY TR.CREATE_DATE DESC NULLS LAST
        </if>
        <if test="order != null and order == 'asc'">
            ORDER BY TR.CREATE_DATE ASC NULLS LAST
        </if>
    </select>
</mapper>