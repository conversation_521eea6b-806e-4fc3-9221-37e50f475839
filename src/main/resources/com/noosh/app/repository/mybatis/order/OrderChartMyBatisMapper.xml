<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.noosh.app.repository.mybatis.order.OrderChartMyBatisMapper">

    <resultMap id="projectChartResultMap" type="com.noosh.app.commons.vo.chart.ProjectChartItemVO">
        <result property="avgType" column="avg_type"/>
        <result property="days" column="days"/>
    </resultMap>

    <resultMap id="orderChartResultMap" type="com.noosh.app.commons.vo.chart.OrderChartItemVO">
        <result property="avgType" column="avg_type"/>
        <result property="orderCreateDays" column="each_buy_order_awarded"/>
        <result property="orderAcceptDays" column="each_buy_order_accepted"/>
        <result property="orderCloseDays" column="each_buy_order_closed"/>
    </resultMap>

    <resultMap id="invoiceChartResultMap" type="com.noosh.app.commons.vo.chart.InvoiceForecastChartItemVO">
        <result property="type" column="type"/>
        <result property="value" column="value"/>
    </resultMap>

    <resultMap id="projectRfeResultMap" type="com.noosh.app.commons.vo.chart.ProjectRFESentVO">
        <result property="projectName" column="projectName"/>
        <result property="projectCreateDate" column="projectCreateDate" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="rfeSentDate" column="rfeSentDate" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="rfeName" column="rfeName"/>
    </resultMap>

    <resultMap id="projectOrderAwardResultMap" type="com.noosh.app.commons.vo.chart.ProjectBuyOrderVO">
        <result property="projectName" column="projectName"/>
        <result property="projectCreateDate" column="projectCreateDate" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="orderCreateDate" column="order_create_date" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="orderAcceptDate" column="ACCEPT_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="orderClosedDate" column="CLOSED_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="orderName" column="orderName"/>
    </resultMap>

    <resultMap id="projectInvoiceResultMap" type="com.noosh.app.commons.vo.chart.ProjectInvoiceVO">
        <result property="projectName" column="projectName"/>
        <result property="projectCreateDate" column="projectCreateDate" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="invoiceCreateDate" column="SUBMIT_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="invoiceAcceptDate" column="ACCEPTED_DATE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="invoiceRef" column="invoiceRef"/>
    </resultMap>

    <select id="findProjectInvoiceDetailInfo" resultMap="projectInvoiceResultMap">
        <if test="type == 'isFirstAccept'">
            select * from (
            select x.SUBMIT_DATE, x.project_create_date projectCreateDate, x.X_PARENT_TITLE projectName, x.invoiceRef,
            row_number() over(partition by x.pm_project_id order by x.SUBMIT_DATE asc) rn
            from (
            select ov.pc_invoice_id, ov.SUBMIT_DATE, pm.project_create_date, pm.pm_project_id, c.X_PARENT_TITLE, ov.REFERENCE invoiceRef
            from pc_invoice ov
            inner join sy_containable c on c.object_id = ov.pc_invoice_id and c.object_class_id = 1000119 and c.PARENT_OBJECT_CLASS_ID = 1000000
            inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
            where pm.owner_ac_workgroup_id = #{workgroupId}
            and ov.SUBMIT_DATE is not null
            AND ov.SUBMIT_DATE &gt;= SYSDATE - #{filter}
            ) x
            )
            where rn = 1
        </if>
        <if test="type == 'isLastAccept'">
            select * from (
            select x.ACCEPTED_DATE, x.project_create_date projectCreateDate, x.X_PARENT_TITLE projectName, x.invoiceRef,
            row_number() over(partition by x.pm_project_id order by x.ACCEPTED_DATE desc) rn
            from (
            select ov.pc_invoice_id, ov.ACCEPTED_DATE, pm.project_create_date, pm.pm_project_id, c.X_PARENT_TITLE, ov.REFERENCE invoiceRef
            from pc_invoice ov
            inner join sy_containable c on c.object_id = ov.pc_invoice_id and c.object_class_id = 1000119 and c.PARENT_OBJECT_CLASS_ID = 1000000
            inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
            where pm.owner_ac_workgroup_id = #{workgroupId}
            and ov.ACCEPTED_DATE is not null
            AND ov.ACCEPTED_DATE &gt;= SYSDATE - #{filter}
            ) x
            )
            where rn = 1
        </if>

    </select>

    <select id="findProjectOrderDetailInfo" resultMap="projectOrderAwardResultMap">
          <if test="type == 'isAward'">
              select * from (
              select x.order_create_date, x.project_create_date projectCreateDate, x.X_PARENT_TITLE projectName, x.orderName,
              row_number() over(partition by x.pm_project_id order by x.order_create_date asc) rn
              from (
              select ov.or_order_id, ov.order_create_date, pm.project_create_date, pm.pm_project_id, c.X_PARENT_TITLE, DECODE(ov.title, NULL, ov.REFERENCE, ov.title) orderName
              from or_order_version ov
              inner join sy_containable c on c.object_id = ov.or_order_id and c.object_class_id in (1000117, 2500785) and c.PARENT_OBJECT_CLASS_ID = 1000000
              inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
              where pm.owner_ac_workgroup_id = #{workgroupId}
              and ov.or_order_type_id in (1000000, 1000001)
              and ov.order_create_date is not null
              and ov.ACCEPT_DATE is not null
              AND ov.order_create_date &gt;= SYSDATE - #{filter}
              ) x
              )
              where rn = 1
          </if>
        <if test="type == 'isAccept'">
            select * from (
            select x.ACCEPT_DATE, x.project_create_date projectCreateDate, x.X_PARENT_TITLE projectName, x.orderName,
            row_number() over(partition by x.pm_project_id order by x.ACCEPT_DATE asc) rn
            from (
            select ov.or_order_id, ov.ACCEPT_DATE, pm.project_create_date, pm.pm_project_id, c.X_PARENT_TITLE, DECODE(ov.title, NULL, ov.REFERENCE, ov.title) orderName
            from or_order_version ov
            inner join OR_ORDER_STATE oros on oros.or_order_id = ov.or_order_id and oros.oc_object_state_id = 2000078
            inner join sy_containable c on c.object_id = ov.or_order_id and c.object_class_id in (1000117, 2500785) and c.PARENT_OBJECT_CLASS_ID = 1000000
            inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
            where pm.owner_ac_workgroup_id = #{workgroupId}
            and ov.or_order_type_id in (1000000, 1000001)
            and ov.ACCEPT_DATE is not null
            AND ov.ACCEPT_DATE &gt;= SYSDATE - #{filter}
            ) x
            )
            where rn = 1
        </if>

        <if test="type == 'isClosed'">
            select * from (
            select x.state_create_date CLOSED_DATE,  x.project_create_date projectCreateDate, x.X_PARENT_TITLE projectName, x.orderName,
            row_number() over(partition by x.pm_project_id order by x.state_create_date asc) rn
            from (
            select ov.or_order_id, oros.state_create_date, pm.project_create_date, pm.pm_project_id, c.X_PARENT_TITLE, DECODE(ov.title, NULL, ov.REFERENCE, ov.title) orderName
            from or_order_version ov
            inner join OR_ORDER_STATE oros on oros.or_order_id = ov.or_order_id and oros.oc_object_state_id = 2500045
            inner join sy_containable c on c.object_id = ov.or_order_id and c.object_class_id in (1000117, 2500785) and c.PARENT_OBJECT_CLASS_ID = 1000000
            inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
            where pm.owner_ac_workgroup_id = #{workgroupId}
            and ov.or_order_type_id in (1000000, 1000001)
            AND oros.state_create_date &gt;= SYSDATE - #{filter}
            ) x
            )
            where rn = 1
        </if>

        <if test="type == 'isQuoteOrderAward'">
            select * from (
            select x.order_create_date, x.project_create_date projectCreateDate, x.X_PARENT_TITLE projectName, x.orderName,
            row_number() over(partition by x.pm_project_id order by x.order_create_date asc) rn
            from (
            select ov.PC_QUOTE_ID , ov.SUBMIT_DATE order_create_date, pm.project_create_date, pm.pm_project_id, c.X_PARENT_TITLE, DECODE(ov.TITLE, NULL, ov.REFERENCE , ov.title) orderName
            from PC_QUOTE ov
            inner join sy_containable c on c.object_id = ov.PC_QUOTE_ID and c.object_class_id = 1000013 and c.PARENT_OBJECT_CLASS_ID = 1000000
            inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
            where pm.owner_ac_workgroup_id = #{workgroupId}
            and ov.SUBMIT_DATE is not null
            AND ov.SUBMIT_DATE &gt;= SYSDATE - #{filter}
            ) x
            )
            where rn = 1
        </if>

    </select>
    
    <select id="findProjectRFEDetailInfo" resultMap="projectRfeResultMap">
          select * from (
            select x.submit_date rfeSentDate, x.project_create_date projectCreateDate, x.X_PARENT_TITLE projectName,
            x.title rfeName,
            row_number() over(partition by x.pm_project_id order by x.submit_date asc) rn
            from (
              select ov.em_rfe_id, ov.submit_date, pm.project_create_date, pm.pm_project_id, c.X_PARENT_TITLE, ov.title
              from em_rfe ov
              inner join sy_containable c on c.object_id = ov.em_rfe_id and c.object_class_id = 1000115 and c.PARENT_OBJECT_CLASS_ID = 1000000
              inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
              where pm.owner_ac_workgroup_id = #{workgroupId}
              and ov.submit_date is not null
              AND ov.submit_date &gt;= SYSDATE - #{filter}
            ) x
          )
          where rn = 1
    </select>

    <select id="findProjectEfficiency" resultMap="projectChartResultMap">
        <if test="isClient == false">
        select 'first_rfe_sent' avg_type, sum(delta)/count(pm_project_id) days
        from (
          select * from (
            select x.submit_date - x.project_create_date delta, x.pm_project_id,
            row_number() over(partition by x.pm_project_id order by x.submit_date asc) rn
            from (
              select ov.em_rfe_id, ov.submit_date, pm.project_create_date, pm.pm_project_id
              from em_rfe ov
              inner join sy_containable c on c.object_id = ov.em_rfe_id and c.object_class_id = 1000115 and c.PARENT_OBJECT_CLASS_ID = 1000000
              inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
              where pm.owner_ac_workgroup_id = #{workgroupId}
              and ov.submit_date is not null
              AND ov.submit_date &gt;= SYSDATE - #{filter}
            ) x
          )
          where rn = 1
        ) avg_x

        union all
        </if>
        select 'first_buy_order_awarded' avg_type, sum(delta)/count(pm_project_id) days
        from (
          select * from (
            select x.order_create_date - x.project_create_date delta, x.pm_project_id,
            row_number() over(partition by x.pm_project_id order by x.order_create_date asc) rn
            from (
              select ov.or_order_id, ov.order_create_date, pm.project_create_date, pm.pm_project_id
              from or_order_version ov 
              inner join sy_containable c on c.object_id = ov.or_order_id and c.object_class_id in (1000117, 2500785) and c.PARENT_OBJECT_CLASS_ID = 1000000 
              inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
              where pm.owner_ac_workgroup_id = #{workgroupId}
              and ov.or_order_type_id in (1000000, 1000001)
              and ov.order_create_date is not null
              and ov.ACCEPT_DATE is not null
              AND ov.order_create_date &gt;= SYSDATE - #{filter}
            ) x
          )
          where rn = 1
        ) avg_x
        
        union all
        
        select 'first_buy_order_accepted' avg_type, sum(delta)/count(pm_project_id) days
        from (
          select * from (
            select x.ACCEPT_DATE - x.project_create_date delta, x.pm_project_id, 
            row_number() over(partition by x.pm_project_id order by x.ACCEPT_DATE asc) rn
            from (
              select ov.or_order_id, ov.ACCEPT_DATE, pm.project_create_date, pm.pm_project_id
              from or_order_version ov 
              inner join OR_ORDER_STATE oros on oros.or_order_id = ov.or_order_id and oros.oc_object_state_id = 2000078
              inner join sy_containable c on c.object_id = ov.or_order_id and c.object_class_id in (1000117, 2500785) and c.PARENT_OBJECT_CLASS_ID = 1000000 
              inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
              where pm.owner_ac_workgroup_id = #{workgroupId}
              and ov.or_order_type_id in (1000000, 1000001)
              and ov.ACCEPT_DATE is not null
              AND ov.ACCEPT_DATE &gt;= SYSDATE - #{filter}
            ) x
          )
          where rn = 1
        ) avg_x
        
        union all
        
        select 'first_buy_order_closed' avg_type, sum(delta)/count(pm_project_id) days
        from (
          select * from (
            select x.state_create_date - x.project_create_date delta, x.pm_project_id, 
            row_number() over(partition by x.pm_project_id order by x.state_create_date asc) rn
            from (
              select ov.or_order_id, oros.state_create_date, pm.project_create_date, pm.pm_project_id
              from or_order_version ov 
              inner join OR_ORDER_STATE oros on oros.or_order_id = ov.or_order_id and oros.oc_object_state_id = 2500045
              inner join sy_containable c on c.object_id = ov.or_order_id and c.object_class_id in (1000117, 2500785) and c.PARENT_OBJECT_CLASS_ID = 1000000 
              inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
              where pm.owner_ac_workgroup_id = #{workgroupId}
              and ov.or_order_type_id in (1000000, 1000001)
              AND oros.state_create_date &gt;= SYSDATE - #{filter}
            ) x
          )
          where rn = 1
        ) avg_x
        
        union all
        
        select 'first_invoice_accept' avg_type, sum(delta)/count(pm_project_id) days
        from (
          select * from (
            select x.SUBMIT_DATE - x.project_create_date delta, x.pm_project_id,
            row_number() over(partition by x.pm_project_id order by x.SUBMIT_DATE asc) rn
            from (
              select ov.pc_invoice_id, ov.SUBMIT_DATE, pm.project_create_date, pm.pm_project_id
              from pc_invoice ov 
              inner join sy_containable c on c.object_id = ov.pc_invoice_id and c.object_class_id = 1000119 and c.PARENT_OBJECT_CLASS_ID = 1000000 
              inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
              where pm.owner_ac_workgroup_id = #{workgroupId}
              and ov.SUBMIT_DATE is not null
              AND ov.SUBMIT_DATE &gt;= SYSDATE - #{filter}
            ) x
          )
          where rn = 1
        ) avg_x
        
        union all
        
        select 'last_invoice_accept' avg_type, sum(delta)/count(pm_project_id) days
        from (
          select * from (
            select x.ACCEPTED_DATE - x.project_create_date delta, x.pm_project_id,  
            row_number() over(partition by x.pm_project_id order by x.ACCEPTED_DATE desc) rn
            from (
              select ov.pc_invoice_id, ov.ACCEPTED_DATE, pm.project_create_date, pm.pm_project_id
              from pc_invoice ov 
              inner join sy_containable c on c.object_id = ov.pc_invoice_id and c.object_class_id = 1000119 and c.PARENT_OBJECT_CLASS_ID = 1000000 
              inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
              where pm.owner_ac_workgroup_id = #{workgroupId}
              and ov.ACCEPTED_DATE is not null
              AND ov.ACCEPTED_DATE &gt;= SYSDATE - #{filter}
            ) x
          )
          where rn = 1
        ) avg_x

        union all

        select 'first_quote_order_awarded' avg_type, sum(delta)/count(pm_project_id) days
        from (
          select * from (
            select x.order_create_date - x.project_create_date delta, x.pm_project_id,
            row_number() over(partition by x.pm_project_id order by x.order_create_date asc) rn
            from (
              select ov.PC_QUOTE_ID , ov.SUBMIT_DATE order_create_date, pm.project_create_date, pm.pm_project_id
            from PC_QUOTE ov
            inner join sy_containable c on c.object_id = ov.PC_QUOTE_ID and c.object_class_id = 1000013 and c.PARENT_OBJECT_CLASS_ID = 1000000
            inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
            where pm.owner_ac_workgroup_id = #{workgroupId}
            and ov.SUBMIT_DATE is not null
            AND ov.SUBMIT_DATE &gt;= SYSDATE - #{filter}
            ) x
          )
          where rn = 1
        ) avg_x
    </select>

    <select id="findTimeToOrder" resultMap="orderChartResultMap">
SELECT ap.first_name || ' ' || ap.last_name AS avg_type, o1.each_buy_order_awarded, o2.each_buy_order_accepted, o3.each_buy_order_closed
        FROM (SELECT count(ov.CREATE_USER_ID) totalNum, ov.CREATE_USER_ID
        	from or_order_version ov WHERE ov.BUYER_AC_WORKGROUP_ID = #{workgroupId}  AND ov.CREATE_DATE &gt;= SYSDATE - #{filter}
            and ov.or_order_type_id in (1000000, 1000001)
            and ov.order_create_date is not NULL AND ov.VERSION_NUMBER =1
        	GROUP BY ov.CREATE_USER_ID ORDER BY totalNum fetch  first 10 rows ONLY) pm
        LEFT JOIN
        (select sum(delta)/count(createUserId) AS each_buy_order_awarded, createUserId
        from (
            select ov.order_create_date - pm.project_create_date delta, ov.create_user_id createUserId
            from or_order_version ov
            inner join OR_ORDER_STATE oros on oros.or_order_id = ov.or_order_id and oros.oc_object_state_id = 2000079
            inner join sy_containable c on c.object_id = ov.or_order_id and c.object_class_id in (1000117, 2500785) and c.PARENT_OBJECT_CLASS_ID = 1000000
            inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
            where pm.owner_ac_workgroup_id = #{workgroupId}
            and ov.or_order_type_id in (1000000, 1000001)
            and ov.order_create_date is not NULL
            AND ov.CREATE_DATE &gt;= SYSDATE - #{filter}
        ) avg_x GROUP BY createuserId) o1 ON pm.CREATE_USER_ID = o1.createUserId
		LEFT JOIN
        (select sum(delta)/count(createUserId) AS each_buy_order_accepted, createUserId
        from (
            select ov.ACCEPT_DATE - ov.order_create_date delta, ov.create_user_id createUserId
            from or_order_version ov
            inner join OR_ORDER_STATE oros on oros.or_order_id = ov.or_order_id and oros.oc_object_state_id = 2000078
            where ov.BUYER_AC_WORKGROUP_ID = #{workgroupId}
            and ov.or_order_type_id in (1000000, 1000001)
            and ov.ACCEPT_DATE is not NULL
            AND ov.CREATE_DATE &gt;= SYSDATE - #{filter}
        ) avg_x GROUP BY createuserId) o2 ON pm.CREATE_USER_ID = o2.createUserId
		LEFT JOIN
         (select sum(delta)/count(createUserId) AS each_buy_order_closed, createUserId
        from (
            select oros.state_create_date - ov.ACCEPT_DATE delta, ov.create_user_id createUserId
            from or_order_version ov
            inner join OR_ORDER_STATE oros on oros.or_order_id = ov.or_order_id and oros.oc_object_state_id = 2500045
            where ov.BUYER_AC_WORKGROUP_ID = #{workgroupId}
            and ov.or_order_type_id in (1000000, 1000001)
            AND ov.CREATE_DATE &gt;= SYSDATE - #{filter}
        ) avg_x GROUP BY createuserId) o3 ON pm.CREATE_USER_ID = o3.createUserId
        INNER JOIN AC_ACCOUNT_USER aau ON aau.user_id = pm.CREATE_USER_ID
        INNER JOIN AC_PERSON ap ON ap.ac_person_id = aau.ac_person_id
    </select>

    <select id="findInvoiceForecast" resultMap="invoiceChartResultMap">
        select 
        'all' type, sum(oi.value + DECODE(oi.tax, NULL, 0, oi.tax) + DECODE(oi.shipping, NULL, 0, oi.shipping)) value
        from 
        or_order o,
        or_order_version ov,
        or_order_item oi
        where 
        ov.is_current = 1
        and exists (select 'x' from OR_ORDER_STATE oros where oros.or_order_id = ov.or_order_id and oros.oc_object_state_id in (2000078, 2500045))
        and ov.or_order_version_id = oi.or_order_version_id
        and ov.or_order_version_id = o.or_order_version_id
        AND o.PARENT_OR_ORDER_ID 
        in (
          select ov.or_order_id
          from 
          or_order o
          inner join or_order_version ov on ov.or_order_version_id = o.or_order_version_id
          inner join sy_containable c on c.object_id = ov.or_order_id and c.object_class_id in (1000117, 2500785) and c.PARENT_OBJECT_CLASS_ID = 1000000 
          inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
          where pm.owner_ac_workgroup_id = #{workgroupId}
          and ov.or_order_type_id in (1000000, 1000001)
          and ov.completion_date between TO_DATE(#{start}, 'MM/DD/YYYY HH24:MI:SS') and TO_DATE(#{end}, 'MM/DD/YYYY HH24:MI:SS')
          and o.PARENT_OR_ORDER_ID = o.OR_ORDER_ID
        )
        
        union all
        
        select 
        'not_yet_invoiced' type, sum(oi.value + DECODE(oi.tax, NULL, 0, oi.tax) + DECODE(oi.shipping, NULL, 0, oi.shipping)) value
        from 
        or_order o,
        or_order_version ov,
        or_order_item oi
        where 
        ov.is_current = 1
        and exists (select 'x' from OR_ORDER_STATE oros where oros.or_order_id = ov.or_order_id and oros.oc_object_state_id in (2000078, 2500045))
        and ov.or_order_version_id = oi.or_order_version_id
        and ov.or_order_version_id = o.or_order_version_id
        AND o.PARENT_OR_ORDER_ID 
        in (
          select ov.or_order_id
          from 
          or_order o
          inner join or_order_version ov on ov.or_order_version_id = o.or_order_version_id
          inner join sy_containable c on c.object_id = ov.or_order_id and c.object_class_id in (1000117, 2500785) and c.PARENT_OBJECT_CLASS_ID = 1000000 
          inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
          where pm.owner_ac_workgroup_id = #{workgroupId}
          and ov.or_order_type_id in (1000000, 1000001)
          and ov.completion_date between TO_DATE(#{start}, 'MM/DD/YYYY HH24:MI:SS') and TO_DATE(#{end}, 'MM/DD/YYYY HH24:MI:SS')
          and o.PARENT_OR_ORDER_ID = o.OR_ORDER_ID
          and not exists (select 'x' from pc_invoice where or_order_id = ov.or_order_id)
        )
        
        union all
        
        select 
        'not_yet_closed' type, sum(oi.value + DECODE(oi.tax, NULL, 0, oi.tax) + DECODE(oi.shipping, NULL, 0, oi.shipping)) value
        from 
        or_order o,
        or_order_version ov,
        or_order_item oi
        where 
        ov.is_current = 1
        and exists (select 'x' from OR_ORDER_STATE oros where oros.or_order_id = ov.or_order_id and oros.oc_object_state_id in (2000078))
        and ov.or_order_version_id = oi.or_order_version_id
        and ov.or_order_version_id = o.or_order_version_id
        AND o.PARENT_OR_ORDER_ID 
        in (
          select ov.or_order_id
          from 
          or_order o
          inner join or_order_version ov on ov.or_order_version_id = o.or_order_version_id
          inner join sy_containable c on c.object_id = ov.or_order_id and c.object_class_id in (1000117, 2500785) and c.PARENT_OBJECT_CLASS_ID = 1000000 
          inner join pm_project pm on pm.pm_project_id = c.PARENT_OBJECT_ID
          where pm.owner_ac_workgroup_id = #{workgroupId}
          and ov.or_order_type_id in (1000000, 1000001)
          and ov.completion_date between TO_DATE(#{start}, 'MM/DD/YYYY HH24:MI:SS') and TO_DATE(#{end}, 'MM/DD/YYYY HH24:MI:SS')
          and o.PARENT_OR_ORDER_ID = o.OR_ORDER_ID
          and not exists (select 'x' from OR_ORDER_STATE oros where oros.or_order_id = ov.or_order_id and oros.oc_object_state_id = 2500045)
        )
    </select>

</mapper>