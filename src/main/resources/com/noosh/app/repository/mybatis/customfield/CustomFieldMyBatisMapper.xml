<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.noosh.app.repository.mybatis.customfield.CustomFieldMyBatisMapper">

    <resultMap id="userFieldResultMap" type="com.noosh.app.commons.dto.customfield.UserFieldDTO">
        <result property="id" column = "AC_CUSTOM_FIELD_ID" jdbcType="DECIMAL"/>
        <result property="fieldValues" column = "FIELD_VALUES" jdbcType="VARCHAR"/>
        <result property="label" column = "LABEL" jdbcType="VARCHAR"/>
        <result property="prPropertyAttributeId" column = "PR_PROPERTY_ATTRIBUTE_ID" jdbcType="DECIMAL"/>
        <result property="numberValue" column = "NUMBER_VALUE" jdbcType="DECIMAL"/>
        <result property="stringValue" column = "STRING_VALUE" jdbcType="VARCHAR"/>
        <result property="dateValue" column = "DATE_VALUE" typeHandler="com.noosh.app.commons.entity.util.LocalDateTimeTypeHandler"/>
        <result property="prDataTypeId" column = "PR_DATA_TYPE_ID" jdbcType="DECIMAL"/>
        <result property="customFieldControlId" column = "AC_CUSTOM_FIELD_CONTROL_ID" jdbcType="DECIMAL"/>
        <result property="paramName" column = "PARAM_NAME" jdbcType="VARCHAR"/>
        <result property="paramId" column = "PR_PROPERTY_PARAM_ID" jdbcType="DECIMAL"/>
        <result property="attributes" column = "ATTRIBUTES" jdbcType="VARCHAR"/>
        <result property="isRequired" column = "IS_REQUIRED" jdbcType="BOOLEAN"/>
        <result property="includeInTotal" column = "INCLUDE_IN_TOTAL" jdbcType="BOOLEAN"/>
    </resultMap>

    <select id="getAllUserFieldsByWorkgroupId" resultMap="userFieldResultMap">
        select cf.AC_CUSTOM_FIELD_ID, cf.FIELD_VALUES, cf.LABEL, pp.PR_DATA_TYPE_ID, pp.PARAM_NAME, cfc.AC_CUSTOM_FIELD_CONTROL_ID, pp.PR_PROPERTY_PARAM_ID, cf.ATTRIBUTES, cf.IS_REQUIRED, cf.INCLUDE_IN_TOTAL
        from ac_custom_field cf, ac_custom_field_type cft,
        ac_custom_field_control cfc, pr_property_param pp, ac_custom_field_class cfcl where
        cf.OWNER_AC_WORKGROUP_ID =#{workgroupId}
        and cfcl.AC_CUSTOM_FIELD_CLASS_ID = cf.AC_CUSTOM_FIELD_CLASS_ID
        and cfcl.AC_CUSTOM_FIELD_CLASS_ID = #{classId}
        and cfc.AC_CUSTOM_FIELD_CONTROL_ID = cf.AC_CUSTOM_FIELD_CONTROL_ID
        and cfc.AC_CUSTOM_FIELD_TYPE_ID = cft.AC_CUSTOM_FIELD_TYPE_ID
        <if test="excludeInvisibleSupplierField">
            and cf.IS_INVISIBLE_TO_SUPPLIER != 1
        </if>
        and cf.PR_PROPERTY_PARAM_ID = pp.PR_PROPERTY_PARAM_ID order by cf.ORDINAL_NUMBER
    </select>



</mapper>