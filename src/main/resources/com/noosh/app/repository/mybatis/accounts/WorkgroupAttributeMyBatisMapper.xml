<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.accounts.WorkgroupAttributeMyBatisMapper">

    <resultMap id="workgroupAttributeResultMap" type="com.noosh.app.commons.dto.accounts.WorkgroupAttributeDTO">
        <id property="id" column="AC_WG_ATTRIBUTE_ID"/>
        <result property="typeId" column="AC_WG_ATTRIBUTE_TYPE_ID"/>
        <result property="code" column="CODE"/>
        <result property="labelStr" column="LABEL"/>
        <result property="labelStrId" column="LABEL_STR_ID"/>
        <result column="AC_WG_ATTRIBUTE_REG_ID" property="acWgAttributeRegId" />
        <association property="type" columnPrefix="T_" resultMap="com.noosh.app.repository.mybatis.accounts.WorkgroupAttributeTypeMyBatisMapper.workgroupAttributeTypeResultMap"/>
    </resultMap>

    <sql id="workgroupAttributeColumns">
        ${alias}.AC_WG_ATTRIBUTE_ID ${prefix}AC_WG_ATTRIBUTE_ID,
        ${alias}.AC_WG_ATTRIBUTE_TYPE_ID ${prefix}AC_WG_ATTRIBUTE_TYPE_ID,
        ${alias}.CODE ${prefix}CODE,
        ${alias}.LABEL ${prefix}LABEL,
        ${alias}.LABEL_STR_ID ${prefix}LABEL_STR_ID
    </sql>

    <select id="findWorkgroupAttributes" resultMap="workgroupAttributeResultMap">
        SELECT R.AC_WG_ATTRIBUTE_REG_ID,
        <include refid="workgroupAttributeColumns">
            <property name="alias" value="A"/>
            <property name="prefix" value=""/>
        </include>
        FROM
        AC_WG_ATTRIBUTE A,
        AC_WG_ATTRIBUTE_TYPE T,
        AC_WG_ATTRIBUTE_REG R
        WHERE
        T.AC_WG_ATTRIBUTE_TYPE_ID = #{typeId}
        AND A.AC_WG_ATTRIBUTE_TYPE_ID = T.AC_WG_ATTRIBUTE_TYPE_ID
        AND R.AC_WG_ATTRIBUTE_ID = A.AC_WG_ATTRIBUTE_ID
        AND R.OWNER_AC_WORKGROUP_ID = #{workgroupId}
        <if test="activeOnly">
            AND R.IS_ACTIVE = 1
        </if>
        ORDER BY R.ORDINAL_NUMBER, A.${orderBy}
    </select>

    <select id="findByType" resultMap="workgroupAttributeResultMap">
        SELECT
        <include refid="workgroupAttributeColumns">
            <property name="alias" value="A"/>
            <property name="prefix" value=""/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.accounts.WorkgroupAttributeTypeMyBatisMapper.workgroupAttributeTypeColumns">
            <property name="alias" value="T"/>
            <property name="prefix" value="T_"/>
        </include>
        FROM
        AC_WG_ATTRIBUTE A,
        AC_WG_ATTRIBUTE_TYPE T
        WHERE
        T.AC_WG_ATTRIBUTE_TYPE_ID = #{typeId}
        AND A.AC_WG_ATTRIBUTE_TYPE_ID = T.AC_WG_ATTRIBUTE_TYPE_ID
        ORDER BY #{orderBy}
    </select>
</mapper>
