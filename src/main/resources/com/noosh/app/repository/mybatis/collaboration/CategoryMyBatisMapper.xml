<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.collaboration.CategoryMyBatisMapper">

    <resultMap id="categoryResultMap" type="com.noosh.app.commons.dto.collaboration.CategoryDTO">
        <id property="id" column="CO_CATEGORY_ID"/>
        <result property="name" column="NAME"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="userId" column="USER_ID"/>
        <result property="workgroupId" column="AC_WORKGROUP_ID"/>
        <result property="objectAttr" column="OBJECT_ATTR"/>
        <result property="isDisabled" column="IS_DISABLED"/>
        <collection property="categoryClasses" columnPrefix="CC_"
                    resultMap="com.noosh.app.repository.mybatis.collaboration.CategoryClassMyBatisMapper.categoryClassResultMap" javaType="ArrayList" />
    </resultMap>

    <sql id="categoryColumns">
        ${alias}.CO_CATEGORY_ID ${prefix}CO_CATEGORY_ID,
        ${alias}.NAME ${prefix}NAME,
        ${alias}.DESCRIPTION ${prefix}DESCRIPTION,
        ${alias}.USER_ID ${prefix}USER_ID,
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID,
        ${alias}.OBJECT_ATTR ${prefix}OBJECT_ATTR,
        ${alias}.IS_DISABLED ${prefix}IS_DISABLED
    </sql>

    <select id="findProjectBudgetCategory" resultMap="categoryResultMap">
        SELECT
        <include refid="categoryColumns">
            <property name="alias" value="C"/>
            <property name="prefix" value=""/>
        </include>
        FROM CO_CATEGORY C
        , CO_CATEGORY_CLASS CC
        WHERE  (
        (C.USER_ID = #{userId} AND C.AC_WORKGROUP_ID IS NULL)
        OR (C.AC_WORKGROUP_ID = #{workgroupId})
        )
        AND C.CO_CATEGORY_ID = CC.CO_CATEGORY_ID
        AND CC.OC_OBJECT_CLASS_ID = 2500726
        AND C.IS_DISABLED = 0
        ORDER BY CC.ORDINAL_NUMBER
    </select>


    <select id="getCategoryList" resultMap="categoryResultMap">
		SELECT distinct
        <include refid="categoryColumns">
            <property name="alias" value="C"/>
            <property name="prefix" value=""/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.collaboration.CategoryClassMyBatisMapper.categoryClassColumns">
            <property name="alias" value="CC"/>
            <property name="prefix" value="CC_"/>
        </include>
        FROM CO_CATEGORY C, CO_CATEGORY_CLASS CC
        WHERE ((C.USER_ID=#{userId, jdbcType=NUMERIC} and C.AC_WORKGROUP_ID is null )
            OR C.AC_WORKGROUP_ID=#{workgroupId, jdbcType=NUMERIC})
        AND C.CO_CATEGORY_ID=CC.CO_CATEGORY_ID
        AND CC.OC_OBJECT_CLASS_ID = 1000000
        and C.IS_DISABLED = 0
        ORDER BY CC.ORDINAL_NUMBER
    </select>
</mapper>
