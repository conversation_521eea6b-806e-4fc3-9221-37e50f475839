<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.security.WorkgroupSecondaryDataMyBatisMapper">

    <resultMap id="workgroupSecondaryDataResultMap" type="com.noosh.app.commons.dto.security.WorkgroupSecondaryDataDTO">
        <id property="id" column="AC_WORKGROUP_SD_ID"/>
        <result property="workgroupId" column="AC_WORKGROUP_ID"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="duns" column="DUNS"/>
        <result property="defaultLocaleId" column="DEFAULT_AC_LOCALE_ID"/>
        <result property="isPublic" column="IS_PUBLIC"/>
        <result property="isInternal" column="IS_INTERNAL"/>
        <result property="financialNumber" column="FINANCIAL_NUMBER"/>
        <result property="activationDate" column="ACTIVATION_DATE"/>
        <result property="contractDate" column="CONTRACT_DATE"/>
        <result property="logoURL" column="LOGO_URL"/>
        <result property="logoOneURL" column="LOGO_ONE_URL"/>
        <result property="website" column="WEBSITE"/>
        <result property="standardHours" column="STANDARD_HOURS"/>
        <result property="standardCapacity" column="STANDARD_CAPACITY"/>
        <result property="standardEndOfDay" column="STANDARD_END_OF_DAY"/>
        <result property="isCapabilitySearchable" column="IS_CAPABILITY_SEARCHABLE"/>
        <result property="partnerGrouping" column="PARTNER_GROUPING"/>
        <result property="billingAccountLabel" column="BILLING_ACCOUNT_LABEL"/>
        <result property="billingAccountCode" column="BILLING_ACCOUNT_CODE"/>
        <result property="includeInBilling" column="INCLUDE_IN_BILLING"/>
        <result property="revenue" column="REVENUE"/>
        <result property="revenueCategory" column="REVENUE_CATEGORY"/>
        <result property="isPaperSupplier" column="IS_PAPER_SUPPLIER"/>
        <result property="uiSettings" column="UI_SETTINGS"/>
    </resultMap>

    <sql id="workgroupSecondaryDataColumns">
        ${alias}.AC_WORKGROUP_SD_ID ${prefix}AC_WORKGROUP_SD_ID,
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID,
        ${alias}.DESCRIPTION ${prefix}DESCRIPTION,
        ${alias}.DUNS ${prefix}DUNS,
        ${alias}.DEFAULT_AC_LOCALE_ID ${prefix}DEFAULT_AC_LOCALE_ID,
        ${alias}.IS_PUBLIC ${prefix}IS_PUBLIC,
        ${alias}.IS_INTERNAL ${prefix}IS_INTERNAL,
        ${alias}.FINANCIAL_NUMBER ${prefix}FINANCIAL_NUMBER,
        ${alias}.ACTIVATION_DATE ${prefix}ACTIVATION_DATE,
        ${alias}.CONTRACT_DATE ${prefix}CONTRACT_DATE,
        ${alias}.LOGO_URL ${prefix}LOGO_URL,
        ${alias}.LOGO_ONE_URL ${prefix}LOGO_ONE_URL,
        ${alias}.WEBSITE ${prefix}WEBSITE,
        ${alias}.STANDARD_HOURS ${prefix}STANDARD_HOURS,
        ${alias}.STANDARD_CAPACITY ${prefix}STANDARD_CAPACITY,
        ${alias}.STANDARD_END_OF_DAY ${prefix}STANDARD_END_OF_DAY,
        ${alias}.IS_CAPABILITY_SEARCHABLE ${prefix}IS_CAPABILITY_SEARCHABLE,
        ${alias}.PARTNER_GROUPING ${prefix}PARTNER_GROUPING,
        ${alias}.BILLING_ACCOUNT_LABEL ${prefix}BILLING_ACCOUNT_LABEL,
        ${alias}.BILLING_ACCOUNT_CODE ${prefix}BILLING_ACCOUNT_CODE,
        ${alias}.INCLUDE_IN_BILLING ${prefix}INCLUDE_IN_BILLING,
        ${alias}.REVENUE ${prefix}REVENUE,
        ${alias}.REVENUE_CATEGORY ${prefix}REVENUE_CATEGORY,
        ${alias}.IS_PAPER_SUPPLIER ${prefix}IS_PAPER_SUPPLIER,
        ${alias}.UI_SETTINGS ${prefix}UI_SETTINGS
    </sql>

</mapper>
