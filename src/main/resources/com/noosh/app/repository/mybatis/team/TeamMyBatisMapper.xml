<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.team.TeamMyBatisMapper">

    <resultMap id="teamResultMap" type="com.noosh.app.commons.dto.team.TeamDTO">
        <result property="teamId" column = "TM_TEAM_ID" />
        <result property="parentTeamId" column = "PARENT_TM_TEAM_ID" />
        <result property="workgroupId" column = "AC_WORKGROUP_ID" />
        <result property="workgroupTypeId" column = "AC_WORKGROUP_TYPE_ID" />
        <result property="workgroupName" column = "WORKGROUP_NAME" jdbcType="VARCHAR" />
        <collection property="members" column="TM_TEAM_ID" ofType="com.noosh.app.commons.dto.team.TeamMemberDTO" javaType="ArrayList" resultMap="teamMemberResultMap"/>
    </resultMap>

    <resultMap id="teamMemberResultMap" type="com.noosh.app.commons.dto.team.TeamMemberDTO">
        <result property="teamMemberId" column = "TM_TEAM_MEMBER_ID" jdbcType="DECIMAL"/>
        <result property="userId" column = "USER_ID" jdbcType="DECIMAL"/>
        <result property="teamId" column = "TEAM_ID" jdbcType="DECIMAL"/>
        <result property="firstName" column = "FIRST_NAME" jdbcType="VARCHAR" />
        <result property="middleName" column = "MIDDLE_NAME" jdbcType="VARCHAR" />
        <result property="lastName" column = "LAST_NAME" jdbcType="VARCHAR" />
        <result property="aliasName" column = "ALIAS_NAME" jdbcType="VARCHAR" />
        <result property="roleId" column = "RE_ROLE_ID" jdbcType="DECIMAL"/>
        <result property="emailAddress" column = "EMAIL_ADDRESS" jdbcType="VARCHAR" />
        <result property="workgroupId" column = "AC_WORKGROUP_ID" jdbcType="DECIMAL"/>
        <result property="workgroupName" column = "WORKGROUP_NAME" jdbcType="VARCHAR" />
        <result property="companyName" column = "COMPANY_NAME" jdbcType="VARCHAR" />
    </resultMap>

    <resultMap id="teamUserResultMap" type="com.noosh.app.commons.dto.team.TeamUserDTO">
        <result property="userId" column = "USER_ID" jdbcType="DECIMAL"/>
        <result property="workgroupId" column = "AC_WORKGROUP_ID" jdbcType="DECIMAL"/>
        <result property="objectId" column = "object_id" jdbcType="DECIMAL"/>
        <result property="objectClassId" column = "oc_object_class_id" jdbcType="DECIMAL"/>
        <result property="teamId" column = "tm_team_id" jdbcType="DECIMAL"/>
    </resultMap>

    <resultMap id="nooshUserResultMap" type="com.noosh.app.collaboration.nfc.security.User">
        <result property="userId" column = "USER_ID" jdbcType="DECIMAL"/>
        <result property="loginName" column = "LOGIN_NAME" jdbcType="VARCHAR" />
    </resultMap>



    <select id="findProjectIds" resultType="java.lang.Long">
            SELECT #{projectId} AS OBJECT_ID FROM DUAL
        UNION
            SELECT OBJECT_ID
            FROM CO_COLLABORATION_OBJECT
            WHERE MASTER_OBJECT_ID = #{projectId}
            AND MASTER_OBJECT_CLASS_ID = 1000000
            AND OC_OBJECT_STATE_ID=2500039
        UNION
            SELECT MASTER_OBJECT_ID
            FROM CO_COLLABORATION_OBJECT
            WHERE OBJECT_ID =#{projectId}
            AND OBJECT_CLASS_ID =1000000
            AND OC_OBJECT_STATE_ID=2500039
    </select>

    <select id="findCollaborationTeamMembers" resultMap="teamResultMap">
        SELECT
            TM.TM_TEAM_MEMBER_ID,
            TM.USER_ID,
            TM.RE_ROLE_ID,
            TM.INVITOR_USER_ID,
            TM.DATE_INVITED,
            TM.ROLE_CHANGE_COMMENT,
            TM.CURRENT_RECORD,
            T.TM_TEAM_ID,
            T.PARENT_TM_TEAM_ID,
            T.AC_WORKGROUP_ID,
            R.RE_ROLE_ID,
            R.PARENT_RE_ROLE_ID,
            R.BASE_RE_ROLE_ID,
            R.RE_ROLE_CLASS_ID,
            R.CONSTANT_TOKEN,
            R.NAME R_NAME,
            R.ROLE_NAME_STR,
            R.ROLE_NAME_STR_ID,
            R.DESCRIPTION,
            R.CUSTOMIZABLE,
            R.CREATE_DATE,
            R.MOD_DATE,
            U.IS_DEFAULT,
            U.IS_LOCKED,
            U.OC_OBJECT_STATE_ID,
            U.INVITATION_DATE,
            U.ACTIVATION_DATE,
            U.DEACTIVATION_DATE,
            U.CUSTOM_PR_PROPERTY_ID,
            P.AC_PERSON_ID,
            P.FIRST_NAME,
            P.LAST_NAME,
            P.MIDDLE_NAME,
            P.ALIAS_NAME,
            P.AC_ADDRESS_ID,
            P.AC_TIME_ZONE_ID,
            P.AC_LOCALE_ID,
            P.IS_PUBLIC,
            P.INVITED_BY_USER_ID,
            P.ACTIVATION_DATE,
            P.PERSONAL_COMPANY_NAME,
            P.CUSTOM_PR_PROPERTY_ID,
            P.AC_PERSON_PROFILE_IMG,
            PD.AC_PERSON_SD_ID,
            PD.AC_PERSON_ID,
            PD.TITLE,
            PD.LOGIN_NAME,
            PD.PASSWORD,
            PD.PASSWORD_HISTORY,
            PD.PASSWORD_MOD_DATE,
            PD.PASSWORD_FAILED_ATTEMPTS,
            PD.PHONE_NUMBER,
            PD.FAX_NUMBER,
            PD.PAGER_NUMBER,
            PD.PAGER_EMAIL,
            PD.ACCEPT_USER_AGREEMENT,
            PD.AGREEMENT_ACCEPTED,
            PD.IS_TEMPORARY_PASSWORD,
            PD.RACF_ID,
            PD.CREATE_DATE ,
            E.AC_PERSON_EMAIL_ID,
            E.AC_PERSON_ID,
            E.EMAIL_ADDRESS,
            E.DESCRIPTION,
            E.IS_DEFAULT,
            E.AUTHORIZATION_CODE,
            E.IS_AUTHORISED,
            E.AUTHORISE_FAIL_COUNT,
            E.CODE_SENT_DATE,
            W.AC_WORKGROUP_TYPE_ID,
            W.NAME WORKGROUP_NAME,
            C.NAME COMPANY_NAME
        FROM
            TM_TEAM_OBJECT TOBJ,
            TM_TEAM_MEMBER TM,
            TM_TEAM T,
            RE_ROLE R,
            AC_ACCOUNT_USER U,
            AC_PERSON P,
            AC_PERSON_SD PD,
            AC_PERSON_EMAIL E,
            AC_WORKGROUP W,
            AC_COMPANY C
        WHERE
            TOBJ.OBJECT_ID IN
            <foreach collection="projectIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND TOBJ.OC_OBJECT_CLASS_ID = 1000000
            AND TM.TM_TEAM_ID = TOBJ.TM_TEAM_ID
            AND TM.CURRENT_RECORD = 1
            AND TM.TM_TEAM_ID = T.TM_TEAM_ID
            AND T.AC_WORKGROUP_ID = W.AC_WORKGROUP_ID
            AND U.USER_ID = TM.USER_ID
            AND R.RE_ROLE_ID = TM.RE_ROLE_ID
            AND P.AC_PERSON_ID = U.AC_PERSON_ID
            AND P.AC_PERSON_ID = E.AC_PERSON_ID
            AND PD.AC_PERSON_ID = P.AC_PERSON_ID
            AND E.IS_DEFAULT = 1
            AND W.AC_COMPANY_ID = C.AC_COMPANY_ID
        ORDER BY
            ABS(TOBJ.OBJECT_ID-#{projectId}),
            TM.TM_TEAM_ID,
            P.LAST_NAME,
            P.FIRST_NAME
    </select>

    <select id="findAllTeamMembers" resultMap="teamMemberResultMap">
        SELECT
        TM.TM_TEAM_ID TEAM_ID,
        TM.USER_ID
        FROM
            TM_TEAM_OBJECT TOBJ,
            TM_TEAM_MEMBER TM
        WHERE
            TOBJ.OBJECT_ID IN
            (
                SELECT #{projectId} AS OBJECT_ID FROM DUAL
            UNION
                SELECT OBJECT_ID
                FROM CO_COLLABORATION_OBJECT
                WHERE MASTER_OBJECT_ID = #{projectId}
                AND MASTER_OBJECT_CLASS_ID = 1000000
                AND OC_OBJECT_STATE_ID=2500039
            UNION
                SELECT MASTER_OBJECT_ID
                FROM CO_COLLABORATION_OBJECT
                WHERE OBJECT_ID =#{projectId}
                AND OBJECT_CLASS_ID =1000000
                AND OC_OBJECT_STATE_ID=2500039

            )
            AND TOBJ.OC_OBJECT_CLASS_ID = 1000000
            AND TM.TM_TEAM_ID = TOBJ.TM_TEAM_ID
            AND TM.CURRENT_RECORD = 1
    </select>

    <select id="findByObjectIdAndObjectClassId" resultMap="teamResultMap">
        SELECT
            TEAM.*,
            TMOBJECT.*
        FROM TM_TEAM TEAM,
        TM_TEAM_OBJECT TMOBJECT
        WHERE TMOBJECT.TM_TEAM_ID = TEAM.TM_TEAM_ID
        AND TMOBJECT.OC_OBJECT_CLASS_ID = #{objectClassId}
        AND TMOBJECT.OBJECT_ID = #{objectId}
    </select>

    <select id="findParticipants" resultMap="teamUserResultMap">
        SELECT
            usr.user_id,
            usr.ac_workgroup_id,
            tobj.object_id,
            tobj.oc_object_class_id,
            tobj.tm_team_id
        FROM
            tm_team_member member,
            tm_team_object tobj,
            ac_account_user usr
        WHERE
            member.tm_team_id = tobj.tm_team_id
            AND usr.user_id = member.user_id
            AND member.current_record = 1
            AND tobj.tm_team_id = #{teamId}
        UNION
        SELECT
            usr.user_id,
            usr.ac_workgroup_id,
            tobj.object_id,
            tobj.oc_object_class_id,
            tm_team.tm_team_id
        FROM
            tm_team_member member,
            tm_team_object tobj,
            ac_account_user usr,
            tm_team
        WHERE
            member.tm_team_id = tobj.tm_team_id
            AND usr.user_id = member.user_id
            AND member.current_record = 1
            AND member.tm_team_id = tm_team.tm_team_id
            AND tm_team.PARENT_TM_TEAM_ID = #{teamId}
        <if test="parentTeamId gt 0">
        UNION
            SELECT
                usr.user_id,
                usr.ac_workgroup_id,
                tobj.object_id,
                tobj.oc_object_class_id,
                tm_team.tm_team_id
            FROM
                tm_team_member member,
                tm_team_object tobj,
                ac_account_user usr,
                tm_team
            WHERE
                member.tm_team_id = tobj.tm_team_id
                AND usr.user_id = member.user_id
                AND member.current_record = 1
                AND member.tm_team_id = tm_team.tm_team_id
                AND tm_team.TM_TEAM_ID = #{parentTeamId}
        </if>
    </select>

    <select id="findAllPermittedUsers" resultMap="teamMemberResultMap">
        SELECT
            re_object_access_control.user_id,
            tm_team_object.tm_team_id,
            re_role.re_role_id,
            re_role.re_role_class_id,
            tm_team_object.object_id,
            tm_team_object.oc_object_class_id,
            ac_account_user.ac_workgroup_id
        FROM
            re_object_access_control,
            tm_team_member,
            re_role,
            tm_team_object,
            ac_account_user
        WHERE
            re_object_access_control.object_id = #{objectId}
            AND re_object_access_control.re_access_context_id = #{permissionId}
            AND re_object_access_control.access_is_allowed = 1
            AND re_object_access_control.team_access IS NULL
            AND re_object_access_control.user_id = ac_account_user.user_id
            AND re_object_access_control.user_id IS NOT NULL
            AND tm_team_member.user_id = ac_account_user.user_id
            AND tm_team_member.re_role_id = re_role.re_role_id
            AND tm_team_member.current_record = 1
            AND tm_team_member.tm_team_id = re_object_access_control.tm_team_id
            AND re_object_access_control.tm_team_id = tm_team_object.tm_team_id
        UNION
        SELECT
            tm_team_member.user_id,
            tm_team_object.tm_team_id,
            re_role.re_role_id,
            re_role.re_role_class_id,
            tm_team_object.object_id,
            tm_team_object.oc_object_class_id,
            ac_account_user.ac_workgroup_id
        FROM
            re_object_access_control,
            tm_team_member,
            re_role,
            tm_team_object,
            ac_account_user
        WHERE
            re_object_access_control.object_id = #{objectId}
            AND re_object_access_control.re_access_context_id = #{permissionId}
            AND re_object_access_control.access_is_allowed = 1
            AND re_object_access_control.team_access = 1
            AND tm_team_member.user_id = ac_account_user.user_id
            AND tm_team_member.re_role_id = re_role.re_role_id
            AND tm_team_member.current_record = 1
            AND tm_team_member.tm_team_id = re_object_access_control.tm_team_id
            AND re_object_access_control.tm_team_id = tm_team_object.tm_team_id
        UNION
         SELECT
            tm_team_member.user_id,
            tm_team_object.tm_team_id,
            re_role.re_role_id,
            re_role.re_role_class_id,
            tm_team_object.object_id,
            tm_team_object.oc_object_class_id,
            ac_account_user.ac_workgroup_id
        FROM
            re_object_access_control,
            tm_team_member,
            re_role,
            tm_team_object,
            ac_account_user
        WHERE
            re_object_access_control.object_id = #{objectId}
            AND re_object_access_control.re_access_context_id = #{permissionId}
            AND re_object_access_control.access_is_allowed = 1
            AND re_object_access_control.re_role_id IS NOT NULL
            AND tm_team_member.user_id = ac_account_user.user_id
            AND tm_team_member.re_role_id = re_role.re_role_id
            AND tm_team_member.current_record = 1
            AND tm_team_member.tm_team_id = re_object_access_control.tm_team_id
            AND tm_team_member.re_role_id = re_object_access_control.re_role_id
            AND re_object_access_control.tm_team_id = tm_team_object.tm_team_id
        UNION
         SELECT
            tm_team_member.user_id,
            tm_team_object.tm_team_id,
            re_role.re_role_id,
            re_role.re_role_class_id,
            tm_team_object.object_id,
            tm_team_object.oc_object_class_id,
            ac_account_user.ac_workgroup_id
        FROM
            re_object_access_control,
            tm_team_member,
            re_role,
            tm_team_object,
            ac_account_user
        WHERE
            re_object_access_control.object_id = #{objectId}
            AND re_object_access_control.re_access_context_id = #{permissionId}
            AND re_object_access_control.access_is_allowed = 1
            AND re_object_access_control.ac_workgroup_id IS NOT NULL
            AND tm_team_member.user_id = ac_account_user.user_id
            AND tm_team_member.re_role_id = re_role.re_role_id
            AND tm_team_member.current_record = 1
            AND tm_team_member.tm_team_id = re_object_access_control.tm_team_id
            AND re_object_access_control.tm_team_id = tm_team_object.tm_team_id
            AND tm_team_member.user_id IN (
            SELECT
                tm_team_member.user_id
            FROM
                tm_team_member,
                tm_team_object,
                ac_account_user
            WHERE
                tm_team_object.object_id = re_object_access_control.ac_workgroup_id
                AND tm_team_object.oc_object_class_id = 1000100
                AND tm_team_member.tm_team_id = tm_team_object.tm_team_id
                AND tm_team_member.user_id = ac_account_user.user_id
                AND ac_account_user.oc_object_state_id = 2000056 )
    </select>

    <select id="findNooshUser" resultMap="nooshUserResultMap">
        SELECT
            AC_ACCOUNT_USER.user_id,ac_person_sd.login_name
        FROM
            AC_ACCOUNT_USER,
            AC_PERSON,
            AC_PERSON_SD,
            AC_ADDRESS,
            AC_WORKGROUP,
            AC_PERSON_EMAIL,
            AC_COMPANY,
            AC_WORKGROUP_SD
        WHERE
            AC_ACCOUNT_USER.AC_PERSON_ID = AC_PERSON.AC_PERSON_ID
            AND AC_ACCOUNT_USER.OC_OBJECT_STATE_ID=2000056
            AND AC_WORKGROUP.AC_WORKGROUP_ID = AC_WORKGROUP_SD.AC_WORKGROUP_ID
            AND AC_PERSON.AC_ADDRESS_ID = AC_ADDRESS.AC_ADDRESS_ID
            AND AC_PERSON.AC_PERSON_ID = AC_PERSON_SD.AC_PERSON_ID
            AND AC_PERSON.AC_PERSON_ID = AC_PERSON_EMAIL.AC_PERSON_ID
            AND AC_PERSON_EMAIL.IS_DEFAULT = 1
            AND AC_ACCOUNT_USER.AC_WORKGROUP_ID = AC_WORKGROUP.AC_WORKGROUP_ID
            AND AC_WORKGROUP.AC_COMPANY_ID = AC_COMPANY.AC_COMPANY_ID

            <if test="searchEmailStr != null and searchEmailStr != ''">
                AND TRIM(UPPER(AC_PERSON_EMAIL.EMAIL_ADDRESS)) LIKE '%' || TRIM(UPPER(#{searchEmailStr})) || '%'
            </if>
    </select>
</mapper>