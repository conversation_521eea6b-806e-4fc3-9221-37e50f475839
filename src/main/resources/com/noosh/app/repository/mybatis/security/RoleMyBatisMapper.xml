<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.security.RoleMyBatisMapper">

    <resultMap id="roleResultMap" type="com.noosh.app.commons.dto.security.RoleDTO">
        <id property="id" column="RE_ROLE_ID"/>
        <result property="parentReRoleId" column="PARENT_RE_ROLE_ID"/>
        <result property="baseRoleId" column="BASE_RE_ROLE_ID"/>
        <result property="reRoleClassId" column="RE_ROLE_CLASS_ID"/>
        <result property="acWorkgroupId" column="AC_WORKGROUP_ID"/>
        <result property="constantToken" column="CONSTANT_TOKEN"/>
        <result property="name" column="NAME"/>
        <result property="roleNameStr" column="ROLE_NAME_STR"/>
        <result property="roleNameStrId" column="ROLE_NAME_STR_ID"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="customizable" column="CUSTOMIZABLE"/>
    </resultMap>

    <sql id="roleColumns">
        ${alias}.RE_ROLE_ID ${prefix}RE_ROLE_ID,
        ${alias}.PARENT_RE_ROLE_ID ${prefix}PARENT_RE_ROLE_ID,
        ${alias}.BASE_RE_ROLE_ID ${prefix}BASE_RE_ROLE_ID,
        ${alias}.RE_ROLE_CLASS_ID ${prefix}RE_ROLE_CLASS_ID,
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID,
        ${alias}.CONSTANT_TOKEN ${prefix}CONSTANT_TOKEN,
        ${alias}.NAME ${prefix}NAME,
        ${alias}.ROLE_NAME_STR ${prefix}ROLE_NAME_STR,
        ${alias}.ROLE_NAME_STR_ID ${prefix}ROLE_NAME_STR_ID,
        ${alias}.DESCRIPTION ${prefix}DESCRIPTION,
        ${alias}.CUSTOMIZABLE ${prefix}CUSTOMIZABLE
    </sql>

    <select id="findWorkgroupRoleByUserId" resultMap="roleResultMap">
        SELECT
        <include refid="roleColumns">
            <property name="alias" value="R"/>
            <property name="prefix" value=""/>
        </include>
        FROM
        AC_ACCOUNT_USER U,
        TM_TEAM_OBJECT T,
        TM_TEAM_MEMBER TM,
        RE_ROLE R
        WHERE
        U.USER_ID = TM.USER_ID
        AND TM.RE_ROLE_ID = R.RE_ROLE_ID
        AND TM.TM_TEAM_ID = T.TM_TEAM_ID
        AND TM.CURRENT_RECORD = 1
        AND T.OBJECT_ID  = U.AC_WORKGROUP_ID
        AND T.OC_OBJECT_CLASS_ID=1000100
        AND U.USER_ID=#{userId}
    </select>

    <select id="findRolesByWorkgroup" resultMap="roleResultMap">
         SELECT
        <include refid="roleColumns">
            <property name="alias" value="R"/>
            <property name="prefix" value=""/>
        </include>
         FROM RE_ROLE R
         WHERE (
                R.AC_WORKGROUP_ID IS NULL OR
                R.AC_WORKGROUP_ID= #{workgroupId})
            AND R.RE_ROLE_CLASS_ID= #{roleClassId}
            AND R.RE_ROLE_ID NOT IN
            (SELECT RE_ROLE_ID FROM TM_TEAM_ROLE_FILTER WHERE AC_WORKGROUP_ID = #{workgroupId})
         ORDER BY R.DESCRIPTION ASC
    </select>

</mapper>
