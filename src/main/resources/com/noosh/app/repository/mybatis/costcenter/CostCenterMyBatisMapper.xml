<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.noosh.app.repository.mybatis.costcenter.CostCenterMyBatisMapper">

    <resultMap id="costCenterAllocationResultMap" type="com.noosh.app.commons.entity.costcenter.CostCenterAllocation">
        <result column="C_AC_COSTCENTER_ALLOCATION_ID" property="id" />
        <result column="C_AC_COSTCENTER_ALLOC_TYPE_ID" property="typeId"/>
        <result column="C_AC_COSTCENTER_ID" property="costCenterId" />
        <result column="C_PERCENT" property="percent" />
        <result column="C_OBJECT_ID" property="objectId" />
        <result column="C_OBJECT_CLASS_ID" property="objectClassId" />
        <result column="C_CUSTOM_PR_PROPERTY_ID" property="customPropertyId" />
        <result column="C_COMPLEMENT" property="complement" />
    </resultMap>

    <resultMap id="costCenterAllocTypeResultMap" type="com.noosh.app.commons.entity.costcenter.CostCenterAllocType">
        <result column="AT_AC_COSTCENTER_ALLOC_TYPE_ID" property="id" />
        <result column="AT_CONSTANT_TOKEN" property="constantToken"/>
        <result column="AT_DESCRIPTION_STR_ID" property="descriptionStrId" />
    </resultMap>

    <resultMap id="orderItemResultMap" type="com.noosh.app.commons.entity.order.OrderItem">
        <result column="OI_OR_ORDER_ITEM_ID" property="id" />
        <result column="OI_OR_ORDER_VERSION_ID" property="orderVersionId"/>
        <result column="OI_SP_SPEC_ID" property="specId" />
        <result column="OI_EM_ESTIMATE_ITEM_PRICE_ID" property="estimateItemPriceId" />
        <result column="OI_QUANTITY" property="quantity" />
        <result column="OI_VALUE" property="value" />
        <result column="OI_VALUE_AC_CURRENCY_ID" property="valueCurrencyId" />
    </resultMap>

    <resultMap id="costCenterAllocationDTOResultMap" type="com.noosh.app.commons.dto.costcenter.CostCenterAllocationDTO">
        <association property="costCenterAllocation" resultMap="costCenterAllocationResultMap" />
        <association property="orderItem" resultMap="orderItemResultMap" />
        <association property="costCenterAllocType" resultMap="costCenterAllocTypeResultMap" />
    </resultMap>


    <select id="findItemLevelCostAllocationByOrderVersionId" resultMap="costCenterAllocationDTOResultMap">
        SELECT
        C.AC_COSTCENTER_ALLOCATION_ID C_AC_COSTCENTER_ALLOCATION_ID,
        C.AC_COSTCENTER_ALLOC_TYPE_ID C_AC_COSTCENTER_ALLOC_TYPE_ID,
        C.AC_COSTCENTER_ID C_AC_COSTCENTER_ID,
        C.PERCENT C_PERCENT,
        C.OBJECT_ID C_OBJECT_ID,
        C.OBJECT_CLASS_ID C_OBJECT_CLASS_ID,
        C.CUSTOM_PR_PROPERTY_ID C_CUSTOM_PR_PROPERTY_ID,
        C.COMPLEMENT C_COMPLEMENT,
        OI.OR_ORDER_ITEM_ID OI_OR_ORDER_ITEM_ID,
        OI.OR_ORDER_VERSION_ID OI_OR_ORDER_VERSION_ID,
        OI.SP_SPEC_ID OI_SP_SPEC_ID,
        OI.EM_ESTIMATE_ITEM_PRICE_ID OI_EM_ESTIMATE_ITEM_PRICE_ID,
        OI.QUANTITY OI_QUANTITY,
        OI.VALUE OI_VALUE,
        OI.VALUE_AC_CURRENCY_ID OI_VALUE_AC_CURRENCY_ID
    FROM
        AC_COSTCENTER_ALLOCATION C,
        OR_ORDER_ITEM OI
    WHERE
        C.OBJECT_ID = OI.OR_ORDER_ITEM_ID
        AND C.OBJECT_CLASS_ID = 2000000
        AND OI.OR_ORDER_VERSION_ID = #{orderVersionId}
    </select>

    <select id="findOrderLevelCostAllocationByOrderId" resultMap="costCenterAllocationDTOResultMap">
        SELECT
        AT.AC_COSTCENTER_ALLOC_TYPE_ID AT_AC_COSTCENTER_ALLOC_TYPE_ID,
        AT.CONSTANT_TOKEN AT_CONSTANT_TOKEN,
        AT.DESCRIPTION_STR_ID AT_DESCRIPTION_STR_ID,
        C.AC_COSTCENTER_ALLOCATION_ID C_AC_COSTCENTER_ALLOCATION_ID,
        C.AC_COSTCENTER_ALLOC_TYPE_ID C_AC_COSTCENTER_ALLOC_TYPE_ID,
        C.AC_COSTCENTER_ID C_AC_COSTCENTER_ID,
        C.PERCENT C_PERCENT,
        C.OBJECT_ID C_OBJECT_ID,
        C.OBJECT_CLASS_ID C_OBJECT_CLASS_ID,
        C.CUSTOM_PR_PROPERTY_ID C_CUSTOM_PR_PROPERTY_ID,
        C.COMPLEMENT C_COMPLEMENT
    FROM
        AC_COSTCENTER_ALLOC_TYPE AT,
        AC_COSTCENTER_ALLOCATION C
    WHERE
        C.OBJECT_ID = #{orderId}
        AND C.OBJECT_CLASS_ID = 1000117
        AND C.AC_COSTCENTER_ALLOC_TYPE_ID = AT.AC_COSTCENTER_ALLOC_TYPE_ID
    </select>

    <select id="deleteCostCenterWithIds">
        DELETE FROM AC_COSTCENTER_ALLOCATION WHERE AC_COSTCENTER_ALLOCATION_ID in
        <foreach item="item" index="index" collection="ids" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>





</mapper>