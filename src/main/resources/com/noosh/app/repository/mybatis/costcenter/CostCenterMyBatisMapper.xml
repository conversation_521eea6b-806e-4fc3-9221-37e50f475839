<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.costcenter.CostCenterMyBatisMapper">
    <resultMap id="costCenterResultMap" type="com.noosh.app.commons.dto.costcenter.CostCenterDTO">
        <id property="id" column="AC_COSTCENTER_ID"/>
        <result property="name" column="NAME" />
        <result property="description" column="DESCRIPTION" />
        <result property="ownerWorkgroupId" column="OWNER_AC_WORKGROUP_ID" />
        <result property="clientId" column="BU_CLIENT_WORKGROUP_ID" />
        <result property="isActive" column="IS_ACTIVE" />
        <result property="client" column="CLIENT_NAME" />
    </resultMap>

    <select id="findUsedCostCenterIds" resultType="java.lang.Long" >
        SELECT CC.AC_COSTCENTER_ID
        FROM AC_COSTCENTER CC
        WHERE CC.OWNER_AC_WORKGROUP_ID = #{workgroupId}
        AND EXISTS (SELECT 'x' FROM AC_COSTCENTER_ALLOCATION WHERE CC.AC_COSTCENTER_ID = AC_COSTCENTER_ALLOCATION.AC_COSTCENTER_ID)
    </select>

    <select id="getCostCenterDetails" resultMap="costCenterResultMap">
        SELECT CC.*,
               CASE WHEN BCW.CLIENT_AC_WORKGROUP_ID IS NULL THEN BCW.NAME
               ELSE BW.NAME
               END AS CLIENT_NAME
        FROM AC_COSTCENTER CC
        LEFT JOIN BU_CLIENT_WORKGROUP BCW ON BCW.BU_CLIENT_WORKGROUP_ID = CC.BU_CLIENT_WORKGROUP_ID
        LEFT JOIN AC_WORKGROUP BW ON BW.AC_WORKGROUP_ID = BCW.CLIENT_AC_WORKGROUP_ID
        WHERE CC.AC_COSTCENTER_ID = #{costCenterId}
    </select>

    <select id="getCostCenterDTOList" resultMap="costCenterResultMap">
        SELECT CC.*,
               CASE WHEN BCW.CLIENT_AC_WORKGROUP_ID IS NULL THEN BCW.NAME
               ELSE BW.NAME
               END AS CLIENT_NAME
        FROM AC_COSTCENTER CC
        LEFT JOIN BU_CLIENT_WORKGROUP BCW ON BCW.BU_CLIENT_WORKGROUP_ID = CC.BU_CLIENT_WORKGROUP_ID
        LEFT JOIN AC_WORKGROUP BW ON BW.AC_WORKGROUP_ID = BCW.CLIENT_AC_WORKGROUP_ID
        WHERE CC.OWNER_AC_WORKGROUP_ID = #{ownerWgId}
        <if test="buClientWgId != null">
            AND CC.BU_CLIENT_WORKGROUP_ID = #{buClientWgId}
        </if>
        <if test="isActive">
            AND CC.IS_ACTIVE = #{isActive}
        </if>
    </select>

    <select id="getNoClientCostCenterDTOList" resultMap="costCenterResultMap">
        SELECT CC.*
        FROM AC_COSTCENTER CC
        WHERE CC.OWNER_AC_WORKGROUP_ID = #{ownerWgId}
        AND BU_CLIENT_WORKGROUP_ID IS NULL
        <if test="isActive">
            AND CC.IS_ACTIVE = #{isActive}
        </if>
    </select>

    <select id="findByNameAndDescription" resultMap="costCenterResultMap">
        SELECT CC.*
        FROM AC_COSTCENTER CC
        WHERE CC.OWNER_AC_WORKGROUP_ID = #{workgroupId}
        <if test="name">
            AND CC.NAME = #{name}
        </if>
        <if test="description !=null and description == ''">
            AND CC.DESCRIPTION is NULL
        </if>
        <if test="description != null and description != ''">
            AND CC.DESCRIPTION = #{description}
        </if>
    </select>
</mapper>