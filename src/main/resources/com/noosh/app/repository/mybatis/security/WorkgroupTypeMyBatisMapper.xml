<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.security.WorkgroupTypeMyBatisMapper">

    <resultMap id="workgroupTypeResultMap" type="com.noosh.app.commons.dto.security.WorkgroupTypeDTO">
        <id property="id" column="AC_WORKGROUP_TYPE_ID"/>
        <result property="constantToken" column="CONSTANT_TOKEN"/>
        <result property="descriptionStrId" column="DESCRIPTION_STR_ID"/>
    </resultMap>

    <sql id="workgroupTypeColumns">
        ${alias}.AC_WORKGROUP_TYPE_ID ${prefix}AC_WORKGROUP_TYPE_ID,
        ${alias}.CONSTANT_TOKEN ${prefix}CONSTANT_TOKEN,
        ${alias}.DESCRIPTION_STR_ID ${prefix}DESCRIPTION_STR_ID
    </sql>

</mapper>
