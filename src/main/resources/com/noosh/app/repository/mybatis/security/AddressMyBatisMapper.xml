<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper">

    <resultMap id="addressResultMap" type="com.noosh.app.commons.dto.security.AddressDTO">
        <id property="id" column="AC_ADDRESS_ID"/>
        <result property="description" column="DESCRIPTION"/>
        <result property="line1" column="LINE_1"/>
        <result property="line2" column="LINE_2"/>
        <result property="line3" column="LINE_3"/>
        <result property="city" column="CITY"/>
        <result property="state" column="STATE"/>
        <result property="postal" column="POSTAL"/>
        <result property="countryId" column="AC_COUNTRY_ID"/>
        <association property="country" columnPrefix="T_" resultMap="com.noosh.app.repository.mybatis.security.CountryMyBatisMapper.countryResultMap"/>
    </resultMap>

    <sql id="addressColumns">
        ${alias}.AC_ADDRESS_ID ${prefix}AC_ADDRESS_ID,
        ${alias}.DESCRIPTION ${prefix}DESCRIPTION,
        ${alias}.LINE_1 ${prefix}LINE_1,
        ${alias}.LINE_2 ${prefix}LINE_2,
        ${alias}.LINE_3 ${prefix}LINE_3,
        ${alias}.CITY ${prefix}CITY,
        ${alias}.STATE ${prefix}STATE,
        ${alias}.POSTAL ${prefix}POSTAL,
        ${alias}.AC_COUNTRY_ID ${prefix}AC_COUNTRY_ID
    </sql>
</mapper>
