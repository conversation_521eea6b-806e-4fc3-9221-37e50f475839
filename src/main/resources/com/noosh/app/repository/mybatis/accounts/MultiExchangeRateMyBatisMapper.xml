<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.accounts.MultiExchangeRateMyBatisMapper">

    <resultMap id="allMerResultMap" type="com.noosh.app.commons.dto.accounts.MultiExchangeRateDTO">
        <id property="id" column="AC_MULTI_EXCHANGE_RATE_ID"/>
        <id property="ownerWorkgroupId" column="OWNER_WORKGROUP_ID"/>
        <id property="clientWorkgroupId" column="BU_CLIENT_WORKGROUP_ID"/>
        <id property="supplierWorkgroupId" column="BU_SUPPLIER_WORKGROUP_ID"/>
        <id property="targetWorkgroupType" column="TARGET_WORKGROUP_TYPE"/>
        <id property="fromCurrencyId" column="FROM_AC_CURRENCY_ID"/>
        <id property="toCurrencyId" column="TO_AC_CURRENCY_ID"/>
        <id property="rate" column="RATE"/>
        <id property="activateDate" column="ACTIVATE_DATE"/>

        <association property="fromCurrency" column="AC_CURRENCY_ID" columnPrefix="FC_" resultMap="currencyResultMap"/>
        <association property="toCurrency" column="AC_CURRENCY_ID"  columnPrefix="TC_" resultMap="currencyResultMap"/>
        <association property="clientWorkgroup" column="BU_CLIENT_WORKGROUP_ID"  columnPrefix="C_" resultMap="clientWorkgroupResultMap"/>
    </resultMap>

    <resultMap id="currencyResultMap" type="com.noosh.app.commons.dto.security.CurrencyDTO">
        <id property = "id" column = "AC_CURRENCY_ID"/>
        <result property = "currency" column = "CURRENCY"/>
        <result property = "description" column = "DESCRIPTION"/>
    </resultMap>

    <resultMap id="clientWorkgroupResultMap" type="com.noosh.app.commons.dto.accounts.ClientWorkgroupDTO">
        <id property = "id" column = "BU_CLIENT_WORKGROUP_ID"/>
        <result property = "ownerWorkgroupId" column = "OWNER_AC_WORKGROUP_ID"/>
        <result property = "clientWorkgroupId" column = "CLIENT_AC_WORKGROUP_ID"/>
        <result property = "markupPercent" column = "MARKUP_PERCENT"/>
        <result property = "markup" column = "MARKUP"/>
        <result property = "markupCurrencyId" column = "MARKUP_AC_CURRENCY_ID"/>
        <result property = "isMarkupVisible" column = "IS_MARKUP_VISIBLE"/>
        <result property = "isReferenced" column = "IS_REFERENCED"/>
        <result property = "isInactive" column = "IS_INACTIVE"/>
        <result property = "isNoosh" column = "IS_NOOSH"/>
        <result property = "name" column = "NAME"/>
        <result property = "addressId" column = "AC_ADDRESS_ID"/>
        <result property = "clientCode" column = "CLIENT_CODE"/>
        <result property = "customPropertyId" column = "CUSTOM_PR_PROPERTY_ID"/>
        <result property = "requiresPricing" column = "PSF_REQUIRES_PRICING"/>
        <result property = "paymentMethodId" column = "BU_PAYMENT_METHOD_ID"/>
        <result property = "defaultClientUserId" column = "DEFAULT_CLIENT_USER_ID"/>
        <result property = "isMarginVisible" column = "IS_MARGIN_VISIBLE"/>
        <result property = "marginPercent" column = "MARGIN_PERCENT"/>
        <association property="clientWorkgroup" column="AC_WORKGROUP_ID"  columnPrefix="W_" resultMap="workgroupResultMap"/>
    </resultMap>

    <resultMap id="workgroupResultMap" type="com.noosh.app.commons.dto.security.WorkgroupDTO">
        <id property = "id" column = "AC_WORKGROUP_ID"/>
        <result property = "name" column = "NAME"/>
        <result property = "parentWorkgroupId" column = "PARENT_AC_WORKGROUP_ID"/>
        <result property = "companyId" column = "AC_COMPANY_ID"/>
        <result property = "workgroupTypeId" column = "AC_WORKGROUP_TYPE_ID"/>
        <result property = "defaultCurrencyId" column = "DEFAULT_AC_CURRENCY_ID"/>
        <result property = "transactionalCurrencyId" column = "TRANS_AC_CURRENCY_ID"/>
        <result property = "statusId" column = "OC_OBJECT_STATE_ID"/>
        <result property = "portalString" column = "PORTAL"/>
        <result property = "customPropertyId" column = "CUSTOM_PR_PROPERTY_ID"/>
        <result property = "guid" column = "AC_WORKGROUP_GUID"/>
        <result property = "isLocked" column = "IS_LOCKED"/>
        <result property = "authenticationMethodId" column = "AC_AUTH_METHOD_ID"/>
        <result property = "partnerWgId" column = "PARTNER_AC_WORKGROUP_ID"/>
        <result property = "isTrial" column = "IS_TRIAL"/>
        <result property = "sourceTypeId" column = "AC_SOURCE_TYPE_ID"/>
        <result property = "signUpSubdomain" column = "SIGN_UP_SUBDOMAIN"/>
        <result property = "appHome" column = "APP_HOME"/>
        <result property = "decimalPlaces" column = "DECIMAL_PLACES"/>
        <result property = "customReportDS" column = "CUSTOM_REPORT_DS"/>
    </resultMap>

    <sql id="currencyColumns">
        ${alias}.AC_CURRENCY_ID ${prefix}AC_CURRENCY_ID, ${alias}.CURRENCY ${prefix}CURRENCY, ${alias}.DESCRIPTION ${prefix}DESCRIPTION
    </sql>

    <sql id="clientWorkgroupColumns">
        ${alias}.BU_CLIENT_WORKGROUP_ID ${prefix}BU_CLIENT_WORKGROUP_ID,
        ${alias}.OWNER_AC_WORKGROUP_ID ${prefix}OWNER_AC_WORKGROUP_ID,
        ${alias}.CLIENT_AC_WORKGROUP_ID ${prefix}CLIENT_AC_WORKGROUP_ID,
        ${alias}.MARKUP_PERCENT ${prefix}MARKUP_PERCENT,
        ${alias}.MARKUP ${prefix}MARKUP,
        ${alias}.MARKUP_AC_CURRENCY_ID ${prefix}MARKUP_AC_CURRENCY_ID,
        ${alias}.IS_MARKUP_VISIBLE ${prefix}IS_MARKUP_VISIBLE,
        ${alias}.IS_REFERENCED ${prefix}IS_REFERENCED,
        ${alias}.IS_INACTIVE ${prefix}IS_INACTIVE,
        ${alias}.IS_NOOSH ${prefix}IS_NOOSH,
        ${alias}.NAME ${prefix}NAME,
        ${alias}.AC_ADDRESS_ID ${prefix}AC_ADDRESS_ID,
        ${alias}.CLIENT_CODE ${prefix}CLIENT_CODE,
        ${alias}.CUSTOM_PR_PROPERTY_ID ${prefix}CUSTOM_PR_PROPERTY_ID,
        ${alias}.PSF_REQUIRES_PRICING ${prefix}PSF_REQUIRES_PRICING,
        ${alias}.BU_PAYMENT_METHOD_ID ${prefix}BU_PAYMENT_METHOD_ID,
        ${alias}.DEFAULT_CLIENT_USER_ID ${prefix}DEFAULT_CLIENT_USER_ID,
        ${alias}.IS_MARGIN_VISIBLE ${prefix}IS_MARGIN_VISIBLE,
        ${alias}.MARGIN_PERCENT ${prefix}MARGIN_PERCENT
    </sql>

    <sql id="workgroupColumns">
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID,
        ${alias}.NAME ${prefix}NAME,
        ${alias}.PARENT_AC_WORKGROUP_ID ${prefix}PARENT_AC_WORKGROUP_ID,
        ${alias}.AC_COMPANY_ID ${prefix}AC_COMPANY_ID,
        ${alias}.AC_WORKGROUP_TYPE_ID ${prefix}AC_WORKGROUP_TYPE_ID,
        ${alias}.DEFAULT_AC_CURRENCY_ID ${prefix}DEFAULT_AC_CURRENCY_ID,
        ${alias}.TRANS_AC_CURRENCY_ID ${prefix}TRANS_AC_CURRENCY_ID,
        ${alias}.OC_OBJECT_STATE_ID ${prefix}OC_OBJECT_STATE_ID,
        ${alias}.PORTAL ${prefix}PORTAL,
        ${alias}.CUSTOM_PR_PROPERTY_ID ${prefix}CUSTOM_PR_PROPERTY_ID,
        ${alias}.AC_WORKGROUP_GUID ${prefix}AC_WORKGROUP_GUID,
        ${alias}.IS_LOCKED ${prefix}IS_LOCKED,
        ${alias}.AC_AUTH_METHOD_ID ${prefix}AC_AUTH_METHOD_ID,
        ${alias}.PARTNER_AC_WORKGROUP_ID ${prefix}PARTNER_AC_WORKGROUP_ID,
        ${alias}.IS_TRIAL ${prefix}IS_TRIAL,
        ${alias}.AC_SOURCE_TYPE_ID ${prefix}AC_SOURCE_TYPE_ID,
        ${alias}.SIGN_UP_SUBDOMAIN ${prefix}SIGN_UP_SUBDOMAIN,
        ${alias}.APP_HOME ${prefix}APP_HOME,
        ${alias}.DECIMAL_PLACES ${prefix}DECIMAL_PLACES,
        ${alias}.CUSTOM_REPORT_DS ${prefix}CUSTOM_REPORT_DS
    </sql>

    <select id="findAllMultiExchangeRates"
            resultMap="allMerResultMap">
        SELECT
        MER.AC_MULTI_EXCHANGE_RATE_ID,
        MER.OWNER_WORKGROUP_ID,
        MER.BU_CLIENT_WORKGROUP_ID,
        MER.BU_SUPPLIER_WORKGROUP_ID,
        MER.TARGET_WORKGROUP_TYPE,
        MER.FROM_AC_CURRENCY_ID,
        MER.TO_AC_CURRENCY_ID,
        MER.RATE,
        MER.ACTIVATE_DATE,

        <include refid="currencyColumns">
            <property name="alias" value="FC"/>
            <property name="prefix" value="FC_"/>
        </include>,
        <include refid="currencyColumns">
            <property name="alias" value="TC"/>
            <property name="prefix" value="TC_"/>
        </include>
        <if test="supportSellSideMultiCurrency">
            <if test='"CLIENT".equalsIgnoreCase(targetWorkgroupType)'>
                ,<include refid="clientWorkgroupColumns">
                    <property name="alias" value="C"/>
                    <property name="prefix" value="C_"/>
                </include>
                ,<include refid="workgroupColumns">
                    <property name="alias" value="W"/>
                    <property name="prefix" value="C_W_"/>
                </include>
            </if>
        </if>

        FROM AC_MULTI_EXCHANGE_RATE MER
        JOIN AC_CURRENCY FC ON MER.FROM_AC_CURRENCY_ID = FC.AC_CURRENCY_ID
        JOIN AC_CURRENCY TC ON MER.TO_AC_CURRENCY_ID = TC.AC_CURRENCY_ID
        <if test="supportSellSideMultiCurrency">
            <if test='"CLIENT".equalsIgnoreCase(targetWorkgroupType)'>
                JOIN BU_CLIENT_WORKGROUP C ON MER.BU_CLIENT_WORKGROUP_ID = C.BU_CLIENT_WORKGROUP_ID
                LEFT JOIN AC_WORKGROUP W ON C.CLIENT_AC_WORKGROUP_ID = W.AC_WORKGROUP_ID
            </if>
        </if>

        WHERE MER.OWNER_WORKGROUP_ID = #{workgroupId}
        AND MER.TARGET_WORKGROUP_TYPE = UPPER(#{targetWorkgroupType})

        <if test="supportSellSideMultiCurrency">
            <if test='"CLIENT".equalsIgnoreCase(targetWorkgroupType)'>
                ORDER BY MER.BU_CLIENT_WORKGROUP_ID, MER.TO_AC_CURRENCY_ID, MER.ACTIVATE_DATE DESC
            </if>
        </if>

        <if test="!supportSellSideMultiCurrency">
            <if test='"CLIENT".equalsIgnoreCase(targetWorkgroupType)'>
                ORDER BY MER.TO_AC_CURRENCY_ID, MER.ACTIVATE_DATE DESC
            </if>
            <if test='"SUPPLIER".equalsIgnoreCase(targetWorkgroupType)'>
                ORDER BY MER.FROM_AC_CURRENCY_ID, MER.ACTIVATE_DATE DESC
            </if>
        </if>
    </select>

    <select id="findMultiExchangeRate"  resultMap="allMerResultMap">
        SELECT T.* FROM (
        SELECT
        MER.AC_MULTI_EXCHANGE_RATE_ID,
        MER.OWNER_WORKGROUP_ID,
        MER.BU_CLIENT_WORKGROUP_ID,
        MER.BU_SUPPLIER_WORKGROUP_ID,
        MER.TARGET_WORKGROUP_TYPE,
        MER.FROM_AC_CURRENCY_ID,
        MER.TO_AC_CURRENCY_ID,
        MER.RATE,
        MER.ACTIVATE_DATE,
        <include refid="currencyColumns">
            <property name="alias" value="FC"/>
            <property name="prefix" value="FC_"/>
        </include>
        ,
        <include refid="currencyColumns">
            <property name="alias" value="TC"/>
            <property name="prefix" value="TC_"/>
        </include>
        FROM AC_MULTI_EXCHANGE_RATE MER
        JOIN AC_CURRENCY FC ON MER.FROM_AC_CURRENCY_ID = FC.AC_CURRENCY_ID
        JOIN AC_CURRENCY TC ON MER.TO_AC_CURRENCY_ID = TC.AC_CURRENCY_ID
        WHERE MER.OWNER_WORKGROUP_ID = #{workgroupId}
        AND MER.TARGET_WORKGROUP_TYPE = UPPER(#{targetWorkgroupType})
        AND MER.ACTIVATE_DATE &lt;= SYSDATE
        <if test="'CLIENT'.equalsIgnoreCase(targetWorkgroupType)">
            <if test="buWorkgroupId > 0">
                AND MER.BU_CLIENT_WORKGROUP_ID = #{buWorkgroupId}
            </if>
            <if test="buWorkgroupId == -1">
                AND MER.BU_CLIENT_WORKGROUP_ID IS NULL
            </if>
            And MER.BU_SUPPLIER_WORKGROUP_ID IS NULL
            AND MER.TO_AC_CURRENCY_ID = #{currencyId}
        </if>
        <if test="'SUPPLIER'.equalsIgnoreCase(targetWorkgroupType)">
            AND MER.BU_CLIENT_WORKGROUP_ID IS NULL
            <if test="buWorkgroupId > 0">
                AND MER.BU_SUPPLIER_WORKGROUP_ID = #{buWorkgroupId}
            </if>
            <if test="buWorkgroupId == -1">
                AND MER.BU_SUPPLIER_WORKGROUP_ID IS NULL
            </if>
            AND MER.FROM_AC_CURRENCY_ID = #{currencyId}
        </if>
        ORDER BY MER.ACTIVATE_DATE DESC
        ) T WHERE ROWNUM = 1
    </select>

    <select id="findSupplierDualCurrencyByTargetGroupId"  resultMap="allMerResultMap">
        SELECT T.* FROM (
        SELECT
        MER.AC_MULTI_EXCHANGE_RATE_ID,
        MER.OWNER_WORKGROUP_ID,
        MER.BU_CLIENT_WORKGROUP_ID,
        MER.BU_SUPPLIER_WORKGROUP_ID,
        MER.TARGET_WORKGROUP_TYPE,
        MER.FROM_AC_CURRENCY_ID,
        MER.TO_AC_CURRENCY_ID,
        MER.RATE,
        MER.ACTIVATE_DATE,
        <include refid="currencyColumns">
            <property name="alias" value="FC"/>
            <property name="prefix" value="FC_"/>
        </include>
        ,
        <include refid="currencyColumns">
            <property name="alias" value="TC"/>
            <property name="prefix" value="TC_"/>
        </include>
        FROM AC_MULTI_EXCHANGE_RATE MER
        JOIN AC_CURRENCY FC ON MER.FROM_AC_CURRENCY_ID = FC.AC_CURRENCY_ID
        JOIN AC_CURRENCY TC ON MER.TO_AC_CURRENCY_ID = TC.AC_CURRENCY_ID
        JOIN AC_WORKGROUP TWG ON MER.FROM_AC_CURRENCY_ID = TWG.TRANS_AC_CURRENCY_ID
        WHERE MER.OWNER_WORKGROUP_ID = #{workgroupId}
        AND MER.TARGET_WORKGROUP_TYPE = 'SUPPLIER'
        AND MER.ACTIVATE_DATE &lt;= SYSDATE
        AND MER.BU_CLIENT_WORKGROUP_ID IS NULL
        AND MER.BU_SUPPLIER_WORKGROUP_ID IS NULL
        AND TWG.AC_WORKGROUP_ID = #{targetGroupId}
        ORDER BY MER.ACTIVATE_DATE DESC
        ) T WHERE ROWNUM = 1
    </select>

    <select id="findClientDualCurrencyByTargetGroupId"  resultMap="allMerResultMap">
        SELECT T.* FROM (
        SELECT
        MER.AC_MULTI_EXCHANGE_RATE_ID,
        MER.OWNER_WORKGROUP_ID,
        MER.BU_CLIENT_WORKGROUP_ID,
        MER.BU_SUPPLIER_WORKGROUP_ID,
        MER.TARGET_WORKGROUP_TYPE,
        MER.FROM_AC_CURRENCY_ID,
        MER.TO_AC_CURRENCY_ID,
        MER.RATE,
        MER.ACTIVATE_DATE,
        <include refid="currencyColumns">
            <property name="alias" value="FC"/>
            <property name="prefix" value="FC_"/>
        </include>
        ,
        <include refid="currencyColumns">
            <property name="alias" value="TC"/>
            <property name="prefix" value="TC_"/>
        </include>
        FROM AC_MULTI_EXCHANGE_RATE MER
        JOIN AC_CURRENCY FC ON MER.FROM_AC_CURRENCY_ID = FC.AC_CURRENCY_ID
        JOIN AC_CURRENCY TC ON MER.TO_AC_CURRENCY_ID = TC.AC_CURRENCY_ID
        JOIN AC_WORKGROUP TWG ON MER.TO_AC_CURRENCY_ID = TWG.TRANS_AC_CURRENCY_ID
        WHERE MER.OWNER_WORKGROUP_ID = #{workgroupId}
        AND MER.TARGET_WORKGROUP_TYPE = 'CLIENT'
        <if test="buWorkgroupId > 0">
            AND MER.BU_CLIENT_WORKGROUP_ID = #{buWorkgroupId}
        </if>
        <if test="buWorkgroupId == -1">
            AND MER.BU_CLIENT_WORKGROUP_ID IS NULL
        </if>
        And MER.BU_SUPPLIER_WORKGROUP_ID IS NULL
        AND TWG.AC_WORKGROUP_ID = #{targetGroupId}
        ORDER BY MER.ACTIVATE_DATE DESC
        ) T WHERE ROWNUM = 1
    </select>

    <select id="findAllMultiExchangeRatesWithoutOrder" resultMap="allMerResultMap">
        SELECT
        MER.AC_MULTI_EXCHANGE_RATE_ID,
        MER.OWNER_WORKGROUP_ID,
        MER.BU_CLIENT_WORKGROUP_ID,
        MER.BU_SUPPLIER_WORKGROUP_ID,
        MER.TARGET_WORKGROUP_TYPE,
        MER.FROM_AC_CURRENCY_ID,
        MER.TO_AC_CURRENCY_ID,
        MER.RATE,
        MER.ACTIVATE_DATE,
        <include refid="currencyColumns">
            <property name="alias" value="FC"/>
            <property name="prefix" value="FC_"/>
        </include>,
        <include refid="currencyColumns">
            <property name="alias" value="TC"/>
            <property name="prefix" value="TC_"/>
        </include>
        <if test="supportSellSideMultiCurrency">
            <if test='"CLIENT".equalsIgnoreCase(targetWorkgroupType)'>
                ,<include refid="clientWorkgroupColumns">
                <property name="alias" value="C"/>
                <property name="prefix" value="C_"/>
            </include>
                ,<include refid="workgroupColumns">
                <property name="alias" value="W"/>
                <property name="prefix" value="C_W_"/>
            </include>
            </if>
        </if>
        FROM AC_MULTI_EXCHANGE_RATE MER
        JOIN AC_CURRENCY FC ON MER.FROM_AC_CURRENCY_ID = FC.AC_CURRENCY_ID
        JOIN AC_CURRENCY TC ON MER.TO_AC_CURRENCY_ID = TC.AC_CURRENCY_ID
        <if test="supportSellSideMultiCurrency">
            <if test='"CLIENT".equalsIgnoreCase(targetWorkgroupType)'>
                LEFT JOIN BU_CLIENT_WORKGROUP C ON MER.BU_CLIENT_WORKGROUP_ID = C.BU_CLIENT_WORKGROUP_ID
                LEFT JOIN AC_WORKGROUP W ON C.CLIENT_AC_WORKGROUP_ID = W.AC_WORKGROUP_ID
            </if>
        </if>
        WHERE MER.OWNER_WORKGROUP_ID = #{workgroupId}
        AND MER.TARGET_WORKGROUP_TYPE = UPPER(#{targetWorkgroupType})
        <if test="activeDateFrom != null and activeDateFrom != ''">
            AND MER.ACTIVATE_DATE &gt;=to_date(#{activeDateFrom}, 'MM/DD/YYYY HH24:MI:SS')
        </if>
        <if test="activeDateTo != null and activeDateTo != ''">
            AND MER.ACTIVATE_DATE &lt;=to_date(#{activeDateTo}, 'MM/DD/YYYY HH24:MI:SS')
        </if>
    </select>


    <select id="getMultiExchangeRateById"  resultMap="allMerResultMap">
        SELECT
        MER.AC_MULTI_EXCHANGE_RATE_ID,
        MER.OWNER_WORKGROUP_ID,
        MER.BU_CLIENT_WORKGROUP_ID,
        MER.BU_SUPPLIER_WORKGROUP_ID,
        MER.TARGET_WORKGROUP_TYPE,
        MER.FROM_AC_CURRENCY_ID,
        MER.TO_AC_CURRENCY_ID,
        MER.RATE,
        MER.ACTIVATE_DATE,
        <include refid="currencyColumns">
            <property name="alias" value="FC"/>
            <property name="prefix" value="FC_"/>
        </include>
        ,
        <include refid="currencyColumns">
            <property name="alias" value="TC"/>
            <property name="prefix" value="TC_"/>
        </include>
        ,<include refid="clientWorkgroupColumns">
             <property name="alias" value="C"/>
             <property name="prefix" value="C_"/>
        </include>
             ,<include refid="workgroupColumns">
             <property name="alias" value="W"/>
            <property name="prefix" value="C_W_"/>
         </include>
        FROM AC_MULTI_EXCHANGE_RATE MER
        JOIN AC_CURRENCY FC ON MER.FROM_AC_CURRENCY_ID = FC.AC_CURRENCY_ID
        JOIN AC_CURRENCY TC ON MER.TO_AC_CURRENCY_ID = TC.AC_CURRENCY_ID
        LEFT JOIN BU_CLIENT_WORKGROUP C ON MER.BU_CLIENT_WORKGROUP_ID = C.BU_CLIENT_WORKGROUP_ID
        LEFT JOIN AC_WORKGROUP W ON C.CLIENT_AC_WORKGROUP_ID = W.AC_WORKGROUP_ID
        WHERE MER.AC_MULTI_EXCHANGE_RATE_ID = #{exchangeRateId}
    </select>
</mapper>