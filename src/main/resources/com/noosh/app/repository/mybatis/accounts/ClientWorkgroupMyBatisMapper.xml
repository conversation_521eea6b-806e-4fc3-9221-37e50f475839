<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>

<mapper namespace="com.noosh.app.repository.mybatis.accounts.ClientWorkgroupMyBatisMapper">

    <resultMap id="clientWorkgroupResultMap" type="com.noosh.app.commons.dto.accounts.ClientWorkgroupDTO">
        <id property="id" column="BU_CLIENT_WORKGROUP_ID"/>
        <result property="ownerWorkgroupId" column="OWNER_AC_WORKGROUP_ID"/>
        <result property="clientWorkgroupId" column="CLIENT_AC_WORKGROUP_ID"/>
        <result property="markupPercent" column="MARKUP_PERCENT"/>
        <result property="markup" column="MARKUP"/>
        <result property="markupCurrencyId" column="MARKUP_AC_CURRENCY_ID"/>
        <result property="isMarkupVisible" column="IS_MARKUP_VISIBLE"/>
        <result property="isReferenced" column="IS_REFERENCED"/>
        <result property="isInactive" column="IS_INACTIVE"/>
        <result property="isNoosh" column="IS_NOOSH"/>
        <result property="name" column="NAME"/>
        <result property="addressId" column="AC_ADDRESS_ID"/>
        <result property="clientCode" column="CLIENT_CODE"/>
        <result property="customPropertyId" column="CUSTOM_PR_PROPERTY_ID"/>
        <result property="requiresPricing" column="PSF_REQUIRES_PRICING"/>
        <result property="paymentMethodId" column="BU_PAYMENT_METHOD_ID"/>
        <result property="defaultClientUserId" column="DEFAULT_CLIENT_USER_ID"/>
        <result property="isMarginVisible" column="IS_MARGIN_VISIBLE"/>
        <result property="marginPercent" column="MARGIN_PERCENT"/>
        <result property="transactionalCurrencyId" column="TRANS_AC_CURRENCY_ID"/>
        <result property="quoteValidDays" column="QUOTE_VALID_DAYS"/>
        <result property="isProjectLevelInactive" column="IS_PROJECT_LEVEL_INACTIVE"/>
        <association property="clientWorkgroup" columnPrefix="W_" resultMap="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper.workgroupResultMap"/>
        <association property="address" columnPrefix="A_" resultMap="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper.addressResultMap"/>
    </resultMap>

    <sql id="clientWorkgroupColumns">
        ${alias}.BU_CLIENT_WORKGROUP_ID ${prefix}BU_CLIENT_WORKGROUP_ID,
        ${alias}.OWNER_AC_WORKGROUP_ID ${prefix}OWNER_AC_WORKGROUP_ID,
        ${alias}.CLIENT_AC_WORKGROUP_ID ${prefix}CLIENT_AC_WORKGROUP_ID,
        ${alias}.MARKUP_PERCENT ${prefix}MARKUP_PERCENT,
        ${alias}.MARKUP ${prefix}MARKUP,
        ${alias}.MARKUP_AC_CURRENCY_ID ${prefix}MARKUP_AC_CURRENCY_ID,
        ${alias}.IS_MARKUP_VISIBLE ${prefix}IS_MARKUP_VISIBLE,
        ${alias}.IS_REFERENCED ${prefix}IS_REFERENCED,
        ${alias}.IS_INACTIVE ${prefix}IS_INACTIVE,
        ${alias}.IS_NOOSH ${prefix}IS_NOOSH,
        ${alias}.NAME ${prefix}NAME,
        ${alias}.AC_ADDRESS_ID ${prefix}AC_ADDRESS_ID,
        ${alias}.CLIENT_CODE ${prefix}CLIENT_CODE,
        ${alias}.CUSTOM_PR_PROPERTY_ID ${prefix}CUSTOM_PR_PROPERTY_ID,
        ${alias}.PSF_REQUIRES_PRICING ${prefix}PSF_REQUIRES_PRICING,
        ${alias}.BU_PAYMENT_METHOD_ID ${prefix}BU_PAYMENT_METHOD_ID,
        ${alias}.DEFAULT_CLIENT_USER_ID ${prefix}DEFAULT_CLIENT_USER_ID,
        ${alias}.IS_MARGIN_VISIBLE ${prefix}IS_MARGIN_VISIBLE,
        ${alias}.MARGIN_PERCENT ${prefix}MARGIN_PERCENT,
        ${alias}.TRANS_AC_CURRENCY_ID ${prefix}TRANS_AC_CURRENCY_ID,
        ${alias}.QUOTE_VALID_DAYS ${prefix}QUOTE_VALID_DAYS,
        ${alias}.IS_PROJECT_LEVEL_INACTIVE ${prefix}IS_PROJECT_LEVEL_INACTIVE
    </sql>

    <select id="findClientsByProjectLevelActive" resultMap="clientWorkgroupResultMap">
        SELECT
            <include refid="clientWorkgroupColumns">
                <property name="alias" value="C"/>
                <property name="prefix" value=""/>
            </include>,
            <include refid="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper.workgroupColumns">
                <property name="alias" value="W"/>
                <property name="prefix" value="W_"/>
            </include>,
            <include refid="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper.addressColumns">
                <property name="alias" value="A"/>
                <property name="prefix" value="A_"/>
            </include>,
            <include refid="com.noosh.app.repository.mybatis.security.CountryMyBatisMapper.countryColumns">
                <property name="alias" value="T"/>
                <property name="prefix" value="A_T_"/>
            </include>,
            (upper(C.name) || upper(W.name)) AS display_name
        FROM BU_CLIENT_WORKGROUP C,
            AC_WORKGROUP W,
            AC_ADDRESS A,
            AC_COUNTRY T
        WHERE C.CLIENT_AC_WORKGROUP_ID = W.AC_WORKGROUP_ID(+)
        AND C.OWNER_AC_WORKGROUP_ID = #{ownerWorkgroupId}
        <choose>
            <when test="isProjectLevelActive">
                AND (C.IS_PROJECT_LEVEL_INACTIVE = 0
                OR C.IS_PROJECT_LEVEL_INACTIVE IS NULL )
            </when>
            <otherwise>
                AND C.IS_PROJECT_LEVEL_INACTIVE = 1
            </otherwise>
        </choose>
        AND C.IS_INACTIVE != 1
        AND C.AC_ADDRESS_ID = A.AC_ADDRESS_ID (+)
        AND A.AC_COUNTRY_ID = T.AC_COUNTRY_ID (+)
        <if test="searchStr != null and searchStr != ''">
            AND (UPPER(C.name) like #{searchStr} ESCAPE '/'
                OR UPPER(C.CLIENT_CODE) like #{searchStr} ESCAPE '/'
                OR UPPER(W.name) like #{searchStr} ESCAPE '/')
        </if>
    </select>

    <select id="findAllSimpleClientList" resultMap="clientWorkgroupResultMap">
        SELECT
        <include refid="clientWorkgroupColumns">
            <property name="alias" value="C"/>
            <property name="prefix" value=""/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper.workgroupColumns">
            <property name="alias" value="W"/>
            <property name="prefix" value="W_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper.addressColumns">
            <property name="alias" value="A"/>
            <property name="prefix" value="A_"/>
        </include>,
        <include refid="com.noosh.app.repository.mybatis.security.CountryMyBatisMapper.countryColumns">
            <property name="alias" value="T"/>
            <property name="prefix" value="A_T_"/>
        </include>
        FROM BU_CLIENT_WORKGROUP C,
        AC_WORKGROUP W,
        AC_ADDRESS A,
        AC_COUNTRY T
        WHERE C.CLIENT_AC_WORKGROUP_ID = W.AC_WORKGROUP_ID(+)
        AND C.OWNER_AC_WORKGROUP_ID = #{ownerWorkgroupId}
        AND C.AC_ADDRESS_ID = A.AC_ADDRESS_ID (+)
        AND A.AC_COUNTRY_ID = T.AC_COUNTRY_ID (+)
    </select>

    <select id="findClients" resultMap="clientWorkgroupResultMap">
        SELECT
            <include refid="clientWorkgroupColumns">
                <property name="alias" value="C"/>
                <property name="prefix" value=""/>
            </include>,
            <include refid="com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper.workgroupColumns">
                <property name="alias" value="W"/>
                <property name="prefix" value="W_"/>
            </include>,
            <include refid="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper.addressColumns">
                <property name="alias" value="A"/>
                <property name="prefix" value="A_"/>
            </include>,
            <include refid="com.noosh.app.repository.mybatis.security.CountryMyBatisMapper.countryColumns">
                <property name="alias" value="T"/>
                <property name="prefix" value="A_T_"/>
            </include>
        FROM
            BU_CLIENT_WORKGROUP  C,
            AC_WORKGROUP         W,
            AC_ADDRESS           A,
            AC_COUNTRY           T
        WHERE
            C.CLIENT_AC_WORKGROUP_ID = W.AC_WORKGROUP_ID (+)
            AND C.OWNER_AC_WORKGROUP_ID = #{ownerWorkgroupId}
            AND C.IS_INACTIVE != 1
            AND C.AC_ADDRESS_ID = A.AC_ADDRESS_ID (+)
            AND A.AC_COUNTRY_ID = T.AC_COUNTRY_ID (+)
    </select>
</mapper>