<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.accounts.WorkgroupAttributeTypeMyBatisMapper">

    <resultMap id="workgroupAttributeTypeResultMap" type="com.noosh.app.commons.dto.accounts.WorkgroupAttributeTypeDTO">
        <id property="id" column="AC_WG_ATTRIBUTE_TYPE_ID"/>
        <result property="labelStr" column="LABEL"/>
        <result property="labelStrId" column="LABEL_STR_ID"/>
        <result property="constantToken" column="CONSTANT_TOKEN"/>
        <result property="isRestricted" column="IS_RESTRICTED"/>
        <result property="workgroupId" column="AC_WORKGROUP_ID"/>
        <result property="options" column="OPTIONS"/>
    </resultMap>

    <sql id="workgroupAttributeTypeColumns">
        ${alias}.AC_WG_ATTRIBUTE_TYPE_ID ${prefix}AC_WG_ATTRIBUTE_TYPE_ID,
        ${alias}.LABEL ${prefix}LABEL,
        ${alias}.LABEL_STR_ID ${prefix}LABEL_STR_ID,
        ${alias}.CONSTANT_TOKEN ${prefix}CONSTANT_TOKEN,
        ${alias}.IS_RESTRICTED ${prefix}IS_RESTRICTED,
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID,
        ${alias}.OPTIONS ${prefix}OPTIONS
    </sql>

    <select id="findUnrestrictedTypesByWgId" resultMap="workgroupAttributeTypeResultMap">
        SELECT
        <include refid="workgroupAttributeTypeColumns">
            <property name="alias" value="WGT"/>
            <property name="prefix" value=""/>
        </include>
        FROM AC_WG_ATTRIBUTE_TYPE WGT
        WHERE WGT.AC_WORKGROUP_ID = #{workgroupId}
        AND WGT.IS_RESTRICTED = 0
    </select>

    <select id="findTypesByWgIdAndLabel" resultMap="workgroupAttributeTypeResultMap">
        SELECT
        <include refid="workgroupAttributeTypeColumns">
            <property name="alias" value="WGT"/>
            <property name="prefix" value=""/>
        </include>
        FROM AC_WG_ATTRIBUTE_TYPE WGT
        WHERE WGT.AC_WORKGROUP_ID = #{workgroupId}
        AND WGT.LABEL = #{label}
    </select>
</mapper>
