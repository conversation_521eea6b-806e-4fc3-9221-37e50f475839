<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.accounts.WarehouseLocationMyBatisMapper">

    <resultMap id="warehouseLocationResultMap" type="com.noosh.app.commons.dto.accounts.WarehouseLocationDTO">
        <id property="id" column="AC_WHLOCATION_ID"/>
        <result property="name" column="NAME"/>
        <result property="phoneNumber" column="PHONE_NUMBER"/>
        <result property="workgroupId" column="AC_WORKGROUP_ID"/>
        <result property="addressTypeId" column="AC_ADDRESS_TYPE_ID"/>
        <result property="addressId" column="AC_ADDRESS_ID"/>
        <result property="contactUserId" column="CONTACT_USER_ID"/>
        <result property="operatingHours" column="OP_HOURS"/>
        <result property="objectStateId" column="OC_OBJECT_STATE_ID"/>
        <association property="address" columnPrefix="ADDR_" resultMap="com.noosh.app.repository.mybatis.security.AddressMyBatisMapper.addressResultMap"/>
        <association property="contactUser" columnPrefix="U_" resultMap="com.noosh.app.repository.mybatis.security.AccountUserMyBatisMapper.accountUserResultMap"/>
    </resultMap>

    <sql id="warehouseLocationColumns">
        ${alias}.AC_WHLOCATION_ID ${prefix}AC_WHLOCATION_ID,
        ${alias}.NAME ${prefix}NAME,
        ${alias}.PHONE_NUMBER ${prefix}PHONE_NUMBER,
        ${alias}.AC_WORKGROUP_ID ${prefix}AC_WORKGROUP_ID,
        ${alias}.AC_ADDRESS_TYPE_ID ${prefix}AC_ADDRESS_TYPE_ID,
        ${alias}.AC_ADDRESS_ID ${prefix}AC_ADDRESS_ID,
        ${alias}.CONTACT_USER_ID ${prefix}CONTACT_USER_ID,
        ${alias}.OP_HOURS ${prefix}OP_HOURS,
        ${alias}.OC_OBJECT_STATE_ID ${prefix}OC_OBJECT_STATE_ID
    </sql>
</mapper>
