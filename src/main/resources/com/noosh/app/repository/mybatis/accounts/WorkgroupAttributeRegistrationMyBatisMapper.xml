<?xml version='1.0' encoding='UTF-8' ?>
<!DOCTYPE mapper PUBLIC '-//mybatis.org//DTD Mapper 3.0//EN' 'http://mybatis.org/dtd/mybatis-3-mapper.dtd'>
<mapper namespace="com.noosh.app.repository.mybatis.accounts.WorkgroupAttributeRegistrationMyBatisMapper">

    <resultMap id="workgroupAttributeRegistrationResultMap" type="com.noosh.app.commons.dto.accounts.WorkgroupAttributeRegistrationDTO">
        <id property="id" column="AC_WG_ATTRIBUTE_REG_ID"/>
        <result property="ownerWorkgroupId" column="OWNER_AC_WORKGROUP_ID"/>
        <result property="workgroupAttributeId" column="AC_WG_ATTRIBUTE_ID"/>
        <result property="isActive" column="IS_ACTIVE"/>
        <result property="ordinalNumber" column="ORDINAL_NUMBER"/>
    </resultMap>

    <sql id="workgroupAttributeRegistrationColumns">
        ${alias}.AC_WG_ATTRIBUTE_REG_ID ${prefix}AC_WG_ATTRIBUTE_REG_ID,
        ${alias}.OWNER_AC_WORKGROUP_ID ${prefix}OWNER_AC_WORKGROUP_ID,
        ${alias}.AC_WG_ATTRIBUTE_ID ${prefix}AC_WG_ATTRIBUTE_ID,
        ${alias}.IS_ACTIVE ${prefix}IS_ACTIVE,
        ${alias}.ORDINAL_NUMBER ${prefix}ORDINAL_NUMBER
    </sql>
    
    <select id="findNextOrdinalNumberByWgIdAndTypeId" resultType="java.lang.Long">
        SELECT
            CASE WHEN max(WGR.ORDINAL_NUMBER) is NULL THEN 0 ELSE max(WGR.ORDINAL_NUMBER) END  WGR_ORDINAL_NUMBER
        FROM AC_WG_ATTRIBUTE_REG WGR,
        AC_WG_ATTRIBUTE WGA, 
        AC_WORKGROUP WG
        WHERE WGR.OWNER_AC_WORKGROUP_ID = WG.AC_WORKGROUP_ID
        AND WGR.AC_WG_ATTRIBUTE_ID = WGA.AC_WG_ATTRIBUTE_ID
        AND WGA.AC_WG_ATTRIBUTE_TYPE_ID = #{typeId}
        AND WG.AC_WORKGROUP_ID = #{workgroupId}
    </select>

</mapper>
