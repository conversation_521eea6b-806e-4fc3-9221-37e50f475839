spring:
  application:
    name: workgroupresource
  cloud:
    config:
      name: workgroupresource
      fail-fast: true
      retry:
        initialInterval: 5000
        maxInterval: 10000
        maxAttempts: 50

info:
  NAME: "workgroupresource"
  DOCKER_REPO: "PLACEHOLDER_DOCKER_REPO"
  DOCKER_TAG: "PLACEHOLDER_DOCKER_TAG"
  SOURCE_BRANCH: "PLACEHOLDER_SOURCE_BRANCH"
  SOURCE_COMMIT: "PLACEHOLDER_SOURCE_COMMIT"
  COMMIT_MSG: "PLACEHOLDER_COMMIT_MSG"