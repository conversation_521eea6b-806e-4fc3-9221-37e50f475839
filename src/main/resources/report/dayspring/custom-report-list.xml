<?xml version="1.0" encoding="UTF-8" ?>
<report-list>
    <report-category name="Custom Reports">
        <report title="Kit Summary Report" description="Kit Summary Report" nrd="/kit-summary.nrd" type="standard" action="run schedule" icon="realtime"/>
        <report title="Kit Summary by component Report" description="Kit Summary by component Report" nrd="/kit-summary-by-component.nrd" type="standard" action="run schedule" icon="realtime"/>
        <report title="Proposal By Kit Report" description="Proposal By Kit Report" nrd="/shared/proposal-by-kit.nrd" type="standard" action="run schedule" icon="realtime"/>
        <report title="Proposal By Item Report" description="Proposal By Item Report" nrd="/shared/proposal-by-item.nrd" type="standard" action="run schedule" icon="realtime"/>
    </report-category>

    <report-category name="Data Extraction Reports - Walmart">
        <report title="Projects" description="Project Report for Walmart" type="data-extraction" nrd="/project-walmart.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER perm:CREATE_REPORT_SPEC" />
        <report title="Orders" description="Order Report for Walmart" type="data-extraction" nrd="/walmart-orders.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER perm:CREATE_REPORT_SPEC" />
        <report title="Order Items (Aggregated)" description="Order item (Aggregated) report for Walmart" type="data-extraction" nrd="/aggr-order-item-walmart.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
    </report-category>

</report-list>
