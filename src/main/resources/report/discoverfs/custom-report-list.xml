<?xml version="1.0" encoding="UTF-8" ?>
<report-list>
    <report-category name="Custom Reports">
        <!--<report title="Invoice Export - (Live)" description="Invoice Export - (Live)" nrd="/dfs-invoice-live.nrd" type="standard" action="run schedule" icon="realtime"/>-->
        <!--<report title="Invoice Export - (Live-Prepaid)" description="Invoice Export - (Live-Prepaid)" nrd="/dfs-invoice-live-prepaid.nrd" type="standard" action="run schedule" icon="realtime"/>-->
        <!--<report title="Invoice Export - (Live-Prepaid post Ariba)" description="Invoice Export - (Live-Prepaid post Ariba)" nrd="/dfs-invoice-live-prepaid-post-ariba.nrd" type="standard" action="run schedule" icon="realtime"/>-->
        <!--<report title="Invoice Export " description="Invoice Export" nrd="/dfs-invoice.nrd" type="standard" action="run schedule" icon="dfs"/>-->
        <report title="Partial Invoice Approval Audit Report" icon="datamart" action="run schedule" type="standard" nrd="/dfs-partial-invoice.nrd" description="Partial Invoice Approval Audit Report"/>
        <!--<report title="PO Export MOB " icon="dfs" action="run" type="standard" nrd="/po-export-mob.nrd" description="PO Export MOB"/>-->
        <!--<report title="PO Export MOB new format " icon="dfs" action="run" type="standard" nrd="/po-export-mob-new.nrd" description="PO Export MOB with MOB date added"/>-->
        <report title="PO Export MOB new format(No Future MOB) " icon="datamart" action="run" type="standard" nrd="/po-export-mob-new-no-future-mob.nrd" description="PO Export MOB with MOB date added(No Future MOB)"/>
        <report title="PO Export Accounting " icon="datamart" action="run" type="standard" nrd="/po-export-accounting.nrd" description="PO Export Accounting"/>
        <report title="PO Export Accounting(No Future MOB) " icon="datamart" action="run" type="standard" nrd="/po-export-accounting-no-future-mob.nrd" description="PO Export Accounting(No Future MOB)"/>
        <report title="PO Export Order " icon="datamart" action="run" type="standard" nrd="/po-export-order.nrd" description="PO Export Order"/>
        <report title="PO Export Order(No Future MOB) " icon="datamart" action="run" type="standard" nrd="/po-export-order-no-future-mob.nrd" description="PO Export Order(No Future MOB)"/>
        <!--<report title="Noosh Paper Obsolescence " icon="dfs" action="run" type="standard" nrd="/dfs-paper-obsolescence.nrd" description="Paper Obsolescence"/>-->
        <report title="Noosh Paper Financial Activity Report By SKU" icon="datamart" action="run" type="standard" nrd="/dfs-paper-financial-activity-by-sku.nrd" description="Noosh Paper Financial Activity Report By SKU"/>
        <report title="Monthly Prepaid Report - Finance" icon="datamart" action="run schedule" type="standard" nrd="/dfs-monthly-prepaid.nrd" description="Monthly Prepaid Report - Finance"/>
        <!--<report title="BP Financial"  nrd="/bp-financial.nrd" type="standard" action="run schedule" icon="dfs" description="AND Order: Supplier Workgroup Name not contains Discover Financial OR Order: Order Closed Date is empty And Invoice Status is NOT rejected, retracted, replaced, cancelled"/>-->
        <report title="BP Financial 2018"  nrd="/bp-financial-2018.nrd" type="standard" action="run schedule" icon="datamart" description="AND Order: Supplier Workgroup Name not contains Discover Financial OR Order: Order Closed Date is empty And Invoice Status is NOT rejected, retracted, replaced, cancelled"/>
    </report-category>
    
    <report-category name="Data Extraction Reports">
        <report title="DFS: Procurement Report - Shipment" description="DFS: Procurement Report - Shipment" type="data-extraction" nrd="/dfs-new-caps-month-end-report.nrd" action="edit" icon="datamart"/>
        <report title="Original, Change and Final Cost with Cost Centers" description="Returns original order items + its respective change costs as columns + its Cost Centers. Only accepted orders and pending/accepted change orders are inlcuded in the output." type="data-extraction" nrd="/procurement/order-orig-change-final-cc.nrd" action="edit" icon="datamart"  check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT  perm:CREATE_REPORT_SPEC" />
    </report-category>


</report-list>
