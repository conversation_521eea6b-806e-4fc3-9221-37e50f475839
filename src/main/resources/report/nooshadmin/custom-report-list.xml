<?xml version="1.0" encoding="UTF-8" ?>
<report-list>
    <report-category name="User Reports">
        <report title="User Login Activities" description="User Login Activities" type="standard" nrd="/user-login-activities.nrd" action="run schedule" icon="datamart" />
        <report title="Nooshone User Login Activities" description="Nooshone User Login Activities Report" nrd="/nooshone-user-activities.nrd"
                type="standard" action="run" icon="realtime"/>
        <report title="Customer Activities" description="Customer Activities Report" nrd="/noosh-workgroup-activities.nrd"
                type="standard" action="run edit" icon="realtime"/>
        <report title="Workgroup Preference Report" description="Workgroup Preference Report" nrd="/noosh-workgroup-preference.nrd"
                type="standard" action="run" icon="realtime"/>
    </report-category>
    <report-category name="Order Activity Reports">
        <report title="Accepted Orders Analysis" description="A cross-customer analysis of accepted orders, by month, with averages of derived order activity data." nrd="/accepted_orders_analysis.nrd" type="standard" action="run" icon="datamart"/>
        <report title="NewlineNoosh All Managed Services - Order Activity by Buyer and Supplier" description="Analyze monthly order activity for all NewlineNoosh Managed Services workgroups by buyer and supplier." nrd="/order-activity-buyer-sup-all-ms.nrd" type="standard" action="run" icon="datamart"/>
        <report title="NewlineNoosh All Workgroups - Order Activity by Buyer and Supplier" description="Analyze monthly order activity for all non-supplier workgroups (USD currency) by buyer and supplier." nrd="/order-activity-buyer-sup-all-workgroups.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Unisource Activity" description="A cross-customer analysis of accepted orders for the Unisource client accounts." nrd="/uww-activity.nrd"  type="standard" action="run" icon="datamart"/>
    </report-category>

    <report-category name="Job-Specific Bid Analysis Reports">
        <report title="NewlineNoosh All Managed Services - Job-Specific Initial Bid Analysis with Bid Count Aggregation" description="Analyze estimates over a specified time period. Show savings over the average and maximum estimates of an order and premium paid over the minimum estimate of an order" type="standard" nrd="/job-analysis-with-bid-count-aggr-all-ms.nrd" action="run" icon="datamart"/>
        <report title="NewlineNoosh All Workgroups - Job-Specific Initial Bid Analysis with Bid Count Aggregation" description="Analyze estimates over a specified time period. Show savings over the average and maximum estimates of an order and premium paid over the minimum estimate of an order" type="standard" nrd="/job-analysis-with-bid-count-aggr-all-workgroups.nrd" action="run" icon="datamart"/>
    </report-category>

    <report-category name="Acceptance Billing Reports">
        <report title="Point" description="Point Acceptance Billing Report." nrd="/order-activity-point.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Charter House" description="Charter House Billing Report." nrd="/order-activity-charterhouse.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Charter House excluded Postage" description="Charter House Billing Report excluded Postage." nrd="/order-activity-exclude-postage-charterhouse.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Charter House excluded Postage/Environmental Tax" description="Charter House excluded Postage/Environmental Tax." nrd="/order-activity-exclude-postage-environmental-tax-charterhouse.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Workflow Enterprise" description="Workflow Enterprise Acceptance Billing Report." nrd="/order-activity-wfo.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Workflow Enterprise - New" description="Workflow Enterprise Acceptance Billing Report." nrd="/order-activity-wfo-new.nrd" type="standard" action="run" icon="datamart"/>
        <report title="PBMS - UK" description="NLN Billing Report - PBMS UK." nrd="/nln-billing-report-pbms-uk.nrd" type="standard" action="run schedule" icon="datamart"/>
        <report title="Graphic Communications" description="Graphic Communications Acceptance Billing Report." nrd="/order-activity-gc.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Communisis Orders" description="Communisis Acceptance Orders Report." nrd="/order-activity-communisis.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Communisis User List" description="Communisis User List Report." nrd="/user-list-communisis.nrd" type="standard" action="run" icon="realtime"/>
    </report-category>

    <report-category name="Finance">
        <report title="Billable Events" description="Monthly billable events report" nrd="/billable-events.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Active Default Users" description="Monthly count of registered default active users" nrd="/active_default_users.nrd" type="standard" action="run" icon="realtime"/>
        <report title="West Orders" description="West Orders" nrd="/west-orders.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Novitex Orders" description="Novitex Orders" nrd="/PBUS-orders.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Alia Premedia Orders" description="Alia Premedia Orders" nrd="/alia-premedia-orders.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Orion Orders" description="PBUS Orders" nrd="/orion-orders.nrd" type="standard" action="run" icon="datamart"/>
        <report title="PBUK Orders" description="PBUK Orders" nrd="/PBUK-orders.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Sothebys  Orders" description="Sothebys  Orders" nrd="/sothebys-orders.nrd" type="standard" action="run" icon="datamart"/>
        <report title="SPS  Orders" description="SPS  Orders" nrd="/SPS-orders.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Bunzl  Orders" description="Bunzl  Orders" nrd="/Bunzl-orders.nrd" type="standard" action="run" icon="datamart"/>
    </report-category>

    <report-category name="NooshOne">
        <report title="NooshOne Beta Acceptance Report" description="NooshOne Beta Acceptance Report." nrd="/nooshone-beta.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Workgroups with Noosh One Turn On" description="Workgroups with Noosh One Turn On" nrd="/workgroups-nooshone.nrd" type="standard" action="run" icon="realtime"/>
        <report title="NooshOne Whitelist Report" description="NooshOne Whitelist Report" nrd="/nooshone-whitelist.nrd" type="standard" action="run" icon="realtime"/>
    </report-category>
</report-list>