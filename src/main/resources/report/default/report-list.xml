<?xml version="1.0" encoding="UTF-8" ?>
<report-list xmlns:th="http://www.thymeleaf.org">
    <report-category name="Account Reports" >
        <report title="Account Activity (buying)"  description="Procurement activities (RFEs, Orders) by buyer and supplier." type="standard" url="/report/standard/activityInput?buying=1" action="run" icon="realtime" check="pref:WORKGROUP_OPTION_ACCOUNT_ACTIVITY_BUYING_REPORT perm:RUN_ALL_ACTIVITY_REPORT perm:RUN_LIMITED_ACTIVITY_REPORT" />
        <report title="Account Activity (selling)" description="Procurement activities (RFEs, Orders) by buyer and supplier." type="standard" url="/report/standard/activityInput?buying=0" action="run" icon="realtime" check="pref:WORKGROUP_OPTION_ACCOUNT_ACTIVITY_SELLING_REPORT perm:RUN_ALL_ACTIVITY_REPORT perm:RUN_LIMITED_ACTIVITY_REPORT" />
        <report title="Workgroup Members" description="Workgroup member report" articleId="************" type="data-extraction" nrd="/user.nrd" action="edit" icon="datamart"  check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER perm:CREATE_REPORT_SPEC" />
        <report title="Workgroup Suppliers" description="Workgroup Supplier report" articleId="************" type="data-extraction" nrd="/workgroup/supplier.nrd" action="edit" icon="datamart"  check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER perm:CREATE_REPORT_SPEC" />
    </report-category>

    <report-category name="Procurement Reports">
        <report title="Invoices" description="Invoice report" type="data-extraction" nrd="/procurement/invoice.nrd" action="edit" icon="datamart"  check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT pref:WORKGROUP_OPTION_ORDER_INVOICING perm:CREATE_REPORT_SPEC" />
        <report title="Invoice Items" description="Invoice item report" type="data-extraction" nrd="/procurement/invoice-item.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT pref:WORKGROUP_OPTION_ORDER_INVOICING perm:CREATE_REPORT_SPEC" />
        <report title="Invoice Base" description="Invoice base" type="data-extraction" nrd="/procurement/invoice-base.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT pref:WORKGROUP_OPTION_ORDER_INVOICING perm:CREATE_REPORT_SPEC" />
        <report title="Order Invoices" description="Order Invoices report" type="data-extraction" nrd="/procurement/order-invoice.nrd" action="edit" icon="datamart"  check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT pref:WORKGROUP_OPTION_ORDER_INVOICING perm:CREATE_REPORT_SPEC" />
        <report title="Aggr Order Invoices" description="Aggregated Order Invoices report" type="data-extraction" nrd="/procurement/aggr-order-invoice.nrd" action="edit" icon="datamart"  check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT pref:WORKGROUP_OPTION_ORDER_INVOICING perm:CREATE_REPORT_SPEC" />
        <report title="Orders" description="Order report" articleId="360042609592" type="data-extraction" nrd="/procurement/order.nrd" action="edit" icon="datamart"  check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT  perm:CREATE_REPORT_SPEC" />

        <report title="Orders (Aggregated)" description="Order (Aggregated) report" articleId="360042609692" type="data-extraction" nrd="/procurement/aggr-order.nrd" action="edit" icon="datamart"  check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT  perm:CREATE_REPORT_SPEC" />
        <report title="Original, Change and Final Cost" description="Returns original order items + its respective change costs as columns. Only accepted orders and pending/accepted change orders are inlcuded in the output." type="data-extraction" nrd="/procurement/order-orig-change-final.nrd" action="edit" icon="datamart"  check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT  perm:CREATE_REPORT_SPEC" />
        <report title="Original, Change and Final Cost(RFE)" description="Returns original order items + its respective change costs as columns,Included RFE. Only accepted orders and pending/accepted change orders are inlcuded in the output." type="data-extraction" nrd="/procurement/order-orig-change-final-rfe.nrd" action="edit" icon="datamart"  check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT  perm:CREATE_REPORT_SPEC" />
        <report title="Order Items" description="Order item report" articleId="360043050751" type="data-extraction" nrd="/procurement/order-item.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />

        <report title="Order Items (Aggregated)" description="Order item (Aggregated) report" articleId="360042609712" type="data-extraction" nrd="/procurement/aggr-order-item.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
        <report title="Order Items (Aggregated) with Cost Centers" description=" The goal is to be allow the user to be able to see which orders have been closed and don’t have any cost centers allocated to it" type="data-extraction" nrd="/procurement/aggr-order-item-costcenter.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
        <report title="Order Item Breakouts" description="Order item price breakout report" type="data-extraction" nrd="/procurement/order-item-breakout.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
        <report title="RFEs" description="RFE report" articleId="360042609652" type="data-extraction" nrd="/procurement/rfe.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
        <report title="RFE Suppliers" description="RFE Suppliers report" type="data-extraction" nrd="/procurement/rfe-supplier.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
        <report title="RFE Items" description="RFE item report" type="data-extraction" nrd="/procurement/rfe-item.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
        <report title="Estimates" description="Estimate report" articleId="360043050791" type="data-extraction" nrd="/procurement/estimate.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
        <report title="Estimate Items" description="Estimate item report"  articleId="360042609672" type="data-extraction" nrd="/procurement/estimate-item.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
        <report title="Estimate Items and RFE Items" description="Estimate item and RFE item report" type="data-extraction" nrd="/procurement/estimate-item-rfe-item.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />

        <report title="Estimate Item Price Breakout" description="Estimate Item Price Breakout report" type="data-extraction" nrd="/procurement/estimate-item-price-breakout.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
<!--        #if ($user.preferences.check("WORKGROUP_OPTION_OUTSOURCING") || $user.preferences.check("WORKGROUP_OPTION_BROKERING"))-->
            <report th:if="${isOutSourcingOrBrokering}" title="Quote Price" description="Quote price report" type="data-extraction" nrd="/procurement/quote-price-outsourcer.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
            <report th:if="${isOutSourcingOrBrokering}" title="RFQ (Outsourcer)" description="RFQ reports" type="data-extraction" nrd="/procurement/rfq-outsourcer.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
            <report th:if="${isOutSourcingOrBrokering}" title="Outsourcing Markup Analysis" description="Outsourcing Markup Analysis report" type="data-extraction" nrd="/procurement/outsourcing-markup-analysis.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
            <report th:if="${isOutSourcingOrBrokering}" title="Outsourcing Markup Analysis and Spec" description="Outsourcing Markup Analysis and Spec info report" type="data-extraction" nrd="/procurement/outsourcing-markup-analysis-spec.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
<!--			#if($portalName == "communisis" || "$user.groupId" == "5128617")-->
              <report th:if="${isOutSourcingOrBrokering}" title="Outsourcing Markup Analysis Invoice and Spec" description="Outsourcing Markup Analysis with Spec and Invoice Report"
              	type="data-extraction" nrd="/procurement/outsourcing-markup-analysis-spec-invoice.nrd" action="edit" icon="datamart" 
              	check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
<!--        	#end-->
<!--        #end-->
        <report title="Sourcing Strategies" description="Sourcing strategy report" type="data-extraction" nrd="/procurement/sourcing-strategy.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_SOURCING_STRATEGY_REPORTS perm:CREATE_REPORT_SPEC" />
        <report title="Sourcing Strategies including Change Order" description="Sourcing strategy including Change Order report" type="data-extraction"
			 nrd="/procurement/sourcing-strategy-change-order.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_SOURCING_STRATEGY_REPORTS perm:CREATE_REPORT_SPEC" />
        <report title="Sourcing Strategy Savings" description="Sourcing strategy savings report" type="data-extraction" nrd="/procurement/sourcing-strategy-savings.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_SOURCING_STRATEGY_REPORTS  perm:CREATE_REPORT_SPEC" />
        <report title="Order Item - Sourcing Strategy Savings" description="Order Item - Sourcing Strategy Savings report" type="data-extraction" nrd="/procurement/sourcing-strategy-savings-sd.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_SOURCING_STRATEGY_REPORTS  perm:CREATE_REPORT_SPEC" />
        <report title="Order Items (Aggregated) with Sourcing Strategies" description="Shows Sourcing Strategies as an outer join to Order Items" type="data-extraction" nrd="/procurement/order-item-with-sourcing-strategy.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_SOURCING_STRATEGY_REPORTS  perm:CREATE_REPORT_SPEC" />
        <report title="Shipments" description="Shipments Report" type="data-extraction" nrd="/procurement/shipment.nrd" action="edit" icon="datamart"  check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT  perm:CREATE_REPORT_SPEC" />
        <report title="Inventory Activity" description="Inventory Shipment Activity Report" type="data-extraction" nrd="/procurement/inventory-activity-shipment.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT pref:WORKGROUP_OPTION_INVENTORY perm:CREATE_REPORT_SPEC" />
        <report title="Proposals" description="Shows cost, price, mark-up, and margin information for proposals." type="standard" nrd="/procurement/proposal.nrd" action="run edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROPOSAL_WRITER  perm:CREATE_REPORT_SPEC" />
        <report title="Proposal Items" description="Shows cost, price and mark-up information for proposal items." type="standard" nrd="/procurement/proposal-item.nrd" action="run edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROPOSAL_WRITER  perm:CREATE_REPORT_SPEC" />
        <report title="Proposal Costs Analysis" description="Shows the proposed prices vs. the actual costs." type="standard" nrd="/procurement/proposal-costs-analysis.nrd" action="run edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROPOSAL_WRITER  perm:CREATE_REPORT_SPEC" />
        <report title="Outsourcing Profitability" description="Outsourcing report analyzing total accepted buy orders vs. total accepted sell orders, aggregated by project or order item." type="standard" url="/report/standard/outsourceProfitInput" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_OUTSOURCE_PROFIT_REPORT perm:RUN_OUTSOURCE_PROFIT_REPORT" />
        <report title="Bid Analysis Report" description="Displays all estimates for all orders, noting which estimate was awarded, and analyzing all estimates in comparison to the minimum estimate." type="standard" nrd="/procurement/procurement-bid-analysis.nrd" action="edit run" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT pref:WORKGROUP_OPTION_BID_ANALYSIS_REPORT perm:CREATE_REPORT_SPEC" />
        <report title="Estimate Analysis (new)" description="Analyze estimates over a specified time period. Show savings over the average estimate of an order and premium paid over the minimum estimate of an order" type="standard" nrd="/procurement/estimate-analysis.nrd" action="run edit" icon="datamart" check="pref:WORKGROUP_OPTION_ESTIMATE_ANALYSIS_REPORT perm:RUN_ESTIMATE_ANALYSIS_REPORT"/>
        <report title="Estimate Analysis with Bid Count Aggregation" description="Analyze estimates over a specified time period. Show savings over the average and maximum estimates of an order and premium paid over the minimum estimate of an order" type="standard" nrd="/procurement/estimate-analysis-with-bid-count-aggr.nrd" action="run edit" icon="datamart" check="pref:WORKGROUP_OPTION_ESTIMATE_ANALYSIS_BID_COUNT_REPORT perm:RUN_ESTIMATE_ANALYSIS_REPORT"/>
        <report title="Job-Specific Initial Bid Analysis with Bid Count Aggregation" description="Analyze estimates over a specified time period. Show savings over the average and maximum estimates of an order and premium paid over the minimum estimate of an order" type="standard" nrd="/procurement/job-analysis-with-bid-count-aggr.nrd" action="run edit" icon="datamart" check="pref:WORKGROUP_OPTION_JOB_ANALYSIS_BID_COUNT_REPORT perm:RUN_ESTIMATE_ANALYSIS_REPORT"/>
        <report title="Estimate Analysis" description="Analyze estimates over a specified time period. Show savings over the average estimate of an order and premium paid over the minimum estimate of an order" type="standard" url="/report/standard/estimateAnalysisParam" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_ESTIMATE_ANALYSIS_REPORT perm:RUN_ESTIMATE_ANALYSIS_REPORT" />
        <report title="Sell Price Analysis" description="Analyze estimates over a specified time period. Show savings over the average and maximum estimate of an order and premium paid over the minimum estimate of an order" type="standard" nrd="/procurement/sell-price-analysis.nrd" action="run edit" icon="datamart" check="pref:WORKGROUP_OPTION_SELL_PRICE_ANALYSIS_REPORT perm:RUN_ESTIMATE_ANALYSIS_REPORT"/>
        <report title="Sell Price Analysis with Bid Count Aggregation" description="Analyze estimates over a specified time period. Show savings over the average and maximum estimate of an order and premium paid over the minimum estimate of an order" type="standard" nrd="/procurement/sell-price-analysis-with-bid-count-aggr.nrd" action="run edit" icon="datamart" check="pref:WORKGROUP_OPTION_SELL_PRICE_ANALYSIS_BID_COUNT_REPORT perm:RUN_ESTIMATE_ANALYSIS_REPORT"/>
        <report title="Estimate Win-Loss" description="Winning and losing estimates analysis" type="standard" url="/report/standard/estimateWinLossParam" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_ESTIMATE_WINLOSS_REPORT perm:RUN_ESTIMATE_WINLOSS_REPORT" nrd=""/>
        <report title="RFE Win-Loss" description="Winning and losing RFE analysis" type="standard" nrd="/procurement/rfe-win-loss.nrd" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_RFE_WINLOSS_REPORT" />
        <report title="Order Activity" description="Analyze monthly order activity by buyer, supplier and project" type="standard" nrd="/procurement/order-activity.nrd" action="run edit" icon="datamart" check="pref:WORKGROUP_OPTION_ORDER_ACTIVITY_REPORT perm:RUN_ORDER_ACTIVITY_REPORT"/>
        <report title="Order Activity by Buyer and Supplier" description="Analyze monthly order activity by buyer and supplier" type="standard" nrd="/procurement/order-activity-buyer-sup.nrd" action="run schedule" icon="datamart" check="pref:WORKGROUP_OPTION_ORDER_ACTIVITY_BY_BUYER_SUPPLIER_REPORT perm:RUN_ORDER_ACTIVITY_REPORT"/>
        <report title="Cost Centers" description="Cost Center report" type="data-extraction" nrd="/procurement/cost_centers.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT pref:WORKGROUP_OPTION_COSTCENTER perm:CREATE_REPORT_SPEC" />
        <report title="Cost Centers (Invoice)" description="Cost Center (Invoice) report" type="data-extraction" nrd="/procurement/cost_centers_invoice.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT pref:WORKGROUP_OPTION_COSTCENTER perm:CREATE_REPORT_SPEC" />
        <report title="Cost Center" description="Analyze the Monthly Order Activity for all Cost Centers." type="standard" nrd="/procurement/cost-center.nrd" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_COST_CENTER_REPORT perm:RUN_COST_CENTER_REPORT" />
        <report title="Order Status" description="Analyze monthly job activity." type="standard" nrd="/procurement/order-status.nrd" action="run edit" icon="datamart" check="pref:WORKGROUP_OPTION_ORDER_STATUS_REPORT perm:RUN_ORDER_STATUS_REPORT"/>
        <report title="Shipments per Order Change Report" description="Displays all shipments for all orders and change orders.  Note:  The complete shipment information is returned for every change order of a given order." type="standard" nrd="/procurement/shipment-order.nrd" action="edit run" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT pref:WORKGROUP_OPTION_UNCUT_SHIPMENT_ORDER_REPORT perm:CREATE_REPORT_SPEC" />
        <report title="Supplier Rating (Standard)" description="Supplier Rating Report" type="standard" nrd="/procurement/supplier-rating.nrd" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_SUPPLIER_RATING_REPORT perm:CREATE_REPORT_SPEC perm:RUN_SUPPLIER_RATING_REPORT" />
        <report title="Detailed Supplier Rating" description="Supplier Rating Report - Details per Supplier" type="standard" nrd="/procurement/supplier-rating-new.nrd" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_SUPPLIER_RATING_REPORT perm:CREATE_REPORT_SPEC perm:RUN_SUPPLIER_RATING_REPORT" />
        <report title="Categories" description="Categories Report" type="standard" nrd="/procurement/categories.nrd" action="run edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_SUPPLIER_RATING_REPORT perm:CREATE_REPORT_SPEC perm:RUN_SUPPLIER_RATING_REPORT" />
        <report title="Supplier Rating" description="Supplier Rating Report" type="data-extraction" nrd="/procurement/supplierrating.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT pref:WORKGROUP_OPTION_SUPPLIER_RATING perm:CREATE_REPORT_SPEC" />
        <report title="Order Estimate Info" description="Information for order and estimates" type="data-extraction" nrd="/procurement/order-estimate.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
        <report title="Estimate Order Info" description="RFE and Order information" type="data-extraction" nrd="/procurement/estimate-item-order.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
        <report title="Workgroup Preference Setting Report" description="Workgroup Preference,Info,NGE Setting Report" type="data-extraction" nrd="/procurement/wg-preference-tab-nge-setting.nrd" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
        <report title="Workgroup Preference Setting Report New" description="Workgroup Preference,Info,NGE,WhiteList Setting Report" type="data-extraction" nrd="/procurement/wg-preference-tab-nge-setting-new.nrd" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
        <report title="Diversity Supplier Spend" description="Diversity Supplier Spend Report" type="standard" nrd="/procurement/diversity-spend.nrd" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
        <report title="Diversity Spend by Suppliers" description="Diversity Spend Report Summary by Suppliers" type="standard" nrd="/procurement/diversity-spend-by-supplier.nrd" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
    </report-category>

    <report-category name="Collaboration Reports">
        <report title="Project Tasks" description="Project tasks report" type="data-extraction" nrd="/collaboration/task.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER perm:CREATE_REPORT_SPEC" />
        <report title="Workgroup Tasks" description="Workgroup tasks report (e.g. Job Request tasks)" type="data-extraction" nrd="/collaboration/workgroup-tasks.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER perm:CREATE_REPORT_SPEC" />
        <report title="Events" description="Event report" type="data-extraction" nrd="/collaboration/tracking.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER perm:CREATE_REPORT_SPEC" />
        <report title="Timecards" description="Timecard report" type="data-extraction" nrd="/collaboration/timecard.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_TIMECARD perm:CREATE_REPORT_SPEC"/>
        <report title="Resource Management" description="Lists resources by Role Group and their availability for a given week" type="standard" nrd="/collaboration/resource-view.nrd" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_RESOURCE_VIEW_REPORT perm:MANAGE_RESOURCES"/>
    </report-category>

    <report-category name="DirectMail Spec Reports">
        <report title="Component Where Used Report" description="Component Where Used Report" type="standard" url="/report/standard/dmSpecInput" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_WHERE_USED_REPORT perm:RUN_WHERE_USED_REPORT"/>
        <report title="Direct Mail Matrix Report" description="Production cell report for direct mail matrix specification" type="standard" url="/report/standard/dmProductionInput" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_PRODUCTION_MATRIX perm:RUN_DIRECT_MAIL_MATRIX_REPORT"/>
    </report-category>

    <report-category name="Project Reports">
        <report title="Projects" description="Project Report" articleId="360043050711" type="data-extraction" nrd="/project.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER perm:CREATE_REPORT_SPEC" />
        <!-- <report title="Projects (Live report)" description="Project Live report" nrd="/project-live.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER perm:CREATE_REPORT_SPEC" /> -->

        <report title="Project Team Members" description="Project team member report" type="data-extraction" nrd="/project-team-member.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER perm:CREATE_REPORT_SPEC" />
        <report title="Cost Items" description="Project Cost Item Report" type="data-extraction" nrd="/project-cost-item.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROJECT_BUDGET perm:CREATE_REPORT_SPEC " />
        <report title="Specs" description="Spec report" articleId="360043050731" type="data-extraction" nrd="/spec.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER perm:CREATE_REPORT_SPEC" />
        <report title="Spec Template" description="Spec template report" type="data-extraction" nrd="/spec_template.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER perm:CREATE_REPORT_SPEC" />
        <report title="Spec Items" description="Spec item report" type="data-extraction" nrd="/spec-item.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER perm:CREATE_REPORT_SPEC" />
        <report title="Spec Items V2" description="Spec item V2 report" type="data-extraction" nrd="/spec-item-v2.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER perm:CREATE_REPORT_SPEC" />
        <report title="Item Master" description="Item Master report" type="data-extraction" nrd="/master_item.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER perm:CREATE_REPORT_SPEC" />
        <report title="Project Budget" description="Analyze budgets against aggregated actual costs." type="standard" nrd="/project-budget.nrd" action="run" icon="realtime" check="pref:WORKGROUP_OPTION_PROJECT_BUDGET_REPORT perm:RUN_PROJECT_BUDGET_REPORT" />
        <report title="Project Category Budget" description="Project Budget Category Report" type="data-extraction" nrd="/project-budget-category.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROJECT_BUDGET_REPORT perm:CREATE_REPORT_SPEC " />
        <report title="Project Status" description="Analyze status of all tasks associated with all projects. Filter for Overdue, Ready To Start, and Ready To Complete tasks." type="standard" nrd="/procurement/project-status.nrd" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_PROJECT_STATUS_REPORT perm:RUN_PROJECT_STATUS_REPORT" />
        <report title="Project Participation" description="Analyze number of projects members are working on based on member role and project status." type="standard" nrd="/procurement/project-participation.nrd" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_PROJECT_PARTICIPATION_REPORT perm:RUN_PROJECT_PARTICIPATION_REPORT" />
        <report title="Project Cost" description="Analyze miscellaneous costs for projects." type="standard" nrd="/project-cost-by-cost.nrd" action="run" icon="realtime" check="pref:WORKGROUP_OPTION_PROJECT_MISC_COST_REPORT perm:RUN_PROJECT_BUDGET_REPORT" />
        <report title="Project Cost by Project" description="Analyze roll-up miscellaneous costs aggregated by project.  Due to the its aggregated nature, this report may take a while." type="standard" nrd="/project-cost-by-project.nrd" action="run" icon="realtime" check="pref:WORKGROUP_OPTION_PROJECT_MISC_COST_REPORT perm:RUN_PROJECT_BUDGET_REPORT" />
        <report title="Work In Progress" description="Shows current status of the Project" type="standard" nrd="/work-in-progress.nrd" action="run" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_WORK_IN_PROGRESS" />
    </report-category>

    <report-category name="Aggregation Reports">
        <report title="Workbook Report" description="Workbook containing multiple reports" type="special" nrd="/workbook.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_WORKBOOK_REPORTS" />
        <report title="Dashboard Report" description="Dashboard containing multiple reports" type="special" nrd="/dashboard.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_DASHBOARD_REPORTS" />
    </report-category>

</report-list>

