<?xml version="1.0" encoding="UTF-8" ?>
<report-list>
    <report-category name="Procurement Reports">
        <report title="Estimate Analysis With All Supplier Estimates" description="Analyze estimates over a specified time period. Show savings over the average estimate of an order and premium paid over the minimum estimate of an order. Includes all submitted estimates." type="standard" nrd="/full-estimate-analysis.nrd" action="run edit" icon="datamart" />
    </report-category>

    <report-category name="Data Extract Reports">
        <report title="Order Item Breakout" description="Order item breakout report. This report returns the most current breakouts (from the last accepted change order) along with the data in the original order." type="data-extraction" nrd="/procurement/order-item-breakout.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
        <report title="Original cost vs final cost" description="Returns original order items + its respective change costs as columns. Only accepted orders and pending/accepted change orders are inlcuded in the output." type="data-extraction" nrd="/procurement/order-orig-vs-final.nrd" action="edit" icon="datamart"  check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT  perm:CREATE_REPORT_SPEC" />
        <report title="RFE Items 2" description="RFE item report 2. This report is similar to the regular RFE item report, except that this report includes the project in which the spec is located rather than the project in which the RFE is located." type="data-extraction" nrd="/procurement/rfe-item-projectspec.nrd" action="edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC" />
        <report title="PeopleSoft Interface Report v84" description="PeopleSoft Interface Report v84" nrd="/sprint-peoplesoft.nrd" type="standard" action="run" icon="datamart"/>
        <report title="PeopleSoft Interface Report v89" description="PeopleSoft Interface Report v89" nrd="/sprint-peoplesoft-v89.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Accrual Report" description="Lists orders that have yet to be invoiced." nrd="/procurement/order-invoice-exception.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Total Invoice Cost and Mail Qty Report" description="This report provides a total view of project costs and shipped quantities." nrd="/total-invoice-cost-and-mail-qty.nrd" type="standard" action="run" icon="datamart"/>
        <report title="Budget Line (CAPS)" description="Budget Line (CAPS)" nrd="/budget-line-caps.nrd" type="standard" action="run" icon="datamart"/>
    </report-category>
</report-list>
