<?xml version="1.0" encoding="UTF-8" ?>
<report-list>
    <report-category name="Custom Reports">
        <report title="Item Cost and Usage Report" description="Report that aggregates all orders of items, showing total quantity and cost, based on the item number." nrd="/procurement/form-cost-usage.nrd" action="run edit" icon="datamart" check="pref:WORKGROUP_OPTION_ENABLE_REPORTWRITER pref:WORKGROUP_OPTION_PROCUREMENT perm:CREATE_REPORT_SPEC"/>
        <report title="New Custom Report for Bid by Buyer" description="New Custom Report for Bid by Buyer" nrd="/wells-bid-by-buyer.nrd" type="standard" action="run"  icon="realtime" />
        <report title="Report for Supply Chain Management (Aggregated)" description="Report for Supply Chain Management (Aggregated)" nrd="/supply-chain-management.nrd" type="standard" action="run"  icon="realtime" />
        <report title="Report for SCM Reduced" description="Report for SCM Reduced" nrd="/supply-chain-management_v2.nrd" type="standard" action="run"  icon="realtime" />
        <report title="Invoice Report" description="Invoice Report" nrd="/wells-weekly-invoice.nrd" type="standard" action="run schedule"  icon="datamart" />
    </report-category>
</report-list>
