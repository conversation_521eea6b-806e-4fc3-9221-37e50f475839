package com.noosh.app.resource;

import com.noosh.app.commons.constant.EnterpriseLinkDestination;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.report.*;
import com.noosh.app.service.report.ReportListService;
import com.noosh.app.service.util.JwtUtil;
import com.noosh.app.service.util.NooshOneUrlUtil;
import com.noosh.app.service.util.BusinessUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 9/13/2021
 */
@RestController
@RequestMapping("/report")
public class ReportListResource {

    private static final Map<String, String> SORT_FIELD_MAP = new HashMap<>();

    @Autowired
    private ReportListService reportListService;

    @PostConstruct
    public void init() {
        SORT_FIELD_MAP.put("REPORT_NAME", "REPORT_NAME");
        SORT_FIELD_MAP.put("REPORT_CREATOR", "REPORT_CREATOR");
        SORT_FIELD_MAP.put("LAST_UPDATE_DATE", "LAST_UPDATE_DATE");
    }

    @Operation(summary = "Retrieves Report List filter", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/getFilter")
    public ServerResponse<ReportListFilterVO> findReportListFilter() {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(reportListService.getFilter(workgroupId, userId));
    }

    @Operation(summary = "Get report filter options", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/filterOptions", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse options() throws Exception {
        Long currentUserId = JwtUtil.getUserId();
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();
        ReportFilterOptionVO optionVO = reportListService.options(currentUserId, currentWorkgroupId);
        return ServerResponse.success(optionVO);
    }
    
    @Operation(summary = "Get report list", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse list(@Parameter(description = "Report name search", example = "test")
                               @RequestParam(value = "search", required = false) String search,
                               @Parameter(description = "Category", example = "-1:All")
                               @RequestParam(value = "categorys") List<String> categorys,
                               @Parameter(description = "Creator", example = "-1:All")
                               @RequestParam(value = "creators") List<String> creators,
                               @Parameter(description = "Report type", example = "-1:All, standard, saved, favorites")
                               @RequestParam(value = "reportTypes") List<String> reportTypes,
                               @Parameter(description = "Real-time reports", example = "true or false")
                               @RequestParam(value = "isRealTime") boolean isRealTime,
                               @Parameter(description = "Data Mart Reports", example = "true or false")
                               @RequestParam(value = "isDataMart") boolean isDataMart,
                               @Parameter(description = "schedule Reports", example = "true or false")
                               @RequestParam(value = "isSchedule") boolean isSchedule,
                               @Parameter(description = "Page Number", example = "1")
                               @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
                               @Parameter(description = "Page Size", example = "10")
                               @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
                               @Parameter(description = "Page Sort", example = "report_name")
                               @RequestParam(value = "sort", defaultValue = "report_name") String sort,
                               @Parameter(description = "Page Order", example = "asc")
                               @RequestParam(value = "order", defaultValue = "asc") String order
                    ) throws Exception {
        Long currentUserId = JwtUtil.getUserId();
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();
        PageVO page = new PageVO(pageNum, pageSize, BusinessUtil.checkAndFormatSortName(sort, SORT_FIELD_MAP), order);
        
        reportListService.updateFilter(categorys, creators, isRealTime, isDataMart, isSchedule, currentUserId, currentWorkgroupId, page);

        ReportFilterVO filterVO = new ReportFilterVO();
        filterVO.setSearch(search);
        Map<String, Boolean> categoryMap = categorys.stream().collect(Collectors.toMap(Function.identity(), s -> Boolean.TRUE));
        filterVO.setCategoryMap(categoryMap);
        Map<String, Boolean> creatorMap = creators.stream().collect(Collectors.toMap(Function.identity(), s -> Boolean.TRUE));
        filterVO.setCreatorMap(creatorMap);
        Map<String, Boolean> reportTypeMap = reportTypes.stream().collect(Collectors.toMap(Function.identity(), s -> Boolean.TRUE));
        filterVO.setReportTypeMap(reportTypeMap);
        filterVO.setRealTime(isRealTime);
        filterVO.setDataMart(isDataMart);
        filterVO.setSchedule(isSchedule);

        List<ReportListVO> reportList = reportListService.list(filterVO, currentUserId, currentWorkgroupId, page);
        return ServerResponse.success(reportList, page);
    }

    @Operation(summary = "AI Get report list", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/listForAI", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse listForAI(@Parameter(description = "Report name search", example = "test")
                               @RequestParam(value = "search", required = false) String search,
                               @Parameter(description = "Category", example = "-1:All")
                               @RequestParam(value = "categorys") List<String> categorys,
                               @Parameter(description = "Creator", example = "-1:All")
                               @RequestParam(value = "creators") List<String> creators,
                               @Parameter(description = "Report type", example = "-1:All, standard, saved, favorites")
                               @RequestParam(value = "reportTypes") List<String> reportTypes,
                               @Parameter(description = "Real-time reports", example = "true or false")
                               @RequestParam(value = "isRealTime") boolean isRealTime,
                               @Parameter(description = "Data Mart Reports", example = "true or false")
                               @RequestParam(value = "isDataMart") boolean isDataMart,
                               @Parameter(description = "Page Number", example = "1")
                               @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
                               @Parameter(description = "Page Size", example = "10")
                               @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize
                    ) throws Exception {
        Long currentUserId = JwtUtil.getUserId();
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();
        PageVO page = new PageVO(pageNum, pageSize, null, null);

        ReportFilterVO filterVO = new ReportFilterVO();
        filterVO.setSearch(search);
        Map<String, Boolean> categoryMap = categorys.stream().collect(Collectors.toMap(Function.identity(), s -> Boolean.TRUE));
        filterVO.setCategoryMap(categoryMap);
        Map<String, Boolean> creatorMap = creators.stream().collect(Collectors.toMap(Function.identity(), s -> Boolean.TRUE));
        filterVO.setCreatorMap(creatorMap);
        Map<String, Boolean> reportTypeMap = reportTypes.stream().collect(Collectors.toMap(Function.identity(), s -> Boolean.TRUE));
        filterVO.setReportTypeMap(reportTypeMap);
        filterVO.setRealTime(isRealTime);
        filterVO.setDataMart(isDataMart);

        Map<String, Object> result = new HashMap<>();
        List<ReportListVO> reportList = reportListService.list(filterVO, currentUserId, currentWorkgroupId, page);
        result.put("reportList", reportList);
        result.put("aiViewMoreExternalUrl", reportListService.getAiViewMoreExternalUrl(currentWorkgroupId, search));
        
        return ServerResponse.success(result, page);
    }

    @Operation(summary = "Update favorite report", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @PostMapping(value = "/updateFavorite", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse updateFavorite(@Parameter(description = "favoriteMap", example = "")
                                         @RequestBody Map<String, Long> favoriteMap) {
        Long currentUserId = JwtUtil.getUserId();
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();
        reportListService.updateFavorite(favoriteMap, currentUserId, currentWorkgroupId);
        return ServerResponse.success();
    }

    @Operation(summary = "Get create report option list", description = "authored by Mario", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/reportOptions", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<ReportOptionListVO> reportOptions(@Parameter(description = "Report types", example = "-1:All, standard, saved")
                                                            @RequestParam(value = "reportTypes") List<String> reportTypes) throws Exception {
        Long currentUserId = JwtUtil.getUserId();
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();
        ReportFilterVO filterVO = new ReportFilterVO();
        Map<String, Boolean> reportTypeMap = reportTypes.stream().collect(Collectors.toMap(Function.identity(), s -> Boolean.TRUE));
        filterVO.setReportTypeMap(reportTypeMap);
        
        ReportOptionListVO optionListVO = reportListService.reportOptions(filterVO, currentUserId, currentWorkgroupId);
        return ServerResponse.success(optionListVO);
    }

}
