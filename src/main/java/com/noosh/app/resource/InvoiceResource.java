package com.noosh.app.resource;

import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.invoice.InvoiceListVO;
import com.noosh.app.exception.NoPreferenceException;
import com.noosh.app.service.InvoiceService;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


/**
 * <AUTHOR>
 * @since 0.0.1
 */
@RestController
@RequestMapping("/invoice")
public class InvoiceResource {

    @Autowired
    private InvoiceService invoiceService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private PreferenceService preferenceService;

    @Operation(summary = "Retrieve invoice list", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/list")
    @Timed
    public ServerResponse<InvoiceListVO> invoiceList(
            @Parameter(description = "project Id", required = true, example = "11243951")
            @RequestParam(value = "projectId") Long projectId,
            @Parameter(description = "Tab Id", required = false, example = "1")
            @RequestParam(value = "tabId", required = false) Integer tabId,

            @Parameter(description = "Page Number", required = false, example = "1")
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @Parameter(description = "Page Size", required = false, example = "10")
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize
    ) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();

        projectService.findProjectById(projectId);

        PageVO page = new PageVO(pageNum, pageSize, null, null);

        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
        if(!preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROCUREMENT, groupPrefs)) {
            throw new NoPreferenceException("Your workgroup is not turned on Procurement");
        }

        return ServerResponse.success(invoiceService.getInvoiceList(workgroupId, userId, projectId, tabId, page), page);
    }

}
