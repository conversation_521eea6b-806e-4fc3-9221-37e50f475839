package com.noosh.app.resource;

import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.chart.*;
import com.noosh.app.repository.mybatis.order.OrderChartMyBatisMapper;
import com.noosh.app.service.chart.ChartService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * order chart for mydesk
 *
 * <AUTHOR>
 * @date 8/12/2021
 */
@RestController
@RequestMapping("/order/chart")
public class OrderChartResource {


    @Autowired
    private OrderChartMyBatisMapper orderChartMyBatisMapper;
    @Autowired
    private ChartService chartService;

    @Operation(summary = "Project Efficiency RFE Chart Detail", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/projectRfeChartDetail")
    @Timed
    public ServerResponse<List<ProjectRFESentVO>> projectRfeChartDetail(@Parameter(description = "filter", required = true, example = "30")
                                                                        @RequestParam(value = "filter", required = true, defaultValue = "30") Integer filter) {

        Long workgroupId = JwtUtil.getWorkgroupId();

        List<ProjectRFESentVO> chartData = orderChartMyBatisMapper.findProjectRFEDetailInfo(workgroupId, filter);
        return ServerResponse.success(chartData);
    }

    @Operation(summary = "Project Efficiency Order Chart Detail", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/projectOrderChartDetail")
    @Timed
    public ServerResponse<List<ProjectBuyOrderVO>> projectOrderChartDetail(@Parameter(description = "filter", required = true, example = "30")
                                                                           @RequestParam(value = "filter", required = true, defaultValue = "30") Integer filter,
                                                                           @Parameter(description = "type", required = true, example = "isAward")
                                                                           @RequestParam(value = "type", required = true, defaultValue = "isAward") String type) {

        Long workgroupId = JwtUtil.getWorkgroupId();

        List<ProjectBuyOrderVO> chartData = orderChartMyBatisMapper.findProjectOrderDetailInfo(workgroupId, filter, type);
        return ServerResponse.success(chartData);
    }

    @Operation(summary = "Project Efficiency Invoice Chart Detail", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/projectInvoiceChartDetail")
    @Timed
    public ServerResponse<List<ProjectInvoiceVO>> projectInvoiceChartDetail(@Parameter(description = "filter", required = true, example = "30")
                                                                            @RequestParam(value = "filter", required = true, defaultValue = "30") Integer filter,
                                                                            @Parameter(description = "type", required = true, example = "isAward")
                                                                            @RequestParam(value = "type", required = true, defaultValue = "isAward") String type) {

        Long workgroupId = JwtUtil.getWorkgroupId();

        List<ProjectInvoiceVO> chartData = orderChartMyBatisMapper.findProjectInvoiceDetailInfo(workgroupId, filter, type);
        return ServerResponse.success(chartData);
    }

    @Operation(summary = "Project efficiency", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/projectEfficiency")
    @Timed
    public ServerResponse<List<ProjectChartItemVO>> projectEfficiency(@Parameter(description = "filter", required = true, example = "30")
                                                                      @RequestParam(value = "filter", required = true, defaultValue = "30") Integer filter,
                                                                      @Parameter(description = "Is client workgroup", required = false, example = "false")
                                                                      @RequestParam(value = "isClient", required = false, defaultValue = "false") Boolean isClient) {

        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        ChartFilter chartFilter = new ChartFilter();
        chartFilter.setShowOption(filter == null ? "30" : String.valueOf(filter));
//        chartService.updateProjectEfficientFilter(workgroupId, userId, chartFilter);
        List<ProjectChartItemVO> chartData = orderChartMyBatisMapper.findProjectEfficiency(workgroupId, filter, isClient == null ? false : isClient);
        return ServerResponse.success(chartData);
    }

    @Operation(summary = "Time to Order", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/timeToOrder")
    @Timed
    public ServerResponse<List<ProjectChartItemVO>> timeToOrder(@Parameter(description = "filter", required = true, example = "30")
                                                                @RequestParam(value = "filter", required = true, defaultValue = "30") Integer filter) {

        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        ChartFilter chartFilter = new ChartFilter();
        chartFilter.setShowOption(filter == null ? "30" : String.valueOf(filter));
//        chartService.updateOrderEfficientFilter(workgroupId, userId, chartFilter);

        List<ProjectChartItemVO> chartData = orderChartMyBatisMapper.findTimeToOrder(workgroupId, filter);
        return ServerResponse.success(chartData);
    }

    @Operation(summary = "invoice Forecast", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/invoiceForecast")
    @Timed
    public ServerResponse<List<InvoiceForecastChartItemVO>> invoiceForecast(
            @Parameter(description = "User Timezone Id", required = true, example = "US/Pacific")
            @RequestParam(value = "userTimezoneId") String userTimezoneId
    ) {

        Long workgroupId = JwtUtil.getWorkgroupId();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss");
        LocalDate today = LocalDate.now();
        String start = today.withDayOfMonth(1).atTime(LocalTime.MIN).atZone(ZoneId.of(userTimezoneId))
                .withZoneSameInstant(ZoneId.of("UTC")).toLocalDateTime()
                .format(formatter);
        String end = today.withDayOfMonth(today.lengthOfMonth()).atTime(LocalTime.MAX).atZone(ZoneId.of(userTimezoneId))
                .withZoneSameInstant(ZoneId.of("UTC")).toLocalDateTime()
                .format(formatter);

        List<InvoiceForecastChartItemVO> chartData = orderChartMyBatisMapper.findInvoiceForecast(workgroupId, start, end);
        return ServerResponse.success(chartData);
    }

    @Operation(summary = "Retrieves Project efficiency or Time to Order filter by workgroup id and user id", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/getFilter")
    public ServerResponse<ChartFilterVO> findFilter(@Parameter(description = "Is Project Efficient", required = true, example = "false")
                                                    @RequestParam(value = "isProjectEfficient", required = true, defaultValue = "false") boolean isProjectEfficient) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        ChartFilterVO vo = new ChartFilterVO();
        if(isProjectEfficient) {
            vo = chartService.getProjectEfficientFilter(workgroupId, userId);
        } else {
            vo = chartService.getOrderEfficientFilter(workgroupId, userId);
        }
        return ServerResponse.success(vo);
    }
}