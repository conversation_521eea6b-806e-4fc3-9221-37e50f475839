package com.noosh.app.resource;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.order.OrderDetailAnalysisDTO;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.order.*;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.service.order.OrderListService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.util.JwtUtil;
import com.noosh.app.service.util.NooshOneUrlUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import jakarta.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @auther mario
 * @date 2/25/2020
 */
@RestController
@RequestMapping("/order")
public class OrderListResource {

    @Inject
    private PermissionService permissionService;
    @Inject
    private OrderListService orderListService;

    private final Map<String, String> sortFieldMap = new HashMap<>();

    @PostConstruct
    public void init() {
        sortFieldMap.put("id", "ov.REFERENCE");
        sortFieldMap.put("lastChanged", "os.STATE_CREATE_DATE");
    }

    @Operation(summary = "Get order list filter", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/orderListFilter", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<OrderListFilterVO> getOrderListFilter(@Parameter(description = "Project Id", required = true, example = "11247195")
                                                                @RequestParam(value = "projectId") Long projectId) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        OrderListFilterVO filterVO = orderListService.getRfeListFilter(projectId, userId, workgroupId);
        return ServerResponse.success(filterVO);
    }

    @Operation(summary = "Retrieves Estimate Detail", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/orderList")
    @Timed
    public ServerResponse<OrderListVO> getOrderList(
            @Parameter(description = "Locale", required = true, example = "en_US")
            @RequestParam(value = "locale") String locale,
            @Parameter(description = "Sort", required = false, example = "orderId")
            @RequestParam(value = "sort", defaultValue = "lastChanged") String sort,
            @Parameter(description = "Sort Order", required = false, example = "desc")
            @RequestParam(value = "order", defaultValue = "desc") String order,

            @Parameter(description = "Project Id", required = true, example = "11247195")
            @RequestParam(value = "projectId") Long projectId,
            @Parameter(description = "Order Type", required = true, example = "buy")
            @RequestParam(value = "orderType", defaultValue = "buy") String orderType,
            @Parameter(description = "Page Number", required = true, example = "1")
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @Parameter(description = "Page Size", required = true, example = "10")
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @Parameter(description = "Cog Filter", required = false, example = "-1")
            @RequestParam(value = "cogFilter") List<Long> cogFilter) throws Exception {

        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();

        if (!permissionService.checkAll(PermissionID.VIEW_ORDER, workgroupId, userId, projectId)) {
            throw new NoPermissionException("Your don't have permission to view order");
        }
        if(sortFieldMap.get(sort) == null) {
            throw new IllegalArgumentException(sort + " is not a valid sort field");
        }
        // "Show All" is mutually exclusive from the rest of the Hide options.
        if (cogFilter == null) {
            cogFilter = new ArrayList<>();
            cogFilter.add(-1L);
        }
        PageVO page = new PageVO(pageNum, pageSize, sortFieldMap.get(sort), order);
        OrderListVO orderList = orderListService.getOrderList(page, workgroupId, userId, projectId, orderType, cogFilter, locale);
        return ServerResponse.success(orderList, page);
    }

    @Operation(summary = "Retrieves Order List Markup Summary", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/markupSummary")
    @Timed
    public ResponseEntity markupSummary(
            @Parameter(description = "Locale", required = true, example = "en_US")
            @RequestParam(value = "locale") String locale,

            @Parameter(description = "Project Id", required = true, example = "11247195")
            @RequestParam(value = "projectId") Long projectId,
            @AuthenticationPrincipal Jwt jwt,
            @Parameter(description = "Price Filter", required = true, example = "-1")
            @RequestParam(value = "priceFilter", defaultValue = "-1") List<Long> priceFilter) throws Exception {

        Long currentUserId = Long.parseLong(jwt.getClaimAsString("userId"));
        Long currentWorkgroupId = Long.parseLong(jwt.getClaimAsString("workgroupId"));

        if (!permissionService.checkAll(PermissionID.VIEW_ORDER, currentWorkgroupId, currentUserId, projectId)) {
            throw new NoPermissionException("Your don't have permission to view order");
        }
        orderListService.updatePriceFilter(currentWorkgroupId, currentUserId, priceFilter);
        Map markupSummary = orderListService.getMarkupSummary(projectId, priceFilter);
        markupSummary.put("buyOrderListLink", NooshOneUrlUtil.composeListOrdersToEnterprise(projectId, (long) 0));
        markupSummary.put("sellOrderListLink", NooshOneUrlUtil.composeListOrdersToEnterprise(projectId, (long) 1));
        return new ResponseEntity<ServerResponse>(ServerResponse.success(markupSummary), HttpStatus.OK);
    }

    @Operation(summary = "Retrieves Order List Markup Summary Price Filter", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/priceFilter")
    @Timed
    public ResponseEntity priceFilter() throws Exception {
        Long currentUserId = JwtUtil.getUserId();
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();
        OrderListPriceFilter filter = orderListService.getPriceFilter(currentWorkgroupId, currentUserId);
        return new ResponseEntity<ServerResponse>(ServerResponse.success(filter), HttpStatus.OK);
    }

    @Operation(summary = "Retrieves Order List Markup Analysis", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/markupAnalysis")
    @Timed
    public ResponseEntity markupAnalysis(
            @Parameter(description = "Locale", required = true, example = "en_US")
            @RequestParam(value = "locale") String locale,

            @Parameter(description = "Project Id", required = true, example = "11247195")
            @RequestParam(value = "projectId") Long projectId,
            @Parameter(description = "Include Pending", required = true, example = "0")
            @RequestParam(value = "includePending", required = true, defaultValue = "0") Integer includePending,
            @Parameter(description = "Include Archived", required = true, example = "0")
            @RequestParam(value = "includeArchived", required = true, defaultValue = "0") Integer includeArchived,
            @Parameter(description = "Default To QuotedCost", required = true, example = "0")
            @RequestParam(value = "defaultToQuotedCost", required = true, defaultValue = "0") Integer defaultToQuotedCost,
            @AuthenticationPrincipal Jwt jwt) throws Exception {

        Long currentUserId = Long.parseLong(jwt.getClaimAsString("userId"));
        Long currentWorkgroupId = Long.parseLong(jwt.getClaimAsString("workgroupId"));

        if (!permissionService.checkAll(PermissionID.VIEW_ORDER, currentWorkgroupId, currentUserId, projectId)) {
            throw new NoPermissionException("Your don't have permission to view order");
        }
        OrderMarkupAnalysisFilter filter = new OrderMarkupAnalysisFilter();
        filter.setDefaultToQuotedCost(defaultToQuotedCost);
        filter.setIncludeArchived(includeArchived);
        filter.setIncludePending(includePending);
        orderListService.updateFilter(currentWorkgroupId, currentUserId, filter);
//        orderListService.updatePriceFilter(currentWorkgroupId, currentUserId, priceFilter);
        OrderDetailAnalysisDTO orderDetailAnalysisDTO = orderListService.getDetailMarkupAnalysis(
                defaultToQuotedCost == 1, includePending == 1,
                includeArchived == 1, currentWorkgroupId, currentUserId, projectId);
        return new ResponseEntity<ServerResponse>(ServerResponse.success(orderDetailAnalysisDTO), HttpStatus.OK);
    }

    @Operation(summary = "Retrieves Order List Markup Analysis Filter", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/markupAnalysisFilter")
    public ServerResponse<OrderMarkupAnalysisFilterVO> markupAnalysisFilter(
            @Parameter(description = "Project Id", required = true, example = "11247195")
            @RequestParam(value = "projectId") Long projectId,
            @AuthenticationPrincipal Jwt jwt) {
        Long currentUserId = Long.parseLong(jwt.getClaimAsString("userId"));
        Long currentWorkgroupId = Long.parseLong(jwt.getClaimAsString("workgroupId"));
        return ServerResponse.success(orderListService.getFilter(currentWorkgroupId, currentUserId, projectId));
    }

    @Operation(summary = "Export Order Detail to Excel", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/exportOrderDetail")
    public void exportOrderDetail(
            @Parameter(description = "Project Id", required = true, example = "11247195")
            @RequestParam(value = "projectId") Long projectId,
            @Parameter(description = "Order Id", required = true, example = "11247195")
            @RequestParam(value = "orderId") Long orderId,
            @Parameter(description = "Locale", required = true, example = "en_US")
            @RequestParam(value = "locale") String locale,
            @AuthenticationPrincipal Jwt jwt,
            HttpServletResponse response) {
        Long currentUserId = Long.parseLong(jwt.getClaimAsString("userId"));
        Long currentWorkgroupId = Long.parseLong(jwt.getClaimAsString("workgroupId"));

        HSSFWorkbook workbook = orderListService.exportOrderDetail(projectId, orderId, currentUserId, currentWorkgroupId, locale);

        String fileName = "orderdetail.xls";

        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename="+ fileName);
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");

        try (OutputStream os = response.getOutputStream()) {
            workbook.write(os);
            os.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
