package com.noosh.app.resource.feignprovider;

import com.noosh.app.commons.vo.rating.SupplierScoreVO;
import com.noosh.app.service.rating.RatingService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.inject.Inject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RequestMapping("/feign/rating")
@RestController
public class RatingFeignProviderResource {

    @Inject
    private RatingService ratingService;

    @Operation(summary = "Get Supplier Score", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/findSupplierScore")
    @Timed
    public ResponseEntity<SupplierScoreVO> findSupplierScore(
            @RequestParam("ownerWorkgroupId") Long ownerWorkgroupId,
            @RequestParam("supplierWorkgroupId") Long supplierWorkgroupId) {
        return ResponseEntity.ok(ratingService.findSupplierScore(ownerWorkgroupId, supplierWorkgroupId));
    }

    @Operation(summary = "Get Supplier Score", security = @SecurityRequirement(name = "JWT"))
    @PostMapping(value = "/bulkFindSupplierScore")
    @Timed
    public ResponseEntity<Map<Long, SupplierScoreVO>> bulkFindSupplierScore(
            @RequestParam("ownerWorkgroupId") Long ownerWorkgroupId,
            @RequestBody List<Long> supplierWorkgroupIds) {
        return ResponseEntity.ok(ratingService.findSupplierScores(ownerWorkgroupId, supplierWorkgroupIds));
    }

}
