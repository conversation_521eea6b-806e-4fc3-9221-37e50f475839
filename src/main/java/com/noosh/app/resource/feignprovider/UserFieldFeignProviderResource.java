package com.noosh.app.resource.feignprovider;

import com.noosh.app.commons.dto.userfield.CustomFieldDTO;
import com.noosh.app.commons.dto.userfield.UserFieldReqDTO;
import com.noosh.app.commons.vo.userfield.UserFieldDefsWithValuesVO;
import com.noosh.app.commons.vo.userfield.UserFieldVO;
import com.noosh.app.mapper.userfield.UserFieldMapper;
import com.noosh.app.service.userfield.UserFieldService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 9/9/2022
 */

@RestController
@RequestMapping("/api/feign/userField")
public class UserFieldFeignProviderResource {

    @Autowired
    private UserFieldService userFieldService;

    @Autowired
    private UserFieldMapper userFieldMapper;

    @Operation(summary = "find user field definitions with values by workgroupId and fieldClassId",
            security = @SecurityRequirement(name = "JWT"), description = "authored by Kevin")
    @PostMapping(value = "/findUserFieldDefsWithValues", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public UserFieldDefsWithValuesVO findUserFieldDefsWithValues(@RequestBody @Valid UserFieldReqDTO reqDTO) {
        Long workgroupId = reqDTO.getWorkgroupId();
        Long fieldClassId = reqDTO.getFieldClassId();
        UserFieldDefsWithValuesVO userFieldDefsWithValuesVO = new UserFieldDefsWithValuesVO();
        userFieldDefsWithValuesVO.setUserFields(userFieldMapper.toVOs(userFieldService.findUserFieldDefs(workgroupId, fieldClassId)));
        userFieldDefsWithValuesVO.setValues(userFieldService.findUserFieldValues(workgroupId, fieldClassId, reqDTO.getPropertyIds(), reqDTO.getIsSupplierView()));
        return userFieldDefsWithValuesVO;
    }

    @Operation(summary = "find user field definitions by workgroupId and fieldClassId",
            security = @SecurityRequirement(name = "JWT"), description = "authored by Kevin")
    @PostMapping(value = "/findUserFieldDefs", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public List<UserFieldVO> findUserFieldDefs(@RequestBody @Valid UserFieldReqDTO reqDTO) {
        List<CustomFieldDTO> customFieldDTOs = userFieldService.findUserFieldDefs(reqDTO.getWorkgroupId(), reqDTO.getFieldClassId());
        return userFieldMapper.toVOs(customFieldDTOs);
    }

    @Operation(summary = "find user field values by workgroupId and fieldClassId",
            security = @SecurityRequirement(name = "JWT"), description = "authored by Kevin")
    @PostMapping(value = "/findUserFieldValues", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public Map<Long, Map<String, Object>> findUserFieldValues(@RequestBody @Valid UserFieldReqDTO reqDTO) {
        return userFieldService.findUserFieldValues(reqDTO.getWorkgroupId(), reqDTO.getFieldClassId(), reqDTO.getPropertyIds(), reqDTO.getIsSupplierView());
    }

    // this API is intended for Feign invocation only and are not exposed to the front-end
    @PostMapping(value = "/findUserFiledValuesByFieldClassIdPropertyIds", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public Map<Long, Map<String, Object>> findUserFiledValuesByFieldClassIdPropertyIds(@RequestParam(value = "workgroupId") Long workgroupId,
                                                                                       @RequestParam(value = "fieldClassId") Long fieldClassId,
                                                                                       @RequestParam(value = "isSupplierView", defaultValue = "false") Boolean isSupplierView,
                                                                                       @RequestBody List<Long> propertyIds) {
        return userFieldService.findUserFieldValues(workgroupId, fieldClassId, propertyIds, isSupplierView);
    }

}
