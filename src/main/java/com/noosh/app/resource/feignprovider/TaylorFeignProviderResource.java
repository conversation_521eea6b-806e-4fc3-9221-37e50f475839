package com.noosh.app.resource.feignprovider;

import com.noosh.app.commons.vo.taylor.TaylorSpecVO;
import com.noosh.app.commons.vo.taylor.TaylorSupplierVO;
import com.noosh.app.service.taylor.TaylorService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * author: Yang
 * Date: 3/6/2025
 */
@RestController
@RequestMapping("/api/feign/taylor")
public class TaylorFeignProviderResource {

    @Autowired
    TaylorService taylorService;

    @Operation(summary = "get taylor RFE AutoInvite Suppliers dataset", security = @SecurityRequirement(name = "JWT"))
    @PostMapping(value = "/getAutoInviteSuppliers", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public List<TaylorSupplierVO> getAutoInviteSuppliers(
            @Parameter(description = "groupId", required = true)
            @RequestParam("groupId") Long groupId,
            @RequestBody List<TaylorSpecVO> taylorSpecVOS) {

        List<TaylorSupplierVO> taylorSuppliers = taylorService.getTaylorSuppliers(groupId, taylorSpecVOS);
        return taylorSuppliers;
    }
}
