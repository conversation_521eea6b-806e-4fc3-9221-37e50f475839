package com.noosh.app.resource;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.TermsTypeID;
import com.noosh.app.commons.dto.order.ChangeOrderDetailDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.vo.ResponseCode;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.order.*;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.mapper.OrderMapper;
import com.noosh.app.service.ChangeService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.terms.TermsService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.inject.Inject;

import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 4/14/20
 */
@RestController
@RequestMapping("/change")
public class ChangeOrderResource {
    @Inject
    private ChangeService changeService;
    @Inject
    private PermissionService permissionService;
    @Inject
    private OrderMapper orderMapper;
    @Inject
    private TermsService termsService;

    @Operation(summary = "Get change order by order id", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    @Transactional(readOnly = true)
    public ResponseEntity getChangeOrderDetail(@Parameter(description = "Order Id", required = true, example = "5364054")
                                                                        @RequestParam(value="orderId") Long orderId,
                                                                    @Parameter(description = "Project Id", required = true, example = "5364054")
                                                                    @RequestParam(value="projectId") Long projectId,
                                                                    @Parameter(description = "Order Type", required = true, example = "buy")
                                                                    @RequestParam(value="orderType") String orderType,
                                                                    @AuthenticationPrincipal Jwt jwt) throws Exception {
        Long currentUserId = Long.parseLong(jwt.getClaimAsString("userId"));
        Long currentWorkgroupId = Long.parseLong(jwt.getClaimAsString("workgroupId"));

        if (!permissionService.checkAll(PermissionID.VIEW_ORDER, currentWorkgroupId, currentUserId, projectId)) {
            throw new NoPermissionException("Your don't have permission to view order");
        }
        ChangeOrderDetailDTO changeOrderDetailDTO = changeService.findChangeOrderDetail(orderId, projectId, currentWorkgroupId, currentUserId, orderType);
        if (changeOrderDetailDTO != null && changeOrderDetailDTO.getOrderVersionDTO() != null) {
            OrderVersionDTO originalOrderDTO = changeOrderDetailDTO.getOrderVersionDTO().getParentOrder();
            ChangeOrderDetailVO orderGeneralInfoVO = orderMapper.toChangeGeneralDetailVO(changeOrderDetailDTO);
            BigDecimal invoiceTotalAmount = changeService.findInvoiceTotalAmount(projectId, currentWorkgroupId, orderGeneralInfoVO, changeOrderDetailDTO);
            orderGeneralInfoVO.setInvoiceTotalAmount(invoiceTotalAmount);
            ChangeOrderButtonVO orderButtonVO = orderMapper.toChangeButtonVO(changeOrderDetailDTO);
            orderMapper.hideAcceptButtonByEnvironmental(orderGeneralInfoVO, originalOrderDTO, orderButtonVO);
            orderMapper.hideAcceptButtonByMaterialBT(orderGeneralInfoVO, originalOrderDTO, orderButtonVO);
            ChangeOrderInfoVO orderDetailVO = new ChangeOrderInfoVO();
            orderDetailVO.setOrderGeneral(orderGeneralInfoVO);
            orderDetailVO.setButtons(orderButtonVO);
            orderDetailVO.setBuyerTerms(termsService.findTermAndCondition(originalOrderDTO.getBuyerTermsId(), TermsTypeID.BUYER_PURCHASE, originalOrderDTO.getBuyerWorkgroupId()));
            orderDetailVO.setSupplierTerms(termsService.findTermAndCondition(originalOrderDTO.getSupplierTermsId(), TermsTypeID.SUPPLIER_SELL, originalOrderDTO.getSupplierWorkgroupId()));
            return new ResponseEntity<ServerResponse>(ServerResponse.success(orderDetailVO), HttpStatus.OK);
        }

        return new ResponseEntity(new ServerResponse<OrderDetailVO>(ResponseCode.ERROR.getCode(), "Order not found!"), HttpStatus.NOT_FOUND);
    }

    @Operation(summary = "get Closing Change Order Custom Attribute", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/hhsap/getClosingChangeOrderCustomAttribute", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    @Transactional(readOnly = true)
    public ResponseEntity getClosingChangeOrderCustomAttribute(@Parameter(description = "original Order Id", required = true, example = "5364054")
                                               @RequestParam(value="originalOrderId") Long originalOrderId) throws Exception {
        return ResponseEntity.ok(ServerResponse.success(changeService.getClosingChangeOrderCustomAttribute(originalOrderId)));
    }
}
