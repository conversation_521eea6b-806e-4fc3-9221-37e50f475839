package com.noosh.app.resource;

import com.noosh.app.commons.vo.client.*;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.account.WorkgroupAddressVO;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.commons.vo.supplier.SupplierListVO;
import com.noosh.app.commons.vo.workgroup.WgAttributeVO;
import com.noosh.app.service.client.ClientService;
import com.noosh.app.service.supplier.SupplierService;
import com.noosh.app.service.util.BusinessUtil;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * My Group | Third-party Options | Clients
 *
 * <AUTHOR>
 * @date 5/4/2022
 */
@RestController
@RequestMapping("/api/client")
public class ClientResource {
    private static final Map<String, String> SORT_FIELD_MAP = new HashMap<>();

    @Autowired
    private ClientService clientService;
    @Autowired
    private SupplierService supplierService;

    @PostConstruct
    public void init() {
        SORT_FIELD_MAP.put("DISPLAY_NAME", "DISPLAY_NAME");
        SORT_FIELD_MAP.put("SUPPLIER_CODE", "SUPPLIER_CODE");
        SORT_FIELD_MAP.put("SW_NAME", "SW_NAME");
        SORT_FIELD_MAP.put("IS_APPROVED", "IS_APPROVED");
    }

    @Operation(summary = "Get active or inactive clients list with pagination", security = @SecurityRequirement(name = "JWT"), description = "authoried by Mario")
    @GetMapping(value = "/listClients", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<ClientListVO> listClients(@Parameter(description = "Is Active Clients", required = true, example = "true")
                                                    @RequestParam(value = "isActive") Boolean isActive,
                                                    @Parameter(description = "Search str, support client name or code", example = "client name/code")
                                                    @RequestParam(value = "searchStr", required = false) String searchStr,
                                                    @Parameter(description = "Page Number", required = true, example = "1")
                                                    @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                    @Parameter(description = "Page Size", required = true, example = "10")
                                                    @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                    @Parameter(description = "Page Sort", example = "display_name")
                                                    @RequestParam(value = "sort", defaultValue = "display_name") String sort,
                                                    @Parameter(description = "Page Order", example = "asc")
                                                    @RequestParam(value = "order", defaultValue = "ASC") String order) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        clientService.updateClientFilter(workgroupId, userId, pageSize, isActive);
        PageVO page = new PageVO(pageNum, pageSize, sort, order);
        ClientListVO clientListVO = clientService.listClients(userId, workgroupId, isActive, searchStr, page);
        return ServerResponse.success(clientListVO, page);
    }

    @Operation(summary = "Get list clients filter", security = @SecurityRequirement(name = "JWT"), description = "authored by Yang")
    @GetMapping(value = "/getClientFilter", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<ClientFilterVO> getClientFilter() {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(clientService.getClientFilter(workgroupId, userId));
    }

    @Operation(summary = "Get active or inactive simple clients list without pagination", security = @SecurityRequirement(name = "JWT"), description = "authored by Jenny")
    @GetMapping(value = "/listSimpleClients", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<SimpleClientListVO> listSimpleClients() {
        Long workgroupId = JwtUtil.getWorkgroupId();
        SimpleClientListVO clientListVO = clientService.listSimpleClients(workgroupId);
        return ServerResponse.success(clientListVO);
    }

    @Operation(summary = "Get transCurrency clients list without pagination", security = @SecurityRequirement(name = "JWT"), description = "authored by Jenny")
    @GetMapping(value = "/listTransClients", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<List<ClientVO>> listTransClients() {
        Long workgroupId = JwtUtil.getWorkgroupId();
        List<ClientVO> clientListVO = clientService.listTransClients(workgroupId);
        return ServerResponse.success(clientListVO);
    }

    @Operation(summary = "Find exist client", security = @SecurityRequirement(name = "JWT"), description = "find the clients before adding")
    @GetMapping(value = "/findClient", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<List<WorkgroupAddressVO>> findClient(@Parameter(description = "Workgroup Name", example = "test")
                                                    @RequestParam(value = "workgroupName", required = false) String workgroupName,
                                                    @Parameter(description = "First Name", example = "apio1g1")
                                                    @RequestParam(value = "firstName", required = false) String firstName,
                                                    @Parameter(description = "Last Name", example = "mgr1")
                                                    @RequestParam(value = "lastName", required = false) String lastName,
                                                    @Parameter(description = "Default Email", example = "<EMAIL>")
                                                    @RequestParam(value = "defaultEmail", required = false) String defaultEmail,
                                                    @Parameter(description = "Page Number", required = true, example = "1")
                                                    @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                    @Parameter(description = "Page Size", required = true, example = "10")
                                                    @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                    @Parameter(description = "Page Sort", example = "name")
                                                    @RequestParam(value = "sort", required = false, defaultValue = "name") String sort,
                                                    @Parameter(description = "Page Order", example = "asc")
                                                    @RequestParam(value = "order", required = false, defaultValue = "ASC") String order) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        PageVO page = new PageVO(pageNum, pageSize, sort, order);
        List<WorkgroupAddressVO> addressVOList = clientService.findClient(workgroupId, workgroupName, firstName, lastName, defaultEmail, page);
        return ServerResponse.success(addressVOList, page);
    }

    @Operation(summary = "Select client type", security = @SecurityRequirement(name = "JWT"), description = "authored by Mario")
    @GetMapping(value = "/selectClientType", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse selectClientType() {
        Long workgroupId = JwtUtil.getWorkgroupId();
        SelectClientTypeVO vo = clientService.selectClientType(workgroupId);
        return ServerResponse.success(vo);
    }

    @Operation(summary = "only return when user enabled the multi-currency", security = @SecurityRequirement(name = "JWT"), description = "authored by Yang")
    @GetMapping(value = "/getClientTransactionalCurrency", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse getClientTransactionalCurrency() {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        List<DropdownVO<Long>> vo = clientService.getTransactionCurrencyDropdown(workgroupId, userId);
        return ServerResponse.success(vo);
    }

    @Operation(summary = "Add noosh client", security = @SecurityRequirement(name = "JWT"), description = "authored by Mario")
    @PostMapping(value = "/addClient", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse addClient(@Parameter(description = "Client workgroup ids", example = "test")
                                    @RequestParam(value = "clientWorkgroupIds") List<Long> clientWorkgroupIds) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        clientService.addClient(userId, workgroupId, clientWorkgroupIds);
        return ServerResponse.success();
    }

    @Operation(summary = "Add non noosh client", security = @SecurityRequirement(name = "JWT"), description = "authored by Mario")
    @PostMapping(value = "/addNonNooshClient", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse addNonNooshClient(@RequestBody NonNooshClientVO nonNooshClientVO) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        clientService.addNonNooshClient(userId, workgroupId, nonNooshClientVO);
        return ServerResponse.success();
    }

    @Operation(summary = "client supplier list", security = @SecurityRequirement(name = "JWT"), description = "suppliers approved for this client")
    @GetMapping(value = "/clientSupplierList", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse clientSupplierList(@Parameter(description = "Client id", example = "test")
                                             @RequestParam(value = "clientId") Long clientId,
                                             @Parameter(description = "HH certification filter", example = "available values: Gold, Silver, Bronze, None")
                                             @RequestParam(value = "hhCertFilter", required = false) String hhCertFilter,
                                             @Parameter(description = "Page Number", required = true, example = "1")
                                             @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                             @Parameter(description = "Page Size", required = true, example = "10")
                                             @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                             @Parameter(description = "Page Sort", example = "DISPLAY_NAME, SUPPLIER_CODE, SW_NAME, IS_APPROVED")
                                             @RequestParam(value = "sort", required = false) String sort,
                                             @Parameter(description = "Page Order", example = "asc")
                                             @RequestParam(value = "order", required = false, defaultValue = "ASC") String order) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        PageVO page = new PageVO(pageNum, pageSize, BusinessUtil.checkAndFormatSortName(sort, SORT_FIELD_MAP), order);
        SupplierListVO supplierList = supplierService.clientSupplierList(workgroupId, clientId, hhCertFilter, page);
        return ServerResponse.success(supplierList, page);
    }

    @Operation(summary = "outsourcer supplier list not in client", security = @SecurityRequirement(name = "JWT"), description = "suppliers approved for current workgroup but not for this client")
    @GetMapping(value = "/outsourcerSupplierList", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse outsourcerSupplierList(@Parameter(description = "Client id", example = "test")
                                             @RequestParam(value = "clientId") Long clientId,
                                             @Parameter(description = "HH certification filter", example = "available values: Gold, Silver, Bronze, None")
                                             @RequestParam(value = "hhCertFilter", required = false) String hhCertFilter,
                                             @Parameter(description = "Page Number", required = true, example = "1")
                                             @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                             @Parameter(description = "Page Size", required = true, example = "10")
                                             @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                             @Parameter(description = "Page Sort", example = "DISPLAY_NAME, SUPPLIER_CODE, SW_NAME, IS_APPROVED")
                                             @RequestParam(value = "sort", required = false) String sort,
                                             @Parameter(description = "Page Order", example = "asc")
                                             @RequestParam(value = "order", required = false, defaultValue = "ASC") String order) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        PageVO page = new PageVO(pageNum, pageSize, BusinessUtil.checkAndFormatSortName(sort, SORT_FIELD_MAP), order);
        SupplierListVO supplierList = supplierService.outsourcerSupplierList(workgroupId, clientId, hhCertFilter, page);
        return ServerResponse.success(supplierList, page);
    }

    @Operation(summary = "go to client view page when click edit button", security = @SecurityRequirement(name = "JWT"), description = "authored by Yang")
    @GetMapping(value = "/viewClientInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse viewClientInfo(@Parameter(description = "Client id", example = "5012859")
                                         @RequestParam(value = "clientId") Long clientId) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        ClientDetailVO clientViewVO = clientService.viewClientInfo(userId, workgroupId, clientId);
        return ServerResponse.success(clientViewVO);
    }

    @Operation(summary = "edit client margin", security = @SecurityRequirement(name = "JWT"), description = "authored by Yang")
    @PostMapping(value = "/editClientMargin", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse editClientMargin(@Parameter(description = "Client id", example = "5012859")
                                           @RequestParam(value = "clientId") Long clientId,
                                           @RequestBody List<WgAttributeVO> wgAttributeVOs) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        clientService.editClientMargin(userId, workgroupId, clientId, wgAttributeVOs);
        return ServerResponse.success();
    }

    @Operation(summary = "inline edit client margin, also support delete", security = @SecurityRequirement(name = "JWT"), description = "authored by Yang")
    @PostMapping(value = "/inlineEditClientMargin", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse inlineEditClientMargin(@Parameter(description = "Client id", example = "5012859")
                                           @RequestParam(value = "clientId") Long clientId,
                                           @Parameter(description = "true for delete this client margin/markup", required = true, example = "true")
                                           @RequestParam(value = "isDelete", defaultValue = "false") Boolean isDelete,
                                           @RequestBody WgAttributeVO wgAttributeVO) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        clientService.inlineEditClientMargin(userId, workgroupId, clientId, isDelete, wgAttributeVO);
        return ServerResponse.success();
    }
}
