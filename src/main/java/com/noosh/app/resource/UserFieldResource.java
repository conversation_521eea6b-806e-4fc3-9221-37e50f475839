package com.noosh.app.resource;

import com.noosh.app.commons.dto.userfield.CustomFieldDTO;
import com.noosh.app.commons.dto.userfield.UserFieldReqDTO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.userfield.UserFieldDefsWithValuesVO;
import com.noosh.app.commons.vo.userfield.UserFieldVO;
import com.noosh.app.mapper.userfield.UserFieldMapper;
import com.noosh.app.service.userfield.UserFieldService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 9/15/2022
 */


@RestController
@RequestMapping("/api/userField")

public class UserFieldResource {

    @Autowired
    private UserFieldService userFieldService;

    @Autowired
    private UserFieldMapper userFieldMapper;

    @Operation(summary = "find user field definitions with values by workgroupId and fieldClassId",
            security = @SecurityRequirement(name = "JWT"), description = "authored by Kevin")
    @PostMapping(value = "/findUserFieldDefsWithValues", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<UserFieldDefsWithValuesVO> findUserFieldDefsWithValues(@RequestBody @Valid UserFieldReqDTO reqDTO) {
        Long workgroupId = reqDTO.getWorkgroupId();
        Long fieldClassId = reqDTO.getFieldClassId();
        UserFieldDefsWithValuesVO userFieldDefsWithValuesVO = new UserFieldDefsWithValuesVO();
        userFieldDefsWithValuesVO.setUserFields(userFieldMapper.toVOs(userFieldService.findUserFieldDefs(workgroupId, fieldClassId)));
        userFieldDefsWithValuesVO.setValues(userFieldService.findUserFieldValues(workgroupId, fieldClassId, reqDTO.getPropertyIds(), reqDTO.getIsSupplierView()));
        return ServerResponse.success(userFieldDefsWithValuesVO);
    }

    @Operation(summary = "find user field definitions by workgroupId and fieldClassId",
            security = @SecurityRequirement(name = "JWT"), description = "authored by Kevin")
    @PostMapping(value = "/findUserFieldDefs", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<List<UserFieldVO>> findUserFieldDefs(@RequestBody @Valid UserFieldReqDTO reqDTO) {
        Long workgroupId = reqDTO.getWorkgroupId();
        if (workgroupId == -1L) {
            workgroupId = JwtUtil.getWorkgroupId();
        }
        List<CustomFieldDTO> customFieldDTOs = userFieldService.findUserFieldDefs(workgroupId, reqDTO.getFieldClassId());
        return ServerResponse.success(userFieldMapper.toVOs(customFieldDTOs));
    }

    @Operation(summary = "find user field values by workgroupId and fieldClassId",
            security = @SecurityRequirement(name = "JWT"), description = "authored by Kevin")
    @PostMapping(value = "/findUserFieldValues", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<Map<Long, Map<String, Object>>> findUserFieldValues(@RequestBody @Valid UserFieldReqDTO reqDTO) {
        return ServerResponse.success(userFieldService.findUserFieldValues(reqDTO.getWorkgroupId(), reqDTO.getFieldClassId(), reqDTO.getPropertyIds(), reqDTO.getIsSupplierView()));
    }

}
