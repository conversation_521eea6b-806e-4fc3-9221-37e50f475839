package com.noosh.app.resource;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.mydesk.MyDeskPendingDTO;
import com.noosh.app.commons.dto.order.OrderInvoiceMyDeskDTO;
import com.noosh.app.commons.dto.order.OrderStatusMyDeskDTO;
import com.noosh.app.commons.dto.order.OrderSupplierMyDeskDTO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.mydesk.*;
import com.noosh.app.commons.vo.order.OrderInvoiceMyDeskListVO;
import com.noosh.app.commons.vo.order.OrderStatusMyDeskListVO;
import com.noosh.app.commons.vo.order.OrderSupplierMyDeskListVO;
import com.noosh.app.mapper.mydesk.MyDeskMapper;
import com.noosh.app.service.mydesk.MyDeskService;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 3/26/24
 */
@RestController
@RequestMapping("/myDesk")
public class MyDeskResource {
    @Autowired
    private MyDeskService myDeskService;
    @Autowired
    private MyDeskMapper myDeskMapper;
    @Autowired
    private PreferenceService preferenceService;

    @Operation(summary = "Retrieve my desk order status chart", security = @SecurityRequirement(name = "JWT"), description = "authored by Leila")
    @GetMapping(value = "/orderStatusWidget")
    @Timed
    public ServerResponse<List<OrderStatusMyDeskListVO>> getOrderStatusWidget(
            @Parameter(description = "Project Dropdown, My Project : 0, All accessible project : 1",
                    required = true, example = "1")
            @RequestParam(value = "eventProject", defaultValue = "1") Long eventProject) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        List<OrderStatusMyDeskListVO> listVOs = new ArrayList<OrderStatusMyDeskListVO>();

        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
        if(!preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROCUREMENT, groupPrefs)) {
            return ServerResponse.success(listVOs);
        }
        List<OrderStatusMyDeskDTO> resultDTOs = myDeskService.findOrderWithStatus(workgroupId, userId, eventProject != null && eventProject == (long) 0);
        return ServerResponse.success(myDeskMapper.toOrderStatusVOs(resultDTOs, workgroupId));
    }

    @Operation(summary = "Retrieve my desk order supplier chart", security = @SecurityRequirement(name = "JWT"), description = "authored by Leila")
    @GetMapping(value = "/orderBySupplierWidget")
    @Timed
    public ServerResponse<List<OrderSupplierMyDeskListVO>> getOrderBySupplierWidget(
            @Parameter(description = "Project Dropdown, My Project : 0, All accessible project : 1",
                    required = true, example = "1")
            @RequestParam(value = "eventProject", defaultValue = "1") Long eventProject,
            @Parameter(description = "Date Range",
                    required = true, example = "30")
            @RequestParam(value = "dateRange", defaultValue = "30") Long dateRange,
            @Parameter(description = "Date Type",
                    required = true, example = "0")
            @RequestParam(value = "dateType", defaultValue = "0") String dateType) throws Exception {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        List<OrderSupplierMyDeskListVO> listVOs = new ArrayList<OrderSupplierMyDeskListVO>();

        MyDeskOrderSupplierQueryFilter filter = new MyDeskOrderSupplierQueryFilter();
        filter.setDateType(dateType);
        filter.setDateRange(dateRange);
//        myDeskService.updateOrderSupplierFilter(workgroupId, userId, filter);
        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
        if(!preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROCUREMENT, groupPrefs)) {
            return ServerResponse.success(listVOs);
        }
        List<OrderSupplierMyDeskDTO> resultDTOs = myDeskService.findOrderWithSupplier(workgroupId, userId,
                eventProject != null && eventProject == (long) 0, dateRange, dateType);
        return ServerResponse.success(myDeskMapper.toOrderSupplierVOs(resultDTOs, workgroupId));
    }

    @Operation(summary = "Retrieves my desk order by suppliers by workgroup id and user id", security = @SecurityRequirement(name = "JWT"), description = "authored by Leila")
    @GetMapping("/orderBySupplierWidget/getFilter")
    public ServerResponse<MyDeskOrderSupplierQueryFilterVO> getOrderBySupplierWidgetFilter () throws Exception {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        return ServerResponse.success(myDeskService.getOrderBySupplierWidgetFilter(workgroupId, userId));
    }

    @Operation(summary = "Retrieve my desk order invoice chart", security = @SecurityRequirement(name = "JWT"), description = "authored by Leila")
    @GetMapping(value = "/invoiceWidget")
    @Timed
    public ServerResponse<OrderInvoiceMyDeskListVO> getOrderByInvoiceWidget(
            @Parameter(description = "Project Dropdown, My Project : 0, All accessible project : 1",
                    required = true, example = "1")
            @RequestParam(value = "eventProject", defaultValue = "1") Long eventProject,
            @Parameter(description = "Date Range",
                    required = true, example = "30")
            @RequestParam(value = "dateRange", defaultValue = "30") Long dateRange,
            @Parameter(description = "Invoice Type",
                    required = false, example = "1")
            @RequestParam(value = "invoiceType", required = false) Long invoiceType) throws Exception {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        OrderInvoiceMyDeskListVO vo = new OrderInvoiceMyDeskListVO();

        MyDeskOrderInvoiceQueryFilter filter = new MyDeskOrderInvoiceQueryFilter();
        filter.setDateRange(dateRange);
//        myDeskService.updateOrderInvoiceFilter(workgroupId, userId, filter);
        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
        if(!preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROCUREMENT, groupPrefs)) {
            return ServerResponse.success(vo);
        }
        List<OrderInvoiceMyDeskDTO> resultDTOs = myDeskService.findOrderWithInvoice(workgroupId, userId,
                eventProject != null && eventProject == (long) 0, dateRange, invoiceType);
        return ServerResponse.success(myDeskMapper.toOrderInvoiceVO(resultDTOs, workgroupId));
    }

    @Operation(summary = "Retrieves my desk order invoice by workgroup id and user id", security = @SecurityRequirement(name = "JWT"), description = "authored by Leila")
    @GetMapping("/invoiceWidget/getFilter")
    public ServerResponse<MyDeskOrderInvoiceQueryFilterVO> getOrderInvoiceWidgetFilter () throws Exception {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        return ServerResponse.success(myDeskService.getOrderInvoiceWidgetFilter(workgroupId, userId));
    }

    @Operation(summary = "Retrieve my desk pending chart", security = @SecurityRequirement(name = "JWT"), description = "authored by Leila")
    @GetMapping(value = "/pendingWidget")
    @Timed
    public ServerResponse<MyDeskPendingVO> getPendingWidget(
            @Parameter(description = "Project Dropdown, My Project : 0, All accessible project : 1",
                    required = true, example = "1")
            @RequestParam(value = "eventProject", defaultValue = "1") Long eventProject,
            @Parameter(description = "Date Range",
                    required = true, example = "30")
            @RequestParam(value = "dateRange", defaultValue = "30") Long dateRange) throws Exception {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        MyDeskPendingVO vo = new MyDeskPendingVO();

        MyDeskPendingQueryFilter filter = new MyDeskPendingQueryFilter();
        filter.setDateRange(dateRange);
//        myDeskService.updatePendingFilter(workgroupId, userId, filter);
        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
        if(!preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROCUREMENT, groupPrefs)) {
            return ServerResponse.success(vo);
        }
        MyDeskPendingDTO resultDTO = myDeskService.findPendingOrDueDeskoid(workgroupId, userId,
                eventProject != null && eventProject == (long) 0, dateRange, false, false, true);
        return ServerResponse.success(myDeskMapper.toMyDeskPendingVO(resultDTO, workgroupId));
    }

    @Operation(summary = "Retrieves my desk pending by workgroup id and user id", security = @SecurityRequirement(name = "JWT"), description = "authored by Leila")
    @GetMapping("/pendingWidget/getFilter")
    public ServerResponse<MyDeskPendingQueryFilterVO> getPendingWidgetFilter () throws Exception {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        return ServerResponse.success(myDeskService.getPendingWidgetFilter(workgroupId, userId));
    }

    @Operation(summary = "Retrieve my desk due chart", security = @SecurityRequirement(name = "JWT"), description = "authored by Leila")
    @GetMapping(value = "/dueWidget")
    @Timed
    public ServerResponse<MyDeskDueWidgetVO> getDueWidget(
            @Parameter(description = "Project Dropdown, My Project : 0, All accessible project : 1",
                    required = true, example = "1")
            @RequestParam(value = "eventProject", defaultValue = "1") Long eventProject,
            @Parameter(description = "Due Date Range",
                    required = true, example = "1")
            @RequestParam(value = "dateRange", defaultValue = "1") Long dateRange) throws Exception {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        MyDeskDueWidgetVO vo = new MyDeskDueWidgetVO();

        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
        if(!preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROCUREMENT, groupPrefs)) {
            return ServerResponse.success(vo);
        }
        MyDeskPendingDTO resultDTO = myDeskService.findPendingOrDueDeskoid(workgroupId, userId,
                eventProject != null && eventProject == (long) 0, dateRange, false, true, false);
        return ServerResponse.success(myDeskMapper.toMyDeskDueWidgetVO(resultDTO, workgroupId));
    }

    @Operation(summary = "Retrieve my desk over due chart", security = @SecurityRequirement(name = "JWT"), description = "authored by Leila")
    @GetMapping(value = "/overdueWidget")
    @Timed
    public ServerResponse<MyDeskDueWidgetVO> getOverdueWidget(
            @Parameter(description = "Project Dropdown, My Project : 0, All accessible project : 1",
                    required = true, example = "1")
            @RequestParam(value = "eventProject", defaultValue = "1") Long eventProject) throws Exception {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        MyDeskDueWidgetVO vo = new MyDeskDueWidgetVO();

        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
        if(!preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROCUREMENT, groupPrefs)) {
            return ServerResponse.success(vo);
        }
        MyDeskPendingDTO resultDTO = myDeskService.findPendingOrDueDeskoid(workgroupId, userId,
                eventProject != null && eventProject == (long) 0, (long) 30, true, false, false);
        return ServerResponse.success(myDeskMapper.toMyDeskDueWidgetVO(resultDTO, workgroupId));
    }

    @Operation(summary = "Save My Desk Pending Widget, Invoice Widget And Supplier Widget Filter", security = @SecurityRequirement(name = "JWT"), description = "authored by Leila")
    @PostMapping(value = "/saveFilter")
    @Timed
    public ServerResponse getOverdueWidget(
            @Parameter(description = "My Desk Pending Widget, Invoice Widget And Supplier Widget Filter",
                    required = true)
            @RequestBody MyDeskOrderQueryFilter filter) throws Exception {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        myDeskService.updateFilter(workgroupId, userId, filter);
        return ServerResponse.success();
    }

}
