package com.noosh.app.resource.costcenter;

import com.noosh.app.commons.dto.costcenter.CostCenterDTO;
import com.noosh.app.commons.dto.costcenter.CostCenterUpdateDTO;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.costcenter.CostCenterDetailsVO;
import com.noosh.app.commons.vo.costcenter.CostCenterListFilterVO;
import com.noosh.app.commons.vo.costcenter.CostCenterVO;
import com.noosh.app.service.costcenter.CostCenterService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * User: leilaz
 * Date: 12/6/21
 */
@RestController
@RequestMapping("/costcenter")
public class CostCenterResource {
    @Autowired
    private CostCenterService costCenterService;

    private final Map<String, String> sortFieldMap = new HashMap<>();

    @PostConstruct
    public void init() {
        sortFieldMap.put("number", "CC.NAME");
        sortFieldMap.put("description", "CC.DESCRIPTION");
        sortFieldMap.put("active", "CC.IS_ACTIVE");
    }

    /**
     * Get order cost center allocation custom field labels
     * If not pass the buy work group id then will use the token's one
     * @return
     */
    @Operation(summary = "Retrieves workgroup cost center list with pagination", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/list")
    public ServerResponse<List<CostCenterVO>> list(@Parameter(description = "Bu Client Workgroup Id", required = false, example = "5364054")
                                                   @RequestParam(value="buClientWorkgroupId", required = false) Long buClientWorkgroupId,
                                                   @Parameter(description = "Is load active cost center, if pass null means load all", required = false, example = "true")
                                                   @RequestParam(value="isActive", required = false) Boolean isActive,

                                                   @Parameter(description = "Page Number", required = false, example = "1")
                                                   @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                   @Parameter(description = "Page Size", required = false, example = "10")
                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                   @Parameter(description = "Page Sort", example = "number")
                                                   @RequestParam(value = "sort", defaultValue = "number") String sort,
                                                   @Parameter(description = "Page Order", example = "ASC")
                                                   @RequestParam(value = "order", defaultValue = "ASC") String order) {
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        costCenterService.updateCostCenterListFilter(currentWorkgroupId, userId, pageSize, sortFieldMap.get(sort), order);
        PageVO page = new PageVO(pageNum, pageSize, sortFieldMap.get(sort), order);
        List<CostCenterVO> costCenterListVO = costCenterService.getCostCenterVOList(currentWorkgroupId, buClientWorkgroupId, isActive, userId, page);
        return ServerResponse.success(costCenterListVO, page);
    }

    @Operation(summary = "Get cost center list filter", security = @SecurityRequirement(name = "JWT"), description = "authored by Jenny")
    @GetMapping(value = "/getCostCenterListFilter", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<CostCenterListFilterVO> getCostCenterListFilter() {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(costCenterService.getCostCenterListFilter(workgroupId, userId));
    }

    @Operation(summary = "Retrieves workgroup cost center list without pagination", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/simpleList")
    public ServerResponse<List<CostCenterVO>> simpleList(@Parameter(description = "Bu Client Workgroup Id", required = false, example = "5364054")
                                                   @RequestParam(value="buClientWorkgroupId", required = false) Long buClientWorkgroupId,
                                                   @Parameter(description = "Is load active cost center, if pass null means load all", required = false, example = "true")
                                                   @RequestParam(value="isActive", required = false) Boolean isActive) {
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();

        List<CostCenterVO> costCenterListVO = costCenterService.getSimpleCostCenterVOList(currentWorkgroupId, buClientWorkgroupId, isActive, userId);
        return ServerResponse.success(costCenterListVO);
    }

    @Operation(summary = "Get cost center preferences", security = @SecurityRequirement(name = "JWT"), description = "Get cost center preferences")
    @GetMapping(value = "/getCostCenterPreferences", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse getCostCenterPreferences() {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Map<String, String> displayPrefs = costCenterService.getCostCenterPreferences(workgroupId);
        return ServerResponse.success(displayPrefs);
    }

    @Operation(summary = "Edit cost center preferences", security = @SecurityRequirement(name = "JWT"), description = "Edit cost center preferences")
    @PostMapping(value = "/editCostCenterPreferences", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse editSupplierPreferences(@Parameter(description = "preferences", example = "")
                                                  @RequestBody Map<String, String> preferences) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        costCenterService.editCostCenterPreferences(userId, workgroupId, preferences);
        return ServerResponse.success();
    }

    @Operation(summary = "View cost center details", security = @SecurityRequirement(name = "JWT"), description = "View cost center details")
    @GetMapping(value = "/viewCostCenter", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse viewCostCenter(@Parameter(description = "Delete Cost Center Id", required = true)
                                             @RequestParam(value = "costCenterId") Long costCenterId) {
        CostCenterDetailsVO costCenterDetailsVO = costCenterService.viewCostCenterDetails(costCenterId);
        return ServerResponse.success(costCenterDetailsVO);
    }

    @Operation(summary = "Create or Edit Cost Center", security = @SecurityRequirement(name = "JWT"), description = "Create or Edit Cost Center")
    @PostMapping(value = "/editCostCenter", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse editCostCenter(@Parameter(description = "Created or updated cost center info", required = true)
                                    @RequestBody CostCenterUpdateDTO costCenterUpdateDTO) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        CostCenterDTO costCenterDTO = costCenterService.createOrEditCostCenter(costCenterUpdateDTO, workgroupId, userId);
        return ServerResponse.success(costCenterDTO.getId());
    }

    @Operation(summary = "Delete Cost Center", security = @SecurityRequirement(name = "JWT"), description = "Delete Cost Center by costCenterId")
    @PostMapping(value = "/deleteCostCenter", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse deleteCostCenter(@Parameter(description = "Delete Cost Center Id", required = true)
                                          @RequestParam(value = "costCenterId") Long costCenterId) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        costCenterService.deleteCostCenter(costCenterId, workgroupId, userId);
        return ServerResponse.success();
    }

    @Operation(summary = "Bulk Delete Cost Center", security = @SecurityRequirement(name = "JWT"), description = "Bulk Delete Cost Center by costCenterIds")
    @PostMapping(value = "/bulkDeleteCostCenter", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse bulkDeleteCostCenter(@Parameter(description = "Delete Cost Center Id", required = true)
                                           @RequestParam(value = "costCenterIds") List<Long> costCenterIds) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        costCenterService.bulkDeleteCostCenter(costCenterIds, workgroupId, userId);
        return ServerResponse.success();
    }

    @Operation(summary = "Bulk Active Cost Center", security = @SecurityRequirement(name = "JWT"), description = "Bulk Active Cost Center by costCenterIds")
    @PostMapping(value = "/bulkActiveCostCenter", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse bulkActiveCostCenter(@Parameter(description = "Delete Cost Center Id", required = true)
                                               @RequestParam(value = "costCenterIds") List<Long> costCenterIds) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        costCenterService.bulkActiveCostCenter(costCenterIds, workgroupId, userId);
        return ServerResponse.success();
    }

    @Operation(summary = "Bulk Inactive Cost Center", security = @SecurityRequirement(name = "JWT"), description = "Bulk Inactive Cost Center by costCenterIds")
    @PostMapping(value = "/bulkInactiveCostCenter", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse bulkInactiveCostCenter(@Parameter(description = "Delete Cost Center Id", required = true)
                                               @RequestParam(value = "costCenterIds") List<Long> costCenterIds) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        costCenterService.bulkInactiveCostCenter(costCenterIds, workgroupId, userId);
        return ServerResponse.success();
    }

}
