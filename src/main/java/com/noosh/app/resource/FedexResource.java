package com.noosh.app.resource;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.fedex.FedexInitVO;
import com.noosh.app.commons.vo.fedex.RateQuotesVO;
import com.noosh.app.service.fedex.FedexService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/fedex")
public class FedexResource {

    @Autowired
    private FedexService fedexService;

    @GetMapping(value = "/fedexInit", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<FedexInitVO> getFedexInitVO() {
        return ServerResponse.success(fedexService.getFedexInitVO(JwtUtil.getWorkgroupId(), JwtUtil.getUserId()));
    }

    @PostMapping(value = "/rates/quotes", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<Map> getFedexRatesQuotes(@RequestBody RateQuotesVO rateQuotesVO) {
        return ServerResponse.success(fedexService.getFedexRatesQuotes(rateQuotesVO));
    }

}
