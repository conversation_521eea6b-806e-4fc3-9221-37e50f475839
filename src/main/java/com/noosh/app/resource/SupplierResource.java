package com.noosh.app.resource;

import com.noosh.app.commons.dto.security.WorkgroupDTO;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.client.ClientOptionVO;
import com.noosh.app.commons.vo.supplier.SupplierFilterVO;
import com.noosh.app.commons.vo.supplier.SupplierListVO;
import com.noosh.app.commons.vo.supplier.SupplierWgVO;
import com.noosh.app.service.client.ClientService;
import com.noosh.app.service.supplier.SupplierService;
import com.noosh.app.service.util.BusinessUtil;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 5/13/2022
 */
@RestController
@RequestMapping("/api/supplier")
public class SupplierResource {
    private static final Map<String, String> SORT_FIELD_MAP = new HashMap<>();
    @Autowired
    private SupplierService supplierService;
    @Autowired
    private ClientService clientService;

    @PostConstruct
    public void init() {
        SORT_FIELD_MAP.put("DISPLAY_NAME", "DISPLAY_NAME");
        SORT_FIELD_MAP.put("SW_NAME", "SW_NAME");
        SORT_FIELD_MAP.put("SUPPLIER_CODE", "SUPPLIER_CODE");
        SORT_FIELD_MAP.put("ALIAS", "ALIAS");
        SORT_FIELD_MAP.put("ACCEPT_QUOTE", "ACCEPT_QUOTE");
        SORT_FIELD_MAP.put("IS_APPROVED", "IS_APPROVED");
    }

    @Operation(summary = "Get supplier preferences", security = @SecurityRequirement(name = "JWT"), description = "authored by Mario")
    @GetMapping(value = "/getSupplierPreferences", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse getSupplierPreferences() {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Map<String, String> displayPrefs = supplierService.getSupplierPreferences(workgroupId);
        return ServerResponse.success(displayPrefs);
    }

    @Operation(summary = "Edit supplier preferences", security = @SecurityRequirement(name = "JWT"), description = "authored by Mario")
    @PostMapping(value = "/editSupplierPreferences", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse editSupplierPreferences(@Parameter(description = "preferences", example = "")
                                          @RequestBody Map<String, String> preferences) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        supplierService.editSupplierPreferences(userId, workgroupId, preferences);
        return ServerResponse.success();
    }

    @Operation(summary = "Get the client dropdown on the supplier list page", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getClientDropdown", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse getClientDropdown() {
        Long ownerWorkgroupId = JwtUtil.getWorkgroupId();
        List<ClientOptionVO> clientOptionVOList = clientService.getClientDropdown(ownerWorkgroupId);
        return ServerResponse.success(clientOptionVOList);
    }


    @Operation(summary = "Get list supplier filter", security = @SecurityRequirement(name = "JWT"), description = "authored by Yang")
    @GetMapping(value = "/getSupplierFilter", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<SupplierFilterVO> getSupplierFilter() {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(supplierService.getSupplierFilter(workgroupId, userId));
    }

    @Operation(summary = "Get supplier list", security = @SecurityRequirement(name = "JWT"), description = "authored by Mario")
    @GetMapping(value = "/listSuppliers", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse listSuppliers(@Parameter(description = "supplierWorkgroupName", example = "supplierWorkgroupName")
                                        @RequestParam(value = "supplierWorkgroupName", defaultValue = "") String supplierWorkgroupName,
                                        @Parameter(description = "call getClientDropdown to get the clientWorkgroupId", required = true, example = "0")
                                        @RequestParam(value = "clientWorkgroupIds") List<Long> clientWorkgroupIds,
                                        @Parameter(description = "Page Number", required = true, example = "1")
                                        @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                        @Parameter(description = "Page Size", required = true, example = "10")
                                        @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                        @Parameter(description = "Page Sort", example = "DISPLAY_NAME, SW_NAME, SUPPLIER_CODE, ALIAS, ACCEPT_QUOTE, IS_APPROVED")
                                        @RequestParam(value = "sort", defaultValue = "DISPLAY_NAME") String sort,
                                        @Parameter(description = "Page Order", example = "asc")
                                        @RequestParam(value = "order", defaultValue = "ASC") String order) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        supplierService.updateSupplierFilter(workgroupId, userId, clientWorkgroupIds, pageSize);
        PageVO page = new PageVO(pageNum, pageSize, BusinessUtil.checkAndFormatSortName(sort, SORT_FIELD_MAP) + " ,CW.NAME, SW.NAME", order);
        SupplierListVO supplierListVO = supplierService.listSuppliers(workgroupId, supplierWorkgroupName, clientWorkgroupIds, page);
        return ServerResponse.success(supplierListVO, page);
    }

    @Operation(summary = "Get supplier detail info", security = @SecurityRequirement(name = "JWT"), description = "authored by Yang")
    @GetMapping(value = "/getSupplierDetail", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse getSupplierDetail(@Parameter(description = "supplierId, get from listSuppliers API", required = true, example = "0")
                                            @RequestParam(value = "supplierId") Long supplierId,
                                            @Parameter(description = "true if edit supplier, false if view supplier", required = true, example = "false")
                                            @RequestParam(value = "isEdit", defaultValue = "false") Boolean isEdit) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        SupplierWgVO supplierWgVO = supplierService.getSupplierDetail(userId, workgroupId, supplierId, isEdit);
        return ServerResponse.success(supplierWgVO);
    }

    @Operation(summary = "Get approved clients for the supplier", security = @SecurityRequirement(name = "JWT"), description = "authored by Yang")
    @GetMapping(value = "/getApprovedClients", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse getApprovedClients(@Parameter(description = "supplierId", required = true, example = "0")
                                             @RequestParam(value = "supplierId") Long supplierId,
                                             @Parameter(description = "client name to search")
                                             @RequestParam(value = "searchStr", required = false) String searchStr,
                                             @Parameter(description = "Page Number", required = true, example = "1")
                                             @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                             @Parameter(description = "Page Size", required = true, example = "10")
                                             @RequestParam(value = "pageSize", defaultValue = "200") Integer pageSize,
                                             @Parameter(description = "Page Order", example = "asc")
                                             @RequestParam(value = "order", defaultValue = "ASC") String order) {
        PageVO page = new PageVO(pageNum, pageSize, "CW.NAME", order);
        Long workgroupId = JwtUtil.getWorkgroupId();
        List<SupplierWgVO> approvedClients = supplierService.getApprovedClients(workgroupId, supplierId, searchStr, page);
        return ServerResponse.success(approvedClients, page);
    }

    @Operation(summary = "Find supplier list", security = @SecurityRequirement(name = "JWT"), description = "authored by Yang")
    @PostMapping(value = "/findSuppliers", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse findSuppliers(@RequestBody SupplierFilterVO supplierFilter,
                                        @Parameter(description = "Page Number", required = true, example = "1")
                                        @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                        @Parameter(description = "Page Size", required = true, example = "10")
                                        @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        PageVO page = new PageVO(pageNum, pageSize, null, null);
        List<WorkgroupDTO> supplierWgs = supplierService.findSuppliers(workgroupId, supplierFilter, page);
        return ServerResponse.success(supplierWgs, page);
    }

    @Operation(summary = "batch check, will return a map, key is supplierWorkgroupId, value is true/false", security = @SecurityRequirement(name = "JWT"), description = "authored by Yang")
    @GetMapping(value = "/batchCheckSupplierExist", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse batchCheckSupplierExist(@Parameter(description = "call getClientDropdown to get the clientWorkgroupId, -1 stands for all", required = true, example = "5003146,5002282")
                                                  @RequestParam(value = "clientWorkgroupIds") List<Long> clientWorkgroupIds,
                                                  @Parameter(description = "supplierWorkgroupIds", required = true, example = "5002347,5002348")
                                                  @RequestParam(value = "supplierWorkgroupIds") List<Long> supplierWorkgroupIds) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Map<Long, Boolean> isExistMap = supplierService.batchCheckSupplierExist(workgroupId, supplierWorkgroupIds, clientWorkgroupIds);
        return ServerResponse.success(isExistMap);
    }

    @Operation(summary = "add supplier warehouse on supplier detail page", security = @SecurityRequirement(name = "JWT"), description = "authored by Yang")
    @PostMapping(value = "/addSupplierWarehouse", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse addSupplierWarehouse(@Parameter(description = "supplierId", required = true, example = "123456")
                                                  @RequestParam(value = "supplierId") Long supplierId,
                                                  @Parameter(description = "warehouseLocationId", required = true, example = "123456")
                                                  @RequestParam(value = "warehouseLocationId") Long warehouseLocationId,
                                                  @Parameter(description = "IDCode", required = true, example = "abc")
                                                  @RequestParam(value = "IDCode") String IDCode) {
        supplierService.addSupplierWarehouse(supplierId, warehouseLocationId, IDCode);
        return ServerResponse.success();
    }

}
