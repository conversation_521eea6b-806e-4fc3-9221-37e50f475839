package com.noosh.app.resource;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.costcenter.InvoiceOrderCostCenterDTO;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.invoice.InvoiceDetailVO;
import com.noosh.app.commons.vo.invoice.InvoiceInitInfoVO;
import com.noosh.app.commons.vo.invoice.order.SelectOrderVO;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.exception.NoPreferenceException;
import com.noosh.app.mapper.OrderMapper;
import com.noosh.app.service.invoice.InvoiceDetailService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @auther mario
 * @date 7/21/2020
 */
@RestController
@RequestMapping("/invoice")
public class InvoiceDetailResource {

    @Autowired
    private PermissionService permissionService;
    @Autowired
    private InvoiceDetailService invoiceDetailService;
    @Autowired
    private OrderMapper orderMapper;

    private final Map<String, String> sortFieldMap = new HashMap<>();

    @PostConstruct
    public void init() {
        sortFieldMap.put("id", "OV.OR_ORDER_ID");
    }

    @Operation(summary = "Get invoice details", security = @SecurityRequirement(name = "JWT"), description = "authored by Mario")
    @GetMapping(value = "/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse detail(
            @Parameter(description = "project Id", required = true, example = "11243951")
            @RequestParam(value = "projectId") Long projectId,
            @Parameter(description = "invoice Id", required = true, example = "123456")
            @RequestParam(value = "invoiceId") Long invoiceId,
            @Parameter(description = "Locale", required = true, example = "en_US")
            @RequestParam(value = "locale") String locale) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        InvoiceDetailVO invoiceDetailVO = invoiceDetailService.detail(userId, workgroupId, projectId, invoiceId, locale);
        return ServerResponse.success(invoiceDetailVO);
    }

    @Operation(summary = "Retrieve create invoice - select orders", security = @SecurityRequirement(name = "JWT"), description = "authored by Mario")
    @GetMapping(value = "/selectOrders")
    @Timed
    public ServerResponse<SelectOrderVO> selectOrders(
            @Parameter(description = "Project Id", required = true, example = "11243951")
            @RequestParam(value = "projectId") Long projectId,

            @Parameter(description = "Page Number", required = false, example = "1")
            @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @Parameter(description = "Page Size", required = false, example = "10")
            @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @Parameter(description = "Sort", required = false, example = "orderId")
            @RequestParam(value = "sort", defaultValue = "id") String sort,
            @Parameter(description = "Sort Order", required = false, example = "desc")
            @RequestParam(value = "order", defaultValue = "desc") String order
    ) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();

        if(sortFieldMap.get(sort) == null) {
            throw new IllegalArgumentException(sort + " is not a valid sort field");
        }

        PageVO page = new PageVO(pageNum, pageSize, sortFieldMap.get(sort), order);

        if(!permissionService.checkAll(PermissionID.VIEW_ORDER, workgroupId, userId, projectId)) {
            throw new NoPreferenceException("You cannot view order");
        }

        return ServerResponse.success(invoiceDetailService.selectOrders(workgroupId, userId, projectId, page), page);
    }

    @Operation(summary = "Retrieve create or edit invoice - select orders", security = @SecurityRequirement(name = "JWT"), description = "authored by Mario")
    @GetMapping(value = "/getEditableInvoice")
    @Timed
    public ServerResponse<InvoiceInitInfoVO> initInformation(
            @Parameter(description = "Project Id", required = true, example = "11243951")
            @RequestParam(value = "projectId") Long projectId,
            @Parameter(description = "Order Id", required = true, example = "11243951")
            @RequestParam(value = "orderId") Long orderId,
            @Parameter(description = "Invoice Id", required = false, example = "11243951")
            @RequestParam(value = "invoiceId", required = false) Long invoiceId,
            @Parameter(description = "Original Invoice Id for replace or clone", example = "11243951")
            @RequestParam(value = "originalInvoiceId", required = false) Long originalInvoiceId,
            @Parameter(description = "Locale", required = true, example = "11243951")
            @RequestParam(value = "locale") String locale,
            @RequestParam(value = "isReplace", required = false) Boolean isReplace
    ) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();

        if (!permissionService.checkAll(PermissionID.VIEW_ORDER, workgroupId, userId, projectId)) {
            throw new NoPreferenceException("You cannot view order");
        }
        InvoiceInitInfoVO initInfo = invoiceDetailService.initInformation(userId, workgroupId, projectId, orderId, invoiceId, originalInvoiceId, locale, isReplace);
        return ServerResponse.success(initInfo);
    }

    @Operation(summary = "Customization API for HHG/Communisis on invoice edit page", security = @SecurityRequirement(name = "JWT"), description = "authored by Yang")
    @GetMapping(value = "/hh-sap/getEditableShipmentRecords")
    @Timed
    public ServerResponse<InvoiceDetailVO> getEditableShipmentRecords(
            @Parameter(description = "Project Id", required = true, example = "11243951")
            @RequestParam(value = "projectId") Long projectId,
            @Parameter(description = "Order Id", required = true, example = "11243951")
            @RequestParam(value = "orderId") Long orderId,
            @Parameter(description = "Invoice Id", example = "11243951")
            @RequestParam(value = "invoiceId", required = false) Long invoiceId,
            @Parameter(description = "Locale", required = true, example = "11243951")
            @RequestParam(value = "locale") String locale) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        InvoiceDetailVO invoiceDetailVO = invoiceDetailService.getEditableShipmentRecords(userId, workgroupId, projectId, orderId, invoiceId, locale);
        return ServerResponse.success(invoiceDetailVO);
    }
    @Operation(summary = "Customization API for HHG/Communisis on invoice detail page", security = @SecurityRequirement(name = "JWT"), description = "authored by Yang")
    @GetMapping(value = "/hh-sap/getShipmentRecords", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<InvoiceDetailVO> getCustomShipmentRecords(
            @Parameter(description = "project Id", required = true, example = "11243951")
            @RequestParam(value = "projectId") Long projectId,
            @Parameter(description = "invoice Id", required = true, example = "123456")
            @RequestParam(value = "invoiceId") Long invoiceId,
            @Parameter(description = "Locale", required = true, example = "en_US")
            @RequestParam(value = "locale") String locale) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        InvoiceDetailVO invoiceDetailVO = invoiceDetailService.getShipmentRecords(workgroupId, projectId, invoiceId, locale);
        return ServerResponse.success(invoiceDetailVO);
    }

    @Operation(summary = "Get invoice cost center list", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/costCenters", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse costCenters(
            @Parameter(description = "project Id", required = true, example = "11243951")
            @RequestParam(value = "projectId") Long projectId,
            @Parameter(description = "invoice Id", required = true, example = "123456")
            @RequestParam(value = "invoiceId") Long invoiceId,
            @Parameter(description = "invoice Id", required = true, example = "123456")
            @RequestParam(value = "orderId") Long orderId,
            @Parameter(description = "Locale", required = true, example = "en_US")
            @RequestParam(value = "locale") String locale) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();

        if (!permissionService.checkAll(PermissionID.VIEW_INVOICE, workgroupId, userId, projectId)) {
            throw new NoPermissionException("Your don't have permission to view invoice detail");
        }
        List<InvoiceOrderCostCenterDTO> invoiceOrderCostCenterDTOs = invoiceDetailService.getInvoiceCostCenter(
                projectId, workgroupId, userId, invoiceId, orderId, locale);
        return ServerResponse.success(orderMapper.toInvoiceCostCenterDetailVO(invoiceOrderCostCenterDTOs));
    }

}
