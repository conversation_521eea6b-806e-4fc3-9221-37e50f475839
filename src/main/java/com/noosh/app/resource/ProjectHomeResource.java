package com.noosh.app.resource;

import com.noosh.app.commons.vo.costcenter.OrderCostCenterDetailVO;
import com.noosh.app.commons.vo.invoice.InvoiceDeskoidVO;
import com.noosh.app.commons.vo.order.OrderDeskoidVO;
import com.noosh.app.service.InvoiceService;
import com.noosh.app.service.OrderService;
import com.noosh.app.service.costcenter.ProjectHomeCostCenterService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.inject.Inject;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/project")
public class ProjectHomeResource {

    @Inject
    private ProjectHomeCostCenterService projectHomeCostCenterService;
    @Inject
    private InvoiceService invoiceService;
    @Inject
    private OrderService orderService;

    @Operation(summary = "cost Center Deskoid", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/costCenterDeskoid")
    @Timed
    public ResponseEntity<List<OrderCostCenterDetailVO>> getCostCenterListByProjectId(
            @Parameter(description = "Project Id", required = true, example = "5364054")
            @RequestParam(value="projectId") Long projectId,
            @Parameter(description = "filter type", example = "all")
            @RequestParam(value = "type", defaultValue = "all") String type) {

        Long currentUserId = JwtUtil.getUserId();
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();

        List<OrderCostCenterDetailVO> costCenterDeskoidVOList = null;
        costCenterDeskoidVOList = projectHomeCostCenterService.getCostCenterDeskoidsByProjectIdAndType(
                projectId, type, currentUserId, currentWorkgroupId);
        return new ResponseEntity<>(costCenterDeskoidVOList, HttpStatus.OK);

    }

    @Operation(summary = "invoice Deskoid", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/invoiceDeskoid")
    @Timed
    @Transactional(readOnly = true)
    public ResponseEntity<InvoiceDeskoidVO> getInvoiceDeskoid(
            @Parameter(description = "project Id", required = true, example = "11243951")
            @RequestParam(value = "projectId") Long projectId,
            @RequestParam(value = "filter") String filter) {

        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();

        InvoiceDeskoidVO invoiceDeskoid = invoiceService.getInvoiceDeskoid(projectId, filter, workgroupId, userId);
        return new ResponseEntity<>(invoiceDeskoid, HttpStatus.OK);
    }

    @Operation(summary = "order Deskoid", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/orderDeskoid")
    @Timed
    public ResponseEntity<OrderDeskoidVO> getOrderListByProjectId(
            @Parameter(description = "Project Id", required = true, example = "11247195")
            @RequestParam(value = "projectId") Long projectId,
            @RequestParam(value = "type", defaultValue = "all") String type,
            @Parameter(description = "Cog Filter", required = false, example = "-1")
            @RequestParam(value = "cogFilter") List<Long> cogFilter) {

        Long currentUserId = JwtUtil.getUserId();
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();

        // "Show All" is mutually exclusive from the rest of the Hide options.
        if (cogFilter == null) {
            cogFilter = new ArrayList<>();
            cogFilter.add(-1L);
        }

        OrderDeskoidVO vo = orderService.getOrderDeskoidVO(projectId, type, cogFilter, currentUserId, currentWorkgroupId);

        return new ResponseEntity<>(vo, HttpStatus.OK);
    }
}
