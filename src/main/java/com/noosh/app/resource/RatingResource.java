package com.noosh.app.resource;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.rating.SupplierRatingDTO;
import com.noosh.app.commons.entity.rating.SrQuestion;
import com.noosh.app.commons.entity.rating.SrSection;
import com.noosh.app.commons.vo.ResponseCode;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.rating.SectionQuestionVO;
import com.noosh.app.commons.vo.rating.SectionVO;
import com.noosh.app.commons.vo.rating.SupplierRatingDetailVO;
import com.noosh.app.commons.vo.rating.SupplierScoreVO;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.mapper.rating.SupplierRatingMapper;
import com.noosh.app.service.rating.RatingService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.inject.Inject;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * User: leilaz
 * Date: 9/27/19
 */
@RestController
@RequestMapping("/rating")
public class RatingResource {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Inject
    private PermissionService permissionService;
    @Inject
    private RatingService ratingService;
    @Inject
    private SupplierRatingMapper supplierRatingMapper;

    /**
     * GET  /procurement/rating -> get the rating by object id, object class id and task type id.
     */
    /**
     * GET  /procurement/rating -> get the rating by object id, object class id and task type id.
     */
    @Operation(summary = "Get task by task object id, task object class id and task type id", security = @SecurityRequirement(name = "JWT"), description = "authored by Leila")
    @GetMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ResponseEntity<ServerResponse<SupplierRatingDetailVO>> getSupplierRating(@Parameter(description = "Object Id", required = true, example = "5364054")
                                                                                    @RequestParam(value="objectId") Long objectId,
                                                                                    @Parameter(description = "Project Id", required = true, example = "11247049")
                                                                                    @RequestParam(value="projectId") Long projectId,
                                                                                    @Parameter(description = "Change order Id", required = false, example = "11247049")
                                                                                    @RequestParam(value="changeOrderId", required = false) Long changeOrderId) throws Exception {
        if (objectId == null || projectId == null) {
            return new ResponseEntity<ServerResponse<SupplierRatingDetailVO>>(
                    new ServerResponse(ResponseCode.SUCCESS.getCode(), "Please pass the valid id!"), HttpStatus.BAD_REQUEST);
        }

        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();

        if (!permissionService.checkAll(PermissionID.VIEW_ORDER, workgroupId, userId, projectId)) {
            throw new NoPermissionException("You don't have permission to view this order");
        }

        SupplierRatingDetailVO supplierRatingDetailVO = ratingService.findRating(objectId,
                workgroupId, projectId, changeOrderId);
        return new ResponseEntity<ServerResponse<SupplierRatingDetailVO>>(ServerResponse.success(supplierRatingDetailVO), HttpStatus.OK);
    }

    /**
     * POST /procurement/rating -> Create rating
     */
    @Operation(summary = "Create Supplier Rating", security = @SecurityRequirement(name = "JWT"), description = "authored by Leila")
    @PostMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ResponseEntity<ServerResponse<String>> createSupplierRating(@Parameter(description = "Supplier Rating DTO", required = true)
                                                            @RequestBody SupplierRatingDTO supplierRatingDTO) throws Exception {
        if (supplierRatingDTO.getSupplierWorkgroupId() == null || supplierRatingDTO.getProjectId() == null
                || supplierRatingDTO.getCreateUserId() == null || supplierRatingDTO.getCurrentWorkgroupId() == null
                || supplierRatingDTO.getOrderId() == null
                || (supplierRatingDTO.getRateByOrderItem() == null && supplierRatingDTO.getRateByOrder() == null)) {
            return new ResponseEntity<ServerResponse<String>>(new ServerResponse(ResponseCode.SUCCESS.getCode(),
                    "Please pass the valid object!"), HttpStatus.BAD_REQUEST);
        }

        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();

        if (!permissionService.checkAll(PermissionID.VIEW_ORDER, workgroupId, userId, supplierRatingDTO.getProjectId())) {
            throw new NoPermissionException("You don't have permission to view this order");
        }

        ratingService.createOrUpdateRating(supplierRatingDTO, supplierRatingDTO.getCurrentWorkgroupId(),
                false, supplierRatingDTO.getCreateUserId());
        return new ResponseEntity<ServerResponse<String>>(ServerResponse.success(), HttpStatus.OK);
    }

    /**
     * POST /procurement/rating/complete -> Complete rating
     */
    @Operation(summary = "Complete Supplier Rating", security = @SecurityRequirement(name = "JWT"), description = "authored by Leila")
    @PostMapping(value = "/complete")
    @Timed
    public ResponseEntity<ServerResponse<String>> completeSupplierRating(@Parameter(description = "Supplier Rating DTO", required = true)
                                                             @RequestBody SupplierRatingDTO supplierRatingDTO) throws Exception {
        if (supplierRatingDTO.getSupplierWorkgroupId() == null || supplierRatingDTO.getProjectId() == null
                || supplierRatingDTO.getCreateUserId() == null || supplierRatingDTO.getCurrentWorkgroupId() == null
                || supplierRatingDTO.getOrderId() == null
                || (supplierRatingDTO.getRateByOrderItem() == null && supplierRatingDTO.getRateByOrder() == null)) {
            return new ResponseEntity<ServerResponse<String>>(new ServerResponse(ResponseCode.SUCCESS.getCode(),
                    "Please pass the valid object!"), HttpStatus.BAD_REQUEST);
        }

        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();

        if (!permissionService.checkAll(PermissionID.VIEW_ORDER, workgroupId, userId, supplierRatingDTO.getProjectId())) {
            throw new NoPermissionException("You don't have permission to view this order");
        }

        ratingService.createOrUpdateRating(supplierRatingDTO, supplierRatingDTO.getCurrentWorkgroupId(),
                true, supplierRatingDTO.getCreateUserId());
        return new ResponseEntity<ServerResponse<String>>(ServerResponse.success(), HttpStatus.OK);
    }

    @Operation(summary = "Get Supplier Rating Question And Section", security = @SecurityRequirement(name = "JWT"), description = "authored by Leila")
    @GetMapping(value = "/sectionQuestion")
    @Timed
    public ResponseEntity<ServerResponse<SectionQuestionVO>> getSupplierSectionQuestion(@Parameter(description = "Outsourcer or Buyer Workgroup Id, not Token workgroup id", required = true, example = "1234567")
                                                                                            @RequestParam(value="workgroupId") Long workgroupId) {

        List<SrSection> sections = ratingService.findSection();
        Map questionMap = ratingService.findAllQuestion(workgroupId);
        if (!questionMap.isEmpty()) {
            List<SrQuestion> questions = (List<SrQuestion>)questionMap.get("questionList");
            List<SectionVO> sectionVOs = sections.stream().map(supplierRatingMapper :: toSectionVO).collect(Collectors.toList());
            sectionVOs.stream().forEach(s -> {
                s.setQuestionList(questions.stream().filter(q -> q.getSectionId().longValue() == s.getId())
                        .map(supplierRatingMapper :: toQuestionVO).collect(Collectors.toList()));
            });
            SectionQuestionVO sectionQuestionVO = new SectionQuestionVO();
            sectionQuestionVO.setGranularity((Long)questionMap.get("granularity"));
            sectionQuestionVO.setNaAllowed((Boolean)questionMap.get("naAllowed"));
            sectionQuestionVO.setSection(sectionVOs);
            return new ResponseEntity<ServerResponse<SectionQuestionVO>>(ServerResponse.success(sectionQuestionVO), HttpStatus.OK);
        }
        return new ResponseEntity<ServerResponse<SectionQuestionVO>>(new ServerResponse<>(ResponseCode.SUCCESS.getCode(),
            "There is no supplier rating question defined in current workgroup!"), HttpStatus.OK);
    }

    @Operation(summary = "Get Supplier Score", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/findSupplierScore")
    @Timed
    public ResponseEntity<SupplierScoreVO> findSupplierScore(
            @RequestParam("ownerWorkgroupId") Long ownerWorkgroupId,
            @RequestParam("supplierWorkgroupId") Long supplierWorkgroupId) {
        return ResponseEntity.ok(ratingService.findSupplierScore(ownerWorkgroupId, supplierWorkgroupId));
    }

    @Operation(summary = "Get Supplier Score", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/bulkFindSupplierScore")
    @Timed
    public ResponseEntity<Map<Long, SupplierScoreVO>> bulkFindSupplierScore(
            @RequestParam("ownerWorkgroupId") Long ownerWorkgroupId,
            @RequestParam("supplierWorkgroupIds") List<Long> supplierWorkgroupIds) {
        return ResponseEntity.ok(ratingService.findSupplierScores(ownerWorkgroupId, supplierWorkgroupIds));
    }
}
