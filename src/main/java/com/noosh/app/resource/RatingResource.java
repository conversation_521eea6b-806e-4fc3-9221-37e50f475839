package com.noosh.app.resource;

import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.rating.RatingPreferenceVO;
import com.noosh.app.commons.vo.rating.RatingQuestionnaireVO;
import com.noosh.app.commons.vo.rating.RatingSectionVO;
import com.noosh.app.service.rating.RatingService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * author: Yang
 * Date: 10/17/2022
 */
@RestController
@RequestMapping("/api/rating")
public class RatingResource {

    @Autowired
    private RatingService ratingService;


    @Operation(summary = "get supplier rating preferences", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getRatingPreferences", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<RatingPreferenceVO> getSupplierRatingPreferences() {
        Long workgroupId = JwtUtil.getWorkgroupId();
        RatingPreferenceVO ratingPreferenceVO = ratingService.getRatingPreferences(workgroupId);
        return ServerResponse.success(ratingPreferenceVO);
    }

    @Operation(summary = "call this API when go to edit rating preferences", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getEditableRatingPreferences", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<RatingPreferenceVO> getEditableRatingPreferences() {
        Long workgroupId = JwtUtil.getWorkgroupId();
        RatingPreferenceVO ratingPreferenceVO = ratingService.getEditableRatingPreferences(workgroupId);
        return ServerResponse.success(ratingPreferenceVO);
    }

    @Operation(summary = "edit rating preferences", security = @SecurityRequirement(name = "JWT"))
    @PostMapping(value = "/editRatingPreferences", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<RatingPreferenceVO> editRatingPreferences(@RequestBody RatingPreferenceVO ratingPreferenceVO) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        RatingPreferenceVO updatedRatingPreferenceVO = ratingService.editRatingPreferences(userId, workgroupId, ratingPreferenceVO);
        return ServerResponse.success(updatedRatingPreferenceVO);
    }

    @Operation(summary = "get rating questionnaire list", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getRatingQuestionnaireList", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<List<RatingQuestionnaireVO>> getRatingQuestionnaireList() {
        Long workgroupId = JwtUtil.getWorkgroupId();
        List<RatingQuestionnaireVO> list = ratingService.getRatingQuestionnaireList(workgroupId);
        return ServerResponse.success(list);
    }

    @Operation(summary = "get rating questionnaire detail, go to edit questionnaire", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getRatingQuestionnaireDetail", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<RatingQuestionnaireVO> getRatingQuestionnaireDetail(@Parameter(description = "questionnaireId", required = true)
                                                  @RequestParam(value = "questionnaireId") Long questionnaireId) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        RatingQuestionnaireVO ratingQuestionnaireVO = ratingService.getRatingQuestionnaireDetail(questionnaireId, workgroupId);
        return ServerResponse.success(ratingQuestionnaireVO);
    }


    @Operation(summary = "edit rating questionnaire", security = @SecurityRequirement(name = "JWT"))
    @PostMapping(value = "/editRatingQuestionnaire", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<Long> editRatingQuestionnaire(@RequestBody RatingQuestionnaireVO ratingQuestionnaireVO) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        long questionnaireId = ratingService.editRatingQuestionnaireWithQuestions(ratingQuestionnaireVO, workgroupId, userId);
        return ServerResponse.success(questionnaireId);
    }

    @Operation(summary = "delete rating questionnaire", security = @SecurityRequirement(name = "JWT"))
    @PostMapping(value = "/deleteRatingQuestionnaire", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse deleteRatingQuestionnaire(@Parameter(description = "questionnaireId", required = true)
                                                    @RequestParam(value = "questionnaireId") Long questionnaireId) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        ratingService.deleteRatingQuestionnaire(questionnaireId, workgroupId);
        return ServerResponse.success();
    }

    @Operation(summary = "create rating questionnaire", security = @SecurityRequirement(name = "JWT"))
    @PostMapping(value = "/createRatingQuestionnaire", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<Long> createRatingQuestionnaire(@RequestBody RatingQuestionnaireVO ratingQuestionnaireVO) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        long questionnaireId = ratingService.createRatingQuestionnaireWithQuestions(ratingQuestionnaireVO, workgroupId, userId);
        return ServerResponse.success(questionnaireId);
    }

    @Operation(summary = "get rating section list", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getRatingSectionList", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<List<RatingSectionVO>> getRatingSectionList() {
        List<RatingSectionVO> list = ratingService.getRatingSectionList();
        return ServerResponse.success(list);
    }

}


