package com.noosh.app.resource;

import com.noosh.app.commons.dto.custom.CustomAttributeReqDTO;
import com.noosh.app.service.custom.CustomAttributeService;
import io.micrometer.core.annotation.Timed;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 6/26/2022
 */
@RestController
@RequestMapping("/api/customAttribute")
public class CustomAttributeResource {

    @Autowired
    private CustomAttributeService customAttributeService;

    // this API is intended for Feign invocation only and are not exposed to the front-end
    @PostMapping(value = "/findCustomAttributes", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public Map<Long, Map<String, Object>> findCustomAttributes(@RequestBody CustomAttributeReqDTO reqDTO) {
        return customAttributeService.findCustomAttributes(reqDTO.getPropertyIds(), reqDTO.getParamNames());
    }

    // this API is intended for Feign invocation only and are not exposed to the front-end
    @PostMapping(value = "/findCustomAttributesByPropertyIds", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public Map<Long, Map<String, Object>> findCustomAttributesByPropertyIds(@RequestBody List<Long> propertyIds) {
        return customAttributeService.findCustomAttributes(propertyIds, null);
    }
}
