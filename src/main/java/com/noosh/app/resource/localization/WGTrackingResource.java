package com.noosh.app.resource.localization;

import com.noosh.app.commons.dto.tracking.WGTrackingDTO;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.tracking.WGTrackingVO;
import com.noosh.app.mapper.tracking.WGTrackingMapper;
import com.noosh.app.service.tracking.LocalizationTrackingService;
import com.noosh.app.service.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 3/18/24
 */
@RestController
@RequestMapping("/api/localization/log")
public class WGTrackingResource {
    @Autowired
    private LocalizationTrackingService localizationTrackingService;
    @Autowired
    private WGTrackingMapper wgTrackingMapper;

    private final Map<String, String> sortFieldMap = new HashMap<>();

    @PostConstruct
    public void init() {
        sortFieldMap.put("createDate", "TR.CREATE_DATE");
    }

    /**
     * Get localization log list
     * @return
     */
    @Operation(summary = "Get localization log list", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/list")
    public ServerResponse<List<WGTrackingVO>> list(@Parameter(description = "Page Number", required = false, example = "1")
                                                   @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                   @Parameter(description = "Page Size", required = false, example = "10")
                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                   @Parameter(description = "Page Sort", example = "createDate")
                                                   @RequestParam(value = "sort", defaultValue = "createDate") String sort,
                                                   @Parameter(description = "Page Order", example = "ASC")
                                                   @RequestParam(value = "order", defaultValue = "ASC") String order,
                                                   @Parameter(description = "Tracking Type List", example = "123, 234")
                                                   @RequestParam(value = "typeIds", defaultValue = "-1") List<Long> typeIds,
                                                   @Parameter(description = "locale", required = true, example = "en_US")
                                                   @RequestParam(value = "locale") String locale,
                                                   @Parameter(description = "Tracking Search String", required = false, example = "test")
                                                   @RequestParam(value = "searchStr", required =  false) String searchStr,
                                                   @Parameter(description = "Create Date From", required = false, example = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
                                                   @RequestParam(value = "dateFrom", required = false) String createDateFrom,
                                                   @Parameter(description = "Create Date To", required = false, example = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
                                                   @RequestParam(value = "dateTo", required = false) String createDateTo) throws Exception {
        Long workgroupId = JwtUtil.getWorkgroupId();
        DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        LocalDateTime dateFrom = null, dateTo = null;
        if (createDateFrom != null) {
            dateFrom = LocalDateTime.parse(createDateFrom, dateFormat);
        }

        if (createDateTo != null) {
            dateTo = LocalDateTime.parse(createDateTo, dateFormat);
        }
        PageVO page = new PageVO(pageNum, pageSize, sortFieldMap.get(sort), order);
        List<WGTrackingDTO> trackingDTOs = localizationTrackingService.findByWorkgroupId(
                workgroupId, typeIds, page, locale, dateFrom, dateTo, searchStr);
        return ServerResponse.success(wgTrackingMapper.toVOs(trackingDTOs), page);
    }
}
