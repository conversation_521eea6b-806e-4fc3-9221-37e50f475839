package com.noosh.app.resource.localization;

import com.noosh.app.commons.dto.localization.PortalDTO;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.localization.PortalConfirmedVO;
import com.noosh.app.commons.vo.localization.PortalVO;
import com.noosh.app.mapper.localization.PortalMapper;
import com.noosh.app.service.localization.LocalizationService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 1/9/24
 */
@RestController
@RequestMapping("/api/portal")
public class PortalResource {
    @Autowired
    private PortalMapper portalMapper;
    @Autowired
    private LocalizationService localizationService;

    private final Map<String, String> sortFieldMap = new HashMap<>();

    @PostConstruct
    public void init() {
        sortFieldMap.put("name", "NAME");
    }

    /**
     * Get portal list
     * @return
     */
    @Operation(summary = "Get portal list", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/list")
    public ServerResponse<List<PortalVO>> list(@Parameter(description = "Portal Name", required = false, example = "default")
                                                   @RequestParam(value="name", required = false) String name,
                                                   @Parameter(description = "Page Number", required = false, example = "1")
                                                   @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                   @Parameter(description = "Page Size", required = false, example = "10")
                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                   @Parameter(description = "Page Sort", example = "name")
                                                   @RequestParam(value = "sort", defaultValue = "name") String sort,
                                                   @Parameter(description = "Page Order", example = "ASC")
                                                   @RequestParam(value = "order", defaultValue = "ASC") String order) {
        Long userId = JwtUtil.getUserId();
        PageVO page = new PageVO(pageNum, pageSize, sortFieldMap.get(sort), order);
        List<PortalDTO> portalDTOs = localizationService.getPortalListWithSearch(name, page, userId);
        return ServerResponse.success(portalMapper.toVOs(portalDTOs), page);
    }

    /**
     * Get portal full list
     * @return
     */
    @Operation(summary = "Get full portal list", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/fullList")
    public ServerResponse<List<PortalVO>> list() {
        Long userId = JwtUtil.getUserId();
        List<PortalDTO> portalDTOs = localizationService.getPortalList(userId);
        return ServerResponse.success(portalMapper.toVOs(portalDTOs));
    }

    @Operation(summary = "Create or Edit portal", security = @SecurityRequirement(name = "JWT"), description = "Create or Edit portal")
    @PostMapping(value = "/save", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse save(@Parameter(description = "Created or updated portal info", required = true)
                               @RequestBody PortalDTO portalDTO) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        localizationService.editPortal(portalDTO, userId, workgroupId);
        return ServerResponse.success();
    }

    /**
     * Delete portal by id
     * @param id
     * @return
     */
    @Operation(summary = "Delete portal by id", description = "authored by Leila",
            security = @SecurityRequirement(name = "JWT"))
    @DeleteMapping("/delete")
    public ServerResponse delete(
            @Parameter(description = "Deleted portal id", required = true)
            @RequestParam(value = "id", required = true) long id,
            @Parameter(description = "Confirmed to delete if there has resource depend on it", required = true)
            @RequestParam(value = "isConfirmed", required = true, defaultValue = "false") boolean isConfirmed) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        PortalConfirmedVO portalConfirmedVO = localizationService.deletePortal(id, userId, isConfirmed, workgroupId);
        return ServerResponse.success(portalConfirmedVO);
    }

}
