package com.noosh.app.resource.localization;

import com.noosh.app.commons.dto.localization.LocalizationListDTO;
import com.noosh.app.commons.dto.localization.LocalizationUpdatedDTO;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.localization.LocalizationListVO;
import com.noosh.app.feign.FileResourceFeignClient;
import com.noosh.app.mapper.localization.LocalizationMapper;
import com.noosh.app.service.localization.LocalizationService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDate;
import java.time.format.TextStyle;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * User: leilaz
 * Date: 11/29/23
 */
@RestController
@RequestMapping("/api/localization")
public class LocalizationResource {
    @Autowired
    private LocalizationMapper localizationMapper;
    @Autowired
    private LocalizationService localizationService;
    @Autowired
    private FileResourceFeignClient fileResourceFeignClient;

    private final Map<String, String> sortFieldMap = new HashMap<>();

    @PostConstruct
    public void init() {
        sortFieldMap.put("language", "LANGUAGE");
        sortFieldMap.put("portal", "PORTAL");
        sortFieldMap.put("resourceName", "RESOURCE_NAME");
        sortFieldMap.put("resourceValue", "RESOURCE_VALUE");
    }

    /**
     * Get localization list
     * @return
     */
    @Operation(summary = "Get localization list", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/list")
    public ServerResponse<LocalizationListVO> list(@Parameter(description = "Language", required = false, example = "en_US")
                                                   @RequestParam(value="language", required = false) String language,
                                                   @Parameter(description = "Portal", required = false, example = "default")
                                                   @RequestParam(value="portal", required = false) String portal,
                                                   @Parameter(description = "Resource Name", required = false, example = "test")
                                                   @RequestParam(value="resourceName", required = false) String resourceName,
                                                   @Parameter(description = "Resource Value", required = false, example = "test")
                                                   @RequestParam(value="resourceValue", required = false) String resourceValue,
                                                   @Parameter(description = "Page Number", required = false, example = "1")
                                                   @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                   @Parameter(description = "Page Size", required = false, example = "10")
                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                   @Parameter(description = "Page Sort", example = "number")
                                                   @RequestParam(value = "sort", defaultValue = "number") String sort,
                                                   @Parameter(description = "Page Order", example = "ASC")
                                                   @RequestParam(value = "order", defaultValue = "ASC") String order) {
        Long userId = JwtUtil.getUserId();
        PageVO page = new PageVO(pageNum, pageSize, sortFieldMap.get(sort), order);
        LocalizationListDTO localizationListDTO = localizationService.getLocalizationList(portal, language, resourceName,
                resourceValue, page, userId);
        return ServerResponse.success(localizationMapper.toListVO(localizationListDTO), page);
    }

    @Operation(summary = "Create or Edit Localization", security = @SecurityRequirement(name = "JWT"), description = "Create or Edit localization")
    @PostMapping(value = "/save", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse save(@Parameter(description = "Created or updated localization info", required = true)
                                         @RequestBody LocalizationUpdatedDTO localizationUpdatedDTO) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        LocalizationListDTO localizationListDTO = localizationService.saveLocalization(localizationUpdatedDTO, userId, workgroupId);
        return ServerResponse.success(localizationMapper.toListVO(localizationListDTO));
    }

    /**
     * POST  /upload -> Upload the localization file
     */
    @Operation(summary = "Upload localization file", security = @SecurityRequirement(name = "JWT"))
    @PostMapping(value = "/upload", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ResponseEntity upload(@Parameter(description = "Localization upload file", required = true)
                                 @RequestParam("file") MultipartFile file,
                                 @Parameter(description = "Language", required = true, example = "en_US")
                                 @RequestParam(value="language", required = true) String language,
                                 @Parameter(description = "Portal", required = false, example = "default")
                                 @RequestParam(value="portal", required = false) String portal,
                                 @Parameter(description = "Is Confirmed", required = true, example = "false")
                                 @RequestParam(value="isConfirmed", required = true) boolean isConfirmed) {

        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        LocalizationListDTO localizationListDTO = localizationService.saveExcel(userId, portal, language, file, isConfirmed, workgroupId);
        return new ResponseEntity(ServerResponse.success(localizationMapper.toListVO(localizationListDTO)), HttpStatus.OK);
    }

    @Operation(summary = "Export localization to Excel", security = @SecurityRequirement(name = "JWT"))
    @PostMapping("/export")
    public void benchmarkExport(@Parameter(description = "Language", required = false, example = "en_US")
                                    @RequestParam(value="language", required = false) String language,
                                @Parameter(description = "Portal", required = false, example = "default")
                                @RequestParam(value="portal", required = false) String portal,
                                @Parameter(description = "Export Localization Ids", required = false)
                                @RequestBody(required = false) List<Long> ids,
                                HttpServletResponse response) {
        Long currentUserId = JwtUtil.getUserId();

        HSSFWorkbook workbook = localizationService.exportLocalization(currentUserId, portal, language, ids, false);

        String fileName = "";
        if (portal == null && language == null) {
            fileName = "customized_localization.xls";
        } else {
            fileName = (language == null ? portal : (language + (portal != null ? ("_" + portal) : ""))) + ".xls";
        }

        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename="+ fileName);
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");

        try (OutputStream os = response.getOutputStream()) {
            workbook.write(os);
            os.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Delete localization by ids
     * @param ids
     * @return
     */
    @Operation(summary = "Delete localization by ids", description = "authored by Leila",
            security = @SecurityRequirement(name = "JWT"))
    @DeleteMapping("/delete")
    public ServerResponse deleteLocalization(
            @Parameter(description = "Deleted localization ids", required = true)
            @RequestParam(value = "ids", required = true) List<Long> ids) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        localizationService.deleteLocalizations(ids, userId, workgroupId);
        return ServerResponse.success();
    }

    /**
     * Get localization merged list
     * @return
     */
    @Operation(summary = "Get localization merged list", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/mergedList")
    public ServerResponse<Map> getMergedList(@Parameter(description = "Language", required = true, example = "en_US")
                                                   @RequestParam(value="language", required = true) String language,
                                                   @Parameter(description = "Portal", required = false, example = "default")
                                                   @RequestParam(value="portal", required = false, defaultValue = "default") String portal) {
        Long userId = JwtUtil.getUserId();
        Map resultMap = localizationService.getMergedLocalizationList(portal, language);
        return ServerResponse.success(resultMap);
    }

}
