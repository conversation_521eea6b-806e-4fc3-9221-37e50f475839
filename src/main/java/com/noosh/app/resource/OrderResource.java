package com.noosh.app.resource;

import com.noosh.app.commons.constant.CustomFieldClassID;
import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.TermsTypeID;
import com.noosh.app.commons.dto.costcenter.CostCenterDetailUpdateDTO;
import com.noosh.app.commons.dto.costcenter.CostCenterUpdateDTO;
import com.noosh.app.commons.dto.breakout.SpecBreakoutDTO;
import com.noosh.app.commons.dto.costcenter.OrderCostCenterDTO;
import com.noosh.app.commons.dto.costcenter.OrderCostCenterDetailDTO;
import com.noosh.app.commons.dto.order.OrderDetailDTO;
import com.noosh.app.commons.dto.order.OrderItemDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.vo.ResponseCode;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.breakout.SpecBreakoutTypeVO;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.commons.vo.custom.CustomFieldVO;
import com.noosh.app.commons.vo.order.*;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.feign.ProjectOpenFeignClient;
import com.noosh.app.mapper.OrderMapper;
import com.noosh.app.mapper.component.DropdownMapper;
import com.noosh.app.mapper.property.CustomFieldMapper;
import com.noosh.app.service.AggregatedOrderService;
import com.noosh.app.service.OrderService;
import com.noosh.app.service.breakout.BreakoutService;
import com.noosh.app.service.costcenter.CostCenterService;
import com.noosh.app.service.customfield.CustomFieldService;
import com.noosh.app.service.order.OrderCreateService;
import com.noosh.app.service.order.OrderEditService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.terms.TermsService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import jakarta.inject.Inject;
import java.util.List;

/**
 * User: leilaz
 * Date: 9/25/19
 */
@RestController
@RequestMapping("/order")
public class OrderResource {
    @Inject
    private CustomFieldService customFieldService;
    @Inject
    private OrderService orderService;
    @Inject
    private OrderCreateService orderCreateService;
    @Inject
    private OrderEditService orderEditService;
    @Inject
    private AggregatedOrderService aggregatedOrderService;
    @Inject
    private PermissionService permissionService;
    @Inject
    private ProjectOpenFeignClient projectOpenFeignClient;
    @Inject
    private CustomFieldMapper customFieldMapper;
    @Inject
    private BreakoutService breakoutService;
    @Inject
    private DropdownMapper dropdownMapper;
    @Inject
    private OrderMapper orderMapper;
    @Inject
    private CostCenterService costCenterService;
    @Inject
    private TermsService termsService;

    /**
     * Get order cost center allocation custom field labels
     * If not pass the buy work group id then will use the token's one
     * @return
     */
    @Operation(summary = "Retrieves order cost center allocation custom field labels", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/customFields")
    public ServerResponse<List<CustomFieldVO>> customFields(@Parameter(description = "Buy Workgroup Id", required = false, example = "5364054")
                                                            @RequestParam(value = "buyWorkgroupId", required = false) Long buyWorkgroupId) {
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(customFieldMapper.toCustomFieldVOs(
                customFieldService.getFieldsByWorkgroupId(buyWorkgroupId == null ? currentWorkgroupId : buyWorkgroupId, CustomFieldClassID.COST_CENTER, false)));
    }

    /**
     * Get budget type custom field labels
     * If not pass the buy work group id then will use the token's one
     *
     * @return
     */
    @Operation(summary = "Retrieves budget type custom field labels", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/budgetTypes")
    public ServerResponse<List<DropdownVO<Long>>> budgetTypes(@Parameter(description = "Buy Workgroup Id", required = false, example = "5364054")
                                                              @RequestParam(value = "buyWorkgroupId", required = false) Long buyWorkgroupId) {
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(dropdownMapper.toBudgetTypeDropdowns(
                customFieldService.getFieldsByWorkgroupId(buyWorkgroupId == null ? currentWorkgroupId : buyWorkgroupId, CustomFieldClassID.PROJECT_BUDGET, false)));
    }

    /**
     * GET  /order -> get the order by order id and project id.
     */
    @Operation(summary = "Get order by order id and project id", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/{orderId}/project/{projectId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    @Transactional(readOnly = true)
    public ResponseEntity getOrderById(@Parameter(description = "Order Id", required = true, example = "5364054")
                                       @PathVariable(value="orderId") Long orderId,
                                       @Parameter(description = "Project Id", required = true, example = "1000000")
                                       @PathVariable(value="projectId") Long projectId,
                                       @AuthenticationPrincipal Jwt jwt) throws Exception {
        Long currentUserId = Long.parseLong(jwt.getClaimAsString("userId"));
        Long currentWorkgroupId = Long.parseLong(jwt.getClaimAsString("workgroupId"));

        if (!permissionService.checkAll(PermissionID.VIEW_ORDER, currentWorkgroupId, currentUserId, projectId)) {
            throw new NoPermissionException("Your don't have permission to view order");
        }

        OrderVersionDTO orderVersionDTO = orderService.findOrderVersionById(orderId, projectId, currentUserId, currentWorkgroupId);
        if (orderVersionDTO != null) {
            OrderDetailVO orderDetailVO = new OrderDetailVO();
            orderMapper.toDetailVO(orderVersionDTO, orderDetailVO);
            return new ResponseEntity<ServerResponse>(ServerResponse.success(orderDetailVO), HttpStatus.OK);
        }

        return new ResponseEntity(new ServerResponse<OrderDetailVO>(ResponseCode.ERROR.getCode(), "Order not found!"), HttpStatus.NOT_FOUND);
    }

    /**
     * GET  /order -> get the order by order id and project id.
     */
    @Operation(summary = "Get Simple order by order id", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/simple", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    @Transactional(readOnly = true)
    public ResponseEntity getSimpleOrderById(@Parameter(description = "Order Id", required = true, example = "5364054")
                                             @RequestParam(value="orderId") Long orderId) throws Exception {
        Long currentUserId = JwtUtil.getUserId();
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();

        OrderVersionDTO orderVersionDTO = orderService.findRatingOrderById(orderId);
        if (orderVersionDTO != null) {
            RatingOrderDetailVO orderDetailVO = orderMapper.toRatingDetailVO(orderVersionDTO);
            return new ResponseEntity<ServerResponse>(ServerResponse.success(orderDetailVO), HttpStatus.OK);
        }

        return new ResponseEntity(new ServerResponse<OrderDetailVO>(ResponseCode.ERROR.getCode(), "Order not found!"), HttpStatus.NOT_FOUND);
    }

    /**
     * GET  /order -> get the order general info by order id and project id.
     */
    @Operation(summary = "Get general order by order id", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    @Transactional(readOnly = true)
    public ResponseEntity getGeneralOrderInfoById(@Parameter(description = "Order Id", required = true, example = "5364054")
                                                  @RequestParam(value="orderId") Long orderId,
                                                  @Parameter(description = "Order Type", required = true, example = "buy")
                                                  @RequestParam(value = "orderType", defaultValue = "buy") String orderType,
                                                  @Parameter(description = "Project Id", required = true, example = "5364054")
                                                  @RequestParam(value="projectId") Long projectId,
                                                  @AuthenticationPrincipal Jwt jwt) throws Exception {
        Long currentUserId = Long.parseLong(jwt.getClaimAsString("userId"));
        Long currentWorkgroupId = Long.parseLong(jwt.getClaimAsString("workgroupId"));

        if (!permissionService.checkAll(PermissionID.VIEW_ORDER, currentWorkgroupId, currentUserId, projectId)) {
            throw new NoPermissionException("Your don't have permission to view order");
        }
        OrderDetailDTO orderDetailDTO = orderService.findOrderInfo(orderId, projectId, currentUserId, currentWorkgroupId, orderType);
        if (orderDetailDTO != null && orderDetailDTO.getOrderVersionDTO() != null) {
            OrderVersionDTO orderVersionDTO = orderDetailDTO.getOrderVersionDTO();
            OrderGeneralInfoVO orderGeneralInfoVO = orderMapper.toGeneralDetailVO(orderDetailDTO);
            OrderButtonVO orderButtonVO = orderMapper.toGeneralTermsAndButtonVO(orderDetailDTO);
            orderMapper.hideAcceptButtonByMaterialBT(orderGeneralInfoVO, orderVersionDTO, orderButtonVO);
            OrderInfoVO orderDetailVO = new OrderInfoVO();
            orderDetailVO.setOrderGeneral(orderGeneralInfoVO);
            orderDetailVO.setButtons(orderButtonVO);
            orderDetailVO.setBuyerTerms(termsService.findTermAndCondition(orderVersionDTO.getBuyerTermsId(), TermsTypeID.BUYER_PURCHASE, orderVersionDTO.getBuyerWorkgroupId()));
            orderDetailVO.setSupplierTerms(termsService.findTermAndCondition(orderVersionDTO.getSupplierTermsId(), TermsTypeID.SUPPLIER_SELL, orderVersionDTO.getSupplierWorkgroupId()));
            return new ResponseEntity<ServerResponse>(ServerResponse.success(orderDetailVO), HttpStatus.OK);
        }

        return new ResponseEntity(new ServerResponse<OrderDetailVO>(ResponseCode.ERROR.getCode(), "Order not found!"), HttpStatus.NOT_FOUND);
    }

    @Operation(summary = "Retrieves order payment method dropdown", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/paymentMethod")
    public ServerResponse<List<DropdownVO<Long>>> paymentMethod() {
        return ServerResponse.success(dropdownMapper.toPaymentDropdowns(orderService.findAllPaymentMethod()));
    }

    @Operation(summary = "Retrieves order reason dropdown", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/selectionReasons")
    public ServerResponse<List<DropdownVO<Long>>> selectionReasons() {
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(dropdownMapper.toSelectionReasons(orderService.findWorkgroupReason(currentWorkgroupId)));
    }

    @Operation(summary = "Retrieves order cost center allocation", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/costCenters")
    public ResponseEntity costCenters( @Parameter(description = "Order Id", required = false, example = "5364054")
                                       @RequestParam(value="orderId", required = false) Long orderId,
                                       @Parameter(description = "Project Id", required = true, example = "5364054")
                                       @RequestParam(value="projectId") Long projectId,
                                       @Parameter(description = "is project cost center", example = "false")
                                       @RequestParam(value="isProjectCostCenter", defaultValue = "false", required = false) boolean isProjectCostCenter) {
        Long currentUserId = JwtUtil.getUserId();
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();

        if (orderId != null && (!isProjectCostCenter)) {
            if (!permissionService.checkAll(PermissionID.VIEW_ORDER, currentWorkgroupId, currentUserId, projectId)) {
                throw new NoPermissionException("Your don't have permission to view order");
            }
            // If it is change order, then needs to replace the quantity with change order's qty - aggregate order's qty
            OrderCostCenterDetailDTO orderCostCenterDetailDTO = costCenterService.getCostCenterForOrder(orderId, projectId,
                    currentWorkgroupId, currentUserId, false);
            if (orderCostCenterDetailDTO.getIsChangeOrder() && orderCostCenterDetailDTO.getBaseOrderId() != null) {
                OrderVersionDTO parentOrder = orderService.findOrderVersionById(orderCostCenterDetailDTO.getBaseOrderId(),
                        projectId, currentUserId, currentWorkgroupId);
                OrderVersionDTO baseOrder = aggregatedOrderService.getAggregatedOrder(parentOrder,
                        orderCostCenterDetailDTO.getOrderAcceptDate(), currentWorkgroupId, currentUserId);
                List<OrderItemDTO> orderItemDTOs = baseOrder.getOrderItemDTOs();
                if (orderCostCenterDetailDTO.getOrderCostCenters() != null) {
                    List<OrderCostCenterDTO> costCenterDTOs = orderCostCenterDetailDTO.getOrderCostCenters();
                    for (OrderItemDTO dto : orderItemDTOs) {
                        OrderCostCenterDTO costCenterDTO = costCenterDTOs.stream().filter(c -> c.getSpecId() != null
                                && c.getSpecId().longValue() == dto.getSpecId()).findAny().orElse(null);
                        if (costCenterDTO != null) {
                            costCenterDTO.setQuantity(costCenterDTO.getQuantity() - (
                                    dto.getQuantity() != null ? dto.getQuantity().doubleValue() : (double) 0));
                        }
                    }
                }

            }
            return new ResponseEntity<ServerResponse>(ServerResponse.success(
                    orderMapper.toOrderCostCenterDetailVO(orderCostCenterDetailDTO)), HttpStatus.OK);
        } else {
            return new ResponseEntity<ServerResponse>(ServerResponse.success(
                    orderMapper.toProjectCostCenterDetailVO(costCenterService.getCostCenterForProject(projectId,
                            currentWorkgroupId, currentUserId))), HttpStatus.OK);
        }
    }

    @Operation(summary = "Create or update cost center", security = @SecurityRequirement(name = "JWT"))
    @PostMapping("/costCenters")
    public ResponseEntity costCenters(@Parameter(description = "Cost center update dto", required = true) @RequestBody CostCenterUpdateDTO costCenterUpdateDTO) {
        Long currentUserId = JwtUtil.getUserId();
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();

        if (costCenterUpdateDTO == null || costCenterUpdateDTO.getDetails() == null || costCenterUpdateDTO.getDetails().size() == 0 || costCenterUpdateDTO.getProjectId() == null) {
            throw new IllegalArgumentException("Please pass the valid object");
        }

        for (CostCenterDetailUpdateDTO detailUpdateDTO : costCenterUpdateDTO.getDetails()) {
            if (detailUpdateDTO.getObjectClassId() == null || detailUpdateDTO.getObjectId() == null) {
                throw new IllegalArgumentException("Please pass the valid object");
            }
            if (detailUpdateDTO.getObjectClassId() == ObjectClassID.OBJECT_CLASS_PROJECT) {
                boolean canAccess = projectOpenFeignClient.canAccessProject(detailUpdateDTO.getObjectId());
                if (!canAccess) throw new NoPermissionException("You don't have permission to view this project!");
            }
            costCenterService.saveCostCenterAllocation(detailUpdateDTO.getCostCenters(), currentWorkgroupId, currentUserId,
                    costCenterUpdateDTO.getProjectId(), detailUpdateDTO.getObjectId(),
                    detailUpdateDTO.getObjectClassId(), costCenterUpdateDTO.getIsCopyToOrder(),
                    detailUpdateDTO.getIsTax(), detailUpdateDTO.getIsShipping());
        }


        return new ResponseEntity<ServerResponse>(ServerResponse.success(), HttpStatus.OK);
    }


    @Operation(summary = "Retrieves Order Create Page Init Info", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/create/init")
    @Timed
    public ServerResponse<OrderDetailVO> getOrderCreateDetail(
            @Parameter(description = "Locale", required = true, example = "en_US")
            @RequestParam(value = "locale", required = true) String locale,
            @Parameter(description = "Project Id", example = "11247195")
            @RequestParam(value = "projectId", required = true) Long projectId,
            @Parameter(description = "pricing flag", example = "1")
            @RequestParam(value = "fromPricing", required = false, defaultValue = "0") String fromPricing,
            @Parameter(description = "supplier User Id", example = "5028578")
            @RequestParam(value = "supplierUserId", required = false) Long supplierUserId,
            @Parameter(description = "supplier workgroup Id", example = "5016868")
            @RequestParam(value = "supplierWgId", required = false) Long supplierWgId,
            @Parameter(description = "pricing contract Id",  example = "5004368")
            @RequestParam(value = "contractId", required = false) Long contractId,
            @Parameter(description = "pricing Quantity", example = "1000")
            @RequestParam(value = "qty", required = false) Double qty,
            @Parameter(description = "pricing Amount", example = "1000.00")
            @RequestParam(value = "amount", required = false) Double amount,
            @Parameter(description = "pricing subtotals", example = "PREMARKUP:17740.0__MARKUPFIXED:180.0")
            @RequestParam(value = "subtotals", required = false) String subtotals,
            @Parameter(description = "Estimate item price ids", example = "1,2,3")
            @RequestParam(value = "estimateItemPriceIds", required = false) List<Long> estimateItemPriceIds,
            @Parameter(description = "Selected Spec Node Ids", example = "1,2,3")
            @RequestParam(value = "selectedSpecNodeIds", required = false) List<Long> selectedSpecNodeIds) throws Exception {

        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        OrderDetailVO orderDetailVO;
        if (estimateItemPriceIds != null) {
            orderDetailVO = orderCreateService.getOrderCreateFromEstDetail(projectId, workgroupId, userId, locale, estimateItemPriceIds);
        } else {
            orderDetailVO = orderCreateService.getOrderCreateDetail(projectId, workgroupId, userId, locale, selectedSpecNodeIds, fromPricing, supplierUserId, supplierWgId, qty, amount, subtotals);
        }
        return ServerResponse.success(orderDetailVO);
    }

    @Operation(summary = "Retrieves Order Edit Page Init Info", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/edit/init")
    @Timed
    public ServerResponse<OrderEditVO> getOrderEditDetail(
            @Parameter(description = "Locale", example = "en_US")
            @RequestParam(value = "locale", required = true) String locale,
            @Parameter(description = "Project Id", example = "11247195")
            @RequestParam(value = "projectId", required = true) Long projectId,
            @Parameter(description = "Order Id", example = "7943224")
            @RequestParam(value = "orderId", required = false) Long orderId,
            @Parameter(description = "mode", example = "quick")
            @RequestParam(value = "mode", required = false) String mode,
            @Parameter(description = "Is reorder", example = "false")
            @RequestParam(value = "isReorder",  required = false, defaultValue = "false") Boolean isReorder) throws Exception {

        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        OrderEditVO orderEditVO = orderEditService.getOrderEditDetail(orderId, projectId, workgroupId, userId, isReorder);
        return ServerResponse.success(orderEditVO);
    }

    @Operation(summary = "reorder init page", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/reorder")
    @Timed
    public ServerResponse<OrderGeneralInfoVO> getReorderDetail(
            @Parameter(description = "Locale", example = "en_US")
            @RequestParam(value = "locale", required = true) String locale,
            @Parameter(description = "Project Id", example = "11247195")
            @RequestParam(value = "projectId", required = true) Long projectId,
            @Parameter(description = "Order Id", example = "7943224")
            @RequestParam(value = "orderId", required = true) Long orderId) throws Exception {

        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        OrderGeneralInfoVO orderGeneralInfoVO = aggregatedOrderService.getReorderDetail(orderId, projectId, workgroupId, userId);
        return ServerResponse.success(orderGeneralInfoVO);
    }

    @Operation(summary = "confirm order view page", security = @SecurityRequirement(name = "JWT"))
    @PostMapping("/confirmOrder")
    public ServerResponse<OrderConfirmVO> confirmOrder(
            @Parameter(description = "Locale", example = "en_US")
            @RequestParam(value = "locale", required = true) String locale,
            @Parameter(description = "Project Id", example = "11247195")
            @RequestParam(value = "projectId", required = true) Long projectId,
            @Parameter(description = "Order DTO", required = true)
            @RequestBody OrderVersionDTO orderVersionDTO) throws Exception {
        Long currentUserId = JwtUtil.getUserId();
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();

        if (orderVersionDTO == null) {
            throw new IllegalArgumentException("Please pass the valid object");
        }

        OrderConfirmVO orderConfirmVO = orderCreateService.getOrderConfirmView(orderVersionDTO, projectId);
        return ServerResponse.success(orderConfirmVO);
    }

    @Operation(summary = "get Breakout Types", security = @SecurityRequirement(name = "JWT"))
    @PostMapping("/getBreakoutTypes")
    public ServerResponse<List<SpecBreakoutTypeVO>> getBreakoutTypes(
            @Parameter(description = "Locale", example = "en_US")
            @RequestParam(value = "locale", required = true) String locale,
            @Parameter(description = "supplier workgroup Id", example = "11247195")
            @RequestParam(value = "supplierWgId", required = true) Long supplierWgId,
            @Parameter(description = "spec breakout dto", required = true)
            @RequestBody List<SpecBreakoutDTO> specBreakoutDTOS) throws Exception {

        List<SpecBreakoutTypeVO> specBreakoutTypeVOS = breakoutService.getBreakoutTypes(supplierWgId, specBreakoutDTOS);
        return ServerResponse.success(specBreakoutTypeVOS);
    }

    @Operation(summary = "add Items", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/addItems")
    @Timed
    public ServerResponse<List<OrderItemVO>> addItems(
            @Parameter(description = "Locale", example = "en_US")
            @RequestParam(value = "locale", required = true) String locale,
            @Parameter(description = "Project Id", example = "11247195")
            @RequestParam(value = "projectId", required = true) Long projectId,
            @Parameter(description = "Selected Spec Node Ids", example = "1,2,3")
            @RequestParam(value = "selectedSpecNodeIds", required = false) List<Long> selectedSpecNodeIds,
            @Parameter(description = "Supplier Workgroup Id", example = "5016868")
            @RequestParam(value = "supplierWgId", required = false) Long supplierWgId,
            @Parameter(description = "index", example = "3")
            @RequestParam(value = "index", required = true) Long index) throws Exception {

        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        List<OrderItemVO> orderItemVOs = orderEditService.addItems(projectId, userId, workgroupId, selectedSpecNodeIds, supplierWgId, index);
        return ServerResponse.success(orderItemVOs);
    }

    @Operation(summary = "reload order item spec and spec options", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/getItemSpecs")
    @Timed
    public ServerResponse<OrderItemSpecsVO> getItemSpecs(
            @Parameter(description = "Locale", example = "en_US")
            @RequestParam(value = "locale", required = true) String locale,
            @Parameter(description = "Project Id", example = "11247195")
            @RequestParam(value = "projectId", required = true) Long projectId,
            @Parameter(description = "Spec Id", example = "5167916")
            @RequestParam(value = "specId", required = true) Long specId,
            @Parameter(description = "Job Id", example = "5360298")
            @RequestParam(value = "jobId", required = true) Long jobId) throws Exception {

        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        OrderItemSpecsVO orderItemSpecsVO = orderEditService.getSpecsOfOrderItem(projectId, specId, jobId, userId, workgroupId);
        return ServerResponse.success(orderItemSpecsVO);
    }

    /**
     * Get order cost center allocation custom field labels for UHG
     * If not pass the buy work group id then will use the token's one
     * @return
     */
    @Operation(summary = "Retrieves order cost center allocation custom field labels for UHG", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/customFieldsForUHG")
    public ServerResponse<List<CustomFieldVO>> customFieldsForUHG(@Parameter(description = "Buy Workgroup Id", required = false, example = "5364054")
                                                            @RequestParam(value = "buyWorkgroupId", required = false) Long buyWorkgroupId) {
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(customFieldMapper.toCustomFieldVOs(
                customFieldService.getFieldsByWorkgroupIdForUHG(buyWorkgroupId == null ? currentWorkgroupId : buyWorkgroupId, CustomFieldClassID.COST_CENTER, false)));
    }

}