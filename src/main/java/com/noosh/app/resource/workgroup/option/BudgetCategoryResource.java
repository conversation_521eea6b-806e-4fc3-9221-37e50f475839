package com.noosh.app.resource.workgroup.option;

import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.service.collaboration.BudgetCategoryService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 7/21/2022
 */
@RestController
@RequestMapping("/api/budgetCategory")
public class BudgetCategoryResource {

    @Autowired
    private BudgetCategoryService budgetCategoryService;

    @Operation(summary = "get Budget Category List", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getBudgetCategory", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse getBudgetCategoryList() {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(budgetCategoryService.getBudgetCategoryList(userId, workgroupId));
    }

}
