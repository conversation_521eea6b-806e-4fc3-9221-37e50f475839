package com.noosh.app.resource.workgroup.option;

import java.util.List;

import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.workgroup.option.members.InviteMemberVO;
import com.noosh.app.commons.vo.workgroup.option.members.InviteNewMemberConfirmVO;
import com.noosh.app.commons.vo.workgroup.option.members.MemberListVO;
import com.noosh.app.commons.vo.workgroup.option.members.MemberVO;
import com.noosh.app.service.util.JwtUtil;
import com.noosh.app.service.workgroup.option.MemberService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Shan
 * @since 8/3/2022
 */
@RestController
@RequestMapping("/api/members")
public class MembersResource {

    @Autowired
    private MemberService memberService;

    @Operation(summary = "init Member List", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/initMemberList")
    @Timed
    public ServerResponse<MemberListVO> initMemberList() {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        MemberListVO memberListVO = memberService.initMemberList(userId, workgroupId);
        return ServerResponse.success(memberListVO);
    }

    @Operation(summary = "get Member List", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/getMemberList")
    @Timed
    public ServerResponse<List<MemberVO>> getMemberList(
            @Parameter(description = "List Type", required = true, example = "ACTIVE, INACTIVE, INVITED, SUSPENDED")
            @RequestParam("listType") String listType,
            @Parameter(description = "Page Number", required = true, example = "1")
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @Parameter(description = "Page Size", required = true, example = "10")
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @Parameter(description = "Page Sort", example = "p_last_name")
            @RequestParam(value = "sort", defaultValue = "p_last_name") String sort,
            @Parameter(description = "Page Order", example = "ASC")
            @RequestParam(value = "order", defaultValue = "ASC") String order) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        PageVO page = new PageVO(pageNum, pageSize, memberService.getSort(sort), order);
        List<MemberVO> memberListVO = memberService.getMemberList(userId, workgroupId, listType, page);
        page.setSort(sort);
        return ServerResponse.success(memberListVO, page);
    }

    @Operation(summary = "init Invite And Assign Member Page", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/initInviteAndAssignMember")
    @Timed
    public InviteMemberVO initInviteAndAssignMember() {
        Long workgroupId = JwtUtil.getWorkgroupId();
        return memberService.initInviteAndAssignMember(workgroupId);
    }

    @Operation(summary = "init Invite New Member Confirm Page", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/initInviteNewMemberConfirm")
    @Timed
    public InviteNewMemberConfirmVO initInviteNewMemberConfirm(
            @Parameter(description = "email", example = "<EMAIL>")
            @RequestParam(value = "email") String email) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        return memberService.inviteNewMemberConfirmView(workgroupId, email);
    }
}
