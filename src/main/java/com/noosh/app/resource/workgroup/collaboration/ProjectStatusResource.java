package com.noosh.app.resource.workgroup.collaboration;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.collaboration.ProjectStatusDTO;
import com.noosh.app.commons.dto.collaboration.ProjectStatusDeleteDTO;
import com.noosh.app.commons.dto.collaboration.ProjectStatusUpdateDTO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.collaboration.ProjectStatusInUsedVO;
import com.noosh.app.commons.vo.collaboration.ProjectStatusListVO;
import com.noosh.app.commons.vo.collaboration.ProjectStatusVO;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.mapper.collaboration.ProjectStatusMapper;
import com.noosh.app.service.permission.PermissionService;
import com.noosh.app.service.util.JwtUtil;
import com.noosh.app.service.workgroup.option.ProjectStatusService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * User: leilaz
 * Date: 7/26/22
 */
@RestController
@RequestMapping("/api/collaboration/projectStatus")
public class ProjectStatusResource {
    @Autowired
    private ProjectStatusService projectStatusService;
    @Autowired
    private ProjectStatusMapper projectStatusMapper;
    @Autowired
    private PermissionService permissionService;

    @Operation(summary = "Retrieve project status list", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getStatusList", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ResponseEntity getStatusList(@Parameter(description = "Sort", required = false, example = "title")
                                        @RequestParam(value = "sort", defaultValue = "name", required = false) String sort,
                                        @Parameter(description = "Sort Order", required = false, example = "desc")
                                        @RequestParam(value = "order", defaultValue = "asc", required = false) String order) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();

        //First load project status list
        List<ProjectStatusDTO> projectStatusDTOs = projectStatusService.getProjectStatusList(workgroupId);
        ProjectStatusListVO projectStatusListVO = new ProjectStatusListVO();
        //Convert dto list to vo list
        projectStatusListVO.setStatusList(projectStatusMapper.toVOs(projectStatusDTOs));
        //Check the order and sort
        if ("name".equalsIgnoreCase(sort) && order != null && projectStatusListVO.getStatusList() != null) {
            List<ProjectStatusVO> sortedList = new ArrayList<ProjectStatusVO>();
            if ("asc".equalsIgnoreCase(order)) {
                sortedList = projectStatusListVO.getStatusList().stream().sorted(Comparator.nullsLast(
                        Comparator.comparing(j -> ((ProjectStatusVO)j ).getName().toUpperCase()))).collect(Collectors.toList());
            } else {
                sortedList = projectStatusListVO.getStatusList().stream().sorted(Comparator.nullsLast(
                        Comparator.comparing(j -> ((ProjectStatusVO)j ).getName().toUpperCase()).reversed())).collect(Collectors.toList());
            }
            projectStatusListVO.setStatusList(sortedList);
        }
        //Set permission, add project status and edit project status permission
        projectStatusListVO.setCanAddProjectStatus(!permissionService.checkWorkgroupLevelCannotPrivilege(
                PermissionID.CREATE_PROJECT_STATUS_OPTIONS, workgroupId, userId));
        projectStatusListVO.setCanEditProjectStatus(!permissionService.checkWorkgroupLevelCannotPrivilege(
                PermissionID.EDIT_PROJECT_STATUS_OPTIONS, workgroupId, userId));
        //Return project status list
        return new ResponseEntity<ServerResponse>(ServerResponse.success(projectStatusListVO), HttpStatus.OK);
    }

    @Operation(summary = "Check project status is used in projects", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/checkStatusIsInUsed", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ResponseEntity checkStatusIsInUsed(@Parameter(description = "Project status id", required = true, example = "123456789")
                                                     @RequestParam(value = "projectStatusId") Long projectStatusId) {
        //Check project status is used in current projects
        boolean isInUsed = projectStatusService.isProjectStatusInUsed(projectStatusId);
        ProjectStatusInUsedVO projectStatusInUsedVO = new ProjectStatusInUsedVO();
        projectStatusInUsedVO.setIsInUsed(isInUsed);
        projectStatusInUsedVO.setProjectStatusId(projectStatusId);
        return new ResponseEntity<ServerResponse>(ServerResponse.success(projectStatusInUsedVO), HttpStatus.OK);
    }

    @Operation(summary = "Create or update project status", security = @SecurityRequirement(name = "JWT"))
    @PostMapping(value = "/createOrUpdateStatus", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ResponseEntity createOrUpdateStatus(@Parameter(description = "Created or updated project status info", required = true)
                                               @RequestBody ProjectStatusUpdateDTO projectStatusUpdateDTO) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        //Check create project status permission
        if (projectStatusUpdateDTO.getId() == null && permissionService.checkWorkgroupLevelCannotPrivilege(
                PermissionID.CREATE_PROJECT_STATUS_OPTIONS, workgroupId, userId)) {
            throw new NoPermissionException("Yu don't have permission to create project status!");
        }
        //Check edit project status permission
        if (projectStatusUpdateDTO.getId() != null && permissionService.checkWorkgroupLevelCannotPrivilege(
                PermissionID.EDIT_PROJECT_STATUS_OPTIONS, workgroupId, userId)) {
            throw new NoPermissionException("Yu don't have permission to edit project status!");
        }
        //Save project status
        projectStatusService.createOrUpdateProjectStatus(workgroupId, userId, projectStatusUpdateDTO);
        return new ResponseEntity<ServerResponse>(ServerResponse.success(), HttpStatus.OK);
    }

    @Operation(summary = "Set default project status", security = @SecurityRequirement(name = "JWT"))
    @PostMapping(value = "/setDefaultStatus", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ResponseEntity setDefaultStatus(@Parameter(description = "New default project status info", required = true)
                                           @RequestBody ProjectStatusUpdateDTO projectStatusUpdateDTO) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        //Set default project status
        projectStatusService.setDefaultProjectStatus(workgroupId, userId, projectStatusUpdateDTO);
        return new ResponseEntity<ServerResponse>(ServerResponse.success(), HttpStatus.OK);
    }

    @Operation(summary = "Remove project status", security = @SecurityRequirement(name = "JWT"))
    @PostMapping(value = "/deleteStatus", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ResponseEntity deleteStatus(@Parameter(description = "Removed project status info", required = true)
                                       @RequestBody ProjectStatusDeleteDTO projectStatusDeleteDTO) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        Long userId = JwtUtil.getUserId();
        //Check edit project status permission
        if (permissionService.checkWorkgroupLevelCannotPrivilege(
                PermissionID.EDIT_PROJECT_STATUS_OPTIONS, workgroupId, userId)) {
            throw new NoPermissionException("Yu don't have permission to edit project status!");
        }
        //Delete project status
        projectStatusService.deleteProjectStatus(workgroupId, userId, projectStatusDeleteDTO.getDeleteStatusId(),
                projectStatusDeleteDTO.getAssignToStatusId(), projectStatusDeleteDTO.getDefaultStatusId());
        return new ResponseEntity<ServerResponse>(ServerResponse.success(), HttpStatus.OK);
    }
}
