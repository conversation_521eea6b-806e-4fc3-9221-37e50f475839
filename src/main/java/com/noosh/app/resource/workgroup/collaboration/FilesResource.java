package com.noosh.app.resource.workgroup.collaboration;

import com.noosh.app.commons.dto.document.TgTagDTO;
import com.noosh.app.commons.dto.workgroup.collaboration.FilesDTO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.workgroup.collaboration.FilesVO;
import com.noosh.app.mapper.workgroup.collaboration.FilesMapper;
import com.noosh.app.service.util.JwtUtil;
import com.noosh.app.service.workgroup.collaboration.FilesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * User: leilaz
 * Date: 4/12/22
 */
@RestController
@RequestMapping("/api/collaboration/files")
public class FilesResource {
    @Autowired
    public FilesService filesService;
    @Autowired
    public FilesMapper filesMapper;

    @Operation(summary = "Get workgroup options files settings", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/getSettings")
    public ServerResponse<FilesVO> getFilesConfig() {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(filesMapper.toVO(filesService.getFilesConfig(workgroupId, userId)));
    }

    @Operation(summary = "Update workgroup options files settings", security = @SecurityRequirement(name = "JWT"))
    @PostMapping("/saveSettings")
    public ServerResponse saveFilesConfig(@Parameter(description = "Workgroup options files update dto", required = true) @RequestBody FilesDTO filesDTO) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        filesService.saveFilesConfig(filesDTO, workgroupId, userId);
        return ServerResponse.success();
    }

    @Operation(summary = "Update workgroup options file tag inline edit", security = @SecurityRequirement(name = "JWT"))
    @PostMapping("/inlineEditTag")
    public ServerResponse inlineEditTag(@Parameter(description = "Files tags update dto", required = true)
                                            @RequestBody TgTagDTO tagDTO) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        filesService.fileTagsInlineEdit(tagDTO, workgroupId, userId);
        return ServerResponse.success();
    }
}
