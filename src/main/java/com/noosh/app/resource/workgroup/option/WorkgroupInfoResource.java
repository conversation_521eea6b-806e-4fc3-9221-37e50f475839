package com.noosh.app.resource.workgroup.option;

import com.noosh.app.commons.dto.security.WorkgroupDTO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.workgroup.option.WorkgroupInfoVO;
import com.noosh.app.mapper.workgroup.option.WorkgroupInfoMapper;
import com.noosh.app.service.util.JwtUtil;
import com.noosh.app.service.util.NooshOneUrlUtil;
import com.noosh.app.service.workgroup.WorkgroupService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Workgroup information.
 *
 * <AUTHOR>
 * @since 6/27/2022
 */
@RestController
@RequestMapping("/api/workgroupInfo")
public class WorkgroupInfoResource {

    @Autowired
    private WorkgroupService workgroupService;
    @Autowired
    private WorkgroupInfoMapper workgroupInfoMapper;

    @Operation(summary = "get Workgroup Info", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getWorkgroupInfo", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<WorkgroupInfoVO> getWorkgroupInfo() {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();

        WorkgroupDTO workgroupDTO = workgroupService.findWorkgroup(workgroupId);
        final WorkgroupInfoVO workgroupInfoVO = workgroupInfoMapper.toVO(workgroupDTO, userId);
        return ServerResponse.success(workgroupInfoVO);
    }

    @Operation(summary = "get All countries", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getAllCountries", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse getAllCountryList() {
        return ServerResponse.success(workgroupService.getCountryListDropdown());
    }

    @Operation(summary = "get All currencies", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getAllCurrencies", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse getAllCurrencyList() {
        return ServerResponse.success(workgroupService.getCurrencyListDropdown());
    }

}
