package com.noosh.app.resource.workgroup.option;

import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.workgroup.option.*;
import com.noosh.app.commons.vo.workgroup.option.dualCurrency.DualCurrencyFilter;
import com.noosh.app.commons.vo.workgroup.option.dualCurrency.DualCurrencySearchFilterVO;
import com.noosh.app.commons.vo.workgroup.option.dualCurrency.DualCurrencyVO;
import com.noosh.app.commons.vo.workgroup.option.dualCurrency.DualCurrencyListVOWithTarget;
import com.noosh.app.service.accounts.DualCurrencyService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Shan
 * @since 7/17/2022
 */
@RestController
@RequestMapping("/api/dualCurrency")
public class DualCurrencyResource {

    @Autowired
    private DualCurrencyService dualCurrencyService;

    private final Map<String, String> sortFieldMap = new HashMap<>();
    @PostConstruct
    public void init() {
        sortFieldMap.put("vendor.currency", "FC_CURRENCY");
        sortFieldMap.put("vendor.activeDate", "ACTIVATE_DATE");
        sortFieldMap.put("client.currency", "TC_CURRENCY");
        sortFieldMap.put("client.activeDate", "ACTIVATE_DATE");
    }

    @Operation(summary = "Retrieve dual currency data", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getDualCurrencyList", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<DualCurrencyListVO> getDualCurrencyList() {
        Long groupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(dualCurrencyService.getDualCurrencyList(groupId));
    }

    @Operation(summary = "Retrieve dual currency data for estimate/order use", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getSupplierDualCurrency", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public Double getSupplierDualCurrency(@Parameter(description = "WorkgroupId", required = true) @RequestParam("groupId") Long groupId,
                                                          @Parameter(description = "BU supplierWorkgroupId", required = true) @RequestParam(value = "buWorkgroupId", defaultValue = "-1") Long buWorkgroupId,
                                                          @Parameter(description = "CurrencyId", required = true) @RequestParam("currencyId") Long currencyId) {
        return dualCurrencyService.getSupplierDualCurrency(groupId, buWorkgroupId, currencyId);
    }


    @Operation(summary = "Retrieve dual currency data for quote use", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getClientDualCurrency", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public Double getClientDualCurrency(@Parameter(description = "workgroupId", required = true) @RequestParam("groupId") Long groupId,
                                                        @Parameter(description = "BU clientWorkgroupId", required = true) @RequestParam("buWorkgroupId") Long buWorkgroupId,
                                                        @Parameter(description = "CurrencyId", required = true) @RequestParam("currencyId") Long currencyId) {
        return dualCurrencyService.getClientDualCurrency(groupId, buWorkgroupId, currencyId);
    }

    @Operation(summary = "Retrieve dual currency data for estimate/order use", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getSupplierDualCurrencyByTargetGroupId", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public Double getSupplierDualCurrencyByTargetGroupId(@Parameter(description = "WorkgroupId", required = true) @RequestParam("groupId") Long groupId,
                                                                         @Parameter(description = "targetGroupId", required = true) @RequestParam("targetGroupId") Long targetGroupId) {
        return dualCurrencyService.getSupplierDualCurrencyByTargetGroupId(groupId, targetGroupId);
    }

    @Operation(summary = "Retrieve dual currency data for quote use", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getClientDualCurrencyByTargetGroupId", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public Double getClientDualCurrencyByTargetGroupId(@Parameter(description = "WorkgroupId", required = true) @RequestParam("groupId") Long groupId,
                                                                       @Parameter(description = "BU clientWorkgroupId", required = true) @RequestParam("buWorkgroupId") Long buWorkgroupId,
                                                                       @Parameter(description = "targetGroupId", required = true) @RequestParam("targetGroupId") Long targetGroupId) {
        return dualCurrencyService.getClientDualCurrencyByTargetGroupId(groupId, buWorkgroupId, targetGroupId);
    }

    @Operation(summary = "Retrieve dual currency data with Pagination", security = @SecurityRequirement(name = "JWT"))
    @PostMapping(value = "/listDualCurrency", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<DualCurrencyListVOWithTarget> listDualCurrency(
            @RequestBody DualCurrencyFilter filter,
            @Parameter(description = "Target Workgroup Type", required = true)
            @RequestParam("target") String target,
            @Parameter(description = "Page Number", required = true)
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @Parameter(description = "Page Size", required = true, example = "10")
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @Parameter(description = "Page Sort", required = true)
            @RequestParam(value = "sort") String sort,
            @Parameter(description = "Page Order", required = true)
            @RequestParam(value = "order") String order) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        dualCurrencyService.updateDualCurrencyFilter(workgroupId, userId, pageSize, target, sort, order);
        PageVO page = new PageVO(pageNum, pageSize, sortFieldMap.get(sort), order);
        String activeFrom = (filter == null || filter.getActivateDateFrom() == null || filter.getActivateDateFrom().toString().isEmpty()) ? "" : filter.getActivateDateFrom().format(DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss"));
        String activeTo = (filter == null || filter.getActivateDateTo() == null || filter.getActivateDateTo().toString().isEmpty()) ? "" : filter.getActivateDateTo().format(DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss"));
        DualCurrencyListVOWithTarget vo = dualCurrencyService.getDualCurrencyList(workgroupId, target, page, activeFrom, activeTo);
        return ServerResponse.success(vo, page);
    }

    @Operation(summary = "Get list dual currency filter", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getDualCurrencyFilter", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<DualCurrencySearchFilterVO> getDualCurrencyFilter() {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(dualCurrencyService.getDualCurrencyFilter(workgroupId, userId));
    }

    @Operation(summary = "Get dual currency detail by Id", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/dualCurrencyDetail", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<DualCurrencyVO> dualCurrencyDetail(@Parameter(description = "multiExchangeRateId", required = true)
                                                             @RequestParam("multiExchangeRateId") Long multiExchangeRateId) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(dualCurrencyService.getDualCurrencyDetail(multiExchangeRateId, workgroupId));
    }

    @Operation(summary = "Get Unconfigured Currency List", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getUnconfiguredCurrencyListForVendor", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<List<String>> getUnconfiguredCurrencyListForVendor() {
        Long workgroupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(dualCurrencyService.getUnconfiguredCurrencyListForSupplier(workgroupId));
    }

    @Operation(summary = "Get Unconfigured Currency List", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getUnconfiguredCurrencyListForClient", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<List<String>> getUnconfiguredCurrencyListForClient() {
        Long workgroupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(dualCurrencyService.getUnconfiguredCurrencyListForClient(workgroupId));
    }

}
