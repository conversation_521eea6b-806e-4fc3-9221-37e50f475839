package com.noosh.app.resource.workgroup.option;

import java.util.List;


import com.noosh.app.commons.dto.accounts.WorkgroupAttributeTypeDTO;
import com.noosh.app.commons.dto.security.WorkgroupDTO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.workgroup.option.customdata.lists.AddOptionsVO;
import com.noosh.app.commons.vo.workgroup.option.customdata.lists.DeleteOptionsVO;
import com.noosh.app.commons.vo.workgroup.option.customdata.lists.ListVO;
import com.noosh.app.commons.vo.workgroup.option.customdata.lists.ListsVO;
import com.noosh.app.commons.vo.workgroup.option.customdata.lists.OptionVO;
import com.noosh.app.commons.vo.workgroup.option.customdata.lists.UpdateListVO;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.service.util.JwtUtil;
import com.noosh.app.service.workgroup.WorkgroupService;
import com.noosh.app.service.workgroup.option.ListService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Custom Data - List
 * 
 * <AUTHOR> Shan
 * @since 8/10/2022
 */
@RestController
@RequestMapping("/api/list")
public class ListResource {

    @Autowired
    private WorkgroupService workgroupService;
    @Autowired
    private ListService listService;

    @Operation(summary = "Get lists", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @GetMapping("/getLists")
    public ServerResponse<ListsVO> getLists() {
        return ServerResponse.success(listService.getLists(JwtUtil.getWorkgroupId()));
    }

    @Operation(summary = "Get a list by list id", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @GetMapping("/getList")
    public ServerResponse<ListVO> getList(
            @Parameter(description = "list id", required = true)
            @RequestParam("listId") Long listId
    ) throws Exception {
        return ServerResponse.success(listService.getList(JwtUtil.getWorkgroupId(), listId));
    }

    @Operation(summary = "Get options by list id", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @GetMapping("/getOptions")
    public ServerResponse<List<OptionVO>> getOptions(
            @Parameter(description = "list id", required = true)
            @RequestParam("listId") Long listId
    ) {
        return ServerResponse.success(listService.getOptions(JwtUtil.getWorkgroupId(), listId));
    }

    @Operation(summary = "Get options by Guid", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @GetMapping("/getOptionsByGuid")
    public ServerResponse<List<OptionVO>> getOptions(
            @Parameter(description = "guid", required = true)
            @RequestParam("guid") String guid,
            @Parameter(description = "list name", required = true)
            @RequestParam("listName") String listName,
            @Parameter(description = "active only", required = true)
            @RequestParam("activeOnly") Boolean activeOnly
    ) {
        WorkgroupDTO workgroupDTO = workgroupService.findWorkgroupByGuid(guid);
        if (workgroupDTO == null) {
            throw new NotFoundException("guid is not found");
        }

        WorkgroupAttributeTypeDTO workgroupAttributeTypeDTO = listService.getListByName(workgroupDTO.getId(), listName);
        if (workgroupAttributeTypeDTO == null) {
            throw new NotFoundException("List name is not found");
        }

        return ServerResponse.success(listService.getOptions(workgroupDTO.getId(), workgroupAttributeTypeDTO.getId(), activeOnly));
    }

    @Operation(summary = "Get list with options by workgroup id and list name", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @GetMapping("/getListByName")
    public ServerResponse<ListVO> getListByName (
            @Parameter(description = "workgroup id", required = true)
            @RequestParam("workgroupId") Long workgroupId,
            @Parameter(description = "list name", required = true)
            @RequestParam("listName") String listName,
            @Parameter(description = "active only", required = true)
            @RequestParam("activeOnly") Boolean activeOnly
    ) throws Exception {
        return ServerResponse.success(listService.getListWithOptionsByName(workgroupId, listName, activeOnly));
    }

    @Operation(summary = "Add an option or options to a list", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @PostMapping("/addOptions")
    public void addOptions(@Validated @RequestBody AddOptionsVO addOptionsVO) {
        listService.addOptions(JwtUtil.getWorkgroupId(), JwtUtil.getUserId(), addOptionsVO);
    }

    @Operation(summary = "Update list", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @PutMapping("/updateList")
    public void updateList(@Validated @RequestBody UpdateListVO updateListVO) throws Exception {
        listService.updateList(JwtUtil.getUserId(), updateListVO);
    }

    @Operation(summary = "Delete an option from a list", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @DeleteMapping("/deleteOption")
    public void deleteOption(
            @Parameter(description = "list id", required = true)
            @RequestParam("listId") Long listId,
            @Parameter(description = "option id", required = true)
            @RequestParam("optionId") Long optionId
    ) throws Exception {
        listService.deleteOption(JwtUtil.getWorkgroupId(), JwtUtil.getUserId(), listId, optionId);
    }

    @Operation(summary = "Delete selected options from a list", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @DeleteMapping("/deleteOptions")
    public void deleteOptions(@Validated @RequestBody DeleteOptionsVO deleteOptionsVO) throws Exception {
        listService.deleteOptions(JwtUtil.getWorkgroupId(), JwtUtil.getUserId(), deleteOptionsVO.getListId(), deleteOptionsVO.getOptionIds());
    }

    @Operation(summary = "Delete all options", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @DeleteMapping("/deleteAllOptions")
    public void deleteAllOptions(
            @Parameter(description = "list id", required = true)
            @RequestParam("listId") Long listId
    ) throws Exception {
        listService.deleteAllOptions(JwtUtil.getWorkgroupId(), JwtUtil.getUserId(), listId);
    }

    @Operation(summary = "Move an option up or down", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @PutMapping("/moveOption")
    public void moveOption(
            @Parameter(description = "list id", required = true)
            @RequestParam("listId") Long listId,
            @Parameter(description = "option id", required = true)
            @RequestParam("optionId") Long optionId,
            @Parameter(description = "promoting", required = true)
            @RequestParam("promoting") Boolean promoting
    ) {
        listService.reorderOptions(JwtUtil.getWorkgroupId(), JwtUtil.getUserId(), listId, optionId, promoting);
    }

    @Operation(summary = "Sort a list", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @PutMapping("/sortList")
    public void sortList(
            @Parameter(description = "list id", required = true)
            @RequestParam("listId") Long listId
    ) {
        listService.arrangeOptionsByAlphabet(JwtUtil.getWorkgroupId(), JwtUtil.getUserId(), listId);
    }
}
