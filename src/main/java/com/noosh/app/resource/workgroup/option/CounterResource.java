package com.noosh.app.resource.workgroup.option;

import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.workgroup.option.customdata.counters.CounterVO;
import com.noosh.app.commons.vo.workgroup.option.customdata.counters.CountersVO;
import com.noosh.app.service.util.JwtUtil;
import com.noosh.app.service.workgroup.option.CounterService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Custom Data - Counter
 *
 * <AUTHOR> Shan
 * @since 8/16/2022
 */
@RestController
@RequestMapping("/api/counter")
public class CounterResource {

    @Autowired
    private CounterService counterService;

    @Operation(summary = "Get counters", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @GetMapping("/getCounters")
    public ServerResponse<CountersVO> getCounters() {
        return ServerResponse.success(counterService.getCounters(JwtUtil.getWorkgroupId()));
    }

    @Operation(summary = "Get counter", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @GetMapping("/getCounter")
    public ServerResponse<CounterVO> getCounter(
            @Parameter(description = "Counter type id", required = true)
            @RequestParam("counterTypeId") Long counterTypeId
    ) {
        return ServerResponse.success(counterService.getCounter(JwtUtil.getWorkgroupId(), counterTypeId));
    }

    @Operation(summary = "Get counter by workgroup id and name", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @GetMapping("/getCounterByName")
    public ServerResponse<CounterVO> getCounter(
            @Parameter(description = "workgroup id", required = true)
            @RequestParam("workGroupId") Long workGroupId,
            @Parameter(description = "counter name", required = true)
            @RequestParam("counterName") String counterName
    ) {
        return ServerResponse.success(counterService.getCounter(workGroupId, counterName));
    }

    @Operation(summary = "Update counter", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @PutMapping("/updateCounter")
    public void updateCounter(
            @Validated @RequestBody CounterVO counterVO
    ) {
        counterService.updateCounter(JwtUtil.getUserId(), counterVO);
    }

    @Operation(summary = "Get Next Value", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @GetMapping("/getNextValue")
    public ServerResponse<Map<String, String>> getNextValue(
            @Parameter(description = "workgroup id", required = true)
            @RequestParam("workGroupId") Long workGroupId,
            @Parameter(description = "counter name", required = true)
            @RequestParam("counterName") String counterName,
            @Parameter(description = "increment By", required = false)
            @RequestParam(value = "incrementBy", required = false, defaultValue = "1") Integer incrementBy
    ) {
        Map<String, String> result = new HashMap<>();
        result.put("nextValue", counterService.getNextValue(workGroupId, counterName, incrementBy));
        return ServerResponse.success(result);
    }
}
