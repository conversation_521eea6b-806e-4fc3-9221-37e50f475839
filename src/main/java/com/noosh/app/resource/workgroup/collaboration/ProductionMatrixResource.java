package com.noosh.app.resource.workgroup.collaboration;

import com.noosh.app.commons.dto.workgroup.collaboration.MilestonesUpdateDTO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.workgroup.collaboration.MilestonesVO;
import com.noosh.app.mapper.workgroup.collaboration.MilestonesMapper;
import com.noosh.app.service.util.JwtUtil;
import com.noosh.app.service.workgroup.collaboration.ProductionMatrixService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * User: leilaz
 * Date: 5/5/22
 */
@RestController
@RequestMapping("/api/collaboration/projectMilestones")
public class ProductionMatrixResource {
    @Autowired
    private ProductionMatrixService productionMatrixService;
    @Autowired
    private MilestonesMapper milestonesMapper;

    @Operation(summary = "Get workgroup options project milestones settings", description = "authored by <PERSON><PERSON>",
            security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/getSettings")
    public ServerResponse<MilestonesVO> getProjectMilestonesConfig() {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(milestonesMapper.toVO(productionMatrixService.getMilestones(workgroupId, userId)));
    }

    @Operation(summary = "Update workgroup options project milestones settings", description = "authored by Leila",
            security = @SecurityRequirement(name = "JWT"))
    @PostMapping("/saveSettings")
    public ServerResponse saveFilesConfig(@Parameter(
            description = "Workgroup options project milestones update dto", required = true) @RequestBody MilestonesUpdateDTO milestonesDTO) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        productionMatrixService.setMilestones(milestonesDTO, workgroupId, userId);
        return ServerResponse.success();
    }
}
