package com.noosh.app.resource.workgroup.collaboration;

import com.noosh.app.commons.dto.collaboration.CategoryUpdateDTO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.workgroup.collaboration.CategoryInUsedVO;
import com.noosh.app.commons.vo.workgroup.collaboration.CategoryVO;
import com.noosh.app.commons.vo.workgroup.collaboration.MilestonesVO;
import com.noosh.app.mapper.collaboration.CategoryMapper;
import com.noosh.app.service.util.JwtUtil;
import com.noosh.app.service.workgroup.collaboration.ProjectCategoryService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * User: leilaz
 * Date: 8/22/22
 */
@RestController
@RequestMapping("/api/collaboration/projectCategory")
public class ProjectCategoryResource {
    @Autowired
    private ProjectCategoryService projectCategoryService;
    @Autowired
    private CategoryMapper categoryMapper;

    @Operation(summary = "Get workgroup options project category list", description = "authored by Leila",
            security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/getCategoryList")
    public ServerResponse<List<CategoryVO>> getCategoryList() {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        return ServerResponse.success(categoryMapper.toVOs(projectCategoryService.getProjectCategoryList(workgroupId, userId)));
    }

    @Operation(summary = "Reorder workgroup options project category List", description = "authored by Leila",
            security = @SecurityRequirement(name = "JWT"))
    @PostMapping("/reorderCategory")
    public ServerResponse reorderCategory(
            @Parameter(description = "Workgroup options category update dto", required = true)
            @RequestBody List<CategoryUpdateDTO> categoryUpdateDTOs) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        projectCategoryService.reorderCategory(categoryUpdateDTOs, workgroupId, userId);
        return ServerResponse.success();
    }

    @Operation(summary = "Save workgroup options project category List", description = "authored by Leila",
            security = @SecurityRequirement(name = "JWT"))
    @PostMapping("/saveCategory")
    public ServerResponse saveCategory(
            @Parameter(description = "Workgroup options category update dto", required = true)
            @RequestBody CategoryUpdateDTO categoryUpdateDTO) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        projectCategoryService.saveCategory(categoryUpdateDTO, workgroupId, userId);
        return ServerResponse.success();
    }

    @Operation(summary = "Delete workgroup options project category List", description = "authored by Leila",
            security = @SecurityRequirement(name = "JWT"))
    @DeleteMapping("/deleteCategory")
    public ServerResponse deleteCategory(
            @Parameter(description = "Deleted Category id", required = true)
            @RequestParam(value = "categoryId", required = true) Long categoryId,
            @Parameter(description = "Replaced Category id", required = false)
            @RequestParam(value = "replacedCategoryId", required = false) Long replacedCategoryId) {
        Long userId = JwtUtil.getUserId();
        Long workgroupId = JwtUtil.getWorkgroupId();
        projectCategoryService.deleteCategory(categoryId, workgroupId, userId, replacedCategoryId);
        return ServerResponse.success();
    }

    @Operation(summary = "Check project category is used in projects", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/checkCategoryIsInUsed", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ResponseEntity checkCategoryIsInUsed(@Parameter(description = "Project category id", required = true, example = "123456789")
                                              @RequestParam(value = "projectCategoryId") Long projectCategoryId) {
        //Check project category is used in current projects
        boolean isInUsed = projectCategoryService.isProjectCategoryInUsed(projectCategoryId);
        CategoryInUsedVO categoryInUsedVO = new CategoryInUsedVO();
        categoryInUsedVO.setIsInUsed(isInUsed);
        categoryInUsedVO.setProjectCategoryId(projectCategoryId);
        return new ResponseEntity<ServerResponse>(ServerResponse.success(categoryInUsedVO), HttpStatus.OK);
    }
}
