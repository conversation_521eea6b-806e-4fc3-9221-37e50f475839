package com.noosh.app.resource.workgroup.option;

import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.workgroup.option.terms.EditTermsVO;
import com.noosh.app.commons.vo.workgroup.option.terms.ViewTermsVO;
import com.noosh.app.service.accounts.TermsService;
import com.noosh.app.service.util.JwtUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 8/8/2022
 */
@RestController
@RequestMapping("/api/terms")
public class TermsResource {

    @Autowired
    private TermsService termsService;

    @GetMapping("/getTerms")
    public ServerResponse<ViewTermsVO> getTerms() {
        return ServerResponse.success(termsService.getTerms(JwtUtil.getWorkgroupId()));
    }

    @PostMapping("/editTerms")
    public void editTerms(@Validated @RequestBody EditTermsVO editTermsVO) {
        termsService.editTerms(JwtUtil.getUserId(), JwtUtil.getWorkgroupId(), editTermsVO);
    }
}
