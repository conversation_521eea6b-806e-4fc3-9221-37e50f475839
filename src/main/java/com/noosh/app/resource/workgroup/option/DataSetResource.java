package com.noosh.app.resource.workgroup.option;

import com.noosh.app.commons.dto.security.WorkgroupDTO;
import com.noosh.app.commons.dto.workgroup.option.CustomDataSetDTO;
import com.noosh.app.commons.dto.workgroup.option.CustomDataSetTypeDTO;
import com.noosh.app.commons.vo.ResponseCode;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.workgroup.option.CustomDataSetVO;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.mapper.workgroup.option.CustomDataSetMapper;
import com.noosh.app.service.util.JwtUtil;
import com.noosh.app.service.workgroup.WorkgroupService;
import com.noosh.app.service.workgroup.option.DataSetService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * User: leilaz
 * Date: 3/29/22
 */
@RestController
@RequestMapping("/dataSet")
public class DataSetResource {
    @Autowired
    private DataSetService dataSetService;
    @Autowired
    private CustomDataSetMapper customDataSetMapper;
    @Autowired
    private WorkgroupService workgroupService;

    @Operation(summary = "Retrieve custom data set", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ResponseEntity getDataSetById(@Parameter(description = "Data Set Id", required = true, example = "5364054")
                                         @RequestParam(value = "dataSetId") Long dataSetId) {
        CustomDataSetDTO customDataSetDTO = dataSetService.findDataSetById(dataSetId);
        if (customDataSetDTO != null) {
            return new ResponseEntity<ServerResponse>(ServerResponse.success(
                    customDataSetMapper.toVO(customDataSetDTO)), HttpStatus.OK);
        }
        return new ResponseEntity(new ServerResponse<CustomDataSetVO>(ResponseCode.ERROR.getCode(),
                "Custom data set not found!"), HttpStatus.NOT_FOUND);
    }

    @Operation(summary = "Retrieve all custom data set by guid", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getDataSetByGuid", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ResponseEntity getDataSetByGuid(
            @Parameter(description = "guid", required = true)
            @RequestParam("guid") String guid,
            @Parameter(description = "list name", required = true)
            @RequestParam("datasetName") String datasetName,
            @Parameter(description = "filters")
            @RequestParam(value = "filters", required = false) String filters) {

        WorkgroupDTO workgroupDTO = workgroupService.findWorkgroupByGuid(guid);
        if (workgroupDTO == null) {
            throw new NotFoundException("guid is not found");
        }

        List<CustomDataSetDTO> customDataSetDTOs = dataSetService.findDataSets(workgroupDTO.getId(), datasetName, filters);
        if (customDataSetDTOs != null) {
            return new ResponseEntity<ServerResponse>(ServerResponse.success(
                    customDataSetMapper.toVOs(customDataSetDTOs)), HttpStatus.OK);
        }
        return new ResponseEntity(new ServerResponse<>(ResponseCode.ERROR.getCode(),
                "Custom data set not found!"), HttpStatus.NOT_FOUND);
    }

    @Operation(summary = "Retrieve custom data set type drop down list", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getTypeList", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ResponseEntity getDataSetTypeByWgId() {
        List<CustomDataSetTypeDTO> customDataSetTypeDTOs = dataSetService.findDataSetTypeByWgId(JwtUtil.getWorkgroupId());
        return new ResponseEntity<ServerResponse>(ServerResponse.success(customDataSetMapper.toTypeVOs(customDataSetTypeDTOs)),
                HttpStatus.OK);
    }

    @Operation(summary = "Update custom data set", security = @SecurityRequirement(name = "JWT"))
    @PostMapping(value = "", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ResponseEntity updateDataSet(@Parameter(description = "Updated data set DTO", required = true)
            @RequestBody CustomDataSetTypeDTO dto) {
        dataSetService.updateCustomDataSet(dto, JwtUtil.getUserId());
        return new ResponseEntity(ServerResponse.success(), HttpStatus.OK);
    }


}
