package com.noosh.app.resource;

import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.messages.MessageVO;
import com.noosh.app.service.messages.BroadcastMessageService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * author: Yang
 * Date: 11/27/2022
 */
@RestController
@RequestMapping("/api/broadcastMessage")
public class BroadcastMessageResource {

    @Autowired
    BroadcastMessageService broadcastMessageService;

    @Operation(summary = "get broadcast message list", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/getBroadcastMessageList", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    public ServerResponse<List<MessageVO>> getBroadcastMessageList( @Parameter(description = "Page Number", required = true, example = "1")
                                                                    @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                    @Parameter(description = "Page Size", required = true, example = "10")
                                                                    @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                                    @Parameter(description = "Page Sort", example = "name")
                                                                    @RequestParam(value = "sort", required = false, defaultValue = "DATE_POSTED") String sort,
                                                                    @Parameter(description = "Page Order", example = "asc")
                                                                    @RequestParam(value = "order", required = false, defaultValue = "ASC") String order) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        PageVO page = new PageVO(pageNum, pageSize, sort, order);
        List<MessageVO> list = broadcastMessageService.getBroadcastMessageList(workgroupId, page);
        return ServerResponse.success(list, page);
    }

}
