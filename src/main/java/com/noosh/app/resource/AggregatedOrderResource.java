package com.noosh.app.resource;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.TermsTypeID;
import com.noosh.app.commons.dto.order.AggregatedOrderDetailDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.order.SpecOrderItemDTO;
import com.noosh.app.commons.vo.ResponseCode;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.order.*;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.mapper.AggregatedOrderMapper;
import com.noosh.app.mapper.OrderMapper;
import com.noosh.app.service.AggregatedOrderService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.terms.TermsService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.inject.Inject;

import java.util.List;

/**
 * User: Neal Shan
 * Date: 5/22/2018
 */
@RestController
@RequestMapping("/aggregate")
public class AggregatedOrderResource {

    @Inject
    private AggregatedOrderService aggregatedOrderService;
    @Inject
    private PermissionService permissionService;
    @Inject
    private AggregatedOrderMapper aggregatedOrderMapper;
    @Inject
    private OrderMapper orderMapper;
    @Inject
    private TermsService termsService;

    /**
     * GET  /detail -> get the order general info by order id and project id.
     */
    @Operation(summary = "Get general order by order id", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    @Transactional(readOnly = true)
    public ResponseEntity getDetail(@Parameter(description = "Order Id", required = true, example = "5364054")
                                    @RequestParam(value = "orderId") Long orderId,
                                    @Parameter(description = "Order Type", required = true, example = "buy")
                                    @RequestParam(value = "orderType", defaultValue = "buy") String orderType,
                                    @Parameter(description = "Project Id", required = true, example = "5364054")
                                    @RequestParam(value = "projectId") Long projectId,
                                    @Parameter(description = "Is Print View", example = "true")
                                    @RequestParam(value="printView", required = false, defaultValue = "false") Boolean printView,
                                    @AuthenticationPrincipal Jwt jwt) throws Exception {
        Long currentUserId = Long.parseLong(jwt.getClaimAsString("userId"));
        Long currentWorkgroupId = Long.parseLong(jwt.getClaimAsString("workgroupId"));

        if (!permissionService.checkAll(PermissionID.VIEW_ORDER, currentWorkgroupId, currentUserId, projectId)) {
            throw new NoPermissionException("Your don't have permission to view order");
        }
        AggregatedOrderDetailDTO dto = aggregatedOrderService.findAggregatedOrderDetail(orderId, projectId, currentWorkgroupId, currentUserId, orderType, printView);
        if (dto != null && dto.getOrderVersionDTO() != null) {
            OrderVersionDTO originalOrderDTO = dto.getOrderVersionDTO().getOriginalOrder();
            AggregatedOrderInfoVO vo = new AggregatedOrderInfoVO();
            AggregatedOrderDetailVO detailVO = aggregatedOrderMapper.toAggregatedDetailVO(dto);
            vo.setOrderGeneral(detailVO);
            vo.setBuyerTerms(termsService.findTermAndCondition(originalOrderDTO.getBuyerTermsId(), TermsTypeID.BUYER_PURCHASE, originalOrderDTO.getBuyerWorkgroupId()));
            vo.setSupplierTerms(termsService.findTermAndCondition(originalOrderDTO.getSupplierTermsId(), TermsTypeID.SUPPLIER_SELL, originalOrderDTO.getSupplierWorkgroupId()));
            AggregatedOrderButtonVO buttonVO = aggregatedOrderMapper.toAggregatedOrderButtonVO(dto);
            vo.setButtons(buttonVO);
            return new ResponseEntity<ServerResponse>(ServerResponse.success(vo), HttpStatus.OK);
        }
        return new ResponseEntity(new ServerResponse<OrderDetailVO>(ResponseCode.ERROR.getCode(), "Order not found!"), HttpStatus.NOT_FOUND);
    }

    /**
     * GET  /aggregatedSpecOrderItem -> get the aggregated spec order item
     */
    @Operation(summary = "Get the aggregated spec order item", security = @SecurityRequirement(name = "JWT"))
    @GetMapping(value = "/aggregatedSpecOrderItem", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed
    @Transactional(readOnly = true)
    public ResponseEntity aggregatedSpecOrderItem(@Parameter(description = "List spec Id", required = true, example = "5364054")
                                                  @RequestParam(value = "specIds") List<Long> specIds,
                                                  @Parameter(description = "Project Id", required = true, example = "5364054")
                                                  @RequestParam(value = "projectId") Long projectId,
                                                  @AuthenticationPrincipal Jwt jwt) throws Exception {
        Long currentUserId = Long.parseLong(jwt.getClaimAsString("userId"));
        Long currentWorkgroupId = Long.parseLong(jwt.getClaimAsString("workgroupId"));

        if (!permissionService.checkAll(PermissionID.VIEW_ORDER, currentWorkgroupId, currentUserId, projectId)) {
            throw new NoPermissionException("Your don't have permission to view order");
        }

        List<SpecOrderItemDTO> specOrderItemDTOs = aggregatedOrderService.findAggregatedOrderItemBySpec(specIds);
        return new ResponseEntity<ServerResponse>(ServerResponse.success(aggregatedOrderMapper.toSpecOrderItemVOs(specOrderItemDTOs)), HttpStatus.OK);
    }

    @Operation(summary = "Retrieves aggregated order cost center allocation", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/costCenters")
    public ResponseEntity costCenters(@Parameter(description = "Order Id", required = true, example = "5364054")
                                      @RequestParam(value = "orderId") Long orderId,
                                      @Parameter(description = "Project Id", required = true, example = "5364054")
                                      @RequestParam(value = "projectId") Long projectId) {
        Long currentUserId = JwtUtil.getUserId();
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();

        if (!permissionService.checkAll(PermissionID.VIEW_ORDER, currentWorkgroupId, currentUserId, projectId)) {
            throw new NoPermissionException("Your don't have permission to view order");
        }

        return new ResponseEntity<ServerResponse>(ServerResponse.success(
                aggregatedOrderMapper.toOrderCostCenterSummaryVO(aggregatedOrderService.getAggregatedCostCenter(orderId, projectId,
                        currentWorkgroupId, currentUserId))), HttpStatus.OK);
    }

    @Operation(summary = "Retrieves aggregated order amount", security = @SecurityRequirement(name = "JWT"))
    @GetMapping("/orderAmount")
    public ResponseEntity orderAmount(@Parameter(description = "Order Id", required = true, example = "5364054")
                                      @RequestParam(value="orderId") Long orderId,
                                      @Parameter(description = "Project Id", required = true, example = "5364054")
                                      @RequestParam(value="projectId") Long projectId) {
        Long currentUserId = JwtUtil.getUserId();
        Long currentWorkgroupId = JwtUtil.getWorkgroupId();

        if (!permissionService.checkAll(PermissionID.VIEW_ORDER, currentWorkgroupId, currentUserId, projectId)) {
            throw new NoPermissionException("Your don't have permission to view order");
        }

        return new ResponseEntity<ServerResponse>(ServerResponse.success(
                aggregatedOrderMapper.toAggregateOrderAmountVO(aggregatedOrderService.getAggregateOrderAmount(orderId, projectId,
                        currentWorkgroupId, currentUserId))), HttpStatus.OK);
    }

}
