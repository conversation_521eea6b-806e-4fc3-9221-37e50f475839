package com.noosh.app.resource;

import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.service.reason.ReasonService;
import com.noosh.app.service.util.JwtUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Project Reason Resource
 * 
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@RequestMapping("/api/reason")
public class ReasonResource {

    @Autowired
    private ReasonService reasonService;

    @Operation(summary = "Get project reasons by workgroup ID or system type", security = @SecurityRequirement(name = "JWT"))
    @Timed
    @GetMapping("/project-reasons")
    public ServerResponse<?> getProjectReasons(
            @Parameter(description = "type")
            @RequestParam(value = "type", required = false) String type
    ) {
        Long workgroupId = JwtUtil.getWorkgroupId();
        if ("system".equals(type)) {
            return ServerResponse.success(reasonService.getSystemProjectReasons(workgroupId));
        } else {
            return ServerResponse.success(reasonService.getProjectReasonsByWorkgroup(workgroupId));
        }
    }
}