package com.noosh.app.exception;

import java.io.PrintStream;
import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * User: leilaz
 * Date: 8/30/21
 */
public class UnexpectedException extends RuntimeException {
    private static final long serialVersionUID = 8820070545336957354L;

    private String message;
    private Throwable source;
    private String stackTrace;

    public UnexpectedException(String message, Throwable source) {
        if (source instanceof UnexpectedException) {
            UnexpectedException ues = (UnexpectedException) source;
            // prepend the new message so we don't lose it if this exception
            // has been tossed around between layers
            if (message != null && message.length() > 0) {
                if (ues.message == null) {
                    ues.message = message;
                } else {
                    ues.message = message + " (" + ues.message + ")";
                }
            }
            throw ues;
        }
        this.message = message;
        this.source = source;
    }

    public UnexpectedException(Throwable source) {
        this(null, source);
    }

    public UnexpectedException(String message) {
        this(message, null);
    }
    public UnexpectedException(long msgId) {
        this(Long.toString(msgId), null);
    }

    public Throwable getSource() {
        return this.source;
    }

    public String getMessage() {
        if (this.source == null || this.source.getMessage() == null) {
            return this.message;
        }
        if (this.message == null) {
            return this.source.getMessage();
        }
        return this.message + " (" + this.source.getMessage() + ")";
    }

    public void printStackTrace(PrintStream s) {
        printStackTrace(s, null, true);
    }

    public void printStackTrace(PrintStream s, PrintWriter w, boolean useSource) {
        if (stackTrace != null) {
            if (s != null) {
                s.print(stackTrace);
            } else {
                w.print(stackTrace);
            }
            return;
        }

        if (!useSource) {
            if (s != null) {
                super.printStackTrace(s);
            } else {
                super.printStackTrace(w);
            }
            return;
        }

        Throwable t = this.getSource();
        if (t instanceof UnexpectedException) {
            ((UnexpectedException) t).printStackTrace(s, w, false);
        } else if (s != null) {
            t.printStackTrace(s);
        } else {
            t.printStackTrace(w);
        }
    }

    public void printStackTrace(PrintWriter w) {
        printStackTrace(null, w, true);
    }

    public void printStackTrace() {
        printStackTrace(System.out);
    }

    public static String getStackTrace(Throwable throwable) {
        StringWriter writer = new StringWriter();
        throwable.printStackTrace(new PrintWriter(writer));
        return writer.toString();
    }
}
