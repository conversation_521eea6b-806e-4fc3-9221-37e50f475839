package com.noosh.app.exception;

import java.util.List;

import com.noosh.app.commons.vo.ErrorVO;
import feign.FeignException;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * User: leilaz
 * Date: 8/30/21
 */
@RestControllerAdvice
public class ExceptionTranslator {
    @ExceptionHandler(UnexpectedException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ErrorVO handleUnexpectedException(UnexpectedException ex) {
        return new ErrorVO(ex.getMessage());
    }

    @ExceptionHandler(NoPermissionException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    @ResponseBody
    public ErrorVO handleNoPermissionException(NoPermissionException ex) {
        return new ErrorVO("NO_PERMISSION_ERROR", ex.getMessage());
    }

    @ExceptionHandler(NotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    @ResponseBody
    public ErrorVO handleNotFoundException(NotFoundException ex) {
        return new ErrorVO(ex.getMessage());
    }

    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ErrorVO handleIllegalArgumentException(IllegalArgumentException ex) {
        return new ErrorVO(ex.getMessage());
    }

    @ExceptionHandler(ServletRequestBindingException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ErrorVO handleServletRequestBindingException(ServletRequestBindingException ex) {
        return new ErrorVO(ex.getMessage());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ErrorVO handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        StringBuilder sb = new StringBuilder();
        BindingResult result = ex.getBindingResult();
        List<FieldError> fieldErrors = result.getFieldErrors();
        for (FieldError fieldError : fieldErrors) {
            sb.append(fieldError.getObjectName());
            sb.append(": ");
            sb.append(fieldError.getField());
            sb.append(", ");
            sb.append(fieldError.getDefaultMessage());
            sb.append("; ");
        }
        return new ErrorVO(sb.toString());
    }
    
    @ExceptionHandler(FeignException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ErrorVO handleFeignException(FeignException ex) {
        return new ErrorVO(ex.getMessage());
    }

    @ExceptionHandler(FeignInvokeBusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public ErrorVO handleFeignInvokeBusinessException(FeignInvokeBusinessException ex) {
        return new ErrorVO(ex.getMessage());
    }
}
