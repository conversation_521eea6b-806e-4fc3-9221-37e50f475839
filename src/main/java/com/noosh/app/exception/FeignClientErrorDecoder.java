package com.noosh.app.exception;

import com.noosh.app.exception.FeignInvokeBusinessException;
import com.noosh.app.exception.ForbiddenException;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.exception.UnexpectedException;
import feign.Response;
import feign.Util;
import feign.codec.ErrorDecoder;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * @Author: Zhenyu Hu
 * @Date: 7/18/2022
 */

@Configuration
public class FeignClientErrorDecoder implements ErrorDecoder {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    public static final String ERROR_MESSAGE = "msg";

    @Override
    public Exception decode(String s, Response response) {
        int status = response.status();

        String responseBody = "";
        String errorMessage = "";
        try {
            responseBody = Util.toString(response.body().asReader(StandardCharsets.UTF_8));
        } catch (IOException e) {
            e.printStackTrace();
            return new UnexpectedException(e.getMessage());
        }
        JSONObject jsonResponse = new JSONObject(responseBody);
        if (jsonResponse.has(ERROR_MESSAGE)) {
            errorMessage = jsonResponse.getString(ERROR_MESSAGE);
        } else {
            errorMessage = responseBody;
        }
        errorMessage += String.format(" (Feign Request: [%d] [%s] %s)", status,
                response.request().httpMethod().name(), response.request().url());
        logger.error(errorMessage);

        if (status == HttpStatus.FORBIDDEN.value()) {
            return new ForbiddenException(errorMessage);
        } else if (status == HttpStatus.NOT_FOUND.value()) {
            return new NotFoundException(errorMessage);
        } else {
            return new FeignInvokeBusinessException(errorMessage);
        }
    }
}
