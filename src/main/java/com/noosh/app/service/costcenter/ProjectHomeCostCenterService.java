package com.noosh.app.service.costcenter;


import com.noosh.app.commons.dto.costcenter.OrderCostCenterDetailDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.order.ProjectOrderWidgetDTO;
import com.noosh.app.commons.vo.costcenter.OrderCostCenterDetailVO;
import com.noosh.app.mapper.OrderMapper;
import com.noosh.app.repository.mybatis.order.OrderMyBatisMapper;
import com.noosh.app.service.AggregatedOrderService;
import com.noosh.app.service.OrderService;
import com.noosh.app.service.permission.ordering.CostCenterAllocationPermission;
import jakarta.inject.Inject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: yangx
 * @Date: 6/18/20
 */

@Service
@Transactional
public class ProjectHomeCostCenterService {

    public static final String BUY_ORDER = "buy";
    public static final String SELL_ORDER = "sell";

    @Inject
    private OrderMyBatisMapper orderMyBatisMapper;
    @Inject
    private OrderService orderService;
    @Inject
    private AggregatedOrderService aggregatedOrderService;
    @Inject
    private OrderMapper orderMapper;
    @Inject
    private CostCenterAllocationPermission costCenterAllocationPermission;

    public List<OrderCostCenterDetailVO> getCostCenterDeskoidsByProjectIdAndType(
            Long projectId,
            String type,
            Long currentUserId,
            Long currentWorkgroupId) {
        List<OrderCostCenterDetailVO> costCenterDeskoidVOList = new ArrayList<>();
        boolean isBuy = false, isSell = false;
        if (BUY_ORDER.toUpperCase().equals(type.toUpperCase())) {
            isBuy = true;
        } else if (SELL_ORDER.toUpperCase().equals(type.toUpperCase())) {
            isSell = true;
        }
        List<ProjectOrderWidgetDTO> orderWidgetDTOS = orderMyBatisMapper.findOriginalOrderByProjectId(projectId, isBuy, isSell);
        for (ProjectOrderWidgetDTO projectOrderWidgetDTO : orderWidgetDTOS) {
            OrderVersionDTO originalOrder = orderService.findOrderVersionById(projectOrderWidgetDTO.getParentOrderId(), projectId, currentUserId, currentWorkgroupId);
            if (costCenterAllocationPermission.check(originalOrder, currentWorkgroupId, currentUserId, projectId)) {
                OrderCostCenterDetailDTO orderCostCenterDetailDTO = aggregatedOrderService.getAggregatedCostCenterForProjectDeskoid(originalOrder.getOrderId(), projectId, currentWorkgroupId, currentUserId);
                OrderCostCenterDetailVO orderCostCenterDetailVO = orderMapper.toOrderCostCenterDetailVO(orderCostCenterDetailDTO);
                costCenterDeskoidVOList.add(orderCostCenterDetailVO);
            }
        }
        return costCenterDeskoidVOList;
    }

}
