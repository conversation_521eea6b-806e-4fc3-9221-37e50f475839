package com.noosh.app.service.costcenter;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.costcenter.CostCenterDTO;
import com.noosh.app.commons.dto.costcenter.CostCenterUpdateDTO;
import com.noosh.app.commons.entity.costcenter.CostCenter;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.costcenter.CostCenterDetailsVO;
import com.noosh.app.commons.vo.costcenter.CostCenterListFilterVO;
import com.noosh.app.commons.vo.costcenter.CostCenterVO;
import com.noosh.app.exception.*;
import com.noosh.app.mapper.costcenter.CostCenterMapper;
import com.noosh.app.repository.jpa.costcenter.CostCenterRepository;
import com.noosh.app.repository.mybatis.costcenter.CostCenterMyBatisMapper;
import com.noosh.app.service.permission.PermissionService;
import com.noosh.app.service.preference.PreferenceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Stream;

import static org.apache.commons.lang3.BooleanUtils.NO;
import static org.apache.commons.lang3.BooleanUtils.YES;

/**
 * User: leilaz
 * Date: 12/5/21
 */
@Service
@Transactional
public class CostCenterService {
    @Autowired
    private CostCenterRepository costCenterRepository;
    @Autowired
    private CostCenterMapper costCenterMapper;
    @Autowired
    private PermissionService permissionService;

    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private CostCenterMyBatisMapper costCenterMyBatisMapper;

    /***
     * Get cost center list
     * @param ownerWgId, clientWgId, isActive, userId
     * @return List<CostCenterVO>
     */
    public List<CostCenterVO> getCostCenterVOList(Long ownerWgId, Long buClientWgId, Boolean isActive, Long userId, PageVO page){
        List<Long> usedCostCenterList = costCenterMyBatisMapper.findUsedCostCenterIds(ownerWgId);
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        pageInfo.setUnsafeOrderBy(page.getOrderBy());
        List<CostCenterDTO>  costCenterList = new ArrayList<>();
        if (buClientWgId != null){
            // Means GET All cost centers for project level
            if(buClientWgId > 0){
                //Means project with client (either online client or offline client)
                List<CostCenterDTO> costCenterDTOList1 = costCenterMyBatisMapper.getCostCenterDTOList(ownerWgId, buClientWgId, isActive);
                List<CostCenterDTO> costCenterDTOList2 = costCenterMyBatisMapper.getNoClientCostCenterDTOList(ownerWgId, isActive);
                Stream.of(costCenterDTOList1, costCenterDTOList2).forEach(costCenterList::addAll);
            }else if(buClientWgId == -1){
                //Means project without any client
                costCenterList = costCenterMyBatisMapper.getNoClientCostCenterDTOList(ownerWgId, isActive);
            }
        }else{
            //For GET All cost centers for workgroup level
            costCenterList = costCenterMyBatisMapper.getCostCenterDTOList(ownerWgId, null, isActive);
        }
        page.setTotal(pageInfo.getTotal());

        Boolean canEdit = permissionService.checkWorkgroupLevelCannotPrivilege(PermissionID.MANAGE_COST_CENTER_ADMIN, ownerWgId, userId)? Boolean.FALSE : Boolean.TRUE;
        List<CostCenterVO> costCenterListVO = new ArrayList<>();
        for (CostCenterDTO costCenterDTO: costCenterList) {
            CostCenterVO costCenterVO = costCenterMapper.toVO(costCenterDTO);
            costCenterVO.setCanEdit(canEdit);
            costCenterVO.setCanDelete(!usedCostCenterList.contains(costCenterDTO.getId()) && canEdit);
            costCenterListVO.add(costCenterVO);
        }
        return costCenterListVO;
    }

    @Transactional
    public void updateCostCenterListFilter(Long workgroupId, Long userId, Integer pageSize, String sort, String order) {
        Map<String, String> preferences = new HashMap<>();
        preferences.put(CostCenterListFilterVO.COSTCENTER_LIST_FILTER_PREFIX + CostCenterListFilterVO.PAGE_SIZE, pageSize.toString());
        preferences.put(CostCenterListFilterVO.COSTCENTER_LIST_FILTER_PREFIX + CostCenterListFilterVO.SORT, sort);
        preferences.put(CostCenterListFilterVO.COSTCENTER_LIST_FILTER_PREFIX + CostCenterListFilterVO.ORDER, order);
        preferenceService.saveUserPreference(workgroupId, userId, preferences);
    }
    public CostCenterListFilterVO getCostCenterListFilter(Long workgroupId, Long userId) {
        Map<String, String> preferences = preferenceService.findUserPrefs(workgroupId, userId);
        CostCenterListFilterVO costCenterListFilterVO = new CostCenterListFilterVO();
        if (preferences.containsKey(CostCenterListFilterVO.COSTCENTER_LIST_FILTER_PREFIX + CostCenterListFilterVO.PAGE_SIZE)) {
            costCenterListFilterVO.setPageSize(Integer.valueOf(preferences.get(CostCenterListFilterVO.COSTCENTER_LIST_FILTER_PREFIX + CostCenterListFilterVO.PAGE_SIZE)));
        } else {
            costCenterListFilterVO.setPageSize(CostCenterListFilterVO.PAGE_SIZE_DEFAULT_VALUE);
        }
        Map<String, String> sortReverseFieldMap = new HashMap<>();
        sortReverseFieldMap.put("CC.NAME", "number");
        sortReverseFieldMap.put("CC.DESCRIPTION", "description");
        sortReverseFieldMap.put("CC.IS_ACTIVE", "active");
        String sort = "number";
        if (preferences.containsKey(CostCenterListFilterVO.COSTCENTER_LIST_FILTER_PREFIX + CostCenterListFilterVO.SORT)) {
            sort = sortReverseFieldMap.get(preferences.get(CostCenterListFilterVO.COSTCENTER_LIST_FILTER_PREFIX + CostCenterListFilterVO.SORT));
            costCenterListFilterVO.setSortBy(sort);
        } else {
            costCenterListFilterVO.setSortBy(sort);
        }
        String order = "ASC";
        if (preferences.containsKey(CostCenterListFilterVO.COSTCENTER_LIST_FILTER_PREFIX + CostCenterListFilterVO.ORDER)) {
            costCenterListFilterVO.setOrder(preferences.get(CostCenterListFilterVO.COSTCENTER_LIST_FILTER_PREFIX + CostCenterListFilterVO.ORDER));
        } else {
            costCenterListFilterVO.setOrder(order);
        }
        return costCenterListFilterVO;
    }

    public List<CostCenterVO> getSimpleCostCenterVOList(Long ownerWgId, Long buClientWgId, Boolean isActive, Long userId){
        List<Long> usedCostCenterList = costCenterMyBatisMapper.findUsedCostCenterIds(ownerWgId);
        List<CostCenterDTO>  costCenterList = new ArrayList<>();
        if (buClientWgId != null){
            if(buClientWgId > 0){
                List<CostCenterDTO> costCenterDTOList1 = costCenterMyBatisMapper.getCostCenterDTOList(ownerWgId, buClientWgId, isActive);
                List<CostCenterDTO> costCenterDTOList2 = costCenterMyBatisMapper.getNoClientCostCenterDTOList(ownerWgId, isActive);
                Stream.of(costCenterDTOList1, costCenterDTOList2).forEach(costCenterList::addAll);
            }else if(buClientWgId == -1){
                costCenterList = costCenterMyBatisMapper.getNoClientCostCenterDTOList(ownerWgId, isActive);
            }
        }else{
            costCenterList = costCenterMyBatisMapper.getCostCenterDTOList(ownerWgId, null, isActive);
        }
        Boolean canEdit = permissionService.checkWorkgroupLevelCannotPrivilege(PermissionID.MANAGE_COST_CENTER_ADMIN, ownerWgId, userId)? Boolean.FALSE : Boolean.TRUE;
        List<CostCenterVO> costCenterListVO = new ArrayList<>();
        for (CostCenterDTO costCenterDTO: costCenterList) {
            CostCenterVO costCenterVO = costCenterMapper.toVO(costCenterDTO);
            costCenterVO.setCanEdit(canEdit);
            costCenterVO.setCanDelete(!usedCostCenterList.contains(costCenterDTO.getId()) && canEdit);
            costCenterListVO.add(costCenterVO);
        }
        return costCenterListVO;
    }

    /***
     * Get cost center preferences
     * @param workgroupId
     * @return Map<String, String>
     */
    public Map<String, String> getCostCenterPreferences(Long workgroupId) {
        Map<String, String> displayPrefs = new HashMap<>();
        List<String> prefs = new ArrayList<>();
        prefs.add(PreferenceID.ALLOW_COSTCENTER_SUP);
        prefs.add(PreferenceID.ALLOCATE_ORDER_ITEM_PRICE_ONLY);
        prefs.add(PreferenceID.APPLY_PROJECT_COST_CENTER_TO_ALL_EXISTING_ORDER);

        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId, prefs);
        if (preferenceService.check(PreferenceID.ALLOW_COSTCENTER_SUP, groupPrefs)) {
            displayPrefs.put(PreferenceID.ALLOW_COSTCENTER_SUP, YES);
        } else {
            displayPrefs.put(PreferenceID.ALLOW_COSTCENTER_SUP, NO);
        }
        if (preferenceService.check(PreferenceID.ALLOCATE_ORDER_ITEM_PRICE_ONLY, groupPrefs)) {
            displayPrefs.put(PreferenceID.ALLOCATE_ORDER_ITEM_PRICE_ONLY, YES);
        } else {
            displayPrefs.put(PreferenceID.ALLOCATE_ORDER_ITEM_PRICE_ONLY, NO);
        }
        if (preferenceService.check(PreferenceID.APPLY_PROJECT_COST_CENTER_TO_ALL_EXISTING_ORDER, groupPrefs)) {
            displayPrefs.put(PreferenceID.APPLY_PROJECT_COST_CENTER_TO_ALL_EXISTING_ORDER, YES);
        } else {
            displayPrefs.put(PreferenceID.APPLY_PROJECT_COST_CENTER_TO_ALL_EXISTING_ORDER, NO);
        }
        return  displayPrefs;
    }

    /**
     * Edit cost center Preferences
     * @param userId
     * @param workgroupId
     * @param preferences
     */
    @Transactional
    public void editCostCenterPreferences(long userId, long workgroupId, Map<String, String> preferences) {
        preferenceService.updatePreferencesForWorkgroup(workgroupId, userId, preferences);
    }

    /***
     * View cost center details
     * @param costCenterId
     * @return CostCenterDetailsVO
     */
    public CostCenterDetailsVO viewCostCenterDetails(Long costCenterId) {
        CostCenterDTO costCenterDTO = costCenterMyBatisMapper.getCostCenterDetails(costCenterId);
        return costCenterMapper.toDetailsVO(costCenterDTO);
    }

    /***
     * Create/Edit cost center details
     * @param costCenterUpdateDTO
     * @param workgroupId
     * @return CostCenterDTO
     */
    @Transactional
    public CostCenterDTO createOrEditCostCenter(CostCenterUpdateDTO costCenterUpdateDTO, Long workgroupId, Long userId) {
        Boolean canEdit = permissionService.checkWorkgroupLevelCannotPrivilege(PermissionID.MANAGE_COST_CENTER_ADMIN, workgroupId, userId)? Boolean.FALSE : Boolean.TRUE;
        if(!canEdit) {
            throw new NoPermissionException("You have no permission to create or edit cost center");
        }
        if (costCenterUpdateDTO == null) throw new NotFoundException("The cost center is null");
        List<CostCenterDTO> existsCCBeans = costCenterMyBatisMapper.findByNameAndDescription(workgroupId, costCenterUpdateDTO.getName(), costCenterUpdateDTO.getDescription());
        CostCenter costCenter = new CostCenter();
        if (costCenterUpdateDTO.getId() == null) {
            if(existsCCBeans != null && existsCCBeans.size() > 0){
                String name = null != costCenterUpdateDTO.getName() ? costCenterUpdateDTO.getName() : "";
                String des = null != costCenterUpdateDTO.getDescription() ? costCenterUpdateDTO.getDescription() : "";
                throw new UnexpectedException("Cost Center " + name + ": " + des + " already exists.");
            }
            costCenter.setOwnerWorkgroupId(workgroupId);
        } else {
            costCenter = costCenterRepository.findById(costCenterUpdateDTO.getId()).orElse(null);
            if (costCenter == null) throw new NotFoundException("Can't find cost center by id " + costCenterUpdateDTO.getId());
            boolean existed = false;
            if(existsCCBeans != null && existsCCBeans.size() > 0){
                for(CostCenterDTO existCC: existsCCBeans){
                    if(!existCC.getId().equals(costCenter.getId())){
                        existed = true;
                        break;
                    }
                }
                if(existed){
                    String name = null != costCenterUpdateDTO.getName() ? costCenterUpdateDTO.getName() : "";
                    String des = null != costCenterUpdateDTO.getDescription() ? costCenterUpdateDTO.getDescription() : "";
                    throw new UnexpectedException("Cost Center " + name + ": " + des + " already exists.");
                }
            }
        }
        setCostCenterInfo(costCenter, costCenterUpdateDTO);
        costCenterRepository.save(costCenter);
        return costCenterMapper.toDTO(costCenter);
    }

    private void setCostCenterInfo(CostCenter costCenter, CostCenterUpdateDTO costCenterUpdateDTO){
        costCenter.setName(costCenterUpdateDTO.getName());
        if(null == costCenterUpdateDTO.getDescription() || (costCenterUpdateDTO.getDescription() != null && "".equals(costCenterUpdateDTO.getDescription()))){
            costCenter.setDescription(null);
        }else{
            costCenter.setDescription(costCenterUpdateDTO.getDescription());
        }
        costCenter.setClientId(costCenterUpdateDTO.getClientId());
        costCenter.setIsActive(costCenterUpdateDTO.getActive());
    }

    /**
     * Delete cost center
     * @param costCenterId
     */
    @Transactional
    public void deleteCostCenter(Long costCenterId, Long ownerWgId, Long userId){
        List<Long> usedCostCenterList = costCenterMyBatisMapper.findUsedCostCenterIds(ownerWgId);
        Boolean canEdit = permissionService.checkWorkgroupLevelCannotPrivilege(PermissionID.MANAGE_COST_CENTER_ADMIN, ownerWgId, userId)? Boolean.FALSE : Boolean.TRUE;
        if(!usedCostCenterList.contains(costCenterId) && canEdit){
            Optional<CostCenter> costCenterOptional =  costCenterRepository.findById(costCenterId);
            if(costCenterOptional.isPresent()){
                CostCenter costCenter = costCenterOptional.get();
                costCenterRepository.delete(costCenter);
            }
        }else{
            throw new NoPermissionException("This cost center had been used, couldn't be deleted.");
        }
    }

    @Transactional
    public void bulkDeleteCostCenter(List<Long> costCenterIds, Long ownerWgId, Long userId) {
        List<Long> usedCostCenterList = costCenterMyBatisMapper.findUsedCostCenterIds(ownerWgId);
        Boolean canEdit = permissionService.checkWorkgroupLevelCannotPrivilege(PermissionID.MANAGE_COST_CENTER_ADMIN, ownerWgId, userId) ? Boolean.FALSE : Boolean.TRUE;
        boolean canAllDelete = true;
        for (Long costCenterId : costCenterIds) {
            if (usedCostCenterList.contains(costCenterId) || !canEdit) {
                canAllDelete = false;
                break;
            }
        }
        if (canAllDelete) {
            for (Long costCenterId : costCenterIds) {
                Optional<CostCenter> costCenterOptional = costCenterRepository.findById(costCenterId);
                if (costCenterOptional.isPresent()) {
                    CostCenter costCenter = costCenterOptional.get();
                    costCenterRepository.delete(costCenter);
                }
            }

        } else {
            throw new ForbiddenException("Some Cost Centers had been used, couldn't be deleted.");
        }
    }


    @Transactional
    public void bulkActiveCostCenter(List<Long> costCenterIds, Long ownerWgId, Long userId) {
        Boolean canEdit = permissionService.checkWorkgroupLevelCannotPrivilege(PermissionID.MANAGE_COST_CENTER_ADMIN, ownerWgId, userId) ? Boolean.FALSE : Boolean.TRUE;
        if (canEdit) {
            for (Long costCenterId : costCenterIds) {
                Optional<CostCenter> costCenterOptional = costCenterRepository.findById(costCenterId);
                if (costCenterOptional.isPresent()) {
                    CostCenter costCenter = costCenterOptional.get();
                    costCenter.setIsActive(Boolean.TRUE);
                    costCenterRepository.save(costCenter);
                }
            }
        } else {
            throw new NoPermissionException("You have no permission to edit the cost centers.");
        }
    }

    @Transactional
    public void bulkInactiveCostCenter(List<Long> costCenterIds, Long ownerWgId, Long userId) {
        Boolean canEdit = permissionService.checkWorkgroupLevelCannotPrivilege(PermissionID.MANAGE_COST_CENTER_ADMIN, ownerWgId, userId) ? Boolean.FALSE : Boolean.TRUE;
        if (canEdit) {
            for (Long costCenterId : costCenterIds) {
                Optional<CostCenter> costCenterOptional = costCenterRepository.findById(costCenterId);
                if (costCenterOptional.isPresent()) {
                    CostCenter costCenter = costCenterOptional.get();
                    costCenter.setIsActive(Boolean.FALSE);
                    costCenterRepository.save(costCenter);
                }
            }
        } else {
            throw new NoPermissionException("You have no permission to edit the cost centers.");
        }
    }

}
