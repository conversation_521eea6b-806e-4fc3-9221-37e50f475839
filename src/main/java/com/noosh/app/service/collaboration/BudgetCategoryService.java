package com.noosh.app.service.collaboration;

import java.util.ArrayList;
import java.util.List;

import com.noosh.app.commons.dto.userfield.CustomFieldDTO;
import com.noosh.app.commons.dto.collaboration.CategoryDTO;
import com.noosh.app.commons.vo.workgroup.option.budget.BudgetCategoryListVO;
import com.noosh.app.commons.vo.workgroup.option.budget.BudgetCategoryVO;
import com.noosh.app.commons.vo.workgroup.option.budget.CustomFieldVO;
import com.noosh.app.repository.mybatis.accounts.CustomFieldMyBatisMapper;
import com.noosh.app.repository.mybatis.collaboration.CategoryMyBatisMapper;
import com.noosh.app.service.util.NooshOneUrlUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 7/22/2022
 */
@Service
public class BudgetCategoryService {

    //existing object attributes
    private static final String OBJECT_ATTR_COMPARE_TO_ACTUAL = "compareToActual";

    //object attributes separators & delimeters
    private static final String OBJECT_ATTR_DELIMETER = " ";
    private static final String OBJECT_ATTR_NAME_VALUE_SEPARATOR = "=";

    @Autowired
    private CategoryMyBatisMapper categoryMyBatisMapper;
    @Autowired
    private CustomFieldMyBatisMapper customFieldMyBatisMapper;

    public BudgetCategoryListVO getBudgetCategoryList(Long userId, Long workgroupId) {
        BudgetCategoryListVO budgetCategoryListVO = new BudgetCategoryListVO();

        List<CategoryDTO> categoryDTOS = categoryMyBatisMapper.findProjectBudgetCategory(userId, workgroupId);
        List<BudgetCategoryVO> categoryList = new ArrayList<>();
        categoryDTOS.forEach(categoryDTO -> {
            BudgetCategoryVO bcVO = new BudgetCategoryVO();
            bcVO.setName(categoryDTO.getName());
            bcVO.setDescription(categoryDTO.getDescription());
            bcVO.setCompareToActual(getObjectAttrElement(categoryDTO.getObjectAttr(), OBJECT_ATTR_COMPARE_TO_ACTUAL).equals("1"));
            bcVO.setEditExternalUrl(NooshOneUrlUtil.gotoUpdateCategoryLink(categoryDTO.getId()));
            bcVO.setDeleteExternalUrl(NooshOneUrlUtil.gotoDeleteCategoryLink(categoryDTO.getId()));
            bcVO.setUpExternalUrl(NooshOneUrlUtil.moveUpCategoryLink(categoryDTO.getId()));
            bcVO.setDownExternalUrl(NooshOneUrlUtil.moveDownCategoryLink(categoryDTO.getId()));
            categoryList.add(bcVO);
        });
        budgetCategoryListVO.setCategoryList(categoryList);


        List<CustomFieldDTO> customFieldDTOS = customFieldMyBatisMapper.findProjectBudgetFields(workgroupId);
        List<CustomFieldVO> fieldList = new ArrayList<>();
        customFieldDTOS.forEach(customFieldDTO -> {
            CustomFieldVO cfVO = new CustomFieldVO();
            cfVO.setLabel(customFieldDTO.getLabel());
            cfVO.setTypeStrId(String.valueOf(customFieldDTO.getCustomFieldControl().getDescriptionStrId()));
            cfVO.setRequired(customFieldDTO.getIsRequired());
            cfVO.setIncludeInTotal(customFieldDTO.getIncludeInTotal());
            cfVO.setEditExternalUrl(NooshOneUrlUtil.gotoUpdateFieldLink(customFieldDTO.getId()));
            cfVO.setDeleteExternalUrl(NooshOneUrlUtil.gotoDeleteFieldLink(customFieldDTO.getId()));
            cfVO.setUpExternalUrl(NooshOneUrlUtil.moveUpFieldLink(customFieldDTO.getId()));
            cfVO.setDownExternalUrl(NooshOneUrlUtil.moveDownFieldLink(customFieldDTO.getId()));
            fieldList.add(cfVO);
        });
        budgetCategoryListVO.setFieldList(fieldList);

        budgetCategoryListVO.setAddCategoryExternalUrl(NooshOneUrlUtil.gotoAddProjectBudgetCategoryLink());
        budgetCategoryListVO.setAddFieldExternalUrl(NooshOneUrlUtil.gotoAddProjectBudgetFieldLink(fieldList.size() + 1));
        budgetCategoryListVO.setBackToOptionsExternalUrl(NooshOneUrlUtil.composeBackToOptionsLinkToEnterprise());

        return budgetCategoryListVO;
    }

    private String getObjectAttrElement(String objectAttr, String name) {
        if (objectAttr == null) {
            return null;
        }

        int startInd = objectAttr.indexOf(name);
        if (startInd < 0) {
            return null;
        }

        int endInd = objectAttr.indexOf(OBJECT_ATTR_DELIMETER, startInd);
        if (endInd < 0) {
            endInd = objectAttr.length();
        }
        String tokenStr = objectAttr.substring(startInd, endInd);

        int valueStartIndex = tokenStr.indexOf(OBJECT_ATTR_NAME_VALUE_SEPARATOR);
        if (valueStartIndex <= 0) {
            return null;
        }

        return tokenStr.substring(valueStartIndex + 1);
    }
}
