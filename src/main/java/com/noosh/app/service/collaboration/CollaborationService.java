package com.noosh.app.service.collaboration;

import com.noosh.app.commons.dto.collaboration.SyContainableDTO;
import com.noosh.app.commons.entity.collaboration.SyContainable;
import com.noosh.app.mapper.collaboration.SyContainableMapper;
import com.noosh.app.repository.collaboration.CollaborationRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.inject.Inject;
import java.time.LocalDateTime;
import java.util.List;

/**
 * User: leilaz
 * Date: 9/19/19
 */
@Service
@Transactional(readOnly = true)
public class CollaborationService {
    @Inject
    private CollaborationRepository collaborationRepository;
    @Inject
    private SyContainableMapper syContainableMapper;

    public List<SyContainableDTO> findOneByObjectIdAndObjectClassId(Long objectId, Long objectClasssId) {
        List<SyContainable> syContainables = collaborationRepository.findByObjectIdAndObjectClassId(objectId, objectClasssId);
        if (syContainables != null && syContainables.size() > 0) {
            return syContainableMapper.toDTOs(syContainables);
        }
        return null;
    }

    public SyContainableDTO findOneByObjectIdAndObjectClassIdAndParent(Long objectId, Long objectClasssId, Long parentObjectId, Long parentObjectClasssId) {
        SyContainable syContainable = collaborationRepository.findByObjectIdAndParentObjectId(objectId, parentObjectId, objectClasssId, parentObjectClasssId);
        return syContainableMapper.toDTO(syContainable);
    }

    @Transactional(readOnly = false)
    public com.noosh.app.commons.dto.collaboration.SyContainableDTO createContainableLinkToProject(SyContainableDTO syContainableDTO){
        SyContainable containableEntity = syContainableMapper.toEntity(syContainableDTO);
        containableEntity.setCreateDate(LocalDateTime.now());
        collaborationRepository.save(containableEntity);
        return syContainableMapper.toDTO(containableEntity);
    }
}
