package com.noosh.app.service.project;

import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.entity.project.Project;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.mapper.ProjectMapper;
import com.noosh.app.repository.jpa.project.ProjectRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Service
@Transactional
public class ProjectService {

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectMapper projectMapper;

    public ProjectDTO findProjectById(Long projectId) {
        Project newProject = projectRepository.findById(projectId).orElse(null);
        if (newProject != null) {
            return projectMapper.toDTO(newProject);
        } else {
            throw new NotFoundException("project not found");
        }
    }

    public ProjectDTO findProjectByMasterAndWorkgroupId(Long masterProjectId, Long workgroupId) {
        Project newProject = projectRepository.findProjectByMasterProjectIdAndOwnerWorkgroupId(masterProjectId, workgroupId);
        if (newProject != null) {
            return projectMapper.toDTO(newProject);
        }
        return null;
    }

}