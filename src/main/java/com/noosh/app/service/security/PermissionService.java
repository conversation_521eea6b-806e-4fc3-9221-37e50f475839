package com.noosh.app.service.security;


import com.noosh.app.feign.AccountOpenFeignClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * @Author: neals
 * @Date: 12/29/2015
 */
@Transactional
@Service
public class PermissionService {

    @Autowired
    private AccountOpenFeignClient accountResourceFeignClient;

    public Boolean checkAll(Long permissionId, Long workgroupId, Long userId, Long projectId) {
        return accountResourceFeignClient.checkPermission(permissionId, workgroupId, userId, projectId);
    }

    public Boolean checkWorkgroupLevelCannotPrivilege(Long permissionId, Long workgroupId, Long userId) {
        return accountResourceFeignClient.checkWorkgroupLevelCannotPrivilege(permissionId, workgroupId, userId);
    }

    public Map<String, Boolean> getPermissionMap(List<Long> permissionIds, Long workgroupId, Long userId, List<Long> projectIds) {
        return accountResourceFeignClient.getPermissionMap(permissionIds, workgroupId, userId, projectIds);
    }

    public boolean check(Long permId, Long projectId, Map<String, Boolean> permMap) {
        return permMap.getOrDefault(permId + "_" + projectId, false);
    }

    public Boolean checkForAdminTeam(Long permissionId, Long objectId, Long objectClassId, Long userId) {
        return accountResourceFeignClient.checkForAdminTeam(permissionId, objectId, objectClassId, userId);
    }

    public Map<Long, Boolean> bulkCheckForAdminTeam(List<Long> permissionIds, Long objectId, Long objectClassId, Long userId) {
        return accountResourceFeignClient.bulkCheckForAdminTeam(permissionIds, objectId, objectClassId, userId);
    }

}
