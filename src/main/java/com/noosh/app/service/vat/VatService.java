package com.noosh.app.service.vat;

import com.noosh.app.service.preference.PreferenceService;
import org.json.JSONArray;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class VatService {
    @Inject
    private PreferenceService preferenceService;

    public List findVATList(Map<String, String> wgPreferences) {
        List vatList = new ArrayList();
        String vatsInfo = preferenceService.getString("VATS_INFO", wgPreferences, "");
        if (!"".equals(vatsInfo)) {
            JSONArray jsonArray = new JSONArray(vatsInfo);
            vatList = jsonArray.toList();
        }
        return vatList;
    }

    public boolean isEnableVAT(Map<String, String> wgPreferences) {
        return preferenceService.check("ENABLE_VAT", wgPreferences);
    }

}
