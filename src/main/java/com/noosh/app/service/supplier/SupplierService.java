package com.noosh.app.service.supplier;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.accounts.*;
import com.noosh.app.commons.dto.security.WorkgroupDTO;
import com.noosh.app.commons.entity.accounts.SupplierWHLocation;
import com.noosh.app.commons.entity.accounts.WarehouseLocation;
import com.noosh.app.commons.entity.security.ClientWorkgroup;
import com.noosh.app.commons.entity.workgroup.SupplierWorkgroup;
import com.noosh.app.commons.entity.workgroup.Workgroup;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.account.SpecialtyVO;
import com.noosh.app.commons.vo.account.WorkgroupCapabilityVO;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.commons.vo.supplier.*;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.exception.UnexpectedException;
import com.noosh.app.repository.jpa.accounts.SupplierWHLocationRepository;
import com.noosh.app.repository.jpa.accounts.WarehouseLocationRepository;
import com.noosh.app.repository.jpa.security.ClientWorkgroupRepository;
import com.noosh.app.repository.jpa.workgroup.SupplierWorkgroupRepository;
import com.noosh.app.repository.jpa.workgroup.WorkgroupRepository;
import com.noosh.app.repository.mybatis.accounts.*;
import com.noosh.app.repository.mybatis.security.AccountUserMyBatisMapper;
import com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper;
import com.noosh.app.service.custom.CustomAttributeService;
import com.noosh.app.service.permission.PermissionService;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.util.BusinessUtil;
import com.noosh.app.service.util.JwtUtil;
import com.noosh.app.service.util.NooshOneUrlUtil;
import com.noosh.app.service.workgroup.WorkgroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 5/13/2022
 */
@Service
public class SupplierService {

    private static final String YES = "1";
    private static final String NO = "0";

    private static final long MAX_SEARCH_RESULT = 200;

    @Autowired
    private ClientWorkgroupRepository clientWorkgroupRepository;
    @Autowired
    private SupplierWorkgroupRepository supplierWorkgroupRepository;
    @Autowired
    private WorkgroupRepository workgroupRepository;
    @Autowired
    private WorkgroupMyBatisMapper workgroupMyBatisMapper;
    @Autowired
    private SupplierWorkgroupMyBatisMapper supplierWorkgroupMyBatisMapper;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private SupplierFlagService supplierFlagService;
    @Autowired
    private CustomAttributeService customAttributeService;
    @Autowired
    private WorkgroupService workgroupService;
    @Autowired
    private WorkgroupProfileMyBatisMapper workgroupProfileMyBatisMapper;
    @Autowired
    private WorkgroupSpecialtyMyBatisMapper workgroupSpecialtyMyBatisMapper;
    @Autowired
    private WorkgroupEquipmentMyBatisMapper workgroupEquipmentMyBatisMapper;
    @Autowired
    private AccountUserMyBatisMapper accountUserMyBatisMapper;
    @Autowired
    private SupplierWHLocationMyBatisMapper supplierWHLocationMyBatisMapper;
    @Autowired
    private WarehouseLocationRepository warehouseLocationRepository;
    @Autowired
    private SupplierWHLocationRepository supplierWHLocationRepository;

    /***
     * get supplier preferences
     * @param workgroupId
     * @return
     */
    public Map<String, String> getSupplierPreferences(Long workgroupId) {
        Map<String, String> displayPrefs = new HashMap<>();
        List<String> prefs = new ArrayList<>();
        prefs.add(PreferenceID.BU_RFE_UNREGIS_SUP);
        prefs.add(PreferenceID.BU_ORDER_UNREGIS_SUP);
        prefs.add(PreferenceID.BU_SUP_CANCEL_ORDER);
        //prefs.add(PreferenceID.BU_BUYER_EDIT_SUP_TERMS);
        prefs.add(PreferenceID.WORKGROUP_OPTION_CANCEL_ORDER);
        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId, prefs);
        if (preferenceService.check(PreferenceID.BU_RFE_UNREGIS_SUP, groupPrefs)) {
            displayPrefs.put(PreferenceID.BU_RFE_UNREGIS_SUP, YES);
        } else {
            displayPrefs.put(PreferenceID.BU_RFE_UNREGIS_SUP, NO);
        }
        if (preferenceService.check(PreferenceID.BU_ORDER_UNREGIS_SUP, groupPrefs)) {
            displayPrefs.put(PreferenceID.BU_ORDER_UNREGIS_SUP, YES);
        } else {
            displayPrefs.put(PreferenceID.BU_ORDER_UNREGIS_SUP, NO);
        }
        if (preferenceService.check(PreferenceID.WORKGROUP_OPTION_CANCEL_ORDER, groupPrefs)) {
            if (preferenceService.check(PreferenceID.BU_SUP_CANCEL_ORDER, groupPrefs)) {
                displayPrefs.put(PreferenceID.BU_SUP_CANCEL_ORDER, YES);
            } else {
                displayPrefs.put(PreferenceID.BU_SUP_CANCEL_ORDER, NO);
            }
        }
        return  displayPrefs;
    }

    /**
     * Edit Supplier Preferences
     * @param userId
     * @param workgroupId
     * @param preferences
     */
    public void editSupplierPreferences(long userId, long workgroupId, Map<String, String> preferences) {
        if (preferences != null && preferences.get(PreferenceID.BU_WORKGROUP_AUTO_EXTENSION_OPENBID_PREF) != null) {
            String extensionVal = preferences.get(PreferenceID.BU_WORKGROUP_AUTO_EXTENSION_OPENBID_PREF);
            // auto extension must be less than 30
            if (Long.valueOf(extensionVal) > 30) {
                // StringID.AUTOMATIC_OPENBID_EXTENSION_CONSTRAINT
                throw new UnexpectedException("Invalid automatic open bid extension.  Automatic open bid extension should not be more than 30 minutes.");
            }
        }
        preferenceService.updatePreferencesForWorkgroup(workgroupId, userId, preferences);
    }

    /**
     * find client supplier list
     * @param workgroupId
     * @param clientId
     * @param page
     */
    public SupplierListVO clientSupplierList(long workgroupId, long clientId, String hhCertFilter, PageVO page) {
        ClientWorkgroup clientWorkgroup = clientWorkgroupRepository.findById(clientId).orElse(null);
        if (clientWorkgroup != null) {
            SupplierListVO supplierList = new SupplierListVO();
            boolean isClient = preferenceService.check(PreferenceID.WORKGROUP_OPTION_CLIENT, workgroupId);
            supplierList.setIsClient(isClient);

            // find client suppliers
            Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
            pageInfo.setUnsafeOrderBy(page.getOrderBy());
            List<SupplierWorkgroupDTO> supplierWorkgroupDTOList = supplierWorkgroupMyBatisMapper.findSupplierWorkgroups(workgroupId,null,
                    BusinessUtil.getClientWorkgroupFilterSql(clientWorkgroup.getClientAcWorkgroupId()), BusinessUtil.getHHCertFilterSql(hhCertFilter));
            page.setTotal(pageInfo.getTotal());

            if (supplierWorkgroupDTOList.isEmpty()) {
                return supplierList;
            }

            // convert to supplierWgVO
            Workgroup ownerWorkgroup = workgroupRepository.findById(workgroupId).orElse(null);
            List<SupplierWgVO> supplierWgVOList = toSupplierWgVOList(isClient, supplierWorkgroupDTOList, ownerWorkgroup);
            supplierList.setSupplierWgs(supplierWgVOList);
            if (permissionService.checkAll(PermissionID.MANAGE_APPROVED_SUPPLIER, JwtUtil.getWorkgroupId(), JwtUtil.getUserId(), -1L)) {
                supplierList.setCanManageSupplier(true);
                HashMap<String, String> params = new HashMap<>();
                params.put("workgroupId", JwtUtil.getWorkgroupId().toString());
                supplierList.setSupplierFindExternalUrl(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.GOTO_FIND_SUPPLIER, params));
            }
            return supplierList;
        } else {
            throw new NotFoundException("client id not found");
        }
    }

    /***
     * find suppliers that the client doesn't have
     * @param workgroupId
     * @param clientId
     * @param page
     * @return
     */
    public SupplierListVO outsourcerSupplierList(long workgroupId, long clientId, String hhCertFilter, PageVO page) {
        ClientWorkgroup clientWorkgroup = clientWorkgroupRepository.findById(clientId).orElse(null);
        if (clientWorkgroup != null) {
            SupplierListVO supplierList = new SupplierListVO();
            boolean isClient = preferenceService.check(PreferenceID.WORKGROUP_OPTION_CLIENT, workgroupId);
            supplierList.setIsClient(isClient);

            // find client suppliers
            Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
            pageInfo.setUnsafeOrderBy(page.getOrderBy());
            List<SupplierWorkgroupDTO> supplierWorkgroupDTOList = supplierWorkgroupMyBatisMapper.findOutsourcerSuppliers(workgroupId, clientWorkgroup.getClientAcWorkgroupId(), BusinessUtil.getHHCertFilterSql(hhCertFilter));
            page.setTotal(pageInfo.getTotal());

            if (supplierWorkgroupDTOList.isEmpty()) {
                return supplierList;
            }

            // convert to supplierWgVO
            Workgroup ownerWorkgroup = workgroupRepository.findById(workgroupId).orElse(null);
            List<SupplierWgVO> supplierWgVOList = toSupplierWgVOList(isClient, supplierWorkgroupDTOList, ownerWorkgroup);
            supplierList.setSupplierWgs(supplierWgVOList);
            return supplierList;
        } else {
            throw new NotFoundException("client id not found");
        }
    }

    /***
     * find current workgroup supplier list
     * @param workgroupId
     * @param supplierWorkgroupName
     * @param clientWorkgroupIds
     * @param page
     * @return
     */
    public SupplierListVO listSuppliers(long workgroupId, String supplierWorkgroupName, List<Long> clientWorkgroupIds, PageVO page) {
        SupplierListVO supplierList = new SupplierListVO();
        List<String> groupsPrefs = new ArrayList<>();
        groupsPrefs.add(PreferenceID.WORKGROUP_OPTION_CLIENT);
        groupsPrefs.add(PreferenceID.WORKGROUP_OPTION_OUTSOURCING);
        groupsPrefs.add(PreferenceID.WORKGROUP_OPTION_BROKERING);
        Map<String, String> groupsPrefsMap = preferenceService.findGroupPrefs(workgroupId, groupsPrefs);
        boolean isClient = preferenceService.check(PreferenceID.WORKGROUP_OPTION_CLIENT, groupsPrefsMap);
        boolean isOutSourcing = preferenceService.check(PreferenceID.WORKGROUP_OPTION_OUTSOURCING, groupsPrefsMap);
        boolean isBroker = preferenceService.check(PreferenceID.WORKGROUP_OPTION_BROKERING, groupsPrefsMap);
        supplierList.setIsClient(isClient);
        supplierList.setIsOutsourcer(isOutSourcing || isBroker);

        String searchString = BusinessUtil.getEscapedSearchString(supplierWorkgroupName);
        String clientWorkgroupFilterSql = BusinessUtil.getClientWorkgroupFilterSql(clientWorkgroupIds);
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        pageInfo.setUnsafeOrderBy(page.getOrderBy());
        List<SupplierWorkgroupDTO> supplierWorkgroupDTOList = supplierWorkgroupMyBatisMapper.findSupplierWorkgroups(workgroupId, searchString, clientWorkgroupFilterSql, null);
        page.setTotal(pageInfo.getTotal());

        // convert to supplierWgVO
        Workgroup ownerWorkgroup = workgroupRepository.findById(workgroupId).orElse(null);
        List<SupplierWgVO> supplierWgVOList = toSupplierWgVOList(isClient, supplierWorkgroupDTOList, ownerWorkgroup);
        if (permissionService.checkAll(PermissionID.MANAGE_APPROVED_SUPPLIER, JwtUtil.getWorkgroupId(), JwtUtil.getUserId(), -1L)) {
            supplierList.setCanManageSupplier(true);
            HashMap<String, String> params = new HashMap<>();
            params.put("workgroupId", JwtUtil.getWorkgroupId().toString());
            supplierList.setSupplierFindExternalUrl(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.GOTO_FIND_SUPPLIER, params));
        }
        supplierList.setSupplierWgs(supplierWgVOList);
        return supplierList;
    }
    @Transactional
    public void updateSupplierFilter(Long workgroupId, Long userId, List<Long> clientWorkgroupIds, Integer pageSize) {
        Map<String, String> preferences = new HashMap<>();
        preferences.put(SupplierFilterVO.SUPPLIER_LIST_FILTER_PREFIX + SupplierFilterVO.PAGE_SIZE, pageSize.toString());
        //limit the workgroup ids to 400 (5177823,.....) as the string value in DB limit 4000
        if (null != clientWorkgroupIds && clientWorkgroupIds.size() <= 400) {
            preferences.put(SupplierFilterVO.SUPPLIER_LIST_FILTER_PREFIX + SupplierFilterVO.CLIENT_WORKGROUP_ID, clientWorkgroupIds.stream().map(Object::toString).collect(Collectors.joining(",")));
        } else {
            preferences.put(SupplierFilterVO.SUPPLIER_LIST_FILTER_PREFIX + SupplierFilterVO.CLIENT_WORKGROUP_ID, "");
        }
        preferenceService.saveUserPreference(workgroupId, userId, preferences);
    }
    public SupplierFilterVO getSupplierFilter(Long workgroupId, Long userId) {
        Map<String, String> preferences = preferenceService.findUserPrefs(workgroupId, userId);
        SupplierFilterVO supplierFilterVO = new SupplierFilterVO();
        if (preferences.containsKey(SupplierFilterVO.SUPPLIER_LIST_FILTER_PREFIX + SupplierFilterVO.PAGE_SIZE)) {
            supplierFilterVO.setPageSize(Integer.valueOf(preferences.get(SupplierFilterVO.SUPPLIER_LIST_FILTER_PREFIX + SupplierFilterVO.PAGE_SIZE)));
        } else {
            supplierFilterVO.setPageSize(SupplierFilterVO.PAGE_SIZE_DEFAULT_VALUE);
        }
        if (preferences.containsKey(SupplierFilterVO.SUPPLIER_LIST_FILTER_PREFIX + SupplierFilterVO.CLIENT_WORKGROUP_ID)) {
            String clientWorkgroupIds = preferences.get(SupplierFilterVO.SUPPLIER_LIST_FILTER_PREFIX + SupplierFilterVO.CLIENT_WORKGROUP_ID);
            if (clientWorkgroupIds != null && !clientWorkgroupIds.isEmpty()) {
                supplierFilterVO.setSelectedClientWorkgroupIds(Arrays.stream(clientWorkgroupIds.split(",")).map(Long::valueOf).collect(Collectors.toList()));
            } else {
                supplierFilterVO.setSelectedClientWorkgroupIds(new ArrayList<>());
            }
        }
        return supplierFilterVO;
    }

    private List<SupplierWgVO> toSupplierWgVOList(boolean isClient, List<SupplierWorkgroupDTO> supplierWorkgroupDTOList, Workgroup ownerWorkgroup) {
        List<SupplierWgVO> supplierWgVOList = new ArrayList<>();
        Map<String, SupplierFlagVO> supplierFlagVOMap = getSupplierFlag(supplierWorkgroupDTOList);
        Map<Long, Map<String, String>> groupsPrefsMap = getSpecificPreferences(supplierWorkgroupDTOList);
        String ownerWorkgroupName = ownerWorkgroup == null ? null : ownerWorkgroup.getName();
        supplierWorkgroupDTOList.stream().forEach(dto -> {
            SupplierWgVO vo = toSupplierWgVO(isClient, ownerWorkgroupName, dto, supplierFlagVOMap, groupsPrefsMap);
            supplierWgVOList.add(vo);
        });
        return supplierWgVOList;
    }
    private SupplierWgVO toSupplierWgVO(boolean isClient, String ownerWorkgroupName, SupplierWorkgroupDTO dto, Map<String, SupplierFlagVO> supplierFlagMap, Map<Long, Map<String, String>> groupsPrefs) {
        SupplierWgVO vo = new SupplierWgVO();
        vo.setSupplierId(dto.getId());
        vo.setSupplierWorkgroupId(dto.getSupplierWorkgroupId());
        vo.setDisplayPrefixId(StringID.CLIENT_OPTION_PREFIX);
        vo.setSupplierWorkgroupName(dto.getSupplierWorkgroup().getName());
        if (dto.getClientWorkgroup() != null) {
            vo.setClientWorkgroupName(dto.getClientWorkgroup().getName());
            vo.setIsClientSupplier(true);
            vo.setClientWorkgroupId(dto.getClientWorkgroupId());
        } else {
            vo.setIsClientSupplier(false);
        }
        vo.setOwnerWorkgroupName(ownerWorkgroupName);
        vo.setSupplierCode(dto.getSupplierCode());
        vo.setDefaultSupplierUser(BusinessUtil.getFullName(dto.getDefaultSupplierUser()));
        vo.setIsApproved(dto.getIsApproved());
        vo.setIsWarehouseEnabled(preferenceService.check(PreferenceID.WORKGROUP_OPTION_WAREHOUSE, dto.getSupplierWorkgroupId(), groupsPrefs));
        vo.setLocations(dto.getSupplierWHLocations());
        boolean isSupplierOutsourcer = false;
        if (isClient) {
            boolean isOutsourcer = preferenceService.check(PreferenceID.WORKGROUP_OPTION_OUTSOURCING, dto.getSupplierWorkgroupId(), groupsPrefs);
            boolean isBroker = preferenceService.check(PreferenceID.WORKGROUP_OPTION_BROKERING, dto.getSupplierWorkgroupId(), groupsPrefs);
            isSupplierOutsourcer = isOutsourcer || isBroker;
            if (isOutsourcer || isBroker) {
                vo.setAcceptQuote(dto.getAcceptQuote());
                vo.setAcceptChangeOrder(dto.getAcceptChangeOrder());
                vo.setAcceptPartialInvoice(dto.getAcceptPartialInvoice());
                vo.setAcceptChangeOrderNoChange(dto.getAcceptChangeOrderNoChange());
            }
        }
        vo.setIsSupplierOutsourcer(isSupplierOutsourcer);
        HashMap<String, String> params = new HashMap<>();
        params.put("supplierId", dto.getId().toString());
        params.put("workgroupId", dto.getOwnerWorkgroupId().toString());
        vo.setSupplierDetailExternalUrl(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.GOTO_SUPPLIER_DETAIL, params));
        // supplier flag
        String supplierFlagKey = supplierFlagService.getSupplierFlagKey(
                true,
                -1L,
                -1L,
                dto.getOwnerWorkgroupId(),
                dto.getSupplierWorkgroupId()
        );
        vo.setSupplierFlag(supplierFlagMap.get(supplierFlagKey));

        return vo;
    }
    private Map<String, SupplierFlagVO> getSupplierFlag(List<SupplierWorkgroupDTO> supplierWorkgroupDTOList) {
        List<SupplierFlagRequestVO> supplierFlagRequestVOs = supplierFlagService.generateSupplierFlagRequestVOs(supplierWorkgroupDTOList);
        return supplierFlagService.getSupplierFlagVOs(supplierFlagRequestVOs);
    }
    private Map<Long, Map<String, String>> getSpecificPreferences(List<SupplierWorkgroupDTO> supplierWorkgroupDTOList) {
        List<String> prefs = new ArrayList<>();
        prefs.add(PreferenceID.WORKGROUP_OPTION_BROKERING);
        prefs.add(PreferenceID.WORKGROUP_OPTION_OUTSOURCING);
        prefs.add(PreferenceID.WORKGROUP_OPTION_WAREHOUSE);
        List<Long> supplierWorkgroupIds = new ArrayList<>();
        supplierWorkgroupDTOList.stream().forEach(supplierWorkgroupDTO -> supplierWorkgroupIds.add(supplierWorkgroupDTO.getSupplierWorkgroupId()));
        return preferenceService.findGroupsPrefs(supplierWorkgroupIds, prefs);
    }

    public List<WorkgroupDTO> findSuppliers(Long workgroupId, SupplierFilterVO supplierFilter, PageVO page) {
        if (supplierFilter.checkIsValid()) {
            Optional<Workgroup> workgroupOptional = workgroupRepository.findById(workgroupId);
            if (workgroupOptional.isPresent()) {
                Workgroup workgroup = workgroupOptional.get();
                if (workgroup.getWorkgroupSd() != null) {
                    boolean isInternal = workgroup.getWorkgroupSd().getIsInternal();
                    Long partnerWgId = workgroup.getPartnerWgId();
                    Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
                    List<WorkgroupDTO> workgroupDTOList = workgroupMyBatisMapper.findWorkgroupsBySupplierFilter(supplierFilter, isInternal, partnerWgId, workgroupId);
                    page.setTotal(pageInfo.getTotal());
                    if (pageInfo.getTotal() > MAX_SEARCH_RESULT) {
                        throw new UnexpectedException(StringID.SEARCH_RESULT_HAS_TOO_MANY_ENTRIES);
                    }
                    return workgroupDTOList;
                }
            }
        }
        throw new UnexpectedException(StringID.INCOMPLETED_SEARCH_CRITERIA);
    }

    public boolean checkSupplierExist(Long workgroupId, Long supplierWorkgroupId, List<Long> clientWorkgroupIds) {
        //if any of the clientWorkgroupId is in the supplierWorkgroups then return true else return false
        List<SupplierWorkgroup> supplierWorkgroups = supplierWorkgroupRepository.findBySupplierWorkgroupIdAndOwnerWorkgroupId(supplierWorkgroupId, workgroupId);
        if (clientWorkgroupIds.size() > supplierWorkgroups.size()) { return false;}
        Set<Long> clientWorkgroupIdSet = new HashSet<>();
        supplierWorkgroups.forEach(supplierWorkgroup -> {
            if (supplierWorkgroup.getClientWorkgroupId() == null) {
                clientWorkgroupIdSet.add(0L);
            } else {
                clientWorkgroupIdSet.add(supplierWorkgroup.getClientWorkgroupId());
            }
        });
        for (Long clientWorkgroupId: clientWorkgroupIds) {
            if (!clientWorkgroupIdSet.contains(clientWorkgroupId)) {
                return false;
            }
        }
        return true;
    }

    public Map<Long, Boolean> batchCheckSupplierExist(Long workgroupId, List<Long> supplierWorkgroupIds, List<Long> clientWorkgroupIds) {
        Map<Long, Boolean> map = new HashMap<>();
        if (clientWorkgroupIds != null && clientWorkgroupIds.size() == 1 && clientWorkgroupIds.get(0) == -1) {
            clientWorkgroupIds = clientWorkgroupRepository.findByOwnerId(workgroupId);
            clientWorkgroupIds.add(0L);
        }
        for (Long supplierWorkgroupId: supplierWorkgroupIds) {
            map.put(supplierWorkgroupId, checkSupplierExist(workgroupId, supplierWorkgroupId, clientWorkgroupIds));
        }
        return map;
    }

    public SupplierWgVO getSupplierDetail(Long userId, Long ownerWorkgroupId, Long supplierId, Boolean isEdit) {
        Optional<SupplierWorkgroup> optionalSupplierWorkgroup = supplierWorkgroupRepository.findById(supplierId);
        if (optionalSupplierWorkgroup.isEmpty()) {throw new NotFoundException("can't find supplier by " + supplierId);}
        if (!optionalSupplierWorkgroup.get().getOwnerWorkgroupId().equals(ownerWorkgroupId)) {
            throw new IllegalArgumentException("invalid supplierId " + supplierId);
        }

        SupplierWorkgroupDTO supplierWorkgroupDTO = supplierWorkgroupMyBatisMapper.find(supplierId);
        supplierWorkgroupDTO.setSupplierWorkgroup(workgroupService.findWorkgroup(supplierWorkgroupDTO.getSupplierWorkgroupId()));
        SupplierWgVO vo = toSupplierWgVO(ownerWorkgroupId, supplierWorkgroupDTO);
        if (isEdit) {
            List<DropdownVO<Long>> additionalClientWorkgroupIds = getDropdownClientIds(supplierWorkgroupDTO);
            vo.setAdditionalClientWorkgroupIds(additionalClientWorkgroupIds);
        }

        Boolean isCapabilitySearchable = supplierWorkgroupDTO.getSupplierWorkgroup().getWgsd().getIsCapabilitySearchable();
        vo.setIsCapabilitySearchable(isCapabilitySearchable);
        if (null != isCapabilitySearchable && isCapabilitySearchable) {
            vo.setWorkgroupCapability(getWorkgroupCapability(supplierWorkgroupDTO.getSupplierWorkgroup()));
        }

        String supplierMemberPageSize = preferenceService.getString("REACT_SUPPLIER_MEMBER_pageSize", ownerWorkgroupId, userId);
        vo.setSupplierMemberPageSize(supplierMemberPageSize.equals("0") ? 10 : Integer.valueOf(supplierMemberPageSize));

        return vo;
    }
    public List<SupplierWgVO> getApprovedClients(Long ownerWorkgroupId, Long supplierId, String searchStr, PageVO page) {
        Optional<SupplierWorkgroup> optionalSupplierWorkgroup = supplierWorkgroupRepository.findById(supplierId);
        if (optionalSupplierWorkgroup.isEmpty()) {throw new NotFoundException("can't find supplier by " + supplierId);}
        if (!optionalSupplierWorkgroup.get().getOwnerWorkgroupId().equals(ownerWorkgroupId)) {
            throw new IllegalArgumentException("invalid supplierId " + supplierId);
        }
        SupplierWorkgroup supplierWorkgroup = optionalSupplierWorkgroup.get();
        String formattedSearchFilter = BusinessUtil.getEscapedSearchString(searchStr);
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        pageInfo.setUnsafeOrderBy(page.getOrderBy());
        List<SupplierWorkgroupDTO> approvedSuppliers = supplierWorkgroupMyBatisMapper.findApprovedClients(supplierWorkgroup.getOwnerWorkgroupId(), supplierWorkgroup.getSupplierWorkgroupId(), formattedSearchFilter);
        page.setTotal(pageInfo.getTotal());
        return approvedSuppliers.stream().map(supplierWorkgroupDTO -> {
            SupplierWgVO supplierWgVO = new SupplierWgVO();
            supplierWgVO.setSupplierId(supplierWorkgroupDTO.getId());
            supplierWgVO.setClientWorkgroupName(supplierWorkgroupDTO.getClientWorkgroup().getName());
            if (supplierWorkgroupDTO.getId().equals(supplierId)) {
                supplierWgVO.setIsCurrentViewSupplier(true);
            }
            return supplierWgVO;
        }).collect(Collectors.toList());
    }

    private WorkgroupCapabilityVO getWorkgroupCapability(WorkgroupDTO supplierWorkgroup) {
        WorkgroupProfileDTO profileDTO = workgroupProfileMyBatisMapper.findByWorkgroupId(supplierWorkgroup.getId());
        if (profileDTO != null) {
            if (profileDTO.getLogo() != null) {
                profileDTO.getLogo().getLogoUrl();
            }
            if (profileDTO.getContactUserId() != null) {
                profileDTO.setContactUser(accountUserMyBatisMapper.findWithPersonAndWorkgroupByUserId(profileDTO.getContactUserId()));
            }
        }

        List<WorkgroupSpecialtyDTO> specialtyDTOList = workgroupSpecialtyMyBatisMapper.findByWorkgroupId(supplierWorkgroup.getId());
        SpecialtyVO specialtyVO = null;
        if (!specialtyDTOList.isEmpty()) {
            List<WorkgroupSpecialtyDTO> productList = new ArrayList<>();
            List<WorkgroupSpecialtyDTO> serviceList = new ArrayList<>();
            specialtyVO = new SpecialtyVO(productList, serviceList);
            for (int i = 0; i < specialtyDTOList.size(); i++) {
                WorkgroupSpecialtyDTO bean = specialtyDTOList.get(i);
                if (bean.getSpecialty().getTypeId() == SpecialtyTypeID.SPECIALTY_TYPE_PRODUCT) {
                    productList.add(bean);
                } else {
                    serviceList.add(bean);
                }
            }
        }
        List<WorkgroupEquipmentDTO> equipmentDTOList = workgroupEquipmentMyBatisMapper.findByWorkgroupId(supplierWorkgroup.getId());
        if (profileDTO != null || !specialtyDTOList.isEmpty() || !equipmentDTOList.isEmpty()) {
            WorkgroupCapabilityVO workgroupCapabilityVO = new WorkgroupCapabilityVO(profileDTO, specialtyVO, equipmentDTOList);
            return workgroupCapabilityVO;
        }
        return null;
    }
    private List<DropdownVO<Long>> getDropdownClientIds(SupplierWorkgroupDTO supplierWorkgroupDTO) {
        List<DropdownVO<Long>> additionalClientWorkgroupIds = new ArrayList<>();
        List<SupplierWorkgroupDTO> additionalDTOs = supplierWorkgroupMyBatisMapper.findByOwnerAndSupplierWorkgroupId(supplierWorkgroupDTO.getOwnerWorkgroupId(), supplierWorkgroupDTO.getSupplierWorkgroupId(), supplierWorkgroupDTO.getId());
        for (SupplierWorkgroupDTO dto: additionalDTOs) {
            DropdownVO<Long> dropdownVO;
            if (dto.getClientWorkgroupId() == null) {
                Optional<Workgroup> workgroupOptional = workgroupRepository.findById(supplierWorkgroupDTO.getOwnerWorkgroupId());
                if (workgroupOptional.isPresent()) {
                    dropdownVO = new DropdownVO<>(0L, workgroupOptional.get().getName());
                    additionalClientWorkgroupIds.add(dropdownVO);
                }
            } else {
                dropdownVO = new DropdownVO<>(dto.getClientWorkgroupId(), "[Client] " + dto.getClientWorkgroup().getName());
                additionalClientWorkgroupIds.add(dropdownVO);
            }
        }
        return additionalClientWorkgroupIds;
    }

    private SupplierWgVO toSupplierWgVO(long ownerWorkgroupId, SupplierWorkgroupDTO dto) {
        SupplierWgVO vo = new SupplierWgVO();
        vo.setSupplierId(dto.getId());
        vo.setSupplierWorkgroupId(dto.getSupplierWorkgroupId());
        if (dto.getClientWorkgroup() != null) {
            vo.setClientWorkgroupName(dto.getClientWorkgroup().getName());
            vo.setIsClientSupplier(true);
            vo.setClientWorkgroupId(dto.getClientWorkgroupId());
        } else {
            vo.setIsClientSupplier(false);
        }
        vo.setClientWorkgroupId(dto.getClientWorkgroupId());
        vo.setSupplierWorkgroupName(dto.getSupplierWorkgroup().getName());
        vo.setSupplierCode(dto.getSupplierCode());
        vo.setIsApproved(dto.getIsApproved());
        vo.setAlias(dto.getAlias());
        vo.setDefaultSupplierUser(BusinessUtil.getFullName(dto.getDefaultSupplierUser()));
        vo.setDefaultSupplierUserId(dto.getDefaultSupplierUserId());
        SupplierFlagVO supplierFlagVO = supplierFlagService.getSupplierFlagVO(true, -1L, -1L,
                dto.getOwnerWorkgroupId(),
                dto.getSupplierWorkgroupId());
        vo.setSupplierFlag(supplierFlagVO);

        Map<String, Object> customFields = customAttributeService.findCustomAttributes(dto.getCustomPropertyId(), null);
        vo.setCustomFields(customFields);

        Map<String, String> ownerGroupPreferences = preferenceService.findGroupPrefs(ownerWorkgroupId);
        boolean isClient = preferenceService.check(PreferenceID.WORKGROUP_OPTION_CLIENT, ownerGroupPreferences);
        boolean isBroker = preferenceService.check(PreferenceID.WORKGROUP_OPTION_BROKERING, ownerGroupPreferences);
        boolean isOutsourcer = preferenceService.check(PreferenceID.WORKGROUP_OPTION_OUTSOURCING, ownerGroupPreferences);
        vo.setIsClient(isClient);
        vo.setIsOutsourcer(isBroker || isOutsourcer);

        List<String> preferencesList = new ArrayList<>();
        preferencesList.add(PreferenceID.WORKGROUP_OPTION_BROKERING);
        preferencesList.add(PreferenceID.WORKGROUP_OPTION_OUTSOURCING);
        preferencesList.add(PreferenceID.WORKGROUP_OPTION_WAREHOUSE);
        Map<String, String> supplierGroupPreferences = preferenceService.findGroupPrefs(dto.getSupplierWorkgroupId(), preferencesList);
        boolean isSupplierOutsourcer = false;
        if (isClient) {
            boolean supplierIsOutsourcer = preferenceService.check(PreferenceID.WORKGROUP_OPTION_OUTSOURCING, supplierGroupPreferences);
            boolean supplierIsBroker = preferenceService.check(PreferenceID.WORKGROUP_OPTION_BROKERING, supplierGroupPreferences);
            isSupplierOutsourcer = supplierIsOutsourcer || supplierIsBroker;
            if (isSupplierOutsourcer) {
                vo.setAcceptQuote(dto.getAcceptQuote());
                vo.setAcceptChangeOrder(dto.getAcceptChangeOrder());
                vo.setAcceptPartialInvoice(dto.getAcceptPartialInvoice());
                vo.setAcceptChangeOrderNoChange(dto.getAcceptChangeOrderNoChange());
            }
        }
        vo.setIsSupplierOutsourcer(isSupplierOutsourcer);

        boolean isEnableWarehouse = preferenceService.check(PreferenceID.WORKGROUP_OPTION_WAREHOUSE, supplierGroupPreferences);
        vo.setIsEnableWarehouse(isEnableWarehouse);
        List<SupplierWHLocationDTO> supplierWHLocationDTOS = supplierWHLocationMyBatisMapper.findAll(dto.getId(), dto.getSupplierWorkgroupId());
        supplierWHLocationDTOS.stream().forEach(
                temp -> temp.setIsInSupplierWarehouseList(temp.getId() != null)
        );
        vo.setLocations(supplierWHLocationDTOS);

        boolean isEnableTealbook = preferenceService.check(PreferenceID.WORKGROUP_OPTION_INTEGRATIONS_TEALBOOK, dto.getOwnerWorkgroupId());
        vo.setIsEnableTealbook(isEnableTealbook);
        if (isEnableTealbook) {
            if (customFields.get("WEBSITE_URL_str") != null) {
                vo.setWebsite((String) customFields.get("WEBSITE_URL_str"));
            }
            if (customFields.get("TEALBOOK_PROFILE_str") != null) {
                vo.setTealbookProfile((String) customFields.get("TEALBOOK_PROFILE_str"));
            }
        }

        Boolean canManageSupplier = permissionService.checkAll(PermissionID.MANAGE_APPROVED_SUPPLIER, JwtUtil.getWorkgroupId(), JwtUtil.getUserId(), -1L);
        Boolean canAddSupplierWarehouse = permissionService.checkAll(PermissionID.EDIT_WORKGROUP, JwtUtil.getWorkgroupId(), JwtUtil.getUserId(), -1L);
        vo.setCanManageSupplier(canManageSupplier);
        vo.setCanAddSupplierWarehouse(canAddSupplierWarehouse);

        WorkgroupDTO supplierWorkgroup = dto.getSupplierWorkgroup();
        vo.setDuns(supplierWorkgroup.getWgsd().getDuns());
        vo.setMainAddress(supplierWorkgroup.getMainAddress());
        vo.setWorkgroupTypeStrId(supplierWorkgroup.getWorkgroupType().getDescriptionStrId());
        vo.setCompany(supplierWorkgroup.getCompany().getName());
        vo.setCurrency(supplierWorkgroup.getDefaultCurrency().getCurrency());
        return vo;
    }

    public void addSupplierWarehouse(Long supplierId, Long warehouseLocationId, String idCode) {
        Optional<SupplierWorkgroup> supplierWorkgroup = supplierWorkgroupRepository.findById(supplierId);
        if (supplierWorkgroup.isEmpty()) {
            throw new NotFoundException("can't find supplier by " + supplierId);
        }
        Optional<WarehouseLocation> warehouseLocation = warehouseLocationRepository.findById(warehouseLocationId);
        if (warehouseLocation.isEmpty() || !warehouseLocation.get().getWorkgroupId().equals(supplierWorkgroup.get().getSupplierWorkgroupId())) {
            throw new NotFoundException("warehouseLocationId " + warehouseLocationId + " is not valid.");
        }
        if (supplierWHLocationRepository.findBySupplierIdAndWarehouseLocationId(supplierId, warehouseLocationId) != null) {
            throw new UnexpectedException(StringID.CANNOT_ADD_SUPPLIER_WHLOCATION_FOREIGN_KEY);
        }
        SupplierWHLocation supplierWHLocation = new SupplierWHLocation();
        supplierWHLocation.setSupplierId(supplierId);
        supplierWHLocation.setWarehouseLocationId(warehouseLocationId);
        supplierWHLocation.setIdCode(idCode);
        supplierWHLocationRepository.save(supplierWHLocation);
    }

    public SupplierWorkgroupDTO findSupplier(Long ownerGroupId, String supplierName) {
        List<SupplierWorkgroupDTO> supplierWorkgroupDTOS = supplierWorkgroupMyBatisMapper.findSupplierWorkgroupsByName(ownerGroupId, supplierName);
        return supplierWorkgroupDTOS.isEmpty() ? null : supplierWorkgroupDTOS.get(0);
    }
}
