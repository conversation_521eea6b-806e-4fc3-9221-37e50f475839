package com.noosh.app.service.supplier;

import com.noosh.app.commons.dto.accounts.SupplierWorkgroupDTO;
import com.noosh.app.commons.vo.supplier.SupplierFlagRequestVO;
import com.noosh.app.commons.vo.supplier.SupplierFlagVO;
import com.noosh.app.feign.AccountResourceFeignClient;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * the diversity icon displays for the particular procurement object, and it’s time dependent.
 * When an RFE or a quick order is sent to suppliers, whatever status the supplier has at that moment is
 * used for the rest of the procurement process.
 * <p>
 * For example, user sent “RFE 1” to suppliers 1, 2, 3 on 9/24/2020.
 * At that moment, supplier 1 is diversified. Therefore, RFE 1 will display supplier 1 as diversify.
 * On 9/25/2020, supplier 1 is NOT diversify anymore.
 * Then Order 1 is created based on RFE 1 with supplier 1 on 9/26/2020.
 * Order 1 is denoted as diversity.
 */
@Service
public class SupplierFlagService {

    @Inject
    private AccountResourceFeignClient accountResourceFeignClient;

    public String getSupplierFlagKey(boolean isRealTimeQuery,
                                     long objectId,
                                     long objectClassId,
                                     long ownerWorkgroupId,
                                     long supplierWorkgroupId) {
        return "" + isRealTimeQuery +
                objectId +
                objectClassId +
                ownerWorkgroupId +
                supplierWorkgroupId;
    }

    public Map<String, SupplierFlagVO> getSupplierFlagVOs(List<SupplierFlagRequestVO> requestVOs) {
        return accountResourceFeignClient.getSupplierFlags(requestVOs);
    }

    public SupplierFlagVO getSupplierFlagVO(boolean isRealTimeQuery,
                                            long objectId,
                                            long objectClassId,
                                            long ownerWorkgroupId,
                                            long supplierWorkgroupId) {

        return accountResourceFeignClient.getSupplierFlag(isRealTimeQuery,
                objectId,
                objectClassId,
                ownerWorkgroupId,
                supplierWorkgroupId);
    }

    public List<SupplierFlagRequestVO> generateSupplierFlagRequestVOs(List<SupplierWorkgroupDTO> supplierWorkgroupDTOs) {
        List<SupplierFlagRequestVO> supplierFlagRequestVOs = new ArrayList<>(supplierWorkgroupDTOs.size());
        supplierWorkgroupDTOs.stream().forEach(dto -> {
            SupplierFlagRequestVO supplierFlagRequestVO = new SupplierFlagRequestVO(true,
                    -1L,
                    -1L,
                    dto.getOwnerWorkgroupId(),
                    dto.getSupplierWorkgroupId());
            supplierFlagRequestVOs.add(supplierFlagRequestVO);
        });
        return supplierFlagRequestVOs;
    }
}
