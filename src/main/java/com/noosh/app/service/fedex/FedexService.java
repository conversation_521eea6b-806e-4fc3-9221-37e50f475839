package com.noosh.app.service.fedex;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.noosh.app.commons.dto.security.CountryDTO;
import com.noosh.app.commons.dto.security.WorkgroupDTO;
import com.noosh.app.commons.dto.workgroup.option.CustomDataSetDTO;
import com.noosh.app.commons.entity.security.AccountUser;
import com.noosh.app.commons.vo.fedex.CenterNumberVO;
import com.noosh.app.commons.vo.fedex.FedexInitVO;
import com.noosh.app.commons.vo.fedex.RateQuotesVO;
import com.noosh.app.repository.jpa.security.AccountUserRepository;
import com.noosh.app.repository.mybatis.security.CountryMyBatisMapper;
import com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper;
import com.noosh.app.service.apijob.WebClientService;
import com.noosh.app.service.custom.CustomAttributeService;
import com.noosh.app.service.workgroup.option.DataSetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class FedexService {

    @Autowired
    private CustomAttributeService customAttributeService;
    @Autowired
    private AccountUserRepository accountUserRepository;
    @Autowired
    private CountryMyBatisMapper countryMyBatisMapper;
    @Autowired
    private DataSetService dataSetService;
    @Autowired
    private WebClientService webClientService;
    @Autowired
    private WorkgroupMyBatisMapper workgroupMyBatisMapper;

    private ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    private Environment environment;

    public FedexInitVO getFedexInitVO(Long workgroupId, Long userId) {
        AccountUser accountUser = accountUserRepository.findById(userId).get();
        Map<String, Object> centerData = new HashMap<>();
        List<CenterNumberVO> centerNumberList = new ArrayList<>();

        if (accountUser.getCustomPropertyId() != null) {
            List<Long> propertyIds = new ArrayList<>();
            propertyIds.add(accountUser.getCustomPropertyId());
            List<String> paramNames = new ArrayList<>();
            paramNames.add("CENTER_ADDR_1_str");
            paramNames.add("CENTER_ADDR_2_str");
            paramNames.add("CENTER_ADDR_3_str");
            paramNames.add("CENTER_CITY_str");
            paramNames.add("CENTER_STATE_str");
            paramNames.add("CENTER_POSTAL_CODE_str");
            paramNames.add("CENTER_COUNTRY_ID_str");
            paramNames.add("CENTER_NO_str");
            paramNames.add("DISTRICT_NO_str");
            Map<Long, Map<String, Object>> customAttributesMap = customAttributeService.findCustomAttributes(propertyIds, paramNames);
            centerData = customAttributesMap.get(accountUser.getCustomPropertyId());

            String districtNoStr = (String) centerData.get("DISTRICT_NO_str");
            if (districtNoStr != null) {
                // find all data sets with the name "Fedex Store Information" (custom data set name
                List<CustomDataSetDTO> customDataSetDTOs = dataSetService.findDataSets(workgroupId, "Fedex Store Information");
                // districtNumber attribute6, centerName attribute7, centerNumber attribute8
                for (CustomDataSetDTO customDataSetDTO : customDataSetDTOs) {
                    if (!districtNoStr.equals(customDataSetDTO.getAttribute6())) {
                        continue;
                    }
                    CenterNumberVO centerNumberVO = new CenterNumberVO();
                    centerNumberVO.setCenterNumber(customDataSetDTO.getAttribute8());
                    centerNumberVO.setCenterName(customDataSetDTO.getAttribute8() + " - " + customDataSetDTO.getAttribute7());
                    centerNumberList.add(centerNumberVO);
                }
            }
        }

        List<CountryDTO> countryList = countryMyBatisMapper.findAll();

        FedexInitVO fedexInitVO = new FedexInitVO();
        fedexInitVO.setCenterData(centerData);
        fedexInitVO.setCountryList(countryList);
        fedexInitVO.setCenterNumberList(centerNumberList);
        return fedexInitVO;
    }

    public Map getFedexRatesQuotes(RateQuotesVO rateQuotesVO) {
        // preload shipper postal code and country code
        WorkgroupDTO supplierWorkgroupWithAllData = workgroupMyBatisMapper.findWorkgroupWithAllData(rateQuotesVO.getSupplierWorkgroupId());
        rateQuotesVO.setShipperPostalCode(supplierWorkgroupWithAllData.getMainAddress().getPostal());
        rateQuotesVO.setShipperCountryCode(supplierWorkgroupWithAllData.getMainAddress().getCountry().getConstantToken());

        // get oauth token
        String accessToken = null;
        try {
            accessToken = getOatuhTokenFromFedexAPI();
        } catch (JsonProcessingException e) {
            Map errorMap = new HashMap();
            errorMap.put("error", "Fedex API is unavailable.");
            return errorMap;
        }
        // save token to redis
        // get rates quotes
        String ratesQuotes = null;
        try {
            ratesQuotes = getRatesQuotesFromFedexAPI(accessToken, rateQuotesVO);
        } catch (JsonProcessingException e) {
            Map errorMap = new HashMap();
            errorMap.put("error", "Fedex API is unavailable.");
            return errorMap;
        }

        try {
            return objectMapper.readValue(ratesQuotes, Map.class);
        } catch (JsonProcessingException e) {
            Map errorMap = new HashMap();
            errorMap.put("error", "Fedex API is unavailable.");
            return errorMap;
        }
    }

    private String getRatesQuotesFromFedexAPI(String accessToken, RateQuotesVO rateQuotesVO) throws JsonProcessingException {
        // TODO set rateQuotesVO to json body
        String serviceType = rateQuotesVO.getServiceType();
        String shipperPostalCode = rateQuotesVO.getShipperPostalCode();
        String shipperCountryCode = rateQuotesVO.getShipperCountryCode();
        String recipientPostalCode = rateQuotesVO.getRecipientPostalCode();
        String recipientCountryCode = rateQuotesVO.getRecipientCountryCode();
        String weight = rateQuotesVO.getWeight();

        String host = environment.getProperty("FEDEX.SHIPPING.HOST");
        String accountNumber = environment.getProperty("FEDEX.SHIPPING.ACCOUNTNUMBER");

        String jsonBody = "{" +
                "    \"accountNumber\": {" +
                "        \"value\": \"" + accountNumber + "\"" +
                "    }," +
                "    \"rateRequestControlParameters\": {" +
                "        \"returnTransitTimes\": true" +
                "    }," +
                "    \"requestedShipment\": {" +
                "        \"serviceType\": \"" + serviceType + "\"," +
                "        \"shipper\": {" +
                "            \"address\": {" +
                "                \"postalCode\": " + shipperPostalCode + "," +
                "                \"countryCode\": \"" + shipperCountryCode + "\"" +
                "            }" +
                "        }," +
                "        \"recipient\": {" +
                "            \"address\": {" +
                "                \"postalCode\": " + recipientPostalCode + "," +
                "                \"countryCode\": \"" + recipientCountryCode + "\"" +
                "            }" +
                "        }," +
                "        \"pickupType\": \"USE_SCHEDULED_PICKUP\"," +
                "        \"rateRequestType\": [" +
                "            \"ACCOUNT\"" +
                "        ]," +
                "        \"requestedPackageLineItems\": [" +
                "            {" +
                "                \"weight\": {" +
                "                    \"units\": \"LB\"," +
                "                    \"value\": " + weight + "" +
                "                }" +
                "            }" +
                "        ]" +
                "    }" +
                "}";
        String path = "/rate/v1/rates/quotes";

        MultiValueMap<String, String> headerMap = new HttpHeaders();
        headerMap.put("content-type", List.of("application/json"));
        headerMap.put("Authorization", List.of("Bearer " + accessToken));
        headerMap.put("X-locale", List.of("en_US"));

        Mono<String> result = webClientService.createWebApiWithJsonBody(
                "https://",
                host,
                path,
                HttpMethod.POST,
                headerMap,
                jsonBody
        );

        return result.block();
    }


    private String getOatuhTokenFromFedexAPI() throws JsonProcessingException {
        String host = environment.getProperty("FEDEX.SHIPPING.HOST");
        String clientId = environment.getProperty("FEDEX.SHIPPING.CLIENTID");
        String clientSecret = environment.getProperty("FEDEX.SHIPPING.CLIENTSECRET");

        Map<String, String> map = new HashMap<>();
        map.put("grant_type", "client_credentials");
        map.put("client_id", clientId);
        map.put("client_secret", clientSecret);
//        String jsonBody = "grant_type=client_credentials&client_id=" + clientId + "&client_secret=" + clientSecret;
        String jsonBody = objectMapper.writeValueAsString(map);

        String path = "/oauth/token";

        MultiValueMap<String, String> headerMap = new HttpHeaders();
        headerMap.put("content-type", List.of("application/x-www-form-urlencoded"));

        Mono<String> result = webClientService.createWebApiWithJsonBody(
                "https://",
                host,
                path,
                HttpMethod.POST,
                headerMap,
                jsonBody
        );

        String response = result.block();
        Map responseMap = objectMapper.readValue(response, Map.class);
        return (String) responseMap.get("access_token");
    }


}
