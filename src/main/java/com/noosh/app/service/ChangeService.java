package com.noosh.app.service;

import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.invoice.InvoiceDTO;
import com.noosh.app.commons.dto.invoice.InvoiceItemDTO;
import com.noosh.app.commons.dto.order.ChangeOrderDetailDTO;
import com.noosh.app.commons.dto.order.ChangeOrderVersionDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.dto.routing.RoutingRecipientDTO;
import com.noosh.app.commons.dto.routing.RoutingSlipDTO;
import com.noosh.app.commons.entity.collaboration.SyContainable;
import com.noosh.app.commons.entity.order.Order;
import com.noosh.app.commons.entity.order.OrderItem;
import com.noosh.app.commons.entity.order.OrderVersion;
import com.noosh.app.commons.entity.reason.PcWorkgroupCoReason;
import com.noosh.app.commons.entity.terms.AcTerms;
import com.noosh.app.commons.vo.order.ChangeOrderDetailVO;
import com.noosh.app.feign.WorkgroupOpenFeignClient;
import com.noosh.app.mapper.OrderVersionMapper;
import com.noosh.app.mapper.terms.AcTermsMapper;
import com.noosh.app.repository.collaboration.CollaborationRepository;
import com.noosh.app.repository.jpa.order.OrderItemRepository;
import com.noosh.app.repository.jpa.order.OrderRepository;
import com.noosh.app.repository.jpa.order.OrderVersionRepository;
import com.noosh.app.repository.mybatis.invoice.InvoiceMyBatisMapper;
import com.noosh.app.repository.reason.WorkgroupCoReasonRepository;
import com.noosh.app.repository.terms.AcTermsRepository;
import com.noosh.app.service.permission.ordering.*;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.routing.RoutingService;
import com.noosh.app.service.util.NooshOneUrlUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.inject.Inject;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * User: leilaz
 * Date: 4/8/20
 */
@Service
@Transactional
public class ChangeService {
    @Inject
    private InvoiceService invoiceService;
    @Inject
    private OrderService orderService;
    @Inject
    private PreferenceService preferenceService;
    @Inject
    private AggregatedOrderService aggregatedOrderService;
    @Inject
    private AcTermsRepository acTermsRepository;
    @Inject
    private CollaborationRepository collaborationRepository;
    @Inject
    private OrderRepository orderRepository;
    @Inject
    private OrderVersionRepository orderVersionRepository;
    @Inject
    private OrderItemRepository orderItemRepository;
    @Inject
    private WorkgroupCoReasonRepository workgroupCoReasonRepository;
    @Inject
    private AcTermsMapper acTermsMapper;
    @Inject
    private AcceptChangeOrderPermission acceptChangeOrderPermission;
    @Inject
    private CancelChangeOrderPermission cancelChangeOrderPermission;
    @Inject
    private CostCenterAllocationPermission costCenterAllocationPermission;
    @Inject
    private CreateChangeOrderPermission createChangeOrderPermission;
    @Inject
    private EditChangeOrderPermission editChangeOrderPermission;
    @Inject
    private RejectChangeOrderPermission rejectChangeOrderPermission;
    @Inject
    private RetractChangeOrderPermission retractChangeOrderPermission;
    @Inject
    private SubmitChangeOrderPermission submitChangeOrderPermission;
    @Inject
    private OrderVersionMapper orderVersionMapper;
    @Inject
    private WorkgroupOpenFeignClient workgroupOpenFeignClient;
    @Inject
    private RoutingService routingService;
    @Inject
    private InvoiceMyBatisMapper invoiceMyBatisMapper;

    public ChangeOrderDetailDTO findChangeOrderDetail(Long orderId, Long projectId, Long currentWorkgroupId,
                                                      Long currentUserId, String orderType) throws Exception {
        ChangeOrderDetailDTO changeOrderDetailDTO = findChangeOrderBaseInfo(orderId, projectId, currentWorkgroupId, currentUserId);
        findChangeOrderReason(changeOrderDetailDTO);
        findChangeOrderButtons(orderId, projectId, currentWorkgroupId, changeOrderDetailDTO, currentUserId, orderType);

        return changeOrderDetailDTO;
    }

    public ChangeOrderDetailDTO findChangeOrderBaseInfo(Long orderId, Long projectId, Long currentWorkgroupId,
                                                        Long currentUserId) throws Exception {
        ChangeOrderDetailDTO changeOrderDetailDTO = new ChangeOrderDetailDTO();

        // change order
        ChangeOrderVersionDTO changeOrderVersionDTO = new ChangeOrderVersionDTO();
        OrderVersionDTO changeOrder = findChangeOrder(orderId, projectId, currentWorkgroupId, currentUserId);
        changeOrderVersionDTO.setChangeOrder(changeOrder);

        // aggregated order
        OrderVersionDTO parentOrder = orderService.findOrderVersionById(changeOrder.getOrder().getParentOrderId(),
                projectId, currentUserId, currentWorkgroupId);
        changeOrderVersionDTO.setParentOrder(parentOrder);
        OrderVersionDTO copyOfParent = orderVersionMapper.toCopy(parentOrder);
        changeOrderVersionDTO.setBaseOrder(aggregatedOrderService.getAggregatedOrder(copyOfParent,
                changeOrder.getAcceptDate(), currentWorkgroupId, currentUserId));

        //Order Send to Supplier Date
        if(parentOrder.isOutsourcingSellOrder(changeOrder.getParent()) || parentOrder.isBrokerSellOrder(changeOrder.getParent())) {
            changeOrderDetailDTO.setOrderSendToSupplierDate(null);
        } else {
            SyContainable syContainable = collaborationRepository.findFirstByObjectIdAndObjectClassIdOrderByCreateDateDesc(
                    parentOrder.getOrderId(), ObjectClassID.ORDER);
            changeOrderDetailDTO.setOrderSendToSupplierDate(syContainable != null ? syContainable.getCreateDate() : null);
        }

        changeOrderDetailDTO.setOrderVersionDTO(changeOrderVersionDTO);

        //Set workgroup preference
        Map<String, String> buyerWorkgroupPreference = preferenceService.findGroupPrefs(changeOrder.getBuyerWorkgroupId());

        routingService.requiresApproval(changeOrderVersionDTO, buyerWorkgroupPreference);
        routingService.requiresManagerApproval(changeOrderVersionDTO, buyerWorkgroupPreference);
        routingService.requiresRouting(changeOrderVersionDTO);
        routingService.requiresManagerRouting(changeOrderVersionDTO, buyerWorkgroupPreference);
        changeOrderVersionDTO.setIsManagerPresentAtCostLevel(routingService.isManagerPresentAtCostLevel(buyerWorkgroupPreference, changeOrder, changeOrderVersionDTO.getBaseOrder()));
        return changeOrderDetailDTO;
    }

    private OrderVersionDTO findChangeOrder(Long orderId, Long projectId, Long currentWorkgroupId, Long currentUserId) throws Exception {
        OrderVersionDTO changeOrder = orderService.findOrderVersionById(orderId, projectId, currentUserId, currentWorkgroupId);

        if (changeOrder.isClosingChangeOrder() && changeOrder.isUserSupplier() && changeOrder.getInvoicingEnable()) {
            changeOrder.setFinalInvoiceTemplate(invoiceService.findFinalInvoiceTemplate(changeOrder.getOrder().getParentOrderId()));
        }
        return changeOrder;
    }

    public BigDecimal findInvoiceTotalAmount(Long projectId, Long workgroupId, ChangeOrderDetailVO changeOrderDetailVO, ChangeOrderDetailDTO changeOrderDetailDTO) throws Exception {
        BigDecimal invoiceTotalAmount = null;
        OrderVersionDTO changeOrder = changeOrderDetailDTO.getOrderVersionDTO().getChangeOrder();
        OrderVersionDTO parentOrder = changeOrderDetailDTO.getOrderVersionDTO().getParentOrder();
        ProjectDTO parent = changeOrder.getParent();
        if (changeOrder.isClosingChangeOrder() && parent.isSupplierProject() && !parentOrder.isCompleted() && changeOrderDetailVO.getCanAccept()) {
            BigDecimal orderAmt = changeOrderDetailDTO.getOrderVersionDTO().getBaseOrder().getGrandTotal();
            if (changeOrder.getGrandTotal() != null) {
                orderAmt = orderAmt.add(changeOrder.getGrandTotal());
            }
            if (changeOrder.getDiscountOrSurchargeTotal() != null) {
                orderAmt = orderAmt.add(changeOrder.getDiscountOrSurchargeTotal());
            }

            // get accepted invoice and item
            LocalDateTime now = LocalDateTime.now();
            List<InvoiceDTO> acceptedInvoiceList = invoiceMyBatisMapper.findInvoiceForOrder(projectId, changeOrderDetailDTO.getOrderVersionDTO().getParentOrder().getOrderId());
            List<InvoiceDTO> acceptedList = new ArrayList();
            if (!acceptedInvoiceList.isEmpty()) {
                for (InvoiceDTO invoice : acceptedInvoiceList) {
                    if (!invoice.isAccepted())
                        continue;
                    if (invoice.getAcceptedDate() != null && invoice.getAcceptedDate().compareTo(now) >= 0)
                        continue;
                    acceptedList.add(invoice);
                }
            }
            acceptedList = acceptedList.stream().map(invoice -> {
                List<InvoiceItemDTO> acceptedInvoiceItems = invoiceMyBatisMapper.findInvoiceItem(invoice.getId(), workgroupId);
                BigDecimal itemTotal = BigDecimal.ZERO;
                if (acceptedInvoiceItems != null && acceptedInvoiceItems.size() > 0) {
                    for (InvoiceItemDTO item : acceptedInvoiceItems) {
                        if (item.getAmount() != null) {
                            itemTotal = itemTotal.add(item.getAmount());
                        }
                        if (invoice.getFinal()) {
                            if (item.getDiscountOrSurcharge() != null) {
                                itemTotal = itemTotal.add(item.getDiscountOrSurcharge());
                            }
                        }
                    }
                }
                invoice.setInvoiceItemTotal(itemTotal);
                return invoice;
            }).collect(Collectors.toList());

            BigDecimal invoiceAmount = BigDecimal.ZERO;
            for (InvoiceDTO invoiceDTO : acceptedList) {
                BigDecimal amt = invoiceDTO.getGrandTotal();
                invoiceAmount = invoiceAmount.add(amt);
            }
            invoiceTotalAmount = orderAmt.subtract(invoiceAmount);
        }
        return invoiceTotalAmount;
    }

    private void findChangeOrderReason(ChangeOrderDetailDTO changeOrderDetailDTO) {
        ChangeOrderVersionDTO changeOrderVersionDTO = changeOrderDetailDTO.getOrderVersionDTO();
        OrderVersionDTO changeOrder = changeOrderVersionDTO.getChangeOrder();
        long buyerWorkgroupId = changeOrder.getBuyerWorkgroupId();
        List<PcWorkgroupCoReason> workgroupCoReasons = workgroupCoReasonRepository.findByWorkgroupId(buyerWorkgroupId);
        changeOrderVersionDTO.setEnableCOReason(workgroupCoReasons != null && workgroupCoReasons.size() > 0);
    }

    private void findChangeOrderButtons(Long orderId, Long projectId, Long currentWorkgroupId, ChangeOrderDetailDTO changeOrderDetailDTO, Long currentUserId, String orderType) throws Exception {
        ChangeOrderVersionDTO changeOrderVersionDTO = changeOrderDetailDTO.getOrderVersionDTO();
        OrderVersionDTO changeOrder = changeOrderVersionDTO.getChangeOrder();
        OrderVersionDTO parentOrder = changeOrderVersionDTO.getParentOrder();

        //acceptButton and acceptAndCarryOverButton
        boolean canAccept = true;
        if (acceptChangeOrderPermission.check(changeOrder, currentWorkgroupId, currentUserId, projectId)) {
            String acceptButton = NooshOneUrlUtil.composeAcceptChangeOrderLinkToEnterprise(projectId,
                    parentOrder.getId(), orderId, changeOrder.getVersion(), orderType);
            changeOrderDetailDTO.setAcceptButton(acceptButton);
        } else {
            canAccept = false;
        }

        boolean isBuyer = (changeOrder.getBuyerWorkgroupId().longValue() == currentWorkgroupId);
        // Add following fix for skip "Accept Change Order/Closing Change Order" Button when routing is needed [NKB142788]
        // TODO: Try to avoid touch above logic for now, need to refactor above code, it's kind of mess up already
        if (changeOrder.getOrderState().getObjectStateId() == ObjectStateID.ORDER_BUYER_TO_ACCEPT  && isBuyer
                && (changeOrder.getRequiresApproval() || changeOrder.getRequiresManagerApproval())) {
            canAccept = Boolean.FALSE;
            changeOrderDetailDTO.setAcceptButton(null);

            if (changeOrder.getRequiresRouting() && (!changeOrder.isCompleted())) {
                String routeForApprovalButton = NooshOneUrlUtil.composeRouteForApprovalChangeOrderLinkToEnterprise(projectId, orderId,
                        changeOrder.getVersion(), orderType);
                changeOrderDetailDTO.setRouteForApprovalButton(routeForApprovalButton);
            } else if (changeOrder.getRequiresManagerRouting()){
                String routeForManagerApprovalButton = NooshOneUrlUtil.composeRouteForManagerApprovalChangeOrderLinkToEnterprise(projectId, orderId,
                        changeOrder.getVersion(), orderType);
                changeOrderDetailDTO.setRouteForManagerApprovalButton(routeForManagerApprovalButton);
            }
        }
//        if (changeOrder.getRequiresApproval() || changeOrder.getRequiresManagerApproval()) {
//            if (changeOrder.getBuyerWorkgroupId().longValue() == currentWorkgroupId && !changeOrder.isRejected()) {
//                if (changeOrder.getRequiresApproval()) {
//                    //Show approve routing
//                } else if (changeOrder.getRequiresManagerApproval()) {
//                    //Show approve routing manager
//
//                }
//                //Add routing for approve and routing for manager
//                //String routingForApprove = NooshOneUrlUtil.composeRouteForApprovalOrderLinkToEnterprise()
//                changeOrderDetailDTO.setAcceptButton(null);
//            }
//        }


        if (changeOrder.getOrderState().getObjectStateId() == ObjectStateID.ORDER_BUYER_TO_ACCEPT && isBuyer) {
            String acceptButton = NooshOneUrlUtil.composeAcceptChangeOrderLinkToEnterprise(projectId,
                    parentOrder.getId(), orderId, changeOrder.getVersion(), orderType);
            changeOrderDetailDTO.setAcceptButton(acceptButton);
        }

        if (changeOrder.getOrderState().getObjectStateId() == ObjectStateID.ORDER_BUYER_TO_ACCEPT && isBuyer &&
                (changeOrder.getRequiresApproval() || changeOrder.getRequiresManagerApproval())) {
            canAccept = false;
        }

        changeOrderDetailDTO.setCanAccept(canAccept);

        if (changeOrder.getIsDisapproved()) {
            changeOrderDetailDTO.setAcceptButton(null);
        }

        // routeForApprovalButton
        //routeForManagerApprovalButton
        if (changeOrder.getBuyerWorkgroupId().longValue() == currentWorkgroupId) {
            if (changeOrder.getRequiresApproval() && (!changeOrder.isDraft())) {
                String routeForManagerApprovalButton = null;
                changeOrderDetailDTO.setAcceptButton(null);
                canAccept = Boolean.FALSE;
                if (changeOrder.getRequiresRouting() && (!changeOrder.isCompleted())) {
                    String routeForApprovalButton = NooshOneUrlUtil.composeRouteForApprovalChangeOrderLinkToEnterprise(projectId, orderId,
                            changeOrder.getVersion(), orderType);
                    changeOrderDetailDTO.setRouteForApprovalButton(routeForApprovalButton);
                } else if (changeOrder.getRequiresManagerRouting()){
                    routeForManagerApprovalButton = NooshOneUrlUtil.composeRouteForManagerApprovalChangeOrderLinkToEnterprise(projectId, orderId,
                            changeOrder.getVersion(), orderType);
                    changeOrderDetailDTO.setRouteForManagerApprovalButton(routeForManagerApprovalButton);
                }
            } else if (changeOrder.getRequiresManagerApproval() && (!changeOrder.isDraft()) && changeOrder.getRequiresManagerRouting()) {
                changeOrderDetailDTO.setAcceptButton(null);
                canAccept = Boolean.FALSE;
                String routeForManagerApprovalButton = NooshOneUrlUtil.composeRouteForManagerApprovalChangeOrderLinkToEnterprise(projectId, orderId,
                        changeOrder.getVersion(), orderType);
                changeOrderDetailDTO.setRouteForManagerApprovalButton(routeForManagerApprovalButton);
            }
        }

        //submitButton
        if ((!changeOrder.isDraft()) && submitChangeOrderPermission.check(changeOrder, currentWorkgroupId, currentUserId, projectId)) {
            String submitButton = NooshOneUrlUtil.composeSubmitChangeOrderLinkToEnterprise(projectId,
                    parentOrder.getId(), orderId, changeOrder.getVersion(), orderType);
            changeOrderDetailDTO.setSubmitButton(submitButton);
        }

        //rejectButton
        if ((!changeOrder.isDraft()) && rejectChangeOrderPermission.check(changeOrder, currentWorkgroupId, currentUserId, projectId)) {
            String rejectButton = NooshOneUrlUtil.composeRejectChangeOrderLinkToEnterprise(projectId,
                    parentOrder.getId(), orderId, changeOrder.getVersion(), orderType);
            changeOrderDetailDTO.setRejectButton(rejectButton);
        }

        //retractButton
        if ((!changeOrder.isDraft()) && retractChangeOrderPermission.check(changeOrder, currentWorkgroupId, currentUserId, projectId)) {
            String retractButton = NooshOneUrlUtil.composeRetractChangeOrderLinkToEnterprise(projectId,
                    parentOrder.getId(), orderId, changeOrder.getVersion(), parentOrder.isUserBuyer() ? "buy" : "sell");
            changeOrderDetailDTO.setRetractButton(retractButton);
        }

        //editButton
        if ((!changeOrder.isDraft()) && editChangeOrderPermission.check(changeOrder, currentWorkgroupId, currentUserId, projectId)) {
            String editButton = NooshOneUrlUtil.composeEditChangeOrderLinkToEnterprise(projectId,
                    parentOrder.getId(), orderId, changeOrder.getVersion(), orderType);
            changeOrderDetailDTO.setEditButton(editButton);
        }

        //cancelButton
        if (cancelChangeOrderPermission.check(changeOrderVersionDTO, currentWorkgroupId, currentUserId, projectId)) {
            String cancelButton = NooshOneUrlUtil.composeCancelChangeOrderLinkToEnterprise(projectId,
                    parentOrder.getId(), orderId, changeOrder.getVersion(), parentOrder.isUserBuyer() ? "buy" : "sell");
            changeOrderDetailDTO.setCancelButton(cancelButton);
        }

        //cost center allocation
        if ((!changeOrder.isDraft()) && costCenterAllocationPermission.check(changeOrder, currentWorkgroupId, currentUserId, projectId)) {
            String costCenter = NooshOneUrlUtil.composeCostCenterAllocationChangeOrderLinkToEnterprise(projectId,
                    parentOrder.getId(), orderId, changeOrder.getVersion());
            changeOrderDetailDTO.setCostCenterAllocationButton(costCenter);
        }

        // Create approve and disapprove button
        boolean allowResponse = false;

        if (changeOrder.getRoutingSlip() != null) {
            RoutingSlipDTO rsb = changeOrder.getRoutingSlip();
            List<RoutingRecipientDTO> recipients = rsb.getRoutingRecipients();

            if (recipients != null) {
                for (int i = 0; i < recipients.size(); i++) {

                    /** @todo: ROUTING: Should we check whether it is this user's turn to respond if one after another is enabled */
                    if (currentUserId.longValue() == recipients.get(i).getToUserId() && recipients.get(i).getResponse() == -1) {
                        if (rsb.getIsAllAtOnce() == null || rsb.getIsAllAtOnce().shortValue() == 0) {
                            if (rsb.getCurrentRecipientId().longValue() == recipients.get(i).getId()) {
                                allowResponse = true;
                            }
                        } else {
                            allowResponse = true;
                        }
                    }
                }
                if (rsb.getFirstResponseMode() != null && rsb.getFirstResponseMode().shortValue() == 1
                        && rsb.getPendingCount() < recipients.size())
                    allowResponse = false;
            }
            if (allowResponse) {
                String backUrl = "/noosh/procurement/ordering/change/viewChangeOrder?changeOrderId=" + orderId + "&objectId="
                        + projectId + "&objectClassId=1000000&renderSpecs=false&orderVersionId=" + changeOrder.getId()
                        + "&acName=" +  "GOTO_CHANGE_ORDER_DETAILS";
                String approveButton = NooshOneUrlUtil.composeRoutingResponseForChangeOrderLinkToEnterprise(projectId, orderId,
                        ObjectClassID.CHANGE_ORDER, backUrl, (long) 1, changeOrder.getBuyerWorkgroupId(),
                        changeOrder.getId(), rsb.getSlipName());
                String disapproveButton = NooshOneUrlUtil.composeRoutingResponseForChangeOrderLinkToEnterprise(projectId, orderId,
                        ObjectClassID.CHANGE_ORDER, backUrl, (long) 0, changeOrder.getBuyerWorkgroupId(),
                        changeOrder.getId(), rsb.getSlipName());
                changeOrderDetailDTO.setRoutingApproveButton(approveButton);
                changeOrderDetailDTO.setRoutingDisapproveButton(disapproveButton);
            }
        }

        //Edit draft change order, delete draft change order
        if (changeOrder.isDraft()) {
            String deleteDraft = NooshOneUrlUtil.composeDeleteDraftChangeOrderLinkToEnterprise(projectId,
                    parentOrder.getOrderId(), changeOrder.getVersion(), changeOrder.getOrderId());
            changeOrderDetailDTO.setDeleteDraftButton(deleteDraft);
            boolean hasPendingChangeOrderItem = false;
            List<Order> changeOrders = orderRepository.findByParentOrderIdOrderByIdAsc(parentOrder.getOrderId());
            if (changeOrders != null && changeOrders.size() > 0) {
                for (Order change : changeOrders) {
                    if (change.getOrderStateSet().stream().filter(c -> c.getIsCurrent() && (
                            c.getObjectStateId() == ObjectStateID.ORDER_BUYER_TO_ACCEPT || c.getObjectStateId() == ObjectStateID.ORDER_CLIENT_TO_ACCEPT
                            || c.getObjectStateId() == ObjectStateID.ORDER_SUPPLIER_TO_ACCEPT
                                    || c.getObjectStateId() == ObjectStateID.ORDER_OUTSOURCER_TO_ACCEPT)).findAny().isPresent()) {
                        OrderVersion orderVersion = orderVersionRepository.findFirstByOrderIdAndIsCurrent(change.getId(), true);
                        if (orderVersion == null) continue;
                        List<OrderItem> orderItems = orderItemRepository.findByOrderVersionIdOrderByItemIndex(orderVersion.getId());
                        if (orderItems != null && orderItems.size() > 0) {
                            for (OrderItem orderItem : orderItems) {
                                if (changeOrder.getOrderItemDTOs() != null && changeOrder.getOrderItemDTOs().size() > 0) {
                                    if (changeOrder.getOrderItemDTOs().stream().filter(oi -> oi.getItemIndex().intValue() == orderItem.getItemIndex()).findAny().isPresent()) {
                                        hasPendingChangeOrderItem = true;
                                        break;
                                    }
                                }
                            }
                        }
                        if (hasPendingChangeOrderItem) {
                            break;
                        }
                    }
                }
            }
            if (!hasPendingChangeOrderItem) {
                String editDraft = NooshOneUrlUtil.composeEditDraftChangeOrderLinkToEnterprise(projectId,
                        parentOrder.getOrderId(), changeOrder.getVersion(), changeOrder.getOrderId());
                changeOrderDetailDTO.setEditDraftButton(editDraft);
            }
        }

        // initiate a carry over change order to the Buy/Sell order
        String carryOverChangeOrderButton = NooshOneUrlUtil.composeCarryOverChangeOrderButtonChangeOrderLinkToEnterprise(projectId,
                parentOrder.getId(), orderId);
        String acceptAndCarryOverButton = NooshOneUrlUtil.composeAcceptAndCarryOverButtonChangeOrderLinkToEnterprise(projectId,
                parentOrder.getId(), orderId);
        ProjectDTO parent = changeOrder.getParent();
        boolean isDisableCarryOverChangeOrder = false;
        boolean isDisableAcceptAndCarryOver = false;
        if (changeOrder.isPending()
                && (parent.isOutsourcerProject() || (parent.isBrokerProject() && parent.isClientOnNoosh()))) {
            // buy orders button is only for outsourcers with create order permission.
            if (!createChangeOrderPermission.check(projectId, currentUserId, currentWorkgroupId)) {
//                carryOverChangeOrderButton = null;
                isDisableCarryOverChangeOrder = true;
            }

            int itemCount = changeOrder.getOrderItemDTOs() != null ? changeOrder.getOrderItemDTOs().size() : 0;
            if (itemCount == 0) {
                isDisableCarryOverChangeOrder = true;
                isDisableAcceptAndCarryOver = true;
//                carryOverChangeOrderButton = null;
//                acceptAndCarryOverButton = null;
            }
            if (changeOrder.isClosingChangeOrder()) {
                acceptAndCarryOverButton = null;
            }
        } else {
            carryOverChangeOrderButton = null;
            acceptAndCarryOverButton = null;
        }

        if (!acceptChangeOrderPermission.check(changeOrder, currentWorkgroupId, currentUserId, projectId)) {
            acceptAndCarryOverButton = null;
        }

//        // change order
//        boolean changeFlag = this.createCarryOverChangeOrders(projectId, changeOrder, currentWorkgroupId, currentUserId);
//        if (!changeFlag) {
//            carryOverChangeOrderButton = null;
//            acceptAndCarryOverButton = null;
//        }
//
//        // close change order
//        if(changeOrder.isClosingChangeOrder()) {
//            boolean closingChangeFlag = this.createCarryOverChangeOrders(changeOrder);
//            if (!closingChangeFlag) {
//                carryOverChangeOrderButton = null;
//                acceptAndCarryOverButton = null;
//            }
//        }
        changeOrderDetailDTO.setIsDisableAcceptAndCarryOver(isDisableAcceptAndCarryOver);
        changeOrderDetailDTO.setIsDisableCarryOverChangeOrder(isDisableCarryOverChangeOrder);
        changeOrderDetailDTO.setCarryOverChangeOrderButton(carryOverChangeOrderButton);
        changeOrderDetailDTO.setAcceptAndCarryOverButton(acceptAndCarryOverButton);
        changeOrderDetailDTO.setShowApprovalLink(NooshOneUrlUtil.composeShowPendingApprovalLinkToEnterprise(projectId, orderId, true));
        //citi pre-accept button
        boolean isCiti = "uww-citi".equals(changeOrder.getBuyerWorkgroup().getPortal());
        boolean isPendingBuyerAcceptance = changeOrder.getOrderState().getObjectStateId() == ObjectStateID.ORDER_BUYER_TO_ACCEPT
                || changeOrder.getOrderState().getObjectStateId() == ObjectStateID.ORDER_CLIENT_TO_ACCEPT;
        if (isCiti && isPendingBuyerAcceptance && changeOrder.isClosingChangeOrder() && isBuyer && changeOrderDetailDTO.getAcceptButton() != null) {
            String preAcceptButton = NooshOneUrlUtil.composePreAcceptChangeOrderLinkToEnterprise(projectId,
                    parentOrder.getId(), orderId, changeOrder.getVersion(), orderType);
            changeOrderDetailDTO.setPreAcceptButton(preAcceptButton);
        }
    }

    /**
     *
     * @param origChangeOrder
     * @return
     */
    private boolean createCarryOverChangeOrders(Long projectId, OrderVersionDTO origChangeOrder, Long currentWorkgroupId, Long currentUserId) throws Exception {
        // for pending change order only
        if (!origChangeOrder.isPending()) {
            return false;
        }

        // only applicable for outsourcing projects and broker with online client
        ProjectDTO parent = origChangeOrder.getParent();
        if (!(parent.isOutsourcerProject() || (parent.isBrokerProject() && parent.isClientOnNoosh()))) {
            return false;
        }

        // get a list of jobId
        // if there's no job - nothing to do
        List<Long> jobIds = new ArrayList<Long>();
        if (origChangeOrder.getOrderItemDTOs() != null && origChangeOrder.getOrderItemDTOs().size() > 0) {
            jobIds = origChangeOrder.getOrderItemDTOs().stream().filter(o -> o.getJobId() != null).map(orderItemDTO -> orderItemDTO.getJobId()).collect(Collectors.toList());
        }
        if (jobIds.size() == 0) {
            return false;
        }

        boolean bCarryToSellChangeOrder = origChangeOrder.isUserBuyer() && !origChangeOrder.hasOfflineBuyer();

        // find all orders of the orderTypeID for the jobs
        // if given change order is a buyChangeOrder (changeOrder.isUserBuyer = True), get only the SELL orders
        // if given change order is a sellChangeOrder ( changeOrder.isUserBuyer = False), get only BUY orders
        List<OrderVersion> orderDTOS = null;
        if (bCarryToSellChangeOrder) {
            if (parent.isOutsourcerProject()) {
                orderDTOS = orderVersionRepository.findOrderBySupplierWgIdAndJobIdOutsourcer(jobIds, parent.getOwnerWorkgroupId());
            } else {
                orderDTOS = orderVersionRepository.findOrderBySupplierWgIdAndJobIdNonOutsourcer(jobIds, parent.getOwnerWorkgroupId());
            }
        } else {
            if (parent.isOutsourcerProject()) {
                orderDTOS = orderVersionRepository.findOrderByBuyWgIdAndJobIdOutsourcer(jobIds, parent.getOwnerWorkgroupId());
            } else {
                orderDTOS = orderVersionRepository.findOrderByBuyWgIdAndJobIdNonOutsourcer(jobIds, parent.getOwnerWorkgroupId());
            }
        }
        List<OrderVersionDTO> orderVersionDTOS = new ArrayList<OrderVersionDTO>();
        if(orderDTOS != null && orderDTOS.size()>0) {
            for(OrderVersion orderDTO : orderDTOS) {
                OrderVersionDTO orderVersionDTO = this.findChangeOrder(orderDTO.getOrderId(), projectId, currentWorkgroupId, currentUserId);
                orderVersionDTOS.add(orderVersionDTO);
            }
        }

        if(orderVersionDTOS.size() > 0) {
            for(OrderVersionDTO orderVersionDTO : orderVersionDTOS) {
                if(!orderVersionDTO.isAccepted())
                    continue;
                return true;
            }
        }
        return false;
    }

    private boolean createCarryOverChangeOrders(OrderVersionDTO origChangeOrder) {
        List<Long> jobIds = origChangeOrder.getOrderItemDTOs().stream().map(orderItemDTO -> orderItemDTO.getJobId()).collect(Collectors.toList());
        ProjectDTO parent = origChangeOrder.getParent();
        List<OrderVersion> orderDTOS = null;
        if (origChangeOrder.isUserBuyer() && !origChangeOrder.hasOfflineBuyer()) {
            if (parent.isOutsourcerProject()) {
                orderDTOS = orderVersionRepository.findOrderBySupplierWgIdAndJobIdOutsourcer(jobIds, parent.getOwnerWorkgroupId());
            } else {
                orderDTOS = orderVersionRepository.findOrderBySupplierWgIdAndJobIdNonOutsourcer(jobIds, parent.getOwnerWorkgroupId());
            }
        } else {
            if (parent.isOutsourcerProject()) {
                orderDTOS = orderVersionRepository.findOrderByBuyWgIdAndJobIdOutsourcer(jobIds, parent.getOwnerWorkgroupId());
            } else {
                orderDTOS = orderVersionRepository.findOrderByBuyWgIdAndJobIdNonOutsourcer(jobIds, parent.getOwnerWorkgroupId());
            }
        }
        int orderCount = orderDTOS != null ? orderDTOS.size() : 0;
        boolean isOne2One = orderCount == 1;
        boolean hasPendingChangeOrder = false;
        for (int oInd=0; isOne2One && oInd < orderCount; oInd++) {
            OrderVersion orderDTO = orderDTOS.get(oInd);
            if (orderService.hasPendingChangeOrders(orderDTO.getId())) {
                hasPendingChangeOrder = true;
                break;
            }
            List<OrderItem> orderItemDTOS = orderItemRepository.findByOrderVersionIdOrderByItemIndex(orderDTO.getId());
            if (orderItemDTOS != null && (orderItemDTOS.size() != origChangeOrder.getOrderItemDTOs().size())) {
                isOne2One = false;
                break;
            }
            for (int jInd=0; jInd < jobIds.size() ; jInd++) {
                List<OrderItem> orderItemDTOList = orderItemRepository.findByJobId(jobIds.get(jInd));
                if (orderItemDTOList.size() == 0) {
                    isOne2One = false;
                    break;
                }
            }
        }
        if (!isOne2One)
            return false;
        if (hasPendingChangeOrder)
            return false;
        return true;
    }

    public Map<String, Object> getClosingChangeOrderCustomAttribute(Long originalOrderId) {
        OrderVersion cco = orderVersionRepository.findPendingOrCompletedClosingChangeOrder(originalOrderId);
        if (cco != null && cco.getCustomPropertyId() != null) {
            List<Long> propertyIds = new ArrayList<>();
            propertyIds.add(cco.getCustomPropertyId());
            Map<Long, Map<String, Object>> customAttributesMap = workgroupOpenFeignClient.findCustomAttribute(propertyIds);
            return customAttributesMap.get(cco.getCustomPropertyId());
        }
        return null;
    }

}
