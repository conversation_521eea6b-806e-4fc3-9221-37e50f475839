package com.noosh.app.service.mydesk;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.invoice.InvoicePendingMyDeskDTO;
import com.noosh.app.commons.dto.mydesk.MyDeskPendingDTO;
import com.noosh.app.commons.dto.order.OrderInvoiceMyDeskDTO;
import com.noosh.app.commons.dto.order.OrderPendingMyDeskDTO;
import com.noosh.app.commons.dto.order.OrderStatusMyDeskDTO;
import com.noosh.app.commons.dto.order.OrderSupplierMyDeskDTO;
import com.noosh.app.commons.entity.account.AccountUser;
import com.noosh.app.commons.vo.mydesk.*;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.repository.jpa.account.AccountUserRepository;
import com.noosh.app.repository.mybatis.order.OrderMyDeskMyBatisMapper;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.security.PermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 3/25/24
 */
@Service
public class MyDeskService {

    @Autowired
    private OrderMyDeskMyBatisMapper orderMyDeskMyBatisMapper;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private AccountUserRepository accountUserRepository;

    public List<OrderStatusMyDeskDTO> findOrderWithStatus(Long workgroupId, Long userId, boolean isMyProject) {
        if ((!permissionService.checkAll(PermissionID.VIEW_ORDER, workgroupId, userId, Long.valueOf(-1)))
                && (!isMyProject)) {
            return null;
        }
        List<OrderStatusMyDeskDTO> orderStatusMyDeskDTOs = orderMyDeskMyBatisMapper.findOrderWithStatus(workgroupId, userId, isMyProject);
        return orderStatusMyDeskDTOs;
    }

    public List<OrderSupplierMyDeskDTO> findOrderWithSupplier(Long workgroupId, Long userId, boolean isMyProject,
                                                              Long dateRange, String dateType) {
        if ((!permissionService.checkAll(PermissionID.VIEW_ORDER, workgroupId, userId, Long.valueOf(-1)))
                && (!isMyProject)) {
            return null;
        }
        List<OrderSupplierMyDeskDTO> orderSupplierMyDeskDTOs =
                orderMyDeskMyBatisMapper.findOrderWithSupplier(workgroupId, userId, isMyProject, dateRange, dateType);
        return orderSupplierMyDeskDTOs;
    }

    public List<OrderInvoiceMyDeskDTO> findOrderWithInvoice(Long workgroupId, Long userId, boolean isMyProject, Long dateRange, Long invoiceType) {
        if ((!permissionService.checkAll(PermissionID.VIEW_ORDER, workgroupId, userId, Long.valueOf(-1)))
                && (!isMyProject)) {
            return null;
        }
        List<OrderInvoiceMyDeskDTO> orderInvoiceMyDeskDTOs =
                orderMyDeskMyBatisMapper.findOrderWithInvoice(workgroupId, userId, isMyProject,
                        invoiceType != null && invoiceType == 1L, invoiceType != null && invoiceType == 2L, dateRange);
        return orderInvoiceMyDeskDTOs;
    }

    public MyDeskPendingDTO findPendingOrDueDeskoid(Long workgroupId, Long userId, boolean isMyProject, Long dateRange, boolean isOverdue, boolean isDue, boolean isPending) throws Exception {
        AccountUser user = accountUserRepository.findById(userId).orElse(null);
        if (user == null) {
            throw new NotFoundException("Can't fins the user with user id: " + userId);
        }
        String timezoneCode = user.getPerson().getTimeZone().getTimezoneCode();
        MyDeskPendingDTO myDeskPendingDTO = new MyDeskPendingDTO();
        if (((permissionService.checkAll(PermissionID.VIEW_ORDER, workgroupId, userId, Long.valueOf(-1)))
                && (!isMyProject)) || isMyProject) {
            myDeskPendingDTO.setCanViewOrders(true);
            List<OrderPendingMyDeskDTO> orderPendingMyDeskDTOs =
                    orderMyDeskMyBatisMapper.findOrderPending(workgroupId, userId, isMyProject, isOverdue, isDue, isPending,
                            isDue ? getStartDatetime(timezoneCode, 0) : getStartDatetime(timezoneCode, 0 - dateRange),
                            isOverdue ? getEndDatetime(timezoneCode, -1) : (isDue ? getEndDatetime(timezoneCode, 0 + dateRange) : getEndDatetime(timezoneCode, 0)));
            List<OrderPendingMyDeskDTO> changeOrderPendingMyDeskDTOs =
                    orderMyDeskMyBatisMapper.findChangeOrderPending(workgroupId, userId, isMyProject, isOverdue, isDue, isPending,
                            isDue ? getStartDatetime(timezoneCode, 0) : getStartDatetime(timezoneCode, 0 - dateRange),
                            isOverdue ? getEndDatetime(timezoneCode, -1) : (isDue ? getEndDatetime(timezoneCode, 0 + dateRange) : getEndDatetime(timezoneCode, 0)));
            myDeskPendingDTO.setChangeOrders(changeOrderPendingMyDeskDTOs);
            myDeskPendingDTO.setOrders(orderPendingMyDeskDTOs);

        }
        if (((permissionService.checkAll(PermissionID.VIEW_INVOICE, workgroupId, userId, Long.valueOf(-1)))
                && (!isMyProject)) || isMyProject) {
            myDeskPendingDTO.setCanViewInvoices(true);
            List<InvoicePendingMyDeskDTO> invoicePendingMyDeskDTOs =
                    orderMyDeskMyBatisMapper.findInvoicePending(workgroupId, userId, isMyProject, isOverdue, isDue, isPending,
                            isDue ? getStartDatetime(timezoneCode, 0) : getStartDatetime(timezoneCode, 0 - dateRange),
                            isOverdue ? getEndDatetime(timezoneCode, -1) : (isDue ? getEndDatetime(timezoneCode, 0 + dateRange) : getEndDatetime(timezoneCode, 0)));
            myDeskPendingDTO.setInvoices(invoicePendingMyDeskDTOs);
        }

        return myDeskPendingDTO;
    }

    public MyDeskOrderSupplierQueryFilterVO getOrderBySupplierWidgetFilter(long workgroupId, long userId) throws Exception {
        Map<String, String> prefs = preferenceService.findUserPrefs(workgroupId, userId);
        MyDeskOrderSupplierQueryFilter filter = new MyDeskOrderSupplierQueryFilter();

        String dateRangeOption = preferenceService.getString(
                MyDeskOrderSupplierQueryFilter.REACT_MY_DESK_DESKOID_ORDER_SUPPLIER_FILTER_PREF_PREFIX
                        + MyDeskOrderSupplierQueryFilter.CONTROLNAME_ORDER_SUPPLIER_DATE_RANGE_OPTION, prefs,
                "" + MyDeskOrderSupplierQueryFilter.THIS_MONTH);
        filter.setDateRange(Long.parseLong(dateRangeOption));

        String dateTypeOption = preferenceService.getString(
                MyDeskOrderSupplierQueryFilter.REACT_MY_DESK_DESKOID_ORDER_SUPPLIER_FILTER_PREF_PREFIX
                        + MyDeskOrderSupplierQueryFilter.CONTROLNAME_ORDER_SUPPLIER_DATE_TYPE_OPTION, prefs,
                MyDeskOrderSupplierQueryFilter.ACCEPTED_DATE);
        filter.setDateType(dateTypeOption);

        MyDeskOrderSupplierQueryFilterVO vo = new MyDeskOrderSupplierQueryFilterVO();
        vo.setFilter(filter);

        return vo;
    }

    public void updateOrderSupplierFilter(Long workgroupId, Long userId, MyDeskOrderSupplierQueryFilter filter) throws Exception {
        Map<String, String> prefs = new HashMap<>();

        prefs.put(MyDeskOrderSupplierQueryFilter.REACT_MY_DESK_DESKOID_ORDER_SUPPLIER_FILTER_PREF_PREFIX
                + MyDeskOrderSupplierQueryFilter.CONTROLNAME_ORDER_SUPPLIER_DATE_TYPE_OPTION,
                filter.getDateType() != null ? filter.getDateType() : MyDeskOrderSupplierQueryFilter.ACCEPTED_DATE);

        prefs.put(MyDeskOrderSupplierQueryFilter.REACT_MY_DESK_DESKOID_ORDER_SUPPLIER_FILTER_PREF_PREFIX
                + MyDeskOrderSupplierQueryFilter.CONTROLNAME_ORDER_SUPPLIER_DATE_RANGE_OPTION,
                filter.getDateRange() != null ? String.valueOf(filter.getDateRange()) : "" + MyDeskOrderSupplierQueryFilter.THIS_MONTH);
        preferenceService.saveUserPreference(workgroupId, userId, prefs);
    }

    public MyDeskOrderInvoiceQueryFilterVO getOrderInvoiceWidgetFilter(long workgroupId, long userId) throws Exception {
        Map<String, String> prefs = preferenceService.findUserPrefs(workgroupId, userId);
        MyDeskOrderInvoiceQueryFilter filter = new MyDeskOrderInvoiceQueryFilter();

        String dateRangeOption = preferenceService.getString(
                MyDeskOrderInvoiceQueryFilter.REACT_MY_DESK_DESKOID_ORDER_INVOICE_FILTER_PREF_PREFIX
                        + MyDeskOrderInvoiceQueryFilter.CONTROLNAME_ORDER_INVOICE_DATE_RANGE_OPTION, prefs,
                "" + MyDeskOrderInvoiceQueryFilter.THIS_MONTH);
        filter.setDateRange(Long.parseLong(dateRangeOption));

        String invoiceOption = preferenceService.getString(
                MyDeskOrderInvoiceQueryFilter.REACT_MY_DESK_DESKOID_ORDER_INVOICE_FILTER_PREF_PREFIX
                        + MyDeskOrderInvoiceQueryFilter.CONTROLNAME_ORDER_INVOICE_OPTION, prefs,
                "" + MyDeskOrderInvoiceQueryFilter.BUY_INVOICE);
        filter.setInvoiceType(Long.parseLong(invoiceOption));

        MyDeskOrderInvoiceQueryFilterVO vo = new MyDeskOrderInvoiceQueryFilterVO();
        vo.setFilter(filter);

        return vo;
    }

    public void updateOrderInvoiceFilter(Long workgroupId, Long userId, MyDeskOrderInvoiceQueryFilter filter) throws Exception {
        Map<String, String> prefs = new HashMap<>();

        prefs.put(MyDeskOrderInvoiceQueryFilter.REACT_MY_DESK_DESKOID_ORDER_INVOICE_FILTER_PREF_PREFIX
                + MyDeskOrderInvoiceQueryFilter.CONTROLNAME_ORDER_INVOICE_DATE_RANGE_OPTION,
                filter.getDateRange() != null ? String.valueOf(filter.getDateRange()) : "" + MyDeskOrderInvoiceQueryFilter.THIS_MONTH);

        preferenceService.saveUserPreference(workgroupId, userId, prefs);
    }

    public MyDeskPendingQueryFilterVO getPendingWidgetFilter(long workgroupId, long userId) throws Exception {
        Map<String, String> prefs = preferenceService.findUserPrefs(workgroupId, userId);
        MyDeskPendingQueryFilter filter = new MyDeskPendingQueryFilter();

        String dateRangeOption = preferenceService.getString(
                MyDeskPendingQueryFilter.REACT_MY_DESK_DESKOID_PENDING_FILTER_PREF_PREFIX
                        + MyDeskPendingQueryFilter.CONTROLNAME_PENDING_DATE_RANGE_OPTION, prefs,
                "" + MyDeskPendingQueryFilter.SEVEN_DAYS);
        filter.setDateRange(Long.parseLong(dateRangeOption));

        MyDeskPendingQueryFilterVO vo = new MyDeskPendingQueryFilterVO();
        vo.setFilter(filter);

        return vo;
    }

    public void updatePendingFilter(Long workgroupId, Long userId, MyDeskPendingQueryFilter filter) throws Exception {
        Map<String, String> prefs = new HashMap<>();

        prefs.put(MyDeskPendingQueryFilter.REACT_MY_DESK_DESKOID_PENDING_FILTER_PREF_PREFIX
                + MyDeskPendingQueryFilter.CONTROLNAME_PENDING_DATE_RANGE_OPTION,
                filter.getDateRange() != null ? String.valueOf(filter.getDateRange()) : "" + MyDeskPendingQueryFilter.SEVEN_DAYS);

        preferenceService.saveUserPreference(workgroupId, userId, prefs);
    }

    private static String getStartDatetime(String userTimezoneCode, long daysToAdd) {
        ZonedDateTime utcTime = ZonedDateTime.now(ZoneId.of("UTC"));
        ZonedDateTime userTime = utcTime.withZoneSameInstant(ZoneId.of(userTimezoneCode));
        ZonedDateTime zstart = userTime.toLocalDate().plusDays(daysToAdd).atTime(0, 0).atZone(ZoneId.of(userTimezoneCode));
        LocalDateTime utcStart = zstart.withZoneSameInstant(ZoneId.of("UTC")).toLocalDateTime();
        DateTimeFormatter datetimeFormat = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm");
        return  utcStart.format(datetimeFormat);
    }

    private static String getEndDatetime(String userTimezoneCode, long daysToAdd) {
        ZonedDateTime utcTime = ZonedDateTime.now(ZoneId.of("UTC"));
        ZonedDateTime userTime = utcTime.withZoneSameInstant(ZoneId.of(userTimezoneCode));
        ZonedDateTime zend = userTime.toLocalDate().plusDays(daysToAdd).atTime(23, 59).atZone(ZoneId.of(userTimezoneCode));
        LocalDateTime utcEnd = zend.withZoneSameInstant(ZoneId.of("UTC")).toLocalDateTime();
        DateTimeFormatter datetimeFormat = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm");
        return  utcEnd.format(datetimeFormat);
    }

    public void updateFilter(Long workgroupId, Long userId, MyDeskOrderQueryFilter filter) throws Exception {
        Map<String, String> prefs = new HashMap<>();

        if (filter.getSupplierDateType() != null) {
            prefs.put(MyDeskOrderSupplierQueryFilter.REACT_MY_DESK_DESKOID_ORDER_SUPPLIER_FILTER_PREF_PREFIX
                            + MyDeskOrderSupplierQueryFilter.CONTROLNAME_ORDER_SUPPLIER_DATE_TYPE_OPTION, filter.getSupplierDateType());
        }

        if (filter.getSupplierDateRange() != null) {
            prefs.put(MyDeskOrderSupplierQueryFilter.REACT_MY_DESK_DESKOID_ORDER_SUPPLIER_FILTER_PREF_PREFIX
                            + MyDeskOrderSupplierQueryFilter.CONTROLNAME_ORDER_SUPPLIER_DATE_RANGE_OPTION, String.valueOf(filter.getSupplierDateRange()));
        }

        if (filter.getPendingDateRange() != null) {
            prefs.put(MyDeskPendingQueryFilter.REACT_MY_DESK_DESKOID_PENDING_FILTER_PREF_PREFIX
                            + MyDeskPendingQueryFilter.CONTROLNAME_PENDING_DATE_RANGE_OPTION, String.valueOf(filter.getPendingDateRange()));
        }

        if (filter.getInvoiceDateRange() != null) {
            prefs.put(MyDeskOrderInvoiceQueryFilter.REACT_MY_DESK_DESKOID_ORDER_INVOICE_FILTER_PREF_PREFIX
                            + MyDeskOrderInvoiceQueryFilter.CONTROLNAME_ORDER_INVOICE_DATE_RANGE_OPTION, String.valueOf(filter.getInvoiceDateRange()));
        }

        if (filter.getInvoiceType() != null) {
            prefs.put(MyDeskOrderInvoiceQueryFilter.REACT_MY_DESK_DESKOID_ORDER_INVOICE_FILTER_PREF_PREFIX
                    + MyDeskOrderInvoiceQueryFilter.CONTROLNAME_ORDER_INVOICE_OPTION, String.valueOf(filter.getInvoiceType()));
        }

        preferenceService.saveUserPreference(workgroupId, userId, prefs);
    }
}
