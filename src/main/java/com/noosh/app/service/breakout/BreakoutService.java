package com.noosh.app.service.breakout;

import com.noosh.app.commons.dto.breakout.BreakoutDTO;
import com.noosh.app.commons.dto.breakout.BreakoutTypeDTO;
import com.noosh.app.commons.dto.breakout.SpecBreakoutDTO;
import com.noosh.app.commons.entity.breakout.Breakout;
import com.noosh.app.commons.entity.estimate.EstimateItemPrice;
import com.noosh.app.commons.vo.breakout.BreakoutTypeVO;
import com.noosh.app.commons.vo.breakout.BreakoutVO;
import com.noosh.app.commons.vo.breakout.SpecBreakoutTypeVO;
import com.noosh.app.mapper.breakout.BreakoutMapper;
import com.noosh.app.mapper.breakout.BreakoutTypeMapper;
import com.noosh.app.repository.mybatis.breakouttype.BreakoutTypeMyBatisMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class BreakoutService {
    private final Logger log = LoggerFactory.getLogger(BreakoutService.class);

    @Inject
    private BreakoutTypeMyBatisMapper breakoutTypeMyBatisMapper;
    @Inject
    private BreakoutTypeMapper breakoutTypeMapper;
    @Inject
    private BreakoutMapper breakoutMapper;

    public List<SpecBreakoutTypeVO> getBreakoutTypes(Long supplierWgId, List<SpecBreakoutDTO> specBreakoutDTOS) throws Exception {
        List<SpecBreakoutTypeVO> specBreakoutTypeVOS = new ArrayList<>();
        if (specBreakoutDTOS != null && specBreakoutDTOS.size() > 0) {
            for (SpecBreakoutDTO specBreakoutDTO : specBreakoutDTOS) {
                Long specTypeId = specBreakoutDTO.getSpecTypeId();
                Long breakoutTypeId = specBreakoutDTO.getBreakoutTypeId();
                List<BreakoutTypeDTO> breakoutTypeDTOS = breakoutTypeMyBatisMapper.findAllWithRootBreakoutTypeIdAndSpecTypeIdAndSupplierGroupId(breakoutTypeId, specTypeId, supplierWgId);
                Map<Long,BreakoutTypeDTO> typeMap = breakoutTypeDTOS.stream().filter(bt -> bt.getParentTypeId() != null && bt.getParentTypeId() > 0).collect(Collectors.toMap(BreakoutTypeDTO::getId, BreakoutTypeDTO->BreakoutTypeDTO));
                buildBreakoutTypeTree(breakoutTypeDTOS, typeMap);
                List<BreakoutTypeVO> breakoutTypeVOS = breakoutTypeDTOS.stream().filter(bt -> bt.getLevel() == 1).map(breakoutTypeMapper::toVOWithDescendents).collect(Collectors.toList());
                SpecBreakoutTypeVO specBreakoutTypeVO = new SpecBreakoutTypeVO();
                specBreakoutTypeVO.setSpecTypeId(specTypeId);
                specBreakoutTypeVO.setBreakoutTypes(breakoutTypeVOS);
                specBreakoutTypeVOS.add(specBreakoutTypeVO);
            }
        }
        return specBreakoutTypeVOS;
    }

    public void buildBreakoutTypeTree(List<BreakoutTypeDTO> breakoutTypeDTOS, Map<Long,BreakoutTypeDTO> typeMap) {
        for (BreakoutTypeDTO breakoutTypeDTO : breakoutTypeDTOS) {
            breakoutTypeDTO.setLevel(breakoutTypeDTO.getLevel() - 1);
            BreakoutTypeDTO parentType = typeMap.get(breakoutTypeDTO.getParentTypeId());
            if (parentType != null){
                parentType.addToDescendents(breakoutTypeDTO);
            }
        }
    }

    public List<BreakoutVO> getBreakouts(EstimateItemPrice itemPrice) {
        if (itemPrice == null) {
            return null;
        }
        List<Breakout> breakouts = itemPrice.getItemOption().getBreakouts();
        if (breakouts != null && breakouts.size() > 0) {
            List<BreakoutDTO> dtos = breakouts.stream().filter(b -> b.getNestingLevel() == (long) 1)
                    .map(b -> breakoutMapper.toDTO(b, breakouts, 1))
                    .collect(Collectors.toList());
            return dtos.stream()
                    .map(breakoutMapper::toVO)
                    .collect(Collectors.toList());
        }
        return null;
    }

}
