package com.noosh.app.service.routing;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.OrderTypeID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.account.AccountUserDTO;
import com.noosh.app.commons.dto.order.ChangeOrderVersionDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.dto.routing.RoutingSlipDTO;
import com.noosh.app.commons.entity.account.AccountUser;
import com.noosh.app.commons.entity.order.OrderItem;
import com.noosh.app.commons.entity.order.OrderVersion;
import com.noosh.app.commons.entity.routing.RoutingSlip;
import com.noosh.app.commons.entity.routing.SpendingLimit;
import com.noosh.app.mapper.OrderVersionMapper;
import com.noosh.app.mapper.RoutingSlipMapper;
import com.noosh.app.mapper.account.AccountUserMapper;
import com.noosh.app.repository.jpa.account.AccountUserRepository;
import com.noosh.app.repository.jpa.order.OrderItemRepository;
import com.noosh.app.repository.jpa.order.OrderVersionRepository;
import com.noosh.app.repository.routing.RoutingSlipRepository;
import com.noosh.app.repository.routing.SpendingLimitRepository;
import com.noosh.app.service.preference.PreferenceService;
import jakarta.inject.Inject;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class RoutingService {
    @Inject
    private RoutingSlipRepository routingSlipRepository;
    @Inject
    private RoutingSlipMapper routingSlipMapper;
    @Inject
    private PreferenceService preferenceService;
    @Inject
    private SpendingLimitRepository spendingLimitRepository;
    @Inject
    private AccountUserRepository accountUserRepository;
    @Inject
    private AccountUserMapper accountUserMapper;
    @Inject
    private OrderVersionRepository orderVersionRepository;
    @Inject
    private OrderItemRepository orderItemRepository;
    @Inject
    private OrderVersionMapper orderVersionMapper;

    public boolean requiresQuickApproval(OrderVersionDTO orderVersionDTO, Map<String, String> buyerWorkgroupPreference) {
        boolean retVal = false;

        if (orderVersionDTO.getIsGenerated()) {
            return false;
        }

        // no need to approve if the sell order is to offline buyer
        ProjectDTO project = orderVersionDTO.getParent();
        if (project.isOutsourcerProject() && orderVersionDTO.hasOfflineBuyer()){
            return false;
        }
        // check to see if the quick order approval is enabled
        if (orderVersionDTO.isClosingChangeOrder() && preferenceService.check(PreferenceID.PS_ENABLE_CCO_COST_LEVELS_APPROVERS, buyerWorkgroupPreference)){
            return false;
        }

        // check to see if the quick order approval is enabled
        if (preferenceService.check(PreferenceID.PS_QUICK_ORDER_APPROVAL, buyerWorkgroupPreference) && orderVersionDTO.isQuickOrder()) {
            return true;
        }
        return retVal;
    }

    public boolean requiresApproval(OrderVersionDTO orderVersionDTO, Map<String, String> buyerWorkgroupPreference) {
        RoutingSlipDTO routingSlipBean;
        if (orderVersionDTO.getRoutingSlip() == null) {
            RoutingSlip routingSlip = routingSlipRepository.findFirstByObjectIdAndObjectClassIdAndAcWorkgroupIdOrderByModDateDesc(
                    orderVersionDTO.getId(), ObjectClassID.ORDER, orderVersionDTO.getBuyerWorkgroupId());
            routingSlipBean = routingSlipMapper.toDTO(routingSlip);
            orderVersionDTO.setRoutingSlip(routingSlipBean);
        } else {
            routingSlipBean = orderVersionDTO.getRoutingSlip();
        }

        if (routingSlipBean != null && (routingSlipBean.getApprovedCount() > 0 || routingSlipBean.getDisapprovedCount() > 0)) {
            orderVersionDTO.setRequiresSequentialRouting(routingSlipBean.getIsSequentialApprovals() != null
                    && routingSlipBean.getIsSequentialApprovals().shortValue() == (short) 1);
            orderVersionDTO.setIsApproved(routingSlipBean != null && routingSlipBean.getApprovedCount() > 0 && routingSlipBean.getDisapprovedCount() == 0);
            orderVersionDTO.setIsDisapproved(routingSlipBean != null && routingSlipBean.getDisapprovedCount() > 0 && routingSlipBean.getApprovedCount() == 0);
            orderVersionDTO.setIsManagerApproved(routingSlipBean.getIsManagerApproved());
        }

        if (orderVersionDTO.getIsGenerated()) {
            orderVersionDTO.setRequiresApproval(false);
            return false;
        }

        ProjectDTO project = orderVersionDTO.getParent();
        if (project.isOutsourcerProject() && orderVersionDTO.hasOfflineBuyer()) {
            orderVersionDTO.setRequiresApproval(false);
            return false;
        }

        // check to see if approval routing is enabled
        //TODO NENT-496 UHG: Approval logic for Invoice Adjustment Orders
        boolean enAbleRoutingTable = preferenceService.check(PreferenceID.PS_ENABLE_ROUNTING_TABLE, buyerWorkgroupPreference);
        if (!preferenceService.check(PreferenceID.WORKGROUP_OPTION_APPROVALS_ROUTING, buyerWorkgroupPreference)) {
            orderVersionDTO.setRequiresApproval(Boolean.FALSE);
            return false;
        }

        if (preferenceService.check(PreferenceID.PS_ROUTE_CLOSING_CHANGE_ORDERS_ONLY, buyerWorkgroupPreference) && orderVersionDTO.isChangeOrder() && !orderVersionDTO.isClosingChangeOrder()) {
            orderVersionDTO.setRequiresApproval(Boolean.FALSE);
            return false;
        }

        boolean retval;
        double orderGrandTotal = orderVersionDTO.getGrandTotal().doubleValue();

        boolean isExcludeTaxShipping = preferenceService.check(PreferenceID.PS_ROUTE_CCO_EXCLUDE_TAX_SHIPPING, buyerWorkgroupPreference);
        if (isExcludeTaxShipping) {
            orderGrandTotal = orderVersionDTO.getOrderItemTotal().doubleValue();
        }

        String ignoreTheseSupplierUserIdsStr = buyerWorkgroupPreference.get(PreferenceID.PS_IGNORE_SUPPLIERS) != null ?
                buyerWorkgroupPreference.get(PreferenceID.PS_IGNORE_SUPPLIERS).trim() : "";
        if (!"".equals(ignoreTheseSupplierUserIdsStr)){
            String[] ignoreSuppliers = ignoreTheseSupplierUserIdsStr.split(",");
            for (int i = 0; i < ignoreSuppliers.length; i++) {
                long ignoreSupplierId = ignoreSuppliers[i] != null && ignoreSuppliers[i].length() > 0 ? Long.parseLong(ignoreSuppliers[i]) : -1;
                if (orderVersionDTO.getSupplierUserId() == ignoreSupplierId) {
                    orderVersionDTO.setRequiresApproval(false);
                    return false;
                }
            }
        }

        // check spending limits for max authorization
        if (preferenceService.check(PreferenceID.WORKGROUP_OPTION_SPENDING_LIMIT, buyerWorkgroupPreference)) {
            SpendingLimit spendingLimitBean = spendingLimitRepository.findByWorkgroupIdAndUserId(
                    orderVersionDTO.getBuyerWorkgroupId(), orderVersionDTO.getBuyerUserId());
            if (spendingLimitBean != null && spendingLimitBean.getAmount() != null && orderGrandTotal <= spendingLimitBean.getAmount().doubleValue()) {
                orderVersionDTO.setRequiresApproval(false);
                return false;
            }
        }

        //Restrict the quantity and total no change routing case to pending acceptance
        if ((orderVersionDTO.isPendingSupplierAcceptance() || orderVersionDTO.isPendingBuyerAcceptance()) && preferenceService.check(PreferenceID.PS_ROUTE_PREVENT_NO_CHANGE_ROUTING, buyerWorkgroupPreference)) {
            long versionNumber = orderVersionDTO.getVersion();
            if (versionNumber > 1) {
                OrderVersion preOrder = orderVersionRepository.findByOrderIdAndVersion(orderVersionDTO.getOrderId(), orderVersionDTO.getVersion() - 1);
                if (preOrder != null) {
                    List<OrderItem> orderItems = orderItemRepository.findByOrderVersionIdOrderByItemIndex(preOrder.getId());
                    OrderVersionDTO preOrderDTO = orderVersionMapper.toSimpleDTO(preOrder, orderItems);
                    //compare order quantity and price
                    double preOrderGrandTotal = preOrderDTO.getGrandTotal().doubleValue();
                    if (isExcludeTaxShipping) {
                        preOrderGrandTotal = preOrderDTO.getOrderItemTotal().doubleValue();
                    }
                    double preOrderQuantity = preOrderDTO.getOrderTotalQuantity().doubleValue();
                    double orderQuantity = orderVersionDTO.getOrderTotalQuantity().doubleValue();
                    if ((preOrderGrandTotal == orderGrandTotal) && (preOrderQuantity == orderQuantity)) {
                        orderVersionDTO.setRequiresApproval(false);
                        return false;
                    }
                }
            }
        }

        //NKB144427 support quick approval
        //check to see if the quick order approval is enabled
        //TODO NENT-496 UHG: Approval logic for Invoice Adjustment Orders
        if (preferenceService.check(PreferenceID.PS_QUICK_ORDER_APPROVAL, buyerWorkgroupPreference) && orderVersionDTO.isQuickOrder()) {
            retval = isQuickApproval(buyerWorkgroupPreference, orderVersionDTO);
        } else {
            retval = isPresentAtCostLevel(buyerWorkgroupPreference, orderGrandTotal, orderVersionDTO);
        }

        if (retval) {
            if (routingSlipBean != null && (routingSlipBean.getApprovedCount() > 0 || routingSlipBean.getDisapprovedCount() > 0)) {
                if (routingSlipBean.getIsSequentialApprovals() != null && routingSlipBean.getIsSequentialApprovals().shortValue() == (short) 1
                        && (!routingSlipBean.getIsAllRecipientsApprovedNoChanges())) {
                    orderVersionDTO.setRequiresApproval(true);
                    return true;
                } else {
                    orderVersionDTO.setRequiresApproval(false);
                    return false;
                }
            }
        }
        orderVersionDTO.setRequiresApproval(retval);
        return retval;
    }

    public boolean requiresManagerApproval(OrderVersionDTO orderVersionDTO, Map<String, String> buyerWorkgroupPreference) {
        if (orderVersionDTO.getIsGenerated()) {
            orderVersionDTO.setRequiresManagerApproval(false);
            return false;
        }

        //if (!this.isUserBuyer())
        //    return false;
        // no need to approve if the sell order is to offline buyer
        ProjectDTO project = orderVersionDTO.getParent();
        if (project.isOutsourcerProject() && orderVersionDTO.hasOfflineBuyer()) {
            orderVersionDTO.setRequiresManagerApproval(false);
            return false;
        }

        boolean retval = false;
        double orderGrandTotal = orderVersionDTO.getGrandTotal().doubleValue();

        boolean isExcludeTaxShipping = preferenceService.check(PreferenceID.PS_ROUTE_CCO_EXCLUDE_TAX_SHIPPING, buyerWorkgroupPreference);
        if (isExcludeTaxShipping) {
            orderGrandTotal = orderVersionDTO.getOrderItemTotal().doubleValue();
        }
        // check to see if approval routing is enabled
        if (!preferenceService.check(PreferenceID.WORKGROUP_OPTION_APPROVALS_ROUTING, buyerWorkgroupPreference)) {
            orderVersionDTO.setRequiresManagerApproval(false);
            return false;
        }

        // check to see if approval routing is enabled
        if (!preferenceService.check(PreferenceID.PS_REQUIRE_MANAGER_APPROVAL, buyerWorkgroupPreference)) {
            orderVersionDTO.setRequiresManagerApproval(false);
            return false;
        }

        //NKB145609
        if (preferenceService.check(PreferenceID.PS_BYPASS_MANAGER_APPROVAL_FROM_ESTIMATE, buyerWorkgroupPreference)) {
            boolean isOrderFromEstimate = orderVersionDTO.getOrderTypeId() == OrderTypeID.ORDER;
            if (isOrderFromEstimate) {
                orderVersionDTO.setRequiresManagerApproval(false);
                return false;
            }
        }

        String ignoreTheseSupplierUserIdsStr = buyerWorkgroupPreference.get(PreferenceID.PS_IGNORE_SUPPLIERS) != null ?
                buyerWorkgroupPreference.get(PreferenceID.PS_IGNORE_SUPPLIERS).trim() : "";
        if (!"".equals(ignoreTheseSupplierUserIdsStr)) {
            String[] ignoreSuppliers = ignoreTheseSupplierUserIdsStr.split(",");
            for (int i = 0; ignoreSuppliers != null && i < ignoreSuppliers.length; i++) {
                long ignoreSupplierId = ignoreSuppliers[i] != null && ignoreSuppliers[i].length() > 0 ? Long.parseLong(ignoreSuppliers[i]) : -1;
                if (orderVersionDTO.getSupplierUserId() == ignoreSupplierId) {
                    orderVersionDTO.setRequiresManagerApproval(false);
                    return false;
                }
            }
        }

        //Not sure if we need spending limits
        // check spending limits for max authorization
        if (preferenceService.check(PreferenceID.WORKGROUP_OPTION_SPENDING_LIMIT, buyerWorkgroupPreference)) {
            SpendingLimit spendingLimitBean = spendingLimitRepository.findByWorkgroupIdAndUserId(
                    orderVersionDTO.getBuyerWorkgroupId(), orderVersionDTO.getBuyerUserId());
            if (spendingLimitBean != null && spendingLimitBean.getAmount() != null && orderGrandTotal <= spendingLimitBean.getAmount().doubleValue()) {
                orderVersionDTO.setRequiresManagerApproval(false);
                return false;
            }
        }

        // check to see if the quick order approval is enabled
        if (preferenceService.check(PreferenceID.PS_QUICK_ORDER_APPROVAL, buyerWorkgroupPreference) && orderVersionDTO.isQuickOrder()) {
            orderVersionDTO.setRequiresManagerApproval(false);
            return false;
        }

        //Restrict the quantity and total no change routing case to pending acceptance
        if ((orderVersionDTO.isPendingSupplierAcceptance() || orderVersionDTO.isPendingBuyerAcceptance()) && preferenceService.check(PreferenceID.PS_ROUTE_PREVENT_NO_CHANGE_ROUTING, buyerWorkgroupPreference)) {
            long versionNumber = orderVersionDTO.getVersion();
            if (versionNumber > 1) {
                OrderVersion preOrder = orderVersionRepository.findByOrderIdAndVersion(orderVersionDTO.getOrderId(), orderVersionDTO.getVersion() - 1);
                if (preOrder != null) {
                    List<OrderItem> orderItems = orderItemRepository.findByOrderVersionIdOrderByItemIndex(preOrder.getId());
                    OrderVersionDTO preOrderDTO = orderVersionMapper.toSimpleDTO(preOrder, orderItems);
                    //compare order quantity and price
                    double preOrderGrandTotal = preOrderDTO.getGrandTotal().doubleValue();
                    if (isExcludeTaxShipping) {
                        preOrderGrandTotal = preOrderDTO.getOrderItemTotal().doubleValue();
                    }
                    double preOrderQuantity = preOrderDTO.getOrderTotalQuantity().doubleValue();
                    double orderQuantity = orderVersionDTO.getOrderTotalQuantity().doubleValue();
                    if ((preOrderGrandTotal == orderGrandTotal) && (preOrderQuantity == orderQuantity)) {
                        orderVersionDTO.setRequiresApproval(false);
                        return false;
                    }
                }
            }
        }

        //TODO NENT-496 UHG: Approval logic for Invoice Adjustment Orders
        retval = isManagerPresentAtCostLevel(buyerWorkgroupPreference, orderGrandTotal, orderVersionDTO);

        if (retval) {
            RoutingSlip routingSlip = routingSlipRepository.findFirstByObjectIdAndObjectClassIdAndAcWorkgroupIdOrderByModDateDesc(
                    orderVersionDTO.getId(), ObjectClassID.ORDER,
                    orderVersionDTO.getBuyerWorkgroupId());
            RoutingSlipDTO routingSlipBean = routingSlipMapper.toDTO(routingSlip);
            if (routingSlipBean != null) {
                if (routingSlipBean.getIsDisapproved() || routingSlipBean.getIsManagerApprovals() == null
                        || routingSlipBean.getIsManagerApprovals().shortValue() == 0
                        || (routingSlipBean.getHasManagerApproval() != null && routingSlipBean.getHasManagerApproval() == 1))
                    retval = false;
            }
        }
        orderVersionDTO.setRequiresManagerApproval(retval);
        return retval;
    }

    /**
     * returns true if the order requires routing, need to work with requires approval
     * @param orderVersionDTO
     * @return
     */
    public boolean requiresRouting(OrderVersionDTO orderVersionDTO) {
        if (orderVersionDTO.getIsGenerated()) {
            orderVersionDTO.setRequiresRouting(false);
            return false;
        }

        if (!orderVersionDTO.isUserBuyer()) {
            orderVersionDTO.setRequiresRouting(false);
            return false;
        }

        boolean retval;
        RoutingSlipDTO routingSlipBean = null;
        if (orderVersionDTO.getRoutingSlip() == null) {
            RoutingSlip routingSlip = routingSlipRepository.findFirstByObjectIdAndObjectClassIdAndAcWorkgroupIdOrderByModDateDesc(
                    orderVersionDTO.getId(), ObjectClassID.ORDER, orderVersionDTO.getBuyerWorkgroupId());
            routingSlipBean = routingSlipMapper.toDTO(routingSlip);
            orderVersionDTO.setRoutingSlip(routingSlipBean);
        } else {
            routingSlipBean = orderVersionDTO.getRoutingSlip();
        }

        if (orderVersionDTO.getRequiresApproval() &&
                (routingSlipBean == null ||
                        (routingSlipBean != null && orderVersionDTO.getOrderState().getObjectStateId() != ObjectStateID.ORDER_PENDING_SUBMISSION)
                ))
            retval = true;
        else
            retval = false;

        orderVersionDTO.setRequiresRouting(retval);
        return retval;
    }

    /**
     * Determines if the order requires manager routing, need to work with require manager approval
     * @param orderVersionDTO
     * @param prefsMap
     * @return
     */
    public boolean requiresManagerRouting(OrderVersionDTO orderVersionDTO, Map<String, String> prefsMap) {
        if (orderVersionDTO.getIsGenerated()) {
            orderVersionDTO.setRequiresManagerRouting(false);
            return false;
        }

        if (!orderVersionDTO.isUserBuyer()) {
            orderVersionDTO.setRequiresManagerRouting(false);
            return false;
        }

        boolean retval;
        RoutingSlipDTO routingSlipBean = null;
        if (orderVersionDTO.getRoutingSlip() == null) {
            RoutingSlip routingSlip = routingSlipRepository.findFirstByObjectIdAndObjectClassIdAndAcWorkgroupIdOrderByModDateDesc(
                    orderVersionDTO.getId(), ObjectClassID.ORDER, orderVersionDTO.getBuyerWorkgroupId());
            routingSlipBean = routingSlipMapper.toDTO(routingSlip);
            orderVersionDTO.setRoutingSlip(routingSlipBean);
        } else {
            routingSlipBean = orderVersionDTO.getRoutingSlip();
        }
        if (orderVersionDTO.getRequiresManagerApproval() && (routingSlipBean == null ||
                (routingSlipBean != null &&
                        routingSlipBean.getIsManagerApprovals() != null && routingSlipBean.getIsManagerApprovals().shortValue() == (short) 1
                        && !(routingSlipBean.getIsManagerRoutingSlip() != null
                        && routingSlipBean.getIsManagerRoutingSlip().shortValue() == (short) 1) &&
                        !requiresApproval(orderVersionDTO, prefsMap)
                )))
            retval = true;
        else
            retval = false;

        orderVersionDTO.setRequiresManagerRouting(retval);
        return retval;
    }

    //change order part
    /**
     * Determines if the change order requires routing, need to work with requires approval
     * @param changeOrderVersionDTO
     * @return
     */
    public boolean requiresRouting(ChangeOrderVersionDTO changeOrderVersionDTO)
    {
        OrderVersionDTO orderVersionDTO = changeOrderVersionDTO.getChangeOrder();
        OrderVersionDTO parentOrder = changeOrderVersionDTO.getParentOrder();

        if (parentOrder.getIsGenerated() || orderVersionDTO.isRejected()) {
            orderVersionDTO.setRequiresRouting(false);
            return false;
        }

        if (!orderVersionDTO.isUserBuyer()) {
            orderVersionDTO.setRequiresRouting(false);
            return false;
        }

        boolean retval;
        RoutingSlipDTO routingSlipBean = null;
        if (orderVersionDTO.getRoutingSlip() == null) {
            RoutingSlip routingSlip = routingSlipRepository.findFirstByObjectIdAndObjectClassIdAndAcWorkgroupIdOrderByModDateDesc(
                    orderVersionDTO.getId(), ObjectClassID.ORDER, orderVersionDTO.getBuyerWorkgroupId());
            routingSlipBean = routingSlipMapper.toDTO(routingSlip);
            orderVersionDTO.setRoutingSlip(routingSlipBean);
        } else {
            routingSlipBean = orderVersionDTO.getRoutingSlip();
        }

        if (orderVersionDTO.getRequiresApproval() && routingSlipBean == null)
            retval = true;
        else
            retval = false;

        orderVersionDTO.setRequiresRouting(retval);
        return orderVersionDTO.getRequiresRouting();
    }

    /**
     * Determines if the change order requires manager routing, need to work with require manager approval
     * @param changeOrderVersionDTO
     * @param buyerWorkgroupPreference
     * @return
     */
    public boolean requiresManagerRouting(ChangeOrderVersionDTO changeOrderVersionDTO, Map<String, String> buyerWorkgroupPreference)
    {
        OrderVersionDTO orderVersionDTO = changeOrderVersionDTO.getChangeOrder();

        if (orderVersionDTO.getIsGenerated() || orderVersionDTO.isRejected()) {
            orderVersionDTO.setRequiresManagerRouting(false);
            return false;
        }

        if (!orderVersionDTO.isUserBuyer()) {
            orderVersionDTO.setRequiresManagerRouting(false);
            return false;
        }

        boolean retval;
        RoutingSlipDTO routingSlipBean = null;
        if (orderVersionDTO.getRoutingSlip() == null) {
            RoutingSlip routingSlip = routingSlipRepository.findFirstByObjectIdAndObjectClassIdAndAcWorkgroupIdOrderByModDateDesc(
                    orderVersionDTO.getId(), ObjectClassID.ORDER, orderVersionDTO.getBuyerWorkgroupId());
            routingSlipBean = routingSlipMapper.toDTO(routingSlip);
            orderVersionDTO.setRoutingSlip(routingSlipBean);
        } else {
            routingSlipBean = orderVersionDTO.getRoutingSlip();
        }

        if (orderVersionDTO.getRequiresManagerApproval() && (routingSlipBean == null ||
                (routingSlipBean != null &&
                        routingSlipBean.getIsManagerApprovals() != null && routingSlipBean.getIsManagerApprovals().shortValue() == (short) 1
                        && !(routingSlipBean.getIsManagerRoutingSlip() != null
                        && routingSlipBean.getIsManagerRoutingSlip().shortValue() == (short)1) &&
                        !requiresApproval(changeOrderVersionDTO, buyerWorkgroupPreference)
                )))
            retval = true;
        else
            retval = false;

        orderVersionDTO.setRequiresManagerRouting(retval);
        return retval;
    }

    public boolean requiresApproval(ChangeOrderVersionDTO changeOrderVersionDTO, Map<String, String> buyerWorkgroupPreference) {
        OrderVersionDTO orderVersionDTO = changeOrderVersionDTO.getChangeOrder();
        OrderVersionDTO parentOrder = changeOrderVersionDTO.getParentOrder();

        RoutingSlipDTO routingSlipBean;
        if (orderVersionDTO.getRoutingSlip() == null) {
            RoutingSlip routingSlip = routingSlipRepository.findFirstByObjectIdAndObjectClassIdAndAcWorkgroupIdOrderByModDateDesc(
                    orderVersionDTO.getId(), ObjectClassID.ORDER, orderVersionDTO.getBuyerWorkgroupId());
            routingSlipBean = routingSlipMapper.toDTO(routingSlip);
            orderVersionDTO.setRoutingSlip(routingSlipBean);
        } else {
            routingSlipBean = orderVersionDTO.getRoutingSlip();
        }

        if (routingSlipBean != null && (routingSlipBean.getApprovedCount() > 0 || routingSlipBean.getDisapprovedCount() > 0)) {
            orderVersionDTO.setRequiresSequentialRouting(routingSlipBean.getIsSequentialApprovals() != null
                    && routingSlipBean.getIsSequentialApprovals() == (short) 1);
            orderVersionDTO.setIsApproved(routingSlipBean != null && routingSlipBean.getApprovedCount() > 0 && routingSlipBean.getDisapprovedCount() == 0);
            orderVersionDTO.setIsDisapproved(routingSlipBean != null && routingSlipBean.getDisapprovedCount() > 0 && routingSlipBean.getApprovedCount() == 0);
            orderVersionDTO.setIsManagerApproved(routingSlipBean.getIsManagerApproved());
        }

        if (orderVersionDTO.getIsGenerated()) {
            orderVersionDTO.setRequiresApproval(false);
            return false;
        }
        //if (!this.isUserBuyer())
        //    return false;
        // no need to approve if the sell order is to offline buyer
        ProjectDTO project = orderVersionDTO.getParent();
        if (project.isOutsourcerProject() && orderVersionDTO.hasOfflineBuyer()) {
            orderVersionDTO.setRequiresApproval(false);
            return false;
        }

        boolean retval = false;
        boolean ignoreChangeOrdersExceptClosing = false;

        // check to see if approval routing is enabled
        if (!preferenceService.check(PreferenceID.WORKGROUP_OPTION_APPROVALS_ROUTING, buyerWorkgroupPreference)) {
            orderVersionDTO.setRequiresApproval(Boolean.FALSE);
            return false;
        }

        ignoreChangeOrdersExceptClosing = preferenceService.check(PreferenceID.PS_ROUTE_CLOSING_CHANGE_ORDERS_ONLY, buyerWorkgroupPreference);
        if (ignoreChangeOrdersExceptClosing && !orderVersionDTO.isClosingChangeOrder()) {
            orderVersionDTO.setRequiresApproval(Boolean.FALSE);
            return false;
        }

        String ignoreTheseSupplierUserIdsStr = buyerWorkgroupPreference.get(PreferenceID.PS_IGNORE_SUPPLIERS) != null ?
                buyerWorkgroupPreference.get(PreferenceID.PS_IGNORE_SUPPLIERS).trim() : "";
        String[] ignoreSuppliers = ignoreTheseSupplierUserIdsStr.split(",");
        for (int i = 0; i < ignoreSuppliers.length; i++) {
            long ignoreSupplierId = ignoreSuppliers[i] != null && ignoreSuppliers[i].length() > 0 ? Long.parseLong(ignoreSuppliers[i]) : -1;
            if (orderVersionDTO.getSupplierUserId() == ignoreSupplierId) {
                orderVersionDTO.setRequiresApproval(false);
                return false;
            }
        }

        double originalGrandTotal = parentOrder.getGrandTotal().doubleValue();
        boolean isExcludeTaxShipping = preferenceService.check(PreferenceID.PS_ROUTE_CCO_EXCLUDE_TAX_SHIPPING, buyerWorkgroupPreference);
        if (isExcludeTaxShipping) {
            originalGrandTotal = parentOrder.getOrderItemSubtotal().doubleValue();
        }
        if (ignoreChangeOrdersExceptClosing && orderVersionDTO.isClosingChangeOrder()) {
            double total = (changeOrderVersionDTO.getBaseOrder().getGrandTotal() != null ? changeOrderVersionDTO.getBaseOrder().getGrandTotal().doubleValue() : 0.0) + orderVersionDTO.getOrderItemSubtotal().doubleValue()
                    + orderVersionDTO.getDiscountOrSurchargeTotal().doubleValue()
                    + (orderVersionDTO.getTax() != null ? orderVersionDTO.getTax().doubleValue() : 0.0)
                    + (orderVersionDTO.getShipping() != null ? orderVersionDTO.getShipping().doubleValue() : 0.0);
            if (isExcludeTaxShipping) {
                total = (changeOrderVersionDTO.getBaseOrder().getOrderItemSubtotal() != null ? changeOrderVersionDTO.getBaseOrder().getOrderItemSubtotal().doubleValue() : 0.0) + orderVersionDTO.getOrderItemSubtotal().doubleValue()
                        + orderVersionDTO.getDiscountOrSurchargeTotal().doubleValue();
            }
            if (total <= originalGrandTotal) {
                orderVersionDTO.setRequiresApproval(Boolean.FALSE);
                return false;
            }
        }

        // if the total of this change order is zero, then obviously we are not modifying the
        // price and thus don't need approval.
        // except in the case where we have the Ignore Change Order Except Closing Change Order option enabled
        double adjustedGrandTotal = orderVersionDTO.getDiscountOrSurchargeTotal().doubleValue() + orderVersionDTO.getGrandTotal().doubleValue();
        if (isExcludeTaxShipping) {
            adjustedGrandTotal = orderVersionDTO.getDiscountOrSurchargeTotal().doubleValue() + orderVersionDTO.getOrderItemSubtotal().doubleValue();
        }
        if (!ignoreChangeOrdersExceptClosing) {
            if (adjustedGrandTotal <= 0) {
                orderVersionDTO.setRequiresApproval(Boolean.FALSE);
                return false;
            }
        }

        double orderGrandTotal = adjustedGrandTotal + changeOrderVersionDTO.getBaseOrder().getGrandTotal().doubleValue();
        if (isExcludeTaxShipping) {
            orderGrandTotal = adjustedGrandTotal + changeOrderVersionDTO.getBaseOrder().getOrderItemSubtotal().doubleValue();
        }
        // check spending limits for max authorization
        if (preferenceService.check(PreferenceID.WORKGROUP_OPTION_SPENDING_LIMIT, buyerWorkgroupPreference)) {
            SpendingLimit spendingLimitBean = spendingLimitRepository.findByWorkgroupIdAndUserId(
                    orderVersionDTO.getBuyerWorkgroupId(), orderVersionDTO.getBuyerUserId());
            if (spendingLimitBean != null && spendingLimitBean.getAmount() != null && orderGrandTotal <= spendingLimitBean.getAmount().doubleValue()) {
                orderVersionDTO.setRequiresApproval(false);
                return false;
            }
        }

        boolean isCCOApprovalEnabled =  preferenceService.check(PreferenceID.PS_ENABLE_CCO_COST_LEVELS_APPROVERS, buyerWorkgroupPreference) && orderVersionDTO.isClosingChangeOrder();
        if (preferenceService.check(PreferenceID.PS_QUICK_ORDER_APPROVAL, buyerWorkgroupPreference) && orderVersionDTO.isChangeOrder() && !isCCOApprovalEnabled && parentOrder.isQuickOrder()) {
           retval = isQuickApproval(buyerWorkgroupPreference, orderVersionDTO);
        } else {
           retval = isPresentAtCostLevel(buyerWorkgroupPreference, orderGrandTotal, orderVersionDTO);
        }

        if (retval) {
            if (routingSlipBean != null && (routingSlipBean.getApprovedCount() > 0 || routingSlipBean.getDisapprovedCount() > 0)) {
                if (routingSlipBean.getIsSequentialApprovals() != null && routingSlipBean.getIsSequentialApprovals().shortValue() == (short) 1
                        && (!routingSlipBean.getIsAllRecipientsApprovedNoChanges())) {
                    orderVersionDTO.setRequiresApproval(true);
                    return true;
                } else {
                    orderVersionDTO.setRequiresApproval(false);
                    return false;
                }
            }
        }
        orderVersionDTO.setRequiresApproval(retval);
        return retval;
    }

    public boolean requiresManagerApproval(ChangeOrderVersionDTO changeOrderVersionDTO, Map<String, String> buyerWorkgroupPreference) {
        OrderVersionDTO orderVersionDTO = changeOrderVersionDTO.getChangeOrder();
        OrderVersionDTO parentOrder = changeOrderVersionDTO.getParentOrder();
        OrderVersionDTO baseOrder = changeOrderVersionDTO.getBaseOrder();

        if (parentOrder.getIsGenerated()) {
            orderVersionDTO.setRequiresManagerApproval(false);
            return false;
        }
        //if (!this.isUserBuyer())
        //    return false;
        // no need to approve if the sell order is to offline buyer
        ProjectDTO project = orderVersionDTO.getParent();
        if (project.isOutsourcerProject() && orderVersionDTO.hasOfflineBuyer()) {
            orderVersionDTO.setRequiresManagerApproval(false);
            return false;
        }

        // check to see if approval routing is enabled
        if (!preferenceService.check(PreferenceID.WORKGROUP_OPTION_APPROVALS_ROUTING, buyerWorkgroupPreference)) {
            orderVersionDTO.setRequiresManagerApproval(false);
            return false;
        }

        // check to see if approval routing is enabled
        if (!preferenceService.check(PreferenceID.PS_REQUIRE_MANAGER_APPROVAL, buyerWorkgroupPreference)) {
            orderVersionDTO.setRequiresManagerApproval(false);
            return false;
        }

        //NKB145609
        if (preferenceService.check(PreferenceID.PS_BYPASS_MANAGER_APPROVAL_FROM_ESTIMATE, buyerWorkgroupPreference)) {
            boolean isOrderFromEstimate = orderVersionDTO.getOrderTypeId() == OrderTypeID.ORDER;
            if (isOrderFromEstimate) {
                orderVersionDTO.setRequiresManagerApproval(false);
                return false;
            }
        }

        boolean ignoreChangeOrdersExceptClosing = preferenceService.check(PreferenceID.PS_ROUTE_CLOSING_CHANGE_ORDERS_ONLY, buyerWorkgroupPreference);
        if (ignoreChangeOrdersExceptClosing && !orderVersionDTO.isClosingChangeOrder()) {
            orderVersionDTO.setRequiresManagerApproval(false);
            return false;
        }

        String ignoreTheseSupplierUserIdsStr = buyerWorkgroupPreference.get(PreferenceID.PS_IGNORE_SUPPLIERS) != null ?
                buyerWorkgroupPreference.get(PreferenceID.PS_IGNORE_SUPPLIERS).trim() : "";
        String[] ignoreSuppliers = ignoreTheseSupplierUserIdsStr.split(",");
        for (int i = 0; ignoreSuppliers != null && i < ignoreSuppliers.length; i++) {
            long ignoreSupplierId = ignoreSuppliers[i] != null && ignoreSuppliers[i].length() > 0 ? Long.parseLong(ignoreSuppliers[i]) : -1;
            if (orderVersionDTO.getSupplierUserId() == ignoreSupplierId) {
                orderVersionDTO.setRequiresManagerApproval(false);
                return false;
            }
        }

        boolean isExcludeTaxShipping = preferenceService.check(PreferenceID.PS_ROUTE_CCO_EXCLUDE_TAX_SHIPPING, buyerWorkgroupPreference);
        if (ignoreChangeOrdersExceptClosing && orderVersionDTO.isClosingChangeOrder()) {
            double originalGrandTotal = parentOrder.getGrandTotal().doubleValue();
            double total = (baseOrder.getGrandTotal() != null ? baseOrder.getGrandTotal().doubleValue() : 0.0) + orderVersionDTO.getOrderItemSubtotal().doubleValue()
                    + orderVersionDTO.getDiscountOrSurchargeTotal().doubleValue()
                    + (orderVersionDTO.getTax() != null ? orderVersionDTO.getTax().doubleValue() : 0.0)
                    + (orderVersionDTO.getShipping() != null ? orderVersionDTO.getShipping().doubleValue() : 0.0);
            if (isExcludeTaxShipping) {
                originalGrandTotal = parentOrder.getOrderItemSubtotal().doubleValue();
                total = (baseOrder.getOrderItemSubtotal() != null ? baseOrder.getOrderItemSubtotal().doubleValue() : 0.0) + orderVersionDTO.getOrderItemSubtotal().doubleValue()
                        + orderVersionDTO.getDiscountOrSurchargeTotal().doubleValue();
            }
            if (total <= originalGrandTotal) {
                orderVersionDTO.setRequiresManagerApproval(false);
                return false;
            }
        }

        // if the total of this change order is zero, then obviously we are not modifying the
        // price and thus don't need approval.
        // except in the case where we have the Ignore Change Order Except Closing Change Order option enabled
        double adjustedGrandTotal = orderVersionDTO.getDiscountOrSurchargeTotal().doubleValue() + orderVersionDTO.getGrandTotal().doubleValue();
        if (isExcludeTaxShipping) {
            adjustedGrandTotal = orderVersionDTO.getDiscountOrSurchargeTotal().doubleValue() + orderVersionDTO.getOrderItemSubtotal().doubleValue();
        }
        if (!ignoreChangeOrdersExceptClosing) {
            if (adjustedGrandTotal <= 0) {
                orderVersionDTO.setRequiresManagerApproval(Boolean.FALSE);
                return false;
            }
        }

        double orderGrandTotal = adjustedGrandTotal + baseOrder.getGrandTotal().doubleValue();
        if (isExcludeTaxShipping) {
            orderGrandTotal = adjustedGrandTotal + baseOrder.getOrderItemSubtotal().doubleValue();
        }
        // check spending limits for max authorization
        if (preferenceService.check(PreferenceID.WORKGROUP_OPTION_SPENDING_LIMIT, buyerWorkgroupPreference)) {
            SpendingLimit spendingLimitBean = spendingLimitRepository.findByWorkgroupIdAndUserId(
                    orderVersionDTO.getBuyerWorkgroupId(), orderVersionDTO.getBuyerUserId());
            if (spendingLimitBean != null && spendingLimitBean.getAmount() != null && orderGrandTotal <= spendingLimitBean.getAmount().doubleValue()) {
                orderVersionDTO.setRequiresManagerApproval(false);
                return false;
            }
        }

        boolean isCCOApprovalEnabled =  preferenceService.check(PreferenceID.PS_ENABLE_CCO_COST_LEVELS_APPROVERS, buyerWorkgroupPreference) && orderVersionDTO.isClosingChangeOrder();
        if (preferenceService.check(PreferenceID.PS_QUICK_ORDER_APPROVAL, buyerWorkgroupPreference) && orderVersionDTO.isChangeOrder() && !isCCOApprovalEnabled && parentOrder.isQuickOrder()) {
            orderVersionDTO.setRequiresManagerApproval(false);
            return false;
        }

        boolean retval = isManagerPresentAtCostLevel(buyerWorkgroupPreference, orderGrandTotal, orderVersionDTO);

        if (retval) {
            RoutingSlip routingSlip = routingSlipRepository.findFirstByObjectIdAndObjectClassIdAndAcWorkgroupIdOrderByModDateDesc(
                    orderVersionDTO.getId(), ObjectClassID.ORDER,
                    orderVersionDTO.getBuyerWorkgroupId());
            RoutingSlipDTO routingSlipBean = routingSlipMapper.toDTO(routingSlip);
            if (routingSlipBean != null) {
                if (routingSlipBean.getIsDisapproved() || routingSlipBean.getIsManagerApprovals() == null
                        || routingSlipBean.getIsManagerApprovals() == 0
                        || (routingSlipBean.getHasManagerApproval() != null && routingSlipBean.getHasManagerApproval() == 1))
                    retval = false;
            }
        }
        orderVersionDTO.setRequiresManagerApproval(retval);
        return retval;
    }

    /**
     * set the setQuickOrderApproval flag and approverRecipients on the orderVersionDTO
     * @param prefsMap
     * @param orderVersionDTO
     * @return
     */
    private boolean isQuickApproval(Map<String, String> prefsMap, OrderVersionDTO orderVersionDTO) {
        boolean retval;
        String userIdsStr = prefsMap.get(PreferenceID.PS_QUICK_ORDER_APPROVERS) != null ?
                prefsMap.get(PreferenceID.PS_QUICK_ORDER_APPROVERS).trim() : "";
        if (userIdsStr.length() == 0) {
            retval = false;
        } else {
            String[] userIds = userIdsStr.split(",");
            List<AccountUser> approverList = new ArrayList<>();
            for (int k = 0; k < userIds.length; k++) {
                approverList.add(k, accountUserRepository.findById(userIds[k] != null && userIds[k].length() > 0 ? Long.parseLong(userIds[k]) : -1).orElse(null));
            }
            List<AccountUserDTO> approverRecipients = approverList.stream().map(accountUserMapper::toDTO).collect(Collectors.toList());
            orderVersionDTO.setApproverRecipients(approverRecipients);
            orderVersionDTO.setQuickOrderApproval(true);
            retval = true;
        }
        return retval;
    }

    private boolean isPresentAtCostLevel(Map<String, String> prefsMap, double orderGrandTotal, OrderVersionDTO orderVersionDTO) {
        boolean retval = false;
        if (orderVersionDTO.isClosingChangeOrder() && preferenceService.check(PreferenceID.PS_ENABLE_CCO_COST_LEVELS_APPROVERS, prefsMap)) {
            long numCostLevels = prefsMap.get(PreferenceID.PS_CCO_NUM_COST_LEVELS) != null ?
                    Long.parseLong(prefsMap.get(PreferenceID.PS_CCO_NUM_COST_LEVELS).trim()) : 5;
            for (int i = 0; i < numCostLevels; i++) {
                boolean isEnabled = preferenceService.check(PreferenceID.PS_CCO_COST_LEVEL_ENABLED_ + i, prefsMap);
                if (!isEnabled)
                    continue;
                double fromAmount = prefsMap.get(PreferenceID.PS_CCO_COST_LEVEL_FROM_ + i) != null ?
                        Double.parseDouble(prefsMap.get(PreferenceID.PS_CCO_COST_LEVEL_FROM_ + i).trim()) : Double.MIN_VALUE;
                double toAmount = prefsMap.get(PreferenceID.PS_CCO_COST_LEVEL_TO_ + i) != null ?
                        Double.parseDouble(prefsMap.get(PreferenceID.PS_CCO_COST_LEVEL_TO_ + i).trim()) : Double.MAX_VALUE;
                String userIdsStr = prefsMap.get(PreferenceID.PS_CCO_COST_LEVEL_APPROVERS_ + i) != null ?
                        prefsMap.get(PreferenceID.PS_CCO_COST_LEVEL_APPROVERS_ + i).trim() : "";
                if (orderGrandTotal >= fromAmount && orderGrandTotal <= toAmount) {
                    if (userIdsStr.length() == 0) {
                        retval = false;
                        break;
                    } else {
                        String[] userIds = userIdsStr.split(",");
                        List<AccountUser> approverList = new ArrayList<AccountUser>();
                        for (int k = 0; k < userIds.length; k++) {
                            approverList.add(k, accountUserRepository.findById(userIds[k] != null && userIds[k].length() > 0 ? Long.parseLong(userIds[k]) : -1).orElse(null));
                        }
                        List<AccountUserDTO> approverRecipients = approverList.stream().map(accountUserMapper::toDTO).collect(Collectors.toList());
                        orderVersionDTO.setApproverRecipients(approverRecipients);
                        retval = true;
                        break;
                    }
                }
            }
        } else {
            long numCostLevels = prefsMap.get(PreferenceID.PS_NUM_COST_LEVELS) != null ?
                    Long.parseLong(prefsMap.get(PreferenceID.PS_NUM_COST_LEVELS).trim()) : 5;
            for (int i = 0; i < numCostLevels; i++) {
                boolean isEnabled = preferenceService.check(PreferenceID.PS_COST_LEVEL_ENABLED_ + i, prefsMap);
                if (!isEnabled)
                    continue;
                double fromAmount = prefsMap.get(PreferenceID.PS_COST_LEVEL_FROM_ + i) != null ?
                        Double.parseDouble(prefsMap.get(PreferenceID.PS_COST_LEVEL_FROM_ + i).trim()) : Double.MIN_VALUE;
                double toAmount = prefsMap.get(PreferenceID.PS_COST_LEVEL_TO_ + i) != null ?
                        Double.parseDouble(prefsMap.get(PreferenceID.PS_COST_LEVEL_TO_ + i).trim()) : Double.MAX_VALUE;
                String userIdsStr = prefsMap.get(PreferenceID.PS_COST_LEVEL_APPROVERS_ + i) != null ?
                        prefsMap.get(PreferenceID.PS_COST_LEVEL_APPROVERS_ + i).trim() : "";
                if (orderGrandTotal >= fromAmount && orderGrandTotal <= toAmount) {
                    if (userIdsStr.length() == 0) {
                        retval = false;
                        break;
                    } else {
                        String[] userIds = userIdsStr.split(",");
                        List<AccountUser> approverList = new ArrayList<AccountUser>();
                        for (int k = 0; k < userIds.length; k++) {
                            approverList.add(k, accountUserRepository.findById(userIds[k] != null && userIds[k].length() > 0 ? Long.parseLong(userIds[k]) : -1).orElse(null));
                        }
                        List<AccountUserDTO> approverRecipients = approverList.stream().map(accountUserMapper::toDTO).collect(Collectors.toList());
                        orderVersionDTO.setApproverRecipients(approverRecipients);
                        retval = true;
                        break;
                    }
                }
            }
        }
        return retval;
    }

    private boolean isManagerPresentAtCostLevel(Map<String, String> prefsMap, double orderGrandTotal, OrderVersionDTO orderVersionDTO) {
        boolean retval = false;
        if (orderVersionDTO.isClosingChangeOrder() && preferenceService.check(PreferenceID.PS_ENABLE_CCO_COST_LEVELS_APPROVERS, prefsMap)) {
            long numCostLevels = prefsMap.get(PreferenceID.PS_CCO_NUM_COST_LEVELS) != null ?
                    Long.parseLong(prefsMap.get(PreferenceID.PS_CCO_NUM_COST_LEVELS).trim()) : 5;
            for (int i = 0; i < numCostLevels; i++) {
                boolean isEnabled = preferenceService.check(PreferenceID.PS_CCO_COST_LEVEL_ENABLED_ + i, prefsMap);
                if (!isEnabled)
                    continue;
                double fromAmount = prefsMap.get(PreferenceID.PS_CCO_COST_LEVEL_FROM_ + i) != null ?
                        Double.parseDouble(prefsMap.get(PreferenceID.PS_CCO_COST_LEVEL_FROM_ + i).trim()) : Double.MIN_VALUE;
                double toAmount = prefsMap.get(PreferenceID.PS_CCO_COST_LEVEL_TO_ + i) != null ?
                        Double.parseDouble(prefsMap.get(PreferenceID.PS_CCO_COST_LEVEL_TO_ + i).trim()) : Double.MAX_VALUE;
                String userIdsStr = prefsMap.get(PreferenceID.PS_CCO_COST_LEVEL_MANAGERS_ + i) != null ?
                        prefsMap.get(PreferenceID.PS_CCO_COST_LEVEL_MANAGERS_ + i).trim() : "";
                if (orderGrandTotal >= fromAmount && orderGrandTotal <= toAmount) {
                    if (userIdsStr.length() == 0) {
                        retval = false;
                        break;
                    } else {
                        String[] userIds = userIdsStr.split(",");
                        List<Long> ids = new ArrayList<>();
                        List<AccountUser> approverList = new ArrayList<AccountUser>();
                        for (int k = 0; k < userIds.length; k++) {
                            approverList.add(k, accountUserRepository.findById(userIds[k] != null && userIds[k].length() > 0 ? Long.parseLong(userIds[k]) : -1).orElse(null));
                        }
                        List<AccountUserDTO> approverRecipients = approverList.stream().map(accountUserMapper::toDTO).collect(Collectors.toList());
                        orderVersionDTO.setApproverRecipients(approverRecipients);
                        retval = true;
                        break;
                    }
                }
            }
        } else {
            long numCostLevels = prefsMap.get(PreferenceID.PS_NUM_COST_LEVELS) != null ?
                    Long.parseLong(prefsMap.get(PreferenceID.PS_NUM_COST_LEVELS).trim()) : 5;

            for (int i = 0; i < numCostLevels; i++) {
                boolean isEnabled = prefsMap.get(PreferenceID.PS_COST_LEVEL_ENABLED_ + i) != null ?
                        prefsMap.get(PreferenceID.PS_COST_LEVEL_ENABLED_ + i).trim().equalsIgnoreCase("1") : false;
                if (!isEnabled)
                    continue;
                double fromAmount = prefsMap.get(PreferenceID.PS_COST_LEVEL_FROM_ + i) != null ?
                        Double.parseDouble(prefsMap.get(PreferenceID.PS_COST_LEVEL_FROM_ + i).trim()) : Double.MIN_VALUE;
                double toAmount = prefsMap.get(PreferenceID.PS_COST_LEVEL_TO_ + i) != null ?
                        Double.parseDouble(prefsMap.get(PreferenceID.PS_COST_LEVEL_TO_ + i).trim()) : Double.MAX_VALUE;
                String userIdsStr = prefsMap.get(PreferenceID.PS_COST_LEVEL_MANAGERS_ + i) != null ?
                        prefsMap.get(PreferenceID.PS_COST_LEVEL_MANAGERS_ + i).trim() : "";
                if (orderGrandTotal >= fromAmount && orderGrandTotal <= toAmount) {
                    if (userIdsStr.length() == 0) {
                        retval = false;
                        break;
                    } else {
                        String[] userIds = userIdsStr.split(",");
                        List<AccountUser> approverList = new ArrayList<AccountUser>();
                        for (int k = 0; k < userIds.length; k++) {
                            approverList.add(k, accountUserRepository.findById(userIds[k] != null && userIds[k].length() > 0 ? Long.parseLong(userIds[k]) : -1).orElse(null));
                        }
                        List<AccountUserDTO> managerRecipients = approverList.stream().map(accountUserMapper::toDTO).collect(Collectors.toList());
                        orderVersionDTO.setManagerRecipients(managerRecipients);
                        retval = true;
                        break;
                    }
                }
            }
        }

        return retval;
    }

    public boolean isManagerPresentAtCostLevel(Map<String, String> prefsMap, OrderVersionDTO orderVersionDTO, OrderVersionDTO baseOrderDTO) {
        BigDecimal discountsAndSurchargeTotal = orderVersionDTO.getDiscountOrSurchargeTotal();
        BigDecimal adjustedGrandTotal = orderVersionDTO.getGrandTotal().add(discountsAndSurchargeTotal);
        BigDecimal orderGrandTotal = adjustedGrandTotal.add(baseOrderDTO.getGrandTotal());
        return isManagerPresentAtCostLevel(prefsMap, orderGrandTotal != null ? orderGrandTotal.doubleValue() : -1, orderVersionDTO);
    }

}
