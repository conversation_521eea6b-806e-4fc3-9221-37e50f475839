package com.noosh.app.service.util;

import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.repository.jpa.security.ObjectStateRepository;
import org.apache.commons.lang.LocaleUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.NoSuchMessageException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Locale;

/**
 * User: leilaz
 * Date: 5/31/16
 */
@Component
public class I18NUtils {
	public final static Locale DEFAULT_LOCALE = Locale.US;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private ObjectStateRepository objectStateRepository;
    private final Logger log = LoggerFactory.getLogger(I18NUtils.class);

    public Locale getCurrentRequestLocale() {
        if(RequestContextHolder.getRequestAttributes() !=null) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String localeCode = request.getParameter("locale");
            if (localeCode != null) {
                Locale locale = Locale.forLanguageTag(localeCode.replaceAll("_", "-"));
                return locale;
            }
        }
        return Locale.ENGLISH;
    }

    public String getObjectStateMessage(Long key) {
        return getObjectStateMessage(key, getCurrentRequestLocale());
    }
    
    public String getObjectStateMessage(Long key, Locale locale) {
    	if (key != null) {
            try {
                return getObjectStateMessage(key, new String[]{}, locale);
            } catch (NoSuchMessageException e) {
                log.error(e.getMessage());
            }
        }
        return null;
    }
    
    public String getObjectStateMessage(Long key, String[] args) {
    	return getObjectStateMessage(key, args,  getCurrentRequestLocale());
    }
    
    public String getObjectStateMessage(Long key, String[] args, Locale locale) {
        ObjectState objectState = objectStateRepository.findById(key).orElse(null);
        return objectState != null ? messageSource.getMessage(objectState.getDescriptionStrId().toString(), args, locale == null ? DEFAULT_LOCALE : locale) : null;
    }


    public String getMessage(Long key) {
    	return getMessage(key, getCurrentRequestLocale());
    }
    
    public String getMessage(Long key,Locale locale) {
    	try {
			return messageSource.getMessage(key.toString(), new String[] {}, locale == null ? DEFAULT_LOCALE : locale);
        } catch (NoSuchMessageException e) {
            log.error(e.getMessage());
        }
        return null;
    }
    
    public String getMessage(String key) {
    	return getMessage(key, getCurrentRequestLocale());
    }

    public String getMessage(String key, Locale locale) {
    	try {
			return messageSource.getMessage(key, new String[] {}, locale == null ? DEFAULT_LOCALE : locale);
        } catch (NoSuchMessageException e) {
            log.error(e.getMessage());
        }
        return null;
    }

    public String getCurrencyPrice(BigDecimal price, String localCode) {
        DecimalFormat formatter = (DecimalFormat)NumberFormat.getCurrencyInstance(LocaleUtils.toLocale(localCode));
        String value = formatter.format(price);
        return value;
    }

    @Async
    public void asyncInit18nDataByLocale(com.noosh.app.commons.entity.security.Locale locale) {
        String localCode = locale.getLocaleCode();
        Long now = System.currentTimeMillis();
        messageSource.getMessage("error.title", new String[] {}, java.util.Locale.forLanguageTag(localCode.replaceAll("_", "-")));
        Long after = System.currentTimeMillis();
        log.info("Message Source: async initialization completed for " + localCode + " in " + (after-now) + " ms");
    }
}
