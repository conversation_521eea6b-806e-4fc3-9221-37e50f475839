package com.noosh.app.service.util;

import java.util.*;

/**
 * User: yangx
 * Date: 6/22/2020
 */
public class ListMap extends LinkedHashMap {

    public ListMap() {
        super();
    }

    public void add(Object key, Object item) {
        List items = (List) get(key);
        if (items == null) {
            items = new ArrayList();
            put(key, items);
        }
        //AES/REVIEW: This means that items acts like an ordered set (LinkedHashSet).
        //MH: don't add the item if it already exists in the list (slow because our implementation of 'equals' is slow)
        if (!items.contains(item)) {
            items.add(item);
        }
    }

    public List getList(Object key) {
        List items = (List) get(key);
        if (items == null) {
            return new ArrayList();
        }
        return items;
    }

    public Collection unpackValues() {
        Collection values = values(); // collection of lists
        List unpacked = new ArrayList(values.size() * 2);
        for (Iterator vi = values.iterator(); vi.hasNext(); ) {
            Collection items = (Collection) vi.next(); // list for a single key
            unpacked.addAll(items);
        }
        return unpacked;
    }
}
