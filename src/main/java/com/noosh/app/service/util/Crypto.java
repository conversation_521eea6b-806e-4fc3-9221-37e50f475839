package com.noosh.app.service.util;


import com.noosh.app.OrderResourceApplication;

import javax.crypto.*;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.PBEParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.Key;

/**
 * Copy from Enterprise
 */
public class Crypto {
    private static Cryptor desCryptor;

    private Crypto() {
    }

    public static void init() throws Exception {
        byte[] keyBytes = OrderResourceApplication.getHrefEncryptionKey().getBytes();
        Crypto.desCryptor = new DESCryptor(keyBytes);
    }

    public static byte[] encrypt(byte[] bytes) throws Exception {
        if (Crypto.desCryptor == null) {
            init();
        }
        return Crypto.desCryptor.encrypt(bytes);
    }

    public static String encrypt(String str) throws Exception {
        if (Crypto.desCryptor == null) {
            init();
        }
        return Crypto.desCryptor.encrypt(str);
    }

    public static byte[] decrypt(byte[] bytes) throws Exception {
        if (Crypto.desCryptor == null) {
            init();
        }
        return Crypto.desCryptor.decrypt(bytes);
    }

    public static String decrypt(String str) throws Exception {
        if (Crypto.desCryptor == null) {
            init();
        }
        return Crypto.desCryptor.decrypt(str);
    }


    private static abstract class Cryptor {
        protected Cipher encryptionCipher;
        protected Cipher decryptionCipher;

        protected Cryptor() {
        }

        public byte[] encrypt(byte[] bytes) throws Exception {
            try {
                return this.encryptionCipher.doFinal(bytes);
            } catch (BadPaddingException bpe) {
                throw new Exception(bpe.getMessage());
            } catch (IllegalBlockSizeException ibe) {
                throw new Exception(ibe.getMessage());
            }
        }

        public String encrypt(String str) throws Exception {
            byte[] bytes = encrypt(str.getBytes());
            return new String(Base64.encode(bytes));
        }

        public byte[] decrypt(byte[] bytes) throws Exception {
            try {
                return this.decryptionCipher.doFinal(bytes);
            } catch (BadPaddingException bpe) {
                throw new Exception(bpe.getMessage());
            } catch (IllegalBlockSizeException ibe) {
                throw new Exception(ibe.getMessage());
            }
        }

        public String decrypt(String str) throws Exception {
            // 1, decode the URL (cause some applications are using escaped  URL)
            // 2, replace character ' '(space) to '+',  cause base64 treat space to +
            try {
                str = java.net.URLDecoder.decode(str, "UTF-8");
            }catch (UnsupportedEncodingException e) {
                // ignore the exception of un-supporting encoding "UTF-8";
            }
            str = str.replaceAll(" ", "+");
            byte[] bytes = Base64.decode(str.getBytes());
            return new String(decrypt(bytes));
        }

        public String toString() {
            return this.encryptionCipher.getAlgorithm();
        }
    }


    private static final class DESCryptor extends Cryptor {
        private DESCryptor(byte[] raw) throws Exception {
            // Create a DES key object specification from the raw data.
            DESKeySpec dks = new DESKeySpec(raw);
            // Create a key factory and use it to turn the DESKeySpec into a SecretKey object.
            Key key = SecretKeyFactory.getInstance("DES").generateSecret(dks);
            // Create a cipher and initialize it for encryption.
            this.encryptionCipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
            this.encryptionCipher.init(Cipher.ENCRYPT_MODE, key);
            // Create a cipher and initialize it for decryption.
            this.decryptionCipher = Cipher.getInstance("DES/ECB/PKCS5Padding");
            this.decryptionCipher.init(Cipher.DECRYPT_MODE, key);
        }
    }


    private static final class AESCryptor extends Cryptor {
        private AESCryptor(byte[] raw) throws Exception {
            // Create a AES key object specification from the raw data.
            SecretKeySpec keySpec = new SecretKeySpec(raw, 0, 16, "AES"); // 128-bit
            // Create a cipher and initialize it for encryption.
            this.encryptionCipher = Cipher.getInstance("AES");
            this.encryptionCipher.init(Cipher.ENCRYPT_MODE, keySpec);
            // Create a cipher and initialize it for decryption.
            this.decryptionCipher = Cipher.getInstance("AES");
            this.decryptionCipher.init(Cipher.DECRYPT_MODE, keySpec);
        }
    }


    private static final class PBECryptor extends Cryptor {
        public PBECryptor(byte[] raw) throws Exception {
            byte[] salt = new byte[8];
            System.arraycopy(raw, 0, salt, 0, 8);
            PBEParameterSpec ps = new PBEParameterSpec(salt, 20);
            SecretKeyFactory kf = SecretKeyFactory.getInstance("PBEWithMD5AndDES");
            SecretKey k = kf.generateSecret(new javax.crypto.spec.PBEKeySpec(new String(raw).toCharArray()));
            this.encryptionCipher = Cipher.getInstance("PBEWithMD5AndDES/CBC/PKCS5Padding");
            this.encryptionCipher.init(Cipher.ENCRYPT_MODE, k, ps);
            this.decryptionCipher = Cipher.getInstance("PBEWithMD5AndDES/CBC/PKCS5Padding");
            this.decryptionCipher.init(Cipher.DECRYPT_MODE, k, ps);
        }
    }

    /*private static String dumpProviders() {
        StringBuffer buffer = new StringBuffer();
        Provider[] providers = Security.getProviders();
        String ls = System.getProperty("line.separator");
        for (int i = 0; i < providers.length; i++) {
            Provider provider = providers[i];
            buffer.append("name: " + provider.getName()).append(ls);
            buffer.append("info: " + provider.getInfo()).append(ls);
            buffer.append("ver : " + provider.getVersion()).append(ls);
            buffer.append(ls);
        }
        return buffer.toString();
    }*/

    /*private static void printCipher(Cipher cipher) {
        System.out.println(cipher.getAlgorithm());
        System.out.println(cipher.getBlockSize());
        System.out.println(cipher.getExemptionMechanism());
        System.out.println(cipher.getIV());
        System.out.println(cipher.getParameters());
        System.out.println(cipher.getProvider());
    }*/

//    public static void main(String[] args) throws Exception {
//        if (System.getProperty("noosh.dist") == null) {
//            System.setProperty("noosh.config", "/Users/<USER>/Documents/src/noosh/p4/workspace/ticon/sahara/dist/conf/noosh-config.xml");
//        }
//        Config.init();
//        Crypto.init();
//
//        //System.out.println(dumpProviders());
//
//        Cryptor[] cryptors = new Cryptor[] {
//                new DESCryptor(Config.getString("/encryptor/key").getBytes()),
//                new AESCryptor(Config.getString("/encryptor/key").getBytes()),
//                new PBECryptor(Config.getString("/encryptor/key").getBytes()),
//
//                new DESCryptor(Config.getString("/encryptor/key").getBytes()),
//                new AESCryptor(Config.getString("/encryptor/key").getBytes()),
//                new PBECryptor(Config.getString("/encryptor/key").getBytes()),
//        };
//
//        if (args == null || args.length == 0) {
//            args = new String[] {
//                    "blah",
//                    "http://scd/noosh/project/home?objectId=123&objectClassId=5000000&rnd=**********",
//                    "http://scd/noosh/contacts/viewContact?%24Lc5hneL3ZpRkEA0SSV//iv4U17P79w6WRGRTDqNgp4/3Xs7sbj4FFw=="
//            };
//        }
//
//        // unit test
//        for (int c = 0; c < cryptors.length; c++) {
//            for (int a = 0; a < args.length; a++) {
//                String str = args[a];
//                String enc = cryptors[c].encrypt(str);
//                String dec = cryptors[c].decrypt(enc);
//                System.out.println(cryptors[c] + "|" + str + "| >> |" + enc + "| << |" + dec + "|");
//                if (!str.equals(dec)) {
//                    throw new AssertionError();
//                }
//            }
//        }
//
//        // performance test
//        int iterations = 10000;
//        for (int c = 0; c < cryptors.length; c++) {
//            long t0 = System.currentTimeMillis();
//            for (int i = 0; i < iterations; i++) {
//                for (int a = 0; a < args.length; a++) {
//                    cryptors[c].decrypt(cryptors[c].encrypt(args[a]));
//                }
//            }
//            long t1 = System.currentTimeMillis();
//            System.out.println(cryptors[c] + "| elapsed: " + (t1 - t0) + "ms");
//        }
//    }
}
