package com.noosh.app.service.util;

import com.noosh.app.commons.constant.DataTypeID;
import com.noosh.app.commons.dto.costcenter.CostCenterAllocationDTO;
import com.noosh.app.commons.dto.property.PropertyAttributeDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;

/**
 * User: leilaz
 * Date: 1/12/21
 */
public class AggregatedCostCenterComparator implements Comparator<CostCenterAllocationDTO> {
    @Override
    public int compare(CostCenterAllocationDTO acca1, CostCenterAllocationDTO acca2) {
        if (acca1.getCostCenterName() != null && acca2.getCostCenterName() != null) {
            int ccNameComparedVal = acca1.getCostCenterName().compareTo(acca2.getCostCenterName());
            if (ccNameComparedVal != 0)
                return ccNameComparedVal;
        } else if (acca1.getCostCenterAllocationId().longValue() != acca2.getCostCenterAllocationId() ) {
            return (int)(acca1.getCostCenterAllocationId() - acca2.getCostCenterAllocationId());
        }

        List<PropertyAttributeDTO> customFields = acca1.getCustomFields() ;
        int cfCount = customFields != null ? customFields.size() : 0;
        for (int i=0; i < cfCount; i++) {
            PropertyAttributeDTO cf = customFields.get(i);
            if (cf.getIsIgnoreCompare()) {
                continue;
            }
            Object custom1 = cf;
            Object custom2 = null;
            if (acca2.getCustomFields() != null && acca2.getCustomFields().size() > 0) {
                custom2 = acca2.getCustomFields().stream().filter(c -> c.getParamName().equalsIgnoreCase(cf.getParamName())).findAny().orElse(null);
            }
            if (custom1 != null && custom2 != null) {
                int cComparedVal = custom1.hashCode() - custom2.hashCode();
                if (cf.getPrDataTypeId() == DataTypeID.STRING) {
                    String s1 = ((PropertyAttributeDTO)custom1).getStringValue();
                    String s2 = ((PropertyAttributeDTO)custom2).getStringValue();
                    if (s1 != null && s2 != null) {
                        cComparedVal = s1.compareTo(s2);
                    } else if (s1 == null && s2 != null) {
                        cComparedVal = -1;
                    } else if (s1 != null && s2 == null) {
                        cComparedVal = 1;
                    } else if (s1 == null && s2 == null) {
                        cComparedVal = 0;
                    }
                } else if (cf.getPrDataTypeId() == DataTypeID.LONG || cf.getPrDataTypeId() == DataTypeID.DOUBLE) {
                    // convert into thousandth
                    BigDecimal s1 = ((PropertyAttributeDTO) custom1).getNumberValue();
                    BigDecimal s2 = ((PropertyAttributeDTO) custom2).getNumberValue();
                    if (s1 != null && s2 != null) {
                        long lVal1 = (long)(s1.doubleValue() * 1000);
                        long lVal2 = (long)(s2.doubleValue() * 1000);
                        cComparedVal = (int)(lVal1 - lVal2);
                    } else if (s1 == null && s2 != null) {
                        cComparedVal = -1;
                    } else if (s1 != null && s2 == null) {
                        cComparedVal = 1;
                    } else if (s1 == null && s2 == null) {
                        cComparedVal = 0;
                    }
                } else if (cf.getPrDataTypeId() == DataTypeID.DATE) {
                    LocalDateTime s1 = ((PropertyAttributeDTO) custom1).getDateValue();
                    LocalDateTime s2 = ((PropertyAttributeDTO) custom2).getDateValue();
                    if (s1 != null && s2 != null) {
                        cComparedVal = s1.compareTo(s2);
                    } else if (s1 == null && s2 != null) {
                        cComparedVal = -1;
                    } else if (s1 != null && s2 == null) {
                        cComparedVal = 1;
                    } else if (s1 == null && s2 == null) {
                        cComparedVal = 0;
                    }
                }

                if (cComparedVal != 0)
                    return cComparedVal;
            }
            else if ( custom1 != null && custom2 == null)
                return 1;
            else if (custom1 == null && custom2 != null)
                return -1;
        }

        return 0;
    }
}
