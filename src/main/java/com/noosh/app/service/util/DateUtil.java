package com.noosh.app.service.util;

import org.apache.commons.lang.LocaleUtils;

import java.text.DateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 4/21/2021
 */
public class DateUtil {

    public static String getDateStr(LocalDateTime dateTime, String format) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return dateTime.format(formatter);
    }

    public static LocalDateTime dateShort(LocalDateTime date, String timeZoneCode, String localeCode) {
        if (date == null) {
            return null;
        }
        date = convertTimeZone(date, timeZoneCode, localeCode);
        return date;
    }

    public static LocalDateTime convertTimeZone(LocalDateTime dateTime, String timeZoneCode, String localeCode) {
        TimeZone timeZone = TimeZone.getTimeZone(timeZoneCode);
        Calendar calendar = Calendar.getInstance(timeZone, LocaleUtils.toLocale(localeCode));
        Date from = Date.from(dateTime.atZone(timeZone.toZoneId()).toInstant());

        calendar.setTime(from);
        calendar.add(Calendar.MILLISECOND, calendar.get(Calendar.ZONE_OFFSET) + calendar.get(Calendar.DST_OFFSET));

        return Instant.ofEpochMilli(calendar.getTime().getTime()).atZone( timeZone.toZoneId()).toLocalDateTime();
    }

    public static String getLocaleDateStr(LocalDateTime dateTime, String localeCode) {
        Locale locale =  Locale.forLanguageTag(localeCode.replace("_", "-"));
        DateFormat df = DateFormat.getDateInstance(DateFormat.SHORT, locale);
        Date date = Date.from(dateTime.toLocalDate().atStartOfDay(ZoneId.systemDefault()).toInstant());
        return df.format(date);
    }

    public static boolean isValidCompletionDate(LocalDateTime completionDate) {
        if (completionDate == null) {
            return false;
        }
        if (completionDate.compareTo(LocalDateTime.now()) > 0) {
            return true;
        }
        return false;
    }

    //LocalDateTime -> Date
    public static Date getDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

}
