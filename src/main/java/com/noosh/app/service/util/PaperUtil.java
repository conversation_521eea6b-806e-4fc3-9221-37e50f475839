package com.noosh.app.service.util;

import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import com.noosh.app.commons.entity.estimate.EstimateItemPrice;

import java.util.List;

public class PaperUtil {

    public static boolean isEstimateOrderWithDiffQty(List<PropertyAttributeDTO> propertyAttributeDTOList, EstimateItemPrice estimateItemPrice) {
        boolean isEstimateOrderWithDiffQty = false;
        String qty1 = (String) OrderUtil.getValue(propertyAttributeDTOList, "PAP_FST_QTY1_str");
        boolean weight = OrderUtil.getValue(propertyAttributeDTOList, "PAP_RE_WEIGHT1_bool") != null ? String.valueOf(OrderUtil.getValue(propertyAttributeDTOList, "PAP_RE_WEIGHT1_bool")).equals("1") ? true : false : false;
        if (estimateItemPrice != null && !estimateItemPrice.getItemOption().getValue().equals(qty1) && !weight) {
            isEstimateOrderWithDiffQty = true;
        }
        return !isEstimateOrderWithDiffQty;
    }

    public static boolean containsPaperInfo(List<PropertyAttributeDTO> propertyAttributeDTOList) {
        return propertyAttributeDTOList.stream().anyMatch(customField -> "PAP_NAME1_str".equals(customField.getParamName()));
    }

}
