package com.noosh.app.service.util;

import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import com.noosh.app.commons.dto.property.PropertyDTO;
import com.noosh.app.commons.vo.paper.PaperDetailVO;
import com.noosh.app.commons.vo.paper.PaperEnvImpactEquivalentVO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 3/21/2021
 */
public class OrderUtil {
    public static final String PAP_NAME = "PAP_NAME";
    public static final String PAP_USED_AS = "PAP_USED_AS";
    public static final String PAP_SPEC_GRADE = "PAP_SPEC_GRADE";
    public static final String PAP_SPEC_BRAND = "PAP_SPEC_BRAND";
    public static final String PAP_SPEC_P_TYPE = "PAP_SPEC_P_TYPE";
    public static final String PAP_SPEC_FINISH = "PAP_SPEC_FINISH";
    public static final String PAP_SPEC_WEIGHT = "PAP_SPEC_WEIGHT";
    public static final String PAP_TYPE = "PAP_TYPE";
    public static final String PAP_SHEET_H = "PAP_SHEET_H";
    public static final String PAP_SHEET_W = "PAP_SHEET_W";
    public static final String PAP_SHEET_U = "PAP_SHEET_U";
    public static final String PAP_NO_SHEET = "PAP_NO_SHEET";
    public static final String PAP_ROLL_W = "PAP_ROLL_W";
    public static final String PAP_ROLL_WN = "PAP_ROLL_WN";
    public static final String PAP_ROLL_WD = "PAP_ROLL_WD";
    public static final String PAP_ROLL_WU = "PAP_ROLL_WU";
    public static final String PAP_ROLL_D = "PAP_ROLL_D";
    public static final String PAP_ROLL_D_UNIT = "PAP_ROLL_D_UNIT";
    public static final String PAP_ROLL_C = "PAP_ROLL_C";
    public static final String PAP_ROLL_C_UNIT = "PAP_ROLL_C_UNIT";
    public static final String PAP_AMOUNT = "PAP_AMOUNT";
    public static final String PAP_UNIT = "PAP_UNIT";
    public static final String PAP_GRADE = "PAP_GRADE";
    public static final String PAP_GRADE_LABEL = "PAP_GRADE_LABEL";
    public static final String PAP_RECYCLE = "PAP_RECYCLE";
    public static final String PAP_COMMENTS = "PAP_COMMENTS";
    public static final String PAP_WOOD = "PAP_WOOD";
    public static final String PAP_GRE = "PAP_GRE";
    public static final String PAP_WATER = "PAP_WATER";
    public static final String PAP_ENERGY = "PAP_ENERGY";
    public static final String PAP_SFI = "PAP_SFI";
    public static final String PAP_FSC = "PAP_FSC";
    public static final String PAP_CONTENT = "PAP_CONTENT";

    public static final String UNIT = "_U";
    public static final String VALUE = "_V";
    public static final String UNIT_UK = "_U_UK";
    public static final String VALUE_UK = "_V_UK";
    public static final String EQU_UNIT = "_EU";
    public static final String EQU_VALUE = "_EV";

    public static final String TOTAL_UNIT = "_TU";
    public static final String TOTAL_VALUE = "_TV";
    public static final String TOTAL_UNIT_UK = "_TU_UK";
    public static final String TOTAL_VALUE_UK = "_TV_UK";

    public static final String NUMBER_SUFFIX = "_num";
    public static final String STRING_SUFFIX = "_str";
    public static final String BOOLEAN_SUFFIX = "_bool";
    public static final String DATE_SUFFIX = "_date";

    public static List<PaperDetailVO> getPaperDetail(List<PropertyAttributeDTO> propertyAttributeDTOList, int paperNumber) {
        if (paperNumber > 0) {
            List<PaperDetailVO> paperDetails = new ArrayList<>();
            Map<String,PropertyAttributeDTO> propertyDTOMap = new HashMap<>();
            for (int i = 1; i<=paperNumber; i++) {
                PaperDetailVO paperDetail = new PaperDetailVO();
                for (PropertyAttributeDTO propertyAttribute : propertyAttributeDTOList) {
                    propertyDTOMap.put(propertyAttribute.getParamName(), propertyAttribute);
                    if ((PAP_NAME + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setPaperName(propertyAttribute.getStringValue());
                    } else if ((PAP_USED_AS + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setUsedAs(propertyAttribute.getStringValue());
                    } else if ((PAP_SPEC_BRAND + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setBrand(propertyAttribute.getStringValue());
                    } else if ((PAP_SPEC_P_TYPE + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setPaperType(propertyAttribute.getStringValue());
                    } else if ((PAP_SPEC_FINISH + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setFinish(propertyAttribute.getStringValue());
                    } else if ((PAP_SPEC_WEIGHT + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setWeight(propertyAttribute.getStringValue());
                    } else if ((PAP_TYPE + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setPaperRequired(propertyAttribute.getStringValue());
                    } else if ((PAP_SHEET_H + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setSheetHeight(propertyAttribute.getNumberValue());
                    } else if ((PAP_SHEET_W + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setSheetWidth(propertyAttribute.getNumberValue());
                    } else if ((PAP_SHEET_U + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setSheetUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_NO_SHEET + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setNumOfSheets(propertyAttribute.getNumberValue());
                    } else if ((PAP_ROLL_W + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRollWidth(propertyAttribute.getNumberValue());
                    } else if ((PAP_ROLL_WN + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRollWidthN(propertyAttribute.getNumberValue());
                    } else if ((PAP_ROLL_WD + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRollWidthD(propertyAttribute.getNumberValue());
                    } else if ((PAP_ROLL_WU + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRollWidthUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_ROLL_D + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRollDiameter(propertyAttribute.getNumberValue());
                    } else if ((PAP_ROLL_D_UNIT + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRollDiameterUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_ROLL_C + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRollCoreDiameter(propertyAttribute.getNumberValue());
                    } else if ((PAP_ROLL_C_UNIT + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRollCoreDiameterUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_AMOUNT + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setTotalWeight(propertyAttribute.getNumberValue());
                    } else if ((PAP_UNIT + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setTotalWeightUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_GRADE + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        BigDecimal gradeValue = propertyAttribute.getNumberValue();
                        if (gradeValue != null) {
                            int num = gradeValue.intValue();
                            paperDetail.setPaperGradeId(num);
                            switch (num) {
                                case 1: paperDetail.setPaperGrade("Coated Freesheet"); break;
                                case 2: paperDetail.setPaperGrade("Uncoated Freesheet"); break;
                                case 3: paperDetail.setPaperGrade("Coated Groundwood"); break;
                                case 4: paperDetail.setPaperGrade("Uncoated Groundwood"); break;
                                case 5: paperDetail.setPaperGrade("Supercalendered"); break;
                                case 6: paperDetail.setPaperGrade("Paperboard: Solid Bleached Sulfate (SBS)"); break;
                                case 7: paperDetail.setPaperGrade("Paperboard: Coated Unbleached Kraft"); break;
                                case 8: paperDetail.setPaperGrade("Paperboard: Coated Recycled Board"); break;
                                case 9: paperDetail.setPaperGrade("Paperboard: Uncoated Bleached Kraft"); break;
                                case 10: paperDetail.setPaperGrade("Paperboard: Uncoated Unbleached Kraft"); break;
                                case 11: paperDetail.setPaperGrade("Paperboard: Uncoated Recycled Board"); break;
                                case 12: paperDetail.setPaperGrade("Linerboard"); break;
                                case 13: paperDetail.setPaperGrade("Corrugating Container"); break;
                                case 14: paperDetail.setPaperGrade("Tissue"); break;
                            }
                        }
                    } else if ((PAP_RECYCLE + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRecycledContent(propertyAttribute.getNumberValue());
                    } else if ((PAP_SFI + i + BOOLEAN_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setSfiCertified(ModelFormatter.yesNo(propertyAttribute.getNumberValue()));
                    } else if ((PAP_FSC + i + BOOLEAN_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setFscCertified(ModelFormatter.yesNo(propertyAttribute.getNumberValue()));
                    } else if ((PAP_CONTENT + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setContent(propertyAttribute.getNumberValue());
                    } else if ((PAP_WOOD + VALUE + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setWoodValue(propertyAttribute.getNumberValue());
                    } else if ((PAP_WOOD + UNIT + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setWoodUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_GRE + VALUE + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setGreenHouseValue(propertyAttribute.getNumberValue());
                    } else if ((PAP_GRE + UNIT + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setGreenHouseUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_WATER + VALUE + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setWaterValue(propertyAttribute.getNumberValue());
                    } else if ((PAP_WATER + UNIT + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setWaterUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_ENERGY + VALUE + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setEnergyValue(propertyAttribute.getNumberValue());
                    } else if ((PAP_ENERGY + UNIT + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setEnergyUnit(propertyAttribute.getStringValue());
                    }
                    setEquivalentImpacts(paperDetail, propertyDTOMap, i);
                }
                paperDetails.add(paperDetail);
            }
            return paperDetails;
        }
        return null;
    }

    public static Object getValue(List<PropertyAttributeDTO> propertyAttributeDTOList, String propertyName) {
        if (propertyName == null) {
            return null;
        }
        for(PropertyAttributeDTO propertyAttribute : propertyAttributeDTOList) {
            if (propertyName.equals(propertyAttribute.getParamName())) {
                if (propertyName.contains(STRING_SUFFIX)) {
                    return propertyAttribute.getStringValue();
                } else if (propertyName.contains(BOOLEAN_SUFFIX)) {
                    return propertyAttribute.getNumberValue();
                }
            }
        }
        return null;
    }

    private static void setEquivalentImpacts(PaperDetailVO paperDetail, Map<String, PropertyAttributeDTO> propertyDTOMap, int paperIndex) {
        List<PaperEnvImpactEquivalentVO> woodEquivalentImpact = new ArrayList<>(1);
        woodEquivalentImpact.add(getEquivalentImpact(propertyDTOMap, PAP_WOOD, paperIndex, 1));
        paperDetail.setWoodEquivalentImpact(woodEquivalentImpact);

        List<PaperEnvImpactEquivalentVO> energyEquivalentImpact = new ArrayList<>(2);
        energyEquivalentImpact.add(getEquivalentImpact(propertyDTOMap, PAP_ENERGY, paperIndex, 1));
        energyEquivalentImpact.add(getEquivalentImpact(propertyDTOMap, PAP_ENERGY, paperIndex, 2));
        paperDetail.setEnergyEquivalentImpact(energyEquivalentImpact);

        List<PaperEnvImpactEquivalentVO> waterEquivalentImpact = new ArrayList<>(2);
        waterEquivalentImpact.add(getEquivalentImpact(propertyDTOMap, PAP_WATER, paperIndex, 1));
        waterEquivalentImpact.add(getEquivalentImpact(propertyDTOMap, PAP_WATER, paperIndex, 2));
        paperDetail.setWaterEquivalentImpact(waterEquivalentImpact);

        List<PaperEnvImpactEquivalentVO> greenHouseEquivalentImpact = new ArrayList<>(1);
        greenHouseEquivalentImpact.add(getEquivalentImpact(propertyDTOMap, PAP_GRE, paperIndex, 1));
        paperDetail.setGreenHouseEquivalentImpact(greenHouseEquivalentImpact);
    }

    private static PaperEnvImpactEquivalentVO getEquivalentImpact(Map<String, PropertyAttributeDTO> propertyDTOMap, String impactType, int paperIndex, int equivalentIndex) {
        PaperEnvImpactEquivalentVO paperEnvImpactEquivalentVO = new PaperEnvImpactEquivalentVO();
        PropertyAttributeDTO unitDTO = propertyDTOMap.get(impactType + EQU_UNIT + paperIndex + "_" + equivalentIndex+ STRING_SUFFIX);
        PropertyAttributeDTO valueDTO = propertyDTOMap.get(impactType + EQU_VALUE + paperIndex + "_" + equivalentIndex+ NUMBER_SUFFIX);
        if (null != unitDTO) {
            paperEnvImpactEquivalentVO.setUnit(unitDTO.getStringValue());
        }
        if (null != valueDTO) {
            paperEnvImpactEquivalentVO.setValue(valueDTO.getNumberValue());
        }
        return paperEnvImpactEquivalentVO;
    }

    /**
     *  Environmental Paper Impact customization for Communisis(UK)
     * @param propertyAttributeDTOList
     * @param paperNumber
     * @return
     */
    public static List<PaperDetailVO> getPaperDetailForCommunisis(List<PropertyAttributeDTO> propertyAttributeDTOList, int paperNumber) {
        if (paperNumber > 0) {
            List<PaperDetailVO> paperDetails = new ArrayList<>();
            Map<String,PropertyAttributeDTO> propertyDTOMap = new HashMap<>();
            for (int i = 1; i<=paperNumber; i++) {
                PaperDetailVO paperDetail = new PaperDetailVO();
                for (PropertyAttributeDTO propertyAttribute : propertyAttributeDTOList) {
                    propertyDTOMap.put(propertyAttribute.getParamName(), propertyAttribute);
                    if ((PAP_NAME + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setPaperName(propertyAttribute.getStringValue());
                    } else if ((PAP_USED_AS + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setUsedAs(propertyAttribute.getStringValue());
                    } else if ((PAP_SPEC_GRADE + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setGrade(propertyAttribute.getStringValue());
                    } else if ((PAP_SPEC_BRAND + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setBrand(propertyAttribute.getStringValue());
                    } else if ((PAP_SPEC_P_TYPE + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setPaperType(propertyAttribute.getStringValue());
                    } else if ((PAP_SPEC_FINISH + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setFinish(propertyAttribute.getStringValue());
                    } else if ((PAP_SPEC_WEIGHT + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setWeight(propertyAttribute.getNumberValue() != null ? propertyAttribute.getNumberValue().toString() : null);
                    } else if ((PAP_TYPE + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setPaperRequired(propertyAttribute.getStringValue());
                    } else if ((PAP_SHEET_H + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setSheetHeight(propertyAttribute.getNumberValue());
                    } else if ((PAP_SHEET_W + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setSheetWidth(propertyAttribute.getNumberValue());
                    } else if ((PAP_SHEET_U + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setSheetUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_NO_SHEET + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setNumOfSheets(propertyAttribute.getNumberValue());
                    } else if ((PAP_ROLL_W + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRollWidth(propertyAttribute.getNumberValue());
                    } else if ((PAP_ROLL_WN + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRollWidthN(propertyAttribute.getNumberValue());
                    } else if ((PAP_ROLL_WD + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRollWidthD(propertyAttribute.getNumberValue());
                    } else if ((PAP_ROLL_WU + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRollWidthUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_ROLL_D + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRollDiameter(propertyAttribute.getNumberValue());
                    } else if ((PAP_ROLL_D_UNIT + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRollDiameterUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_ROLL_C + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRollCoreDiameter(propertyAttribute.getNumberValue());
                    } else if ((PAP_ROLL_C_UNIT + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRollCoreDiameterUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_AMOUNT + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setTotalWeight(propertyAttribute.getNumberValue());
                    } else if ((PAP_UNIT + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setTotalWeightUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_RECYCLE + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setRecycledContent(propertyAttribute.getNumberValue());
                    } else if ((PAP_COMMENTS + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setPaperComments(propertyAttribute.getStringValue());
                    } else if ((PAP_WOOD + VALUE_UK + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setWoodValue(propertyAttribute.getNumberValue());
                    } else if ((PAP_WOOD + UNIT_UK + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setWoodUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_GRE + VALUE_UK + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setGreenHouseValue(propertyAttribute.getNumberValue());
                    } else if ((PAP_GRE + UNIT_UK + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setGreenHouseUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_WATER + VALUE_UK + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setWaterValue(propertyAttribute.getNumberValue());
                    } else if ((PAP_WATER + UNIT_UK + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setWaterUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_ENERGY + VALUE_UK + i + NUMBER_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setEnergyValue(propertyAttribute.getNumberValue());
                    } else if ((PAP_ENERGY + UNIT_UK + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setEnergyUnit(propertyAttribute.getStringValue());
                    } else if ((PAP_GRADE_LABEL + i + STRING_SUFFIX).equals(propertyAttribute.getParamName())) {
                        paperDetail.setPaperGrade(propertyAttribute.getStringValue());
                    }
                    setEquivalentImpacts(paperDetail, propertyDTOMap, i);
                }
                paperDetails.add(paperDetail);
            }
            return paperDetails;
        }
        return null;
    }

}
