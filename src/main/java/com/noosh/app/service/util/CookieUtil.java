package com.noosh.app.service.util;

import com.noosh.app.OrderResourceApplication;
import org.apache.commons.lang.LocaleUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Locale;

/**
 * @Author: <PERSON><PERSON>yu <PERSON>
 * @Date: 11/12/2015
 */
public class CookieUtil {


    public static String getCurrentCookieLocaleCode() {
    	String currentCookieLocale = "en_US";
    	if(RequestContextHolder.getRequestAttributes() !=null) {
	    	HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
	        String locale = getCookie(request, "nooshOne.locale");
	        if(locale != null){
	            currentCookieLocale = locale;
	        }
    	}
        return currentCookieLocale;
    }

    public static Locale getCurrentCookieLocale() {
        return LocaleUtils.toLocale(getCurrentCookieLocaleCode());
    }

    public static String getCookie(HttpServletRequest request, String cookieName) {
        String cookieValue = null;
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (int i = 0; i < cookies.length; i++) {
                if (cookies[i].getName().equalsIgnoreCase(cookieName)) {
                    cookieValue = cookies[i].getValue();
                    break;
                }
            }
        }
        return cookieValue;
    }

    public static String wrapWithAppInstance(String cookieName) {
        return wrapWithAppInstance(cookieName, "_");
    }

    /**
     * coporate with legcy NE cookie
     */
    public static String wrapWithAppInstance(String cookieName, String separator) {
        return cookieName + separator + OrderResourceApplication.getInstance();
    }
}
