package com.noosh.app.service.util;

import com.noosh.app.WorkgroupResourceApplication;
import com.noosh.app.commons.constant.EnterpriseLinkDestination;
import com.noosh.app.config.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;

/**
 * User: leilaz
 * Date: 12/14/15
 */

//Raymond:
//NO-1844: Revisit, to test links, isSecure, etc
public class NooshOneUrlUtil {

    private static final Logger log = LoggerFactory.getLogger(NooshOneUrlUtil.class);

    public static String getClientIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getHeader("HTTP_X_FORWARDED");
        }
        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getHeader("HTTP_X_CLUSTER_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getHeader("HTTP_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getHeader("HTTP_FORWARDED");
        }
        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getHeader("HTTP_VIA");
        }
        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getHeader("REMOTE_ADDR");
        }
        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    public static Map<String, String> getUrlParamMap(String url) {
        String[] array = new String[0];
        Map<String, String> map = new HashMap<>();
        try {
            array = URLDecoder.decode(url, "UTF-8").split("&");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        for (String string : array) {
            String[] keyValue = string.split("=");
            if (keyValue.length == 2) {
                map.put(keyValue[0], keyValue[1]);
            } else {
                map.put(keyValue[0], "");
            }
        }
        return map;
    }

    public static String composeMenuLinkToNGE(final String subDomain, final String rootDomain, final String url, Map params) {
    	boolean isHttps = true;
        String host = getNGERootDomainNoPort(subDomain, rootDomain);
        if (params == null) {
            params = new HashMap<>();
        }
        return new NooshUrl(Constants.SSL_PROTOCOL, host, getPort(isHttps), url, params).toString();
    }

    public static String composeLinkToReact(final String url, Map params) {
    	boolean isHttps = true;
        String host = getReactRootDomain();
        if (params == null) {
            params = new HashMap<>();
        }
        NooshUrl nooshUrl = new NooshUrl(Constants.SSL_PROTOCOL, host, getPort(isHttps), url, params);
        nooshUrl.setEncrypt(false);
        return nooshUrl.toString();
    }

    public static String getReactRootDomain() {
        String instanceName = WorkgroupResourceApplication.getInstance().equalsIgnoreCase("dev") ? "dist" : WorkgroupResourceApplication.getInstance();
        if (("spd").equals(instanceName)) {
            return "nooshone.noosh.com";
        }
        else {
            return "nooshone." + instanceName + ".noosh.com";
        }

    }

    public static int getPort(boolean isHttps) {
        String instance = WorkgroupResourceApplication.getInstance().equalsIgnoreCase("dev") ? "dist" : WorkgroupResourceApplication.getInstance();
        if (instance != null && instance.equalsIgnoreCase("dist")) {
            return isHttps ? 7443 : 8080;
        } else if (instance != null) {
            return -1;
        }
        return -1;
    }

    public static String getNGERootDomainNoPort(String subDomain, String rootDomain) {
        String instance = WorkgroupResourceApplication.getInstance().equalsIgnoreCase("dev") ? "dist" : WorkgroupResourceApplication.getInstance();
        if (instance != null && instance.equalsIgnoreCase("dist")) {
            return subDomain + "." + instance + "." + rootDomain;
        } else if (instance != null && instance.equalsIgnoreCase("spd")) {
            return subDomain + "." + rootDomain;
        } else if (instance != null) {
            return subDomain + "." + instance + "." + rootDomain;
        }
        return "";
    }

    /**
     * compose link to Enterprise link
     *
     * @param url - manage link in EnterpriseLinkDestination.java
     * @return absolute url
     */
    public static String composeLinkToEnterprise(final String url) {
        return composeLinkToEnterprise(url, null);
    }

    public static String composeLinkToEnterprise(final String url, HashMap<String, String> params) {
        if (params == null) {
            params = new HashMap<>();
        }

        boolean isHttps = true;
		String host = WorkgroupResourceApplication.getEnterpriseDomain();
		RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
    	if(attributes !=null && attributes instanceof ServletRequestAttributes) {
    		HttpServletRequest request = ((ServletRequestAttributes) attributes).getRequest();
    		if (request != null) {
    			isHttps = true;//request.isSecure();
                String neEntry = request.getParameter("domain");
    			if (neEntry != null) {
    				host = neEntry;
    			}

                appendMobileFlagToUrl(request, url, params);
            }
    	}

        return new NooshUrl(Constants.SSL_PROTOCOL, host, getNEPort(isHttps), url, params).toString();
    }

    public static String composeLinkToEnterprise(HttpServletRequest request, final String url, HashMap<String, String> params) {
        if (params == null) {
            params = new HashMap<>();
        }
        appendMobileFlagToUrl(request, url, params);

        // TODO: will be revisited, since it doesn't allow parallel stream, or will get null
        //HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        boolean isHttps = true;//request.isSecure();
        String host = WorkgroupResourceApplication.getEnterpriseDomain();
        String neEntry = CookieUtil.getCookie(request, CookieUtil.wrapWithAppInstance("nooshOne.host"));
        if (neEntry != null) {
            host = neEntry;
        }

        return new NooshUrl(Constants.SSL_PROTOCOL, host, getNEPort(isHttps), url, params).toString();
    }

    private static void appendMobileFlagToUrl(HttpServletRequest request, String url, HashMap<String, String> params) {
        if(("").equals(url)) {
            // NOOP
            // This is for generating Enterprise Host, the base URL for front-end to concat with relative path, Ex:,
            // https://scdqa.noosh.com/noosh/home/<USER>
        } else {
            if(url.contains("?")) {
                // if the url has ?, that means its from database and already generated, Ex
                // ex: https://scdqa.noosh.com/noosh/procurement/estimating/estimate/home
                //?%24q8worxVU3ihhUGhzZdoWjXDJyX9GUaBrJjbGuthIsBG9UMK2xVPBKupZhLM29qzGnkNKFTFcrTKHcX6SnvIgJXFrlcT4a2NK06NNdmSHzzU=
                url = url + "&isFromMobile=" + MobileUtil.isFromMobileDevice(request);
            } else {
                // For url from com.noosh.app.commons.domain.constant.EnterpriseLinkDestination
                params.put("isFromMobile", MobileUtil.isFromMobileDevice(request));
                if(params.containsKey("back")) {
                    String backUrl = params.get("back");
                    try {
                        String queryString = backUrl.substring((backUrl.indexOf("?")+1), backUrl.length());
                        String value = URLEncryptor.decrypt(queryString);
                        if(value != null && !value.contains("isFromMobile")) {
                            value = value + "&isFromMobile=" + MobileUtil.isFromMobileDevice(request);
                            String back = backUrl.substring(0, backUrl.indexOf("?")+1)
                                            + URLEncryptor.encrypt(value);
                            params.put("back",  back);
                        }
                    } catch (Exception e) {
                        log.error("URLEncryptor Error!");//handle it later
                    }
                }
            }
        }
    }

    private static int getNEPort(boolean isHttps) {
        String instance = WorkgroupResourceApplication.getInstance();
        if (instance != null && instance.equalsIgnoreCase("dist")) {
            return isHttps ? 8443 : 7777;
        } else if (instance != null) {
            return -1;
        }
        return -1;
    }

    /**
     * compose link to amazon s3 url
     *
     * @param url - relative url
     * @return absolute url
     */
    public static String composeLinkToS3(final String url) {
        String host = WorkgroupResourceApplication.getAWSUri();
        host = "https://" + host;
        return host + "/" + url;
    }

    public static String composeEditDualCurrencyLinkToEnterprise() {
        String url = EnterpriseLinkDestination.GOTO_EDIT_EXCHANGE_RATES;
        return composeLinkToEnterprise(url);
    }

    public static String composeBackToOptionsLinkToEnterprise() {
        String url = EnterpriseLinkDestination.GOTO_MY_GROUP;
        return composeLinkToEnterprise(url);
    }
    
    public static String composeEditWorkgroupInfoLinkToEnterprise() {
        String url = EnterpriseLinkDestination.ACTION_EDIT_WORKGROUP_INFO;
        return composeLinkToEnterprise(url);
    }

    public static String gotoUpdateCategoryLink(Long categoryId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("categoryId", "" + categoryId);
        params.put("acName", "GOTO_UPDATE");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/budgetCategory/update", params);
    }

    public static String gotoDeleteCategoryLink(Long categoryId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("categoryId", "" + categoryId);
        params.put("acName", "GOTO_DELETE");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/budgetCategory/delete", params);
    }

    public static String moveUpCategoryLink(Long categoryId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("categoryId", "" + categoryId);
        params.put("acName", "ACTION_CATEGORY_MOVE_UP");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/budgetCategory/move", params);
    }

    public static String moveDownCategoryLink(Long categoryId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("categoryId", "" + categoryId);
        params.put("acName", "ACTION_CATEGORY_MOVE_DOWN");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/budgetCategory/move", params);
    }

    public static String gotoUpdateFieldLink(Long fieldId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("customFieldId", "" + fieldId);
        params.put("customFieldClassId", "1000002");
        params.put("acName", "ACTION_EDIT_FIELD");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/customFields/edit", params);
    }

    public static String gotoDeleteFieldLink(Long fieldId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("customFieldId", "" + fieldId);
        params.put("customFieldClassId", "1000002");
        params.put("acName", "ACTION_DELETE_FIELD");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/customFields/edit", params);
    }

    public static String moveUpFieldLink(Long fieldId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("customFieldId", "" + fieldId);
        params.put("customFieldClassId", "1000002");
        params.put("acName", "ACTION_PROMOTE_FIELD");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/customFields/changeOrdering", params);
    }

    public static String moveDownFieldLink(Long fieldId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("customFieldId", "" + fieldId);
        params.put("customFieldClassId", "1000002");
        params.put("acName", "ACTION_DEMOTE_FIELD");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/customFields/changeOrdering", params);
    }

    public static String gotoAddProjectBudgetCategoryLink() {
        HashMap<String, String> params = new HashMap<>();
        params.put("acName", "GOTO_UPDATE");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/budgetCategory/update", params);
    }

    public static String gotoAddProjectBudgetFieldLink(int nextOrdinal) {
        HashMap<String, String> params = new HashMap<>();
        params.put("customFieldId", "-1");
        params.put("customFieldClassId", "1000002");
        params.put("nextOrdinal", "" + nextOrdinal);
        params.put("acName", "ACTION_ADD_FIELD");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/customFields/edit", params);
    }
}
