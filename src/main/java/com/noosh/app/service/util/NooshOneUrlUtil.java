package com.noosh.app.service.util;


import com.noosh.app.OrderResourceApplication;
import com.noosh.app.commons.constant.EnterpriseLinkDestination;
import com.noosh.app.commons.constant.ObjectClassID;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;

/**
 * User: leilaz
 * Date: 12/14/15
 */
public class NooshOneUrlUtil {

    public static final String SSL_PROTOCOL = "https";
    public static final String HTTP_PROTOCOL = "http";

    /**
     * compose link to Enterprise link
     *
     * @param url - manage link in EnterpriseLinkDestination.java
     * @return absolute url
     */
    public static String composeLinkToEnterprise(final String url) {
        return composeLinkToEnterprise(url, null);
    }

    public static String composeLinkToEnterprise(final String url, HashMap<String, String> params) {
        if (params == null) {
            params = new HashMap<>();
        }

        boolean isHttps = true;
        String host = OrderResourceApplication.getEnterpriseDomain();
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if(attributes !=null && attributes instanceof ServletRequestAttributes) {
            HttpServletRequest request = ((ServletRequestAttributes) attributes).getRequest();
            if (request != null) {
                isHttps = true;
                String neEntry = request.getParameter("domain");
                if (neEntry != null) {
                    host = neEntry;
                }
            }
        }

        return new NooshUrl(isHttps ? SSL_PROTOCOL : HTTP_PROTOCOL, host, getNEPort(isHttps), url, params).toString();
    }

    private static int getNEPort(boolean isHttps) {
        String instance = OrderResourceApplication.getInstance();
        if (instance != null && instance.equalsIgnoreCase("dist")) {
            return isHttps ? 8443 : 7777;
        } else if (instance != null) {
            return -1;
        }
        return -1;
    }

    /**
     * compose link to amazon s3 url
     *
     * @param url - relative url
     * @return absolute url
     */
    public static String composeLinkToS3(final String url) {
        String host = OrderResourceApplication.getAWSUri();
        boolean isHttps = true;
        if(RequestContextHolder.getRequestAttributes() !=null) {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            isHttps = true;//request.isSecure();
        }
        host = (isHttps ? "https://" : "http://") + host;
        return host + "/" + url;
    }

    public static String composeGotoProjectHomeLinkToEnterprise(final long objectId) {
        HashMap params = new HashMap();
        params.put("objectId", "" + objectId);
        params.put("objectClassId", String.valueOf(ObjectClassID.OBJECT_CLASS_PROJECT));
//        params.put("acName", "GOTO_PROJECT_HOME", EnterpriseLinkDestination.GOTO_PROJECT_HOME));

        String url = EnterpriseLinkDestination.GOTO_PROJECT_HOME;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeOrderWithChangesLinkToEnterprise(final Long objectId, Long orderId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("aggregate", "true");
        params.put("orders_pn", "1");
        params.put("orders_ops", "20");
        params.put("renderSpecs", "false");

        String url = EnterpriseLinkDestination.VIEW_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeGotoCreateInvoiceLinkToEnterprise(Long objectId) {
        // /noosh/procurement/invoice/create
        HashMap params = new HashMap();
        params.put("objectId", "" + objectId);
        params.put("objectClassId", String.valueOf(ObjectClassID.OBJECT_CLASS_PROJECT));
//        params.put("acName", "GOTO_PROJECT_HOME", EnterpriseLinkDestination.GOTO_PROJECT_HOME));

        String url = EnterpriseLinkDestination.CREATE_INVOICE;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeViewOrderLinkToEnterprise(final Long objectId, Long orderId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);

        String url = EnterpriseLinkDestination.VIEW_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeViewChangeOrderLinkToEnterprise(final Long objectId, Long orderId, Long changeOrderId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("changeOrderId", "" + changeOrderId);

        String url = EnterpriseLinkDestination.VIEW_CHANGE_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeSupplierRatingLinkToEnterprise(final Long objectId, Long orderId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);

        String url = EnterpriseLinkDestination.SUPPLIER_RATING;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeListOrdersToEnterprise(final Long objectId, Long orderSection) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderSection", "" + orderSection);

        String url = EnterpriseLinkDestination.LIST_ORDERS;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeCreateQuickOrderToEnterprise(final Long objectId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);

        String url = EnterpriseLinkDestination.CREATE_QUICK_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeViewInvoiceLinkToEnterprise(final Long objectId, Long invoiceId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("invoiceId", "" + invoiceId);

        String url = EnterpriseLinkDestination.VIEW_INVOICE;
        return composeLinkToEnterprise(url, params);
    }

    public static String composePrintInvoiceLinkToEnterprise(final Long objectId, Long invoiceId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("invoiceId", "" + invoiceId);

        String url = EnterpriseLinkDestination.PRINT_INVOICE;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeCopyInvoiceLinkToEnterprise(final Long objectId, Long invoiceId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("invoiceId", "" + invoiceId);
        params.put("acName", "ACTION_COPY_INVOICE");

        String url = EnterpriseLinkDestination.COPY_INVOICE;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeEditInvoiceLinkToEnterprise(final Long objectId, Long invoiceId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("invoiceId", "" + invoiceId);

        String url = EnterpriseLinkDestination.EDIT_INVOICE;
        return composeLinkToEnterprise(url, params);
    }

    public static String composePrintViewInvoiceLinkToEnterprise(final Long objectId, Long invoiceId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("invoiceId", "" + invoiceId);

        String url = EnterpriseLinkDestination.PRINT_INVOICE;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeDeleteInvoiceLinkToEnterprise(final Long objectId, Long invoiceId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("invoiceId", "" + invoiceId);
        params.put("acName", "ACTION_DELETE_INVOICE");

        String url = EnterpriseLinkDestination.INVOICE_LIST;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeRejectInvoiceLinkToEnterprise(final Long objectId, Long invoiceId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("invoiceId", "" + invoiceId);
        params.put("acName", "ACTION_REACT_REJECT_INVOICE");

        String url = EnterpriseLinkDestination.REJECT_INVOICE;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeCreateInvoiceAdjustmentOrderLinkToEnterprise(final Long objectId, Long orderId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("acName", "GOTO_CREATE_INVOICE_ADJUSTMENT_ORDER");

        String url = EnterpriseLinkDestination.CREATE_INVOICE_ADJUSTMENT_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeRetractInvoiceLinkToEnterprise(final Long objectId, Long invoiceId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("invoiceId", "" + invoiceId);
        params.put("acName", "ACTION_REACT_RETRACT_INVOICE");

        String url = EnterpriseLinkDestination.RETRACT_INVOICE;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeAcceptInvoiceLinkToEnterprise(final Long objectId, Long invoiceId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("invoiceId", "" + invoiceId);
        params.put("acName", "ACTION_REACT_ACCEPT_INVOICE");

        String url = EnterpriseLinkDestination.VIEW_INVOICE;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeApproveInvoiceLinkToEnterprise(final Long objectId, Long invoiceId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("invoiceId", "" + invoiceId);
        params.put("acName", "ACTION_REACT_APPROVE_INVOICE");

        String url = EnterpriseLinkDestination.VIEW_INVOICE;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeInvoiceListLinkToEnterprise(final Long objectId, int invoiceSection) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("invoiceSection", "" + invoiceSection);
        params.put("acName", "ACTION_HOME");

        String url = EnterpriseLinkDestination.INVOICE_LIST;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeReplaceInvoiceLinkToEnterprise(final Long objectId, Long invoiceId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("invoiceId", "" + invoiceId);
        params.put("acName", "ACTION_REPLACE_INVOICE");

        String url = EnterpriseLinkDestination.VIEW_INVOICE;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeViewSpecLinkForOrderItemToEnterprise(final Long nodeId, final Long specId, final Long objectId, String back) {
        HashMap params = new HashMap();
        params.put("context", "order");
        params.put("specId", "" + specId);
        params.put("nodeId", "" + nodeId);
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("back", back);
        params.put("renderSpecs", "" + false);
        String url = EnterpriseLinkDestination.VIEW_SPEC;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeCopySpecLinkForOrderItemToEnterprise(final Long nodeId, final Long specId, final Long objectId) {
        HashMap params = new HashMap();
        params.put("context", "order");
        params.put("specId", "" + specId);
        params.put("nodeId", "" + nodeId);
        params.put("objectId", "" + objectId);
        params.put("objectClassId", "" + 1000000);
        params.put("specmode", "copy");
        String url = EnterpriseLinkDestination.CREATE_SPEC_OPTION;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeEditSpecLinkForOrderItemToEnterprise(final Long nodeId, final Long specId, final Long objectId) {
        HashMap params = new HashMap();
        params.put("context", "order");
        params.put("specId", "" + specId);
        params.put("nodeId", "" + nodeId);
        params.put("objectId", "" + objectId);
        params.put("objectClassId", "" + 1000000);
        params.put("specmode", "edit");
        String url = EnterpriseLinkDestination.EDIT_SPEC_OPTION;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeEditDraftChangeOrderLinkToEnterprise(final Long objectId, Long orderId, Long versionId, Long changeOrderId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("changeOrderId", "" + changeOrderId);
        params.put("acName", "GOTO_EDIT_CHANGE_ORDER_DRAFT");
        params.put("versionNumber", "" + versionId);

        String url = EnterpriseLinkDestination.EDIT_CHANGE_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeDeleteDraftChangeOrderLinkToEnterprise(final Long objectId, Long orderId, Long versionId, Long changeOrderId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("changeOrderId", "" + changeOrderId);
        params.put("acName", "ACTION_DELETE_CHANGE_ORDER_DRAFT");
        params.put("versionNumber", "" + versionId);

        String url = EnterpriseLinkDestination.VIEW_CHANGE_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String getOrderSection(String orderType) {
        if (orderType != null) {
            if (orderType.equalsIgnoreCase("buy")) {
                return "0";
            } else if (orderType.equalsIgnoreCase("sell")) {
                return "1";
            } else if (orderType.equalsIgnoreCase("markupSummary")) {
                return "2";
            } else if (orderType.equalsIgnoreCase("markupAnalysis")) {
                return "3";
            }
        }
        return "0";
    }

    public static String composeEditDraftOrderLinkToEnterprise(final Long objectId, Long orderId, Long versionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("acName", "GOTO_EDIT_ORDER_DRAFT");
        params.put("versionNumber", "" + versionId);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.EDIT_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeDeleteDraftOrderLinkToEnterprise(final Long objectId, Long orderId, Long versionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("acName", "ACTION_DELETE_ORDER_DRAFT");
        params.put("versionNumber", "" + versionId);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.ACTION_DELETE_ORDER_DRAFT;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeEditOrderLinkToEnterprise(final Long objectId, Long orderId, Long versionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("acName", "GOTO_EDIT_ORDER");
        params.put("versionNumber", "" + versionId);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.EDIT_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeAcceptOrderLinkToEnterprise(final Long objectId, Long orderId, Long versionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("acName", "ACTION_ACCEPT_ORDER");
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);
        params.put("orderSection", getOrderSection(orderType));
        
        String url = EnterpriseLinkDestination.ACTION_ACCEPT_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeRouteForApprovalOrderLinkToEnterprise(final Long objectId, Long orderId, Long versionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("acName", "ACTION_ROUTE_ORDER_FOR_APPROVAL");
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.VIEW_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeRouteForManagerApprovalOrderLinkToEnterprise(final Long objectId, Long orderId, Long versionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("acName", "ACTION_ROUTE_ORDER_FOR_MANAGER_APPROVAL");
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.VIEW_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeSubmitOrderLinkToEnterprise(final Long objectId, Long orderId, Long versionId, Long orderVersionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("acName", "ACTION_SUBMIT_ORDER");
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);
        params.put("orderVersionId", "" + orderVersionId);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.VIEW_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeRejectOrderLinkToEnterprise(final Long objectId, Long orderId, Long versionId, Long orderVersionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("versionNumber", "" + versionId);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.REJECT_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeRejectOrderDirectlyLinkToEnterprise(final Long objectId, Long orderId, Long versionId, Long orderVersionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("versionNumber", "" + versionId);
        params.put("acName", "ACTION_REJECT_ORDER");
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.REJECT_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeRetractOrderLinkToEnterprise(final Long objectId, Long orderId, Long versionId, Long orderVersionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("acName", "ACTION_RETRACT_ORDER");
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.ACTION_RETRACT_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeReorderOrderLinkToEnterprise(final Long objectId, Long orderId, Long versionId, Long orderVersionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("versionNumber", "" + versionId);
        params.put("mode", "quick");
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.COPY_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeCancelOrderLinkToEnterprise(final Long objectId, Long orderId, Long versionId, Long orderVersionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("acName", "ACTION_CANCEL_ORDER");
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.ACTION_CANCEL_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeCreateChangeOrderLinkToEnterprise(final Long objectId, Long orderId, Long versionId, Long orderVersionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("acName", "GOTO_CREATE_CHANGE_ORDER");
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.CREATE_CHANGE_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeUpdateOrderLinkToEnterprise(final Long objectId, Long orderId, Long versionId, Long orderVersionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("acName", "ACTION_VIEW_ORDER_STATUS");
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.UPDATE_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeUpdateSupplierTrackingLinkToEnterprise(final Long objectId, Long orderId, Long versionId, String backUrl, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);
        params.put("back", backUrl);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.EDIT_SUPPLIER_REF;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeEditGeneralInfoLinkToEnterprise(final Long objectId, Long orderId, Long versionId, String backUrl, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);
        params.put("back", backUrl);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.EDIT_GENERAL_INFO;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeCostCenterAllocationLinkToEnterprise(final Long objectId, Long orderId, Long versionId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);

        String url = EnterpriseLinkDestination.COST_CENTER_ALLOCATION;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeAggregatedCostCenterAllocationLinkToEnterprise(final Long objectId, Long orderId, Long versionId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);

        String url = EnterpriseLinkDestination.AGGREGATED_COST_CENTER_ALLOCATION;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeEditCostCenterAllocationLinkToEnterprise(final Long objectId, Long orderId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);

        String url = EnterpriseLinkDestination.EDIT_COST_CENTER_ALLOCATION;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeEditChangeCenterAllocationLinkToEnterprise(final Long objectId, Long orderId,
                                                                           Long changeOrderId, Long versionId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("changeOrderId", "" + changeOrderId);
        params.put("versionNumber", "" + versionId);
        params.put("acName", "ACTION_EDIT_ALLOCATION_CHANGE_ORDER");

        String url = EnterpriseLinkDestination.EDIT_CHANGE_ORDER_COST_CENTER_ALLOCATION;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeEditAggregatedCenterAllocationLinkToEnterprise(final Long objectId, Long orderId,
                                                                           Long changeOrderId, Long versionId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("changeOrderId", "" + changeOrderId);
        params.put("versionNumber", "" + versionId);

        String url = EnterpriseLinkDestination.EDIT_AGGREGATED_ORDER_COST_CENTER_ALLOCATION;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeEditShipmentLinkToEnterprise(final Long objectId, Long orderId, Long versionId,
                                                             String backUrl, List<String> shipmentIds) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("acName", "GOTO_BATCH_EDIT");
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);
        params.put("back", "" + backUrl);
        params.put("selectedShipmentId", shipmentIds);

        String url = EnterpriseLinkDestination.EDIT_SHIPMENT;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeSupplierRatingLinkToEnterprise(final Long objectId, Long orderId, Long versionId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);

        String url = EnterpriseLinkDestination.SUPPLIER_RATING;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeSourcingLinkToEnterprise(final Long objectId, Long orderId, Long versionId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);
        params.put("showResult", "" + true);

        String url = EnterpriseLinkDestination.SOURCING_STRATEGIES;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeCompleteOrderLinkToEnterprise(final Long objectId, Long orderId, Long versionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("acName", "GOTO_CLOSE_ORDER");
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);
        params.put("closing", "" + 1);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.CLOSING_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeRoutingResponseLinkToEnterprise(final Long objectId, Long orderId, Long routableClassId,
                                                                String back, Long response, Long routingGroupId, Long routableId,
                                                                String routingName) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("acName", "ACTION_RESPOND");
        params.put("renderSpecs", "" + false);
        params.put("rs_rocid", "" + routableClassId);
        params.put("rs_rgid", "" + routingGroupId);
        params.put("rs_resp", "" + response);
        params.put("rs_roid", "" + routableId);
        params.put("rs_rname", "" + routingName);
        params.put("back", "" + back);
        params.put("response", "" + response);
        String url = EnterpriseLinkDestination.ROUTING_RESPONSE;
        return composeLinkToEnterprise(url, params);
    }

    public static String composedismissSupplierButtonLinkToEnterprise(final Long objectId, Long requestId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("requestId", "" + requestId);

        String url = EnterpriseLinkDestination.ACTION_GOTO_DISMISS_SUPPLIER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeShowPendingApprovalLinkToEnterprise(final Long projectId, Long orderId, boolean isChangeOrder) {
        HashMap params = new HashMap();
        params.put("projectId", "" + projectId);
        params.put("orderId", "" + orderId);
        params.put("isChangeOrder", isChangeOrder ? "true" : "false");

        String url = EnterpriseLinkDestination.SHOW_PENDING_APPROVAL;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeEditOrderItemShipmentLinkToEnterprise(final Long objectId, Long shipmentId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("shipmentId", "" + shipmentId);

        String url = EnterpriseLinkDestination.EDIT_ORDER_ITEM_SHIPMENT;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeCarryOverChangeOrderButtonChangeOrderLinkToEnterprise(final Long objectId, Long orderId, Long changeOrderId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("changeOrderId", "" + changeOrderId);
        params.put("acName", "ACTION_CARRY_CHANGE_ORDER_OVER");

        String url = EnterpriseLinkDestination.ACTION_CARRY_CHANGE_ORDER_OVER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeAcceptChangeOrderLinkToEnterprise(final Long objectId, Long orderId, Long changeOrderId, Long versionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("changeOrderId", "" + changeOrderId);
        params.put("acName", "ACTION_ACCEPT_CHANGE_ORDER");
        params.put("versionNumber", "" + versionId);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.ACTION_ACCEPT_CHANGE_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composePreAcceptChangeOrderLinkToEnterprise(final Long objectId, Long orderId, Long changeOrderId, Long versionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("changeOrderId", "" + changeOrderId);
        params.put("acName", "ACTION_PRE_ACCEPT_CHANGE_ORDER");
        params.put("versionNumber", "" + versionId);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.ACTION_PRE_ACCEPT_CHANGE_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeRouteForApprovalChangeOrderLinkToEnterprise(final Long objectId, Long changeOrderId, Long versionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("changeOrderId", "" + changeOrderId);
        params.put("acName", "ACTION_ROUTE_CHANGE_ORDER_FOR_APPROVAL");
        params.put("versionNumber", "" + versionId);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.VIEW_CHANGE_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeRouteForManagerApprovalChangeOrderLinkToEnterprise(final Long objectId, Long changeOrderId, Long versionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("changeOrderId", "" + changeOrderId);
        params.put("acName", "ACTION_ROUTE_CHANGE_ORDER_FOR_MANAGER_APPROVAL");
        params.put("versionNumber", "" + versionId);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.VIEW_CHANGE_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeSubmitChangeOrderLinkToEnterprise(final Long objectId, Long orderId, Long changeOrderId, Long versionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("changeOrderId", "" + changeOrderId);
        params.put("acName", "ACTION_SUBMIT_CHANGE_ORDER");
        params.put("versionNumber", "" + versionId);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.VIEW_CHANGE_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeRejectChangeOrderLinkToEnterprise(final Long objectId, Long orderId, Long changeOrderId, Long versionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("changeOrderId", "" + changeOrderId);
        params.put("acName", "ACTION_REJECT_CHANGE_ORDER");
        params.put("versionNumber", "" + versionId);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.REJECT_CHANGE_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeRetractChangeOrderLinkToEnterprise(final Long objectId, Long orderId, Long changeOrderId, Long versionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("changeOrderId", "" + changeOrderId);
        params.put("acName", "ACTION_RETRACT_CHANGE_ORDER");
        params.put("versionNumber", "" + versionId);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.ACTION_RETRACT_CHANGE_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeEditChangeOrderLinkToEnterprise(final Long objectId, Long orderId, Long changeOrderId, Long versionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("changeOrderId", "" + changeOrderId);
        params.put("acName", "GOTO_EDIT_CHANGE_ORDER");
        params.put("versionNumber", "" + versionId);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.EDIT_CHANGE_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeCancelChangeOrderLinkToEnterprise(final Long objectId, Long orderId, Long changeOrderId, Long versionId, String orderType) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("changeOrderId", "" + changeOrderId);
        params.put("acName", "ACTION_CANCEL_CHANGE_ORDER");
        params.put("versionNumber", "" + versionId);
        params.put("orderSection", getOrderSection(orderType));

        String url = EnterpriseLinkDestination.ACTION_CANCEL_CHANGE_ORDER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeCostCenterAllocationChangeOrderLinkToEnterprise(final Long objectId, Long orderId, Long changeOrderId, Long versionId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("changeOrderId", "" + changeOrderId);
        params.put("versionNumber", "" + versionId);
        params.put("renderSpecs", "" + false);

        String url = EnterpriseLinkDestination.CHANGE_ORDER_COST_CENTER_ALLOCATION;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeRoutingResponseForChangeOrderLinkToEnterprise(final Long objectId, Long orderId, Long routableClassId,
                                                                              String back, Long response, Long routingGroupId, Long routableId,
                                                                              String routingName) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("acName", "ACTION_RESPOND");
        params.put("renderSpecs", "" + false);
        params.put("rs_rocid", "" + routableClassId);
        params.put("rs_rgid", "" + routingGroupId);
        params.put("rs_resp", "" + response);
        params.put("rs_roid", "" + routableId);
        params.put("rs_rname", "" + routingName);
        params.put("back", "" + back);
        params.put("response", "" + response);
        params.put("orderVersionId", "" + routableId);
        params.put("changeOrderId", "" + orderId);

        String url = EnterpriseLinkDestination.ROUTING_RESPONSE;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeAcceptAndCarryOverButtonChangeOrderLinkToEnterprise(final Long objectId, Long orderId, Long changeOrderId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderId", "" + orderId);
        params.put("changeOrderId", "" + changeOrderId);
        params.put("acName", "ACTION_ACCEPT_AND_CARRY_CHANGE_ORDER_OVER");

        String url = EnterpriseLinkDestination.ACTION_ACCEPT_AND_CARRY_CHANGE_ORDER_OVER;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeMarkupAnalysisGoButtonLinkToEnterprise(final Long objectId, long actionId) {
        HashMap params = new HashMap();
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("orderSection", "" + 3);
        params.put("acName", "ACTION_BATCH_ACTION");
        params.put("actionId", "" + actionId);

        String url = EnterpriseLinkDestination.LIST_ORDERS;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeViewSpecLinkToEnterprise(final Long nodeId, final Long objectId) {
        HashMap params = new HashMap();
        params.put("nodeId", "" + nodeId);
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("acName", "GOTO_VIEW_SPEC");

        String url = EnterpriseLinkDestination.PRODUCTIZE_SPEC;
        return composeLinkToEnterprise(url, params);
    }

    public static String composeV1000IReportLinkToEnterprise(final Long projectId, Long orderId, Long clientWorkgroupId) {
        HashMap params = new HashMap();
        params.put("projectId", "" + projectId);
        params.put("orderId", "" + orderId);
        params.put("clientWorkgroupId", "" + clientWorkgroupId);
        params.put("exec", "y");
        params.put("output", "view");
        params.put("report", "/staples-v1000i-report.nrd");

        String url = EnterpriseLinkDestination.REPORT_RUN;
        return composeLinkToEnterprise(url, params);
    }

    public static String composePrintVendorReportLinkToEnterprise(final Long objectId, final Long projectId, Long orderId, Long clientWorkgroupId) {
        HashMap params = new HashMap();
        params.put("projectId", "" + projectId);
        params.put("objectId", "" + objectId);
        params.put("objectClassId", "" + 1000000);
        params.put("orderId", "" + orderId);
        params.put("clientWorkgroupId", "" + clientWorkgroupId);
        params.put("exec", "y");
        params.put("output", "view");
        params.put("report", "/spire/order-output-report.nrd");

        String url = EnterpriseLinkDestination.REPORT_RUN;
		return composeLinkToEnterprise(url, params);
	}

    public static String composeEditSourcingLinkToEnterprise(final Long objectId, final Long orderId) {
        HashMap params = new HashMap();
        params.put("orderId", "" + orderId);
        params.put("objectClassId", "" + 1000000);
        params.put("objectId", "" + objectId);
        params.put("acName", "GOTO_EDIT_SOURCING");

        String url = EnterpriseLinkDestination.EDIT_SOURCING_STRATEGIES;
        return composeLinkToEnterprise(url, params);
    }

    public static String getProjectLink(long projectId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
        params.put("objectId", "" + projectId);
        return NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.VIEW_PROJECT_HOME, params);
    }
}
