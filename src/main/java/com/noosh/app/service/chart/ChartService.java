package com.noosh.app.service.chart;

import com.noosh.app.commons.vo.chart.ChartFilter;
import com.noosh.app.commons.vo.chart.ChartFilterVO;
import com.noosh.app.service.preference.PreferenceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.inject.Inject;
import java.util.HashMap;
import java.util.Map;

/**
 * User: leilaz
 * Date: 2/17/22
 */
@Service
@Transactional
public class ChartService {
    private final Logger log = LoggerFactory.getLogger(ChartService.class);

    @Inject
    private PreferenceService preferenceService;

    @Transactional(readOnly = false)
    public void updateProjectEfficientFilter(Long workgroupId, Long userId, ChartFilter filter) {
        Map<String, String> prefs = new HashMap<String, String>();
        prefs.put(ChartFilter.PROJECT_EFFICIENT_FILTER_PREF_PREFIX
                + ChartFilter.CONTROL_NAME_CONTACTS_SHOW_OPTION, "" + filter.getShowOption());
        preferenceService.saveUserPreference(workgroupId, userId, prefs);
    }

    @Transactional(readOnly = true)
    public ChartFilterVO getProjectEfficientFilter(long workgroupId, long userId) {
        Map<String, String> prefs = preferenceService.findUserPrefs(workgroupId, userId);

        ChartFilterVO vo = new ChartFilterVO();
        String showOption = preferenceService.getString(ChartFilter.PROJECT_EFFICIENT_FILTER_PREF_PREFIX
                + ChartFilter.CONTROL_NAME_CONTACTS_SHOW_OPTION, prefs,
                String.valueOf(30));
        ChartFilter filter = new ChartFilter();
        filter.setShowOption(showOption);
        vo.setFilter(filter);
        return vo;
    }

    @Transactional(readOnly = false)
    public void updateOrderEfficientFilter(Long workgroupId, Long userId, ChartFilter filter) {
        Map<String, String> prefs = new HashMap<String, String>();
        prefs.put(ChartFilter.ORDER_EFFICIENT_FILTER_PREF_PREFIX
                + ChartFilter.CONTROL_NAME_CONTACTS_SHOW_OPTION, "" + filter.getShowOption());
        preferenceService.saveUserPreference(workgroupId, userId, prefs);
    }

    @Transactional(readOnly = true)
    public ChartFilterVO getOrderEfficientFilter(long workgroupId, long userId) {
        Map<String, String> prefs = preferenceService.findUserPrefs(workgroupId, userId);

        ChartFilterVO vo = new ChartFilterVO();
        String showOption = preferenceService.getString(ChartFilter.ORDER_EFFICIENT_FILTER_PREF_PREFIX
                + ChartFilter.CONTROL_NAME_CONTACTS_SHOW_OPTION, prefs,
                String.valueOf(30));
        ChartFilter filter = new ChartFilter();
        filter.setShowOption(showOption);
        vo.setFilter(filter);
        return vo;
    }
}
