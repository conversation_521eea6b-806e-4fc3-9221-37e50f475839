package com.noosh.app.service.reason;

import com.noosh.app.commons.dto.collaboration.WorkgroupDeactivationReasonDTO;
import com.noosh.app.commons.vo.collaboration.SystemDeactivationReasonVO;
import com.noosh.app.commons.entity.collaboration.DeactivationReason;
import com.noosh.app.commons.entity.collaboration.WorkgroupDeactivationReason;
import com.noosh.app.mapper.collaboration.WorkgroupDeactivationReasonMapper;
import com.noosh.app.repository.jpa.collaboration.DeactivationReasonRepository;
import com.noosh.app.repository.jpa.collaboration.WorkgroupDeactivationReasonRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.ArrayList;

@Service
@Transactional
public class ReasonService {

    @Autowired
    private DeactivationReasonRepository deactivationReasonRepository;

    @Autowired
    private WorkgroupDeactivationReasonRepository workgroupDeactivationReasonRepository;

    @Autowired
    private WorkgroupDeactivationReasonMapper workgroupDeactivationReasonMapper;

    public List<WorkgroupDeactivationReasonDTO> getProjectReasonsByWorkgroup(Long workgroupId) {
        List<WorkgroupDeactivationReason> workgroupReasons = workgroupDeactivationReasonRepository.findActiveByWorkgroupIdOrderByReasonOrder(workgroupId);
        return workgroupDeactivationReasonMapper.toDTOs(workgroupReasons);
    }

    public List<SystemDeactivationReasonVO> getSystemProjectReasons(Long workgroupId) {
        List<DeactivationReason> systemReasons = deactivationReasonRepository.findSystemReasons();
        List<WorkgroupDeactivationReason> workgroupReasons = workgroupDeactivationReasonRepository.findActiveByWorkgroupIdOrderByReasonOrder(workgroupId);
        
        List<Long> addedReasonIds = new ArrayList<>();
        for (WorkgroupDeactivationReason wgReason : workgroupReasons) {
            addedReasonIds.add(wgReason.getReasonId());
        }
        
        List<SystemDeactivationReasonVO> result = new ArrayList<>();
        for (DeactivationReason reason : systemReasons) {
            SystemDeactivationReasonVO vo = new SystemDeactivationReasonVO();
            
            // Copy fields from DeactivationReasonDTO
            vo.setId(reason.getId());
            vo.setConstantToken(reason.getConstantToken());
            vo.setDescriptionStrId(reason.getDescriptionStrId());
            vo.setNameStr(reason.getNameStr());
            vo.setIsSystem(reason.getIsSystem());
            
            // Set isAdded field
            vo.setIsAdded(addedReasonIds.contains(reason.getId()));
            
            result.add(vo);
        }
        
        return result;
    }
}