package com.noosh.app.service.estimate;

import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.estimate.EstimateManagerFilterDTO;
import com.noosh.app.commons.dto.estimate.EstimateManagerResultItemDTO;
import com.noosh.app.commons.entity.rfe.RfeSupplier;
import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.account.SupplierFlagRequestVO;
import com.noosh.app.commons.vo.account.SupplierFlagVO;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.commons.vo.estimate.EstimateManagerEstimateVO;
import com.noosh.app.commons.vo.estimate.EstimateManagerFilter;
import com.noosh.app.commons.vo.estimate.EstimateManagerFilterVO;
import com.noosh.app.commons.vo.estimate.EstimateManagerVO;
import com.noosh.app.mapper.estimate.EstimateMapper;
import com.noosh.app.repository.jpa.rfe.RfeSupplierRepository;
import com.noosh.app.repository.jpa.security.ObjectStateRepository;
import com.noosh.app.repository.jpa.security.WorkgroupRepository;
import com.noosh.app.repository.mybatis.estimate.EstimateMyBatisMapper;
import com.noosh.app.service.account.SupplierFlagService;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.util.*;

import java.time.Instant;
import java.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.PostConstruct;
import java.text.Collator;
import java.text.RuleBasedCollator;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.noosh.app.commons.vo.estimate.EstimateManagerFilter.*;

/**
 * Estimate manager service
 *
 * <AUTHOR>
 * @date 3/7/2021
 */
@Service
public class EstimateManagerService {


    @Autowired
    private EstimateMyBatisMapper estimateMyBatisMapper;
    @Autowired
    private EstimateMapper estimateMapper;
    @Autowired
    private I18NUtils i18NUtils;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private ObjectStateRepository objectStateRepository;
    @Autowired
    private WorkgroupRepository workgroupRepository;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private SupplierFlagService supplierFlagService;
    @Autowired
    private RfeSupplierRepository rfeSupplierRepository;

    /**
     * sort field map
     */
    private static final Map<String, String> SORT_FIELD_MAP = new HashMap<>();
    private static final List<String> PREF_IDS = new ArrayList<>(9);

    @PostConstruct
    public void init() {
        SORT_FIELD_MAP.put("ref_no", "UPPER(E_OWNER_REFERENCE)");
        SORT_FIELD_MAP.put("title", "UPPER(E_TITLE)");
        SORT_FIELD_MAP.put("submit_date", "E_SUBMIT_DATE");
        SORT_FIELD_MAP.put("project_name", "UPPER(PRJ_NAME)");
        SORT_FIELD_MAP.put("supplier", "UPPER(W_SUPPLIER_WORKGROUP_NAME), UPPER(E_SUPPLIER_CONTACT_NAME)");
        SORT_FIELD_MAP.put("rfe", "UPPER(R_TITLE)");
        SORT_FIELD_MAP.put("status", "E_STATUS, E_EXPIRATION_DATE");
        SORT_FIELD_MAP.put("project_owners", "OWNER_STR");

        PREF_IDS.add(EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_CREATE_DATE_FROM);
        PREF_IDS.add(EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_CREATE_DATE_TO);
        PREF_IDS.add(EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_CREATE_DATE_PAST_DAYS);
        PREF_IDS.add(EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_CREATE_DATE_FILTER_TYPE);
        PREF_IDS.add(EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_STATUS_IDS);
        PREF_IDS.add(EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_FILTER_OPTION);
        PREF_IDS.add(EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_PAGE_SIZE);
        PREF_IDS.add(EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_SORT);
        PREF_IDS.add(EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_SORT_ORDER);

    }

    public boolean isSortFieldExist(String sort) {
        return SORT_FIELD_MAP.containsKey(sort.toLowerCase());
    }

    public EstimateManagerFilterVO getFilter(long workgroupId, long userId) {

        Map<String, String> prefs = preferenceService.findUserPrefs(workgroupId, userId, PREF_IDS);
        EstimateManagerFilter filter = new EstimateManagerFilter();

        // create date
        String createDateFromPref = preferenceService.getString(
                EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_CREATE_DATE_FROM, prefs);
        if (NooshStringUtils.isNotEmpty(createDateFromPref)) {
            filter.setCreateDateFrom(Instant.ofEpochMilli(Long.valueOf(createDateFromPref)).atZone(ZoneId.of("UTC")).toLocalDateTime());
        }

        String createDateToPref = preferenceService.getString(
                EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_CREATE_DATE_TO, prefs);
        if (NooshStringUtils.isNotEmpty(createDateToPref)) {
            filter.setCreateDateTo(Instant.ofEpochMilli(Long.valueOf(createDateToPref)).atZone(ZoneId.of("UTC")).toLocalDateTime());
        }

        String createDateRangePref = preferenceService.getString(
                EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_CREATE_DATE_PAST_DAYS, prefs, "60");
        filter.setCreatePastDays(Integer.valueOf(createDateRangePref));

        String createDateFilterTypePref = preferenceService.getString(
                EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_CREATE_DATE_FILTER_TYPE, prefs, "2");
        filter.setCreateDateFilterType(Integer.valueOf(createDateFilterTypePref));


        String statusIdsStr = preferenceService.getString(
                EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_STATUS_IDS, prefs);
        if (NooshStringUtils.isNotEmpty(statusIdsStr)) {
            String[] statusIdsPref = Util.split(statusIdsStr, ",");
            filter.setStatusIds(Util.toLongArray(statusIdsPref));
        } else {
            filter.setStatusIds(new Long[]{});
        }

        String filterOption = preferenceService.getString(
                EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_FILTER_OPTION, prefs, "");
        if (!StringUtils.isEmpty(filterOption)) {
            filter.setShowOption(Integer.parseInt(filterOption));
        } else {
            if (permissionService.checkAll(PermissionID.VIEW_ESTIMATE, workgroupId, userId, Long.valueOf(-1))) {
                filter.setShowOption((int) FILTER_OPTION_WORKGROUP);
            } else {
                filter.setShowOption((int) FILTER_OPTION_USER);
            }
        }

        EstimateManagerFilterVO vo = new EstimateManagerFilterVO();
        vo.setFilter(filter);
        String pageSizeStr = preferenceService.getString(
                EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_PAGE_SIZE, prefs,
                String.valueOf(10));
        vo.setPageSize(Integer.valueOf(pageSizeStr));

        String sort = preferenceService.getString(
                EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_SORT, prefs,
                "ref_no");
        vo.setSort(sort);

        String order = preferenceService.getString(
                EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_SORT_ORDER, prefs,
                "desc");
        vo.setOrder(order);

        return vo;
    }

    public List<DropdownVO<Long>> getStatusList(String locale) {
        List<ObjectState> objectStates = objectStateRepository.findByObjectClassId(ObjectClassID.ESTIMATE);
        objectStates.removeIf(os -> os.getId() == ObjectStateID.ESTIMATE_CREATED);
        objectStates.removeIf(os -> os.getId() == ObjectStateID.ESTIMATE_PARTIAL_ACCEPTED);
        objectStates.removeIf(os -> os.getId() == ObjectStateID.ESTIMATE_DELETED);
        List<DropdownVO<Long>> dropdown = new ArrayList<>();
        objectStates.forEach(os -> {
            dropdown.add(new DropdownVO<>(os.getId(), i18NUtils.getMessage(os.getDescriptionStrId()), null, String.valueOf(os.getDescriptionStrId())));
        });

        dropdown.sort((o1, o2) -> {
            RuleBasedCollator collator = (RuleBasedCollator) Collator.getInstance(Locale.forLanguageTag(locale.replaceAll("_", "-")));
            return collator.compare(o1.getLabel(), o2.getLabel());
        });
        return dropdown;
    }


    public void updateFilter(Long workgroupId, Long userId, EstimateManagerFilter filter, Integer pageSize,
                             String sort, String order) throws Exception {
        Map<String, String> prefs = new HashMap<>();
        // create date
        prefs.put(
                EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_CREATE_DATE_FROM,
                (filter.getCreateDateFrom() == null) ? ""
                        : String.valueOf(filter.getCreateDateFrom().atZone(ZoneId.of("UTC")).toInstant().toEpochMilli()));
        prefs.put(
                EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_CREATE_DATE_TO,
                (filter.getCreateDateTo() == null) ? ""
                        : String.valueOf(filter.getCreateDateTo().atZone(ZoneId.of("UTC")).toInstant().toEpochMilli()));
        prefs.put(
                EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_CREATE_DATE_PAST_DAYS,
                (filter.getCreatePastDays() == null) ? "14" : String.valueOf(filter.getCreatePastDays()));
        prefs.put(
                EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_CREATE_DATE_FILTER_TYPE,
                (filter.getCreateDateFilterType() == null) ? "1" : String.valueOf(filter.getCreateDateFilterType()));

        if (filter.isStatusLimited()) {
            prefs.put(
                    EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_STATUS_IDS,
                    Util.arrayToString(filter.getStatusIds(), ",", false));
        } else {
            prefs.put(
                    EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_STATUS_IDS, "");
        }

        if (filter.getShowOption() != null) {
            prefs.put(
                    EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_FILTER_OPTION,
                    Long.toString(filter.getShowOption()));
        }

        prefs.put(
                EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_PAGE_SIZE, String.valueOf(pageSize));

        prefs.put(
                EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_SORT, sort);

        prefs.put(
                EST_MANAGER_FILTER_PREF_PREFIX + CONTROLNAME_SORT_ORDER, order);

        preferenceService.saveUserPreference(workgroupId, userId, prefs);
    }

    /**
     * make sure the RFE is also connected in the containable
     * since the supplier should not be able to see estimates for rfes that has been retracted by buyer
     * in practice, the supplier should still be able to see those estimates.  However, they currently
     * do not see it in the estimate home page.  So, we need to do this for consistencies
     *
     * @param filter      filter
     * @param workgroupId workgroup id
     * @param userId      curren user id
     * @param page        pagination
     * @return estimate manager vo
     */
    public EstimateManagerVO findEstimates(EstimateManagerFilter filter, long workgroupId, long userId, PageVO page) {
        long workgroupTypeId = workgroupRepository.findById(workgroupId).get().getWorkGroupTypeId();
        boolean isWorkgroupSupplier = workgroupTypeId == WorkgroupTypeID.SUPPLIER;

        EstimateManagerVO vo = new EstimateManagerVO();

        EstimateManagerFilterDTO filterDTO = toEstimateManagerFilterDTO(filter);

        String decodedStatus = getDecodedStatus();

        List<EstimateManagerEstimateVO> estimateVOs = new ArrayList<>();

        if (permissionService.checkWorkgroupLevelCannotPrivilege(PermissionID.VIEW_ESTIMATE, workgroupId, userId)) {
            page.setTotal(0);

        } else {

            List<EstimateManagerResultItemDTO> dtos = estimateMyBatisMapper.findEstimateManagerResultItems(

                    filterDTO, workgroupId, userId, decodedStatus, PageUtil.toPageDTO(page, SORT_FIELD_MAP));

            if (dtos != null && dtos.size() > 0) {
                page.setTotal(dtos.get(0).getMaxRownum());

                List<Long> rfeIds = dtos.stream()
                        .map(EstimateManagerResultItemDTO::getRfeId)
                        .collect(Collectors.toList());
                List<RfeSupplier> rfeSuppliers = rfeSupplierRepository.findDistinctByRfeIdIn(rfeIds);

                Map<String, Long> rfeIdSupplierIdMap = new HashMap<>();
                rfeSuppliers.forEach(rfeSupplier -> {
                    rfeIdSupplierIdMap.put(rfeSupplier.getRfeId() + "_" + rfeSupplier.getGroupId(),
                            rfeSupplier.getId());
                });

                List<SupplierFlagRequestVO> supplierFlagRequestVOs = new ArrayList<>(dtos.size());
                for (EstimateManagerResultItemDTO dto : dtos) {
                    SupplierFlagRequestVO supplierFlagRequestVO = new SupplierFlagRequestVO(false,
                            rfeIdSupplierIdMap.get(dto.getRfeId() + "_" + dto.getSupplierWorkgroupId()),
                            ObjectClassID.RFE_SUPPLIER,
                            dto.getRfeOwnerWorkgroupId(),
                            dto.getSupplierWorkgroupId());
                    supplierFlagRequestVOs.add(supplierFlagRequestVO);
                }
                Map<String, SupplierFlagVO> supplierFlagMap = supplierFlagService.getSupplierFlagVOs(supplierFlagRequestVOs);

                // prepare hot project map
                List<Long> projectIds = dtos.stream()
                        .map(EstimateManagerResultItemDTO::getProjectId)
                        .collect(Collectors.toList());
                Map<Long, Boolean> hotProjectMap = projectService.getIsHotForUser(projectIds, userId);

                for (EstimateManagerResultItemDTO dto : dtos) {
                    EstimateManagerEstimateVO itemVO = estimateMapper.toEstimateManagerResultItemVO(dto);
                    String supplierFlagKey = supplierFlagService.getSupplierFlagKey(
                            false,
                            rfeIdSupplierIdMap.get(dto.getRfeId() + "_" + dto.getSupplierWorkgroupId()),
                            ObjectClassID.RFE_SUPPLIER,
                            dto.getRfeOwnerWorkgroupId(),
                            dto.getSupplierWorkgroupId()
                    );
                    itemVO.setSupplierFlag(supplierFlagMap.get(supplierFlagKey));

                    itemVO.setIsHotProject(hotProjectMap.containsKey(dto.getProjectId()) ?
                            hotProjectMap.get(dto.getProjectId()) :
                            false);

                    String state = i18NUtils.getObjectStateMessage(dto.getObjectStateId());
                    itemVO.setStatus(state);
                    itemVO.setStatusId(dto.getObjectStateId());

                    itemVO.setProjectInactive(dto.getProjectObjectStateId() == ObjectStateID.PROJECT_INACTIVATED);
                    itemVO.setEstimateId(dto.getEstimateId());
                    if (dto.isUserBuyer()) {
                        itemVO.setRfeExternalLink(NooshOneUrlUtil.composeGotoRfeLinkToEnterprise(dto.getProjectId(), dto.getRfeId()));
                        itemVO.setEstimateExternalLink(NooshOneUrlUtil.composeGotoEstimateLinkToEnterprise(dto.getProjectId(), dto.getRfeId(), dto.getEstimateId()));
                    } else {
                        itemVO.setRfeExternalLink(NooshOneUrlUtil.composeGotoSupplierRfeLinkToEnterprise(dto.getProjectId(), dto.getRfeId()));
                        itemVO.setEstimateExternalLink(NooshOneUrlUtil.composeGotoSupplierEstimateLinkToEnterprise(dto.getProjectId(), dto.getRfeId(), dto.getEstimateId()));
                    }

                    itemVO.setProjectId(dto.getProjectId());
                    itemVO.setProjectExternalLink(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(dto.getProjectId()));
                    estimateVOs.add(itemVO);
                }
            } else {
               page.setTotal(0);
            }
        }

        vo.setWorkgroupSupplier(isWorkgroupSupplier);
        vo.setEstimates(estimateVOs);
        return vo;
    }


    private EstimateManagerFilterDTO toEstimateManagerFilterDTO(EstimateManagerFilter filter) {
        EstimateManagerFilterDTO dto = new EstimateManagerFilterDTO();

        dto.setSearchString(formatQueryString(filter.getSearchStr()));
        dto.setFilterByWorkgroup(filter.isFilterByWorkgroup());

        if (filter.isStatusLimited()) {
            dto.setStatusIds(Arrays.asList(filter.getStatusIds()));
        }


        LocalDateTime createDateFrom;
        LocalDateTime createDateTo;

        if (filter.getCreateDateFilterType() == 2) {
            // fixed date range for past days
            createDateTo = LocalDateTime.now(ZoneId.of("UTC"));
            createDateFrom = createDateTo.minusDays(filter.getCreatePastDays());
        } else {
            createDateFrom = filter.getCreateDateFrom();
            createDateTo = filter.getCreateDateTo();
        }

        if (createDateFrom != null) {
            String dateFrom = createDateFrom.format(DateTimeFormatter.ofPattern("MM/dd/yyyy"));
            String datetimeFrom = createDateFrom.format(DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss"));

            dto.setCreateDateFrom(dateFrom);
            dto.setCreateDatetimeFrom(datetimeFrom);
        }

        if (createDateTo != null) {
            String dateTo = createDateTo.format(DateTimeFormatter.ofPattern("MM/dd/yyyy"));
            String datetimeTo = createDateTo.format(DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss"));

            dto.setCreateDateTo(dateTo);
            dto.setCreateDatetimeTo(datetimeTo);
        }

        return dto;
    }

    /**
     * a* search by prefix a, a%
     * *a search by suffix a, %a
     * *a* or a search by field contains a, %a%
     *
     * @param originalSearchString original search string
     * @return formatted string with %
     */
    private String formatQueryString(String originalSearchString) {
        String searchString = null;
        if (originalSearchString != null && originalSearchString.length() > 0) {
            searchString = originalSearchString.replaceAll("\\*", "");
        }
        return searchString;
    }

    public List<DropdownVO<Long>> getFilterOptionDropdown(Long workgroupId, Long userId) {
        List<DropdownVO<Long>> dropdown = new ArrayList<>();

        if (permissionService.checkAll(PermissionID.VIEW_ESTIMATE, workgroupId, userId, Long.valueOf(-1))) {
            dropdown.add(new DropdownVO<>(FILTER_OPTION_WORKGROUP, i18NUtils.getMessage(StringID.ALL_ESTIMATES), null, String.valueOf(StringID.ALL_ESTIMATES)));
        }
        dropdown.add(new DropdownVO<>(FILTER_OPTION_USER, i18NUtils.getMessage(StringID.MY_ESTIMATES), null, String.valueOf(StringID.MY_ESTIMATES)));

        return dropdown;
    }

    public String getDecodedStatus() {
        List<ObjectState> objectStates = objectStateRepository.findByObjectClassId(ObjectClassID.ESTIMATE);
        objectStates.removeIf(os -> os.getId() == ObjectStateID.ESTIMATE_CREATED);
        objectStates.removeIf(os -> os.getId() == ObjectStateID.ESTIMATE_PARTIAL_ACCEPTED);
        objectStates.removeIf(os -> os.getId() == ObjectStateID.ESTIMATE_DELETED);
        StringBuilder sb = new StringBuilder();
        sb.append("DECODE(E.OC_OBJECT_STATE_ID,");
        objectStates.forEach(os -> {
            sb.append(os.getId() + ", '" + i18NUtils.getMessage(os.getDescriptionStrId()) + "',");
        });
        sb.append("'')");
        return sb.toString();
    }
}
