package com.noosh.app.service.account;

import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.entity.security.Workgroup;
import com.noosh.app.repository.jpa.security.WorkgroupRepository;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * <AUTHOR>
 * @date 4/29/2021
 */
@Service
public class AccountService {

    @Inject
    private WorkgroupRepository workgroupRepository;

    /**
     * get communisis portal
     * @param projectDTO
     * @param workgroupId
     * @return
     */
    public boolean isCommunisisPortal(ProjectDTO projectDTO, Long workgroupId) {
        String portal;
        if (projectDTO.getPortalWorkgroupId() > 0) {
            //WorkgroupBeanHome.find() is cached
            Workgroup portalGroup = workgroupRepository.findById(projectDTO.getPortalWorkgroupId()).orElse(null);
            portal = portalGroup.getPortal();
        } else {
            Workgroup workgroup = workgroupRepository.findById(workgroupId).orElse(null);
            portal = workgroup.getPortal();
        }
        return portal != null ? portal.contains("comm") : false;
    }

}
