package com.noosh.app.service.account;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.entity.account.SupplierWorkgroup;
import com.noosh.app.commons.entity.preference.Preference;
import com.noosh.app.commons.vo.account.SupplierFlagVO;
import com.noosh.app.feign.AccountOpenFeignClient;
import com.noosh.app.repository.account.SupplierRelationRepository;
import com.noosh.app.repository.jpa.preference.PreferenceRepository;
import com.noosh.app.repository.jpa.property.PropertyAttributeRepository;
import com.noosh.app.repository.mybatis.accounts.SupplierWorkgroupMybatisMapper;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: neals
 * @Date: 08/04/2016
 */
@Service
public class SupplierWorkgroupService {

    @Inject
    private SupplierWorkgroupMybatisMapper supplierWorkgroupMybatisMapper;
    @Inject
    private PreferenceRepository preferenceRepository;
    @Inject
    private PropertyAttributeRepository propertyAttributeRepository;
    @Inject
    private SupplierRelationRepository supplierRelationRepository;
    @Inject
    private AccountOpenFeignClient accountOpenFeignClient;

    public boolean isSupplierApproved(long ownerGroupId, long supplierGroupId, long clientGroupId, long objectClassId) {
        boolean result = false;
        SupplierWorkgroup supplierWorkgroup = findSupplierWorkgroup(ownerGroupId, supplierGroupId, clientGroupId);
        if (supplierWorkgroup != null) {
            result = supplierWorkgroup.getIsApproved();
        } else {
            Preference pref = preferenceRepository.findByObjectClassIdAndObjectId(ObjectClassID.WORKGROUP, ownerGroupId);
            Map prefs = new HashMap<>();

            pref.getProperty().getPropertyAttributeSet().forEach(pa -> {
                prefs.put(pa.getPropertyParam().getParamName(), pa.getStringValue());
            });
            String prefString = null;
            if (objectClassId == ObjectClassID.ORDER) {
                prefString = "BU_ORDER_UNREGIS_SUP";
            } else if (objectClassId == ObjectClassID.RFE) {
                prefString = "BU_RFE_UNREGIS_SUP";
            } else {
                // throw new UnexpectedException(objectClassId+" not supported in this method");
            }

            //Here we check if there has value and value = 0 means false, as NE default it to true
            String value = (String) prefs.get(prefString);
            if (value != null && value.equals("0"))
                result = Boolean.FALSE;
            else
                result = Boolean.TRUE;
        }

        return result;
    }

    public SupplierWorkgroup findSupplierWorkgroup(long ownerGroupId, long supplierGroupId, long clientGroupId) {
        List<SupplierWorkgroup> supplierWorkgroups = supplierWorkgroupMybatisMapper.find(ownerGroupId, supplierGroupId, clientGroupId);
        SupplierWorkgroup supplierWorkgroup = null;
        if(supplierWorkgroups != null && supplierWorkgroups.size() > 0){
            supplierWorkgroup = supplierWorkgroups.get(supplierWorkgroups.size() - 1);
        }
        return supplierWorkgroup;
    }

    public List<SupplierWorkgroup> findSupplierWorkgroups(long ownerGroupId, long supplierGroupId, long clientGroupId) {
        List<SupplierWorkgroup> supplierWorkgroups = supplierWorkgroupMybatisMapper.find(ownerGroupId, supplierGroupId, clientGroupId);
        return supplierWorkgroups;
    }

    public SupplierFlagVO getSupplierFlagVO(boolean isRealTimeQuery,
                                            long objectId,
                                            long objectClassId,
                                            long ownerWorkgroupId,
                                            long supplierWorkgroupId) {
        return accountOpenFeignClient.getSupplierFlagVO(isRealTimeQuery, objectId, objectClassId, ownerWorkgroupId, supplierWorkgroupId);
    }
}
