package com.noosh.app.service.accounts;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.noosh.app.commons.constant.StringID;
import com.noosh.app.commons.dto.accounts.TermsDTO;
import com.noosh.app.commons.dto.accounts.TermsTypeDTO;
import com.noosh.app.commons.entity.accounts.Terms;
import com.noosh.app.commons.vo.workgroup.option.terms.EditTermsVO;
import com.noosh.app.commons.vo.workgroup.option.terms.TermVO;
import com.noosh.app.commons.vo.workgroup.option.terms.ViewTermsVO;
import com.noosh.app.repository.jpa.accounts.TermsRepository;
import com.noosh.app.repository.mybatis.accounts.TermsMyBatisMapper;
import com.noosh.app.repository.mybatis.accounts.TermsTypeMyBatisMapper;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.util.NooshOneUrlUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> Shan
 * @since 8/8/2022
 */
@Service
public class TermsService {

    @Autowired
    private TermsTypeMyBatisMapper termsTypeMyBatisMapper;
    @Autowired
    private TermsMyBatisMapper termsMyBatisMapper;
    @Autowired
    private TermsRepository termsRepository;
    @Autowired
    private PreferenceService preferenceService;

    private static final Map<Long, String> TYPE_DESC_TO_PREF_MAP = new HashMap<>(7);
    private static final List<String> PREFS = new ArrayList<>(7);

    static {
        TYPE_DESC_TO_PREF_MAP.put(StringID.AC_TERMS_TYPE_DESCRIPTION_RFE, "WORKGROUP_OPTION_RFE_TC");
        TYPE_DESC_TO_PREF_MAP.put(StringID.AC_TERMS_TYPE_DESCRIPTION_PURCHASE, "WORKGROUP_OPTION_PURCHASE_TC");
        TYPE_DESC_TO_PREF_MAP.put(StringID.AC_TERMS_TYPE_DESCRIPTION_ESTIMATE, "WORKGROUP_OPTION_ESTIMATE_TC");
        TYPE_DESC_TO_PREF_MAP.put(StringID.AC_TERMS_TYPE_DESCRIPTION_SALES, "WORKGROUP_OPTION_ORDER_TC");
        TYPE_DESC_TO_PREF_MAP.put(StringID.AC_TERMS_TYPE_DESCRIPTION_INVOICE, "WORKGROUP_OPTION_INVOICE_TC");
        TYPE_DESC_TO_PREF_MAP.put(StringID.AC_TERMS_TYPE_DESCRIPTION_PROPOSAL, "WORKGROUP_OPTION_PROPOSAL_TC");
        TYPE_DESC_TO_PREF_MAP.put(StringID.AC_TERMS_TYPE_DESCRIPTION_QUOTE, "WORKGROUP_OPTION_NGE_QUOTE_TERMS_CONDITION");

        PREFS.addAll(TYPE_DESC_TO_PREF_MAP.values());
    }

    public ViewTermsVO getTerms(Long workgroupId) {
        Map<String, String> prefs = preferenceService.findGroupPrefs(workgroupId, PREFS);

        List<TermsTypeDTO> termsTypeDTOS = termsTypeMyBatisMapper.findAll();

        ViewTermsVO viewTermsVO = new ViewTermsVO();

        List<TermVO> termVOS = new ArrayList<>();
        termsTypeDTOS.forEach(termsTypeDTO -> {
            TermsDTO termsDTO = termsMyBatisMapper.findCurrentByWorkgroupIdAndTermsTypeId(workgroupId, termsTypeDTO.getId());
            final boolean enabled = preferenceService.check(TYPE_DESC_TO_PREF_MAP.get(termsTypeDTO.getDescriptionStrId()), prefs);
            if (enabled) {
                TermVO termVO = new TermVO();
                termVO.setEnabled(true);
                termVO.setTermTypeDescStrId(termsTypeDTO.getDescriptionStrId());
                termVO.setTermTypeId(termsTypeDTO.getId());
                if (termsDTO != null) {
                    termVO.setId(termsDTO.getId());
                    termVO.setText(termsDTO.getText());
                }
                termVOS.add(termVO);
            }

        });
        viewTermsVO.setTerms(termVOS);

        viewTermsVO.setBackToOptionsExternalUrl(getBackToOptionsExternalUrl());

        return viewTermsVO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void editTerms(Long userId, Long workgroupId, EditTermsVO editTermsVO) {
        Map<String, String> prefs = preferenceService.findGroupPrefs(workgroupId, PREFS);
        List<TermsTypeDTO> termTypeDTOS = termsTypeMyBatisMapper.findAll();
        Map<Long, TermsTypeDTO> termTypeMap = new HashMap<>();
        for (TermsTypeDTO termTypeDTO : termTypeDTOS) {
            termTypeMap.put(termTypeDTO.getId(), termTypeDTO);
        }

        editTermsVO.getTerms().forEach(termVO -> {

            if (!termTypeMap.containsKey(termVO.getTermTypeId())) {
                throw new IllegalArgumentException("invalid term type id " + termVO.getTermTypeId());
            }

            final boolean enabled = preferenceService.check(TYPE_DESC_TO_PREF_MAP.get(termTypeMap.get(termVO.getTermTypeId()).getDescriptionStrId()), prefs);
            if(!enabled) {
                throw new IllegalArgumentException("invalid term type id " + termVO.getTermTypeId());
            }

            Terms term;
            if (termVO.getId() == null) {
                TermsDTO termsDTO = termsMyBatisMapper.findCurrentByWorkgroupIdAndTermsTypeId(workgroupId, termVO.getTermTypeId());
                if (termsDTO != null) {
                    throw new IllegalArgumentException("Term as type id " + termVO.getTermTypeId() + " is already exist, please set the correct term id in request body");
                }

                term = new Terms();
                term.setText(termVO.getText());
                term.setTermsTypeId(termVO.getTermTypeId());
                term.setVersionNumber(0L);
                term.setWorkgroupId(workgroupId);
                term.setCreateUserId(userId);
            } else {
                TermsDTO termsDTO = termsMyBatisMapper.findCurrentByWorkgroupIdAndTermsTypeId(workgroupId, termVO.getTermTypeId());
                term = new Terms();
                term.setText(termVO.getText());
                term.setTermsTypeId(termVO.getTermTypeId());
                term.setVersionNumber(termsDTO.getVersionNumber() + 1L);
                term.setWorkgroupId(workgroupId);
                term.setCreateUserId(userId);
            }

            termsRepository.saveAndFlush(term);
        });
    }

    private String getBackToOptionsExternalUrl() {
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/home");
    }

}
