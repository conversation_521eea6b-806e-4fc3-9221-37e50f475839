package com.noosh.app.service.accounts;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.accounts.MultiExchangeRateDTO;
import com.noosh.app.commons.dto.security.CurrencyDTO;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.workgroup.option.CurrencyExchangeRateVO;
import com.noosh.app.commons.vo.workgroup.option.DualCurrencyListVO;
import com.noosh.app.commons.vo.workgroup.option.dualCurrency.DualCurrencySearchFilterVO;
import com.noosh.app.commons.vo.workgroup.option.dualCurrency.DualCurrencyListVOWithTarget;
import com.noosh.app.commons.vo.workgroup.option.dualCurrency.DualCurrencyVO;
import com.noosh.app.repository.mybatis.CurrencyMyBatisMapper;
import com.noosh.app.repository.mybatis.accounts.MultiExchangeRateMyBatisMapper;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.util.Money;
import com.noosh.app.service.util.NooshOneUrlUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> Shan
 * @since 7/18/2022
 */
@Service
public class DualCurrencyService {
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private MultiExchangeRateMyBatisMapper multiExchangeRateMyBatisMapper;
    @Autowired
    private CurrencyMyBatisMapper currencyMyBatisMapper;

    public DualCurrencyListVO getDualCurrencyList(Long groupId) {
        boolean supportSellSideMultiCurrency = preferenceService.check(PreferenceID.WORKGROUP_OPTION_SELL_SIDE_MULTIPLE_CURRENCY, groupId);

        List<MultiExchangeRateDTO> supplierCurrencyExchangeRateDTOs = multiExchangeRateMyBatisMapper.findAllMultiExchangeRates(groupId,
                "supplier", supportSellSideMultiCurrency);
        List<MultiExchangeRateDTO> clientCurrencyExchangeRateDTOs = multiExchangeRateMyBatisMapper.findAllMultiExchangeRates(groupId,
                "client", supportSellSideMultiCurrency);

        DualCurrencyListVO dualCurrencyListVO = new DualCurrencyListVO();

        // Vendor Currency Exchange Rate
        List<CurrencyExchangeRateVO> vendorCurrencyExchangeRateVOs = new ArrayList<>();
        supplierCurrencyExchangeRateDTOs.forEach(cerDTO -> {
            CurrencyExchangeRateVO vo = new CurrencyExchangeRateVO();
            vo.setCurrency(cerDTO.getFromCurrency().getCurrency());
            vo.setCurrencySymbol(Money.getCurrencySymbol(cerDTO.getFromCurrency().getCurrency()));
            vo.setExchangeRate(cerDTO.getRate());
            vo.setDateToActivate(cerDTO.getActivateDate());
            vendorCurrencyExchangeRateVOs.add(vo);
        });
        dualCurrencyListVO.setVendorCurrencyExchangeRates(vendorCurrencyExchangeRateVOs);

        // Client Currency Exchange Rate
        dualCurrencyListVO.setShowClientColumn(supportSellSideMultiCurrency);

        List<CurrencyExchangeRateVO> clientCurrencyExchangeRateVOs = new ArrayList<>();
        clientCurrencyExchangeRateDTOs.forEach(cerDTO -> {
            CurrencyExchangeRateVO vo = new CurrencyExchangeRateVO();
            if (supportSellSideMultiCurrency && cerDTO.getClientWorkgroup() != null) {
                if (cerDTO.getClientWorkgroup().getIsNoosh() != null && cerDTO.getClientWorkgroup().getIsNoosh()) {
                    vo.setClient(cerDTO.getClientWorkgroup().getClientWorkgroup().getName());
                } else {
                    vo.setClient(cerDTO.getClientWorkgroup().getName());
                }
            }
            vo.setCurrency(cerDTO.getToCurrency().getCurrency());
            vo.setCurrencySymbol(Money.getCurrencySymbol(cerDTO.getToCurrency().getCurrency()));
            vo.setExchangeRate(cerDTO.getRate());
            vo.setDateToActivate(cerDTO.getActivateDate());
            clientCurrencyExchangeRateVOs.add(vo);
        });
        dualCurrencyListVO.setClientCurrencyExchangeRates(clientCurrencyExchangeRateVOs);

        // buttons
        dualCurrencyListVO.setEditExternalUrl(NooshOneUrlUtil.composeEditDualCurrencyLinkToEnterprise());
        dualCurrencyListVO.setBackToOptionsExternalUrl(NooshOneUrlUtil.composeBackToOptionsLinkToEnterprise());

        return dualCurrencyListVO;
    }

    public Double getSupplierDualCurrency(Long workgroupId, Long buWorkgroupId, Long currencyId) {
        MultiExchangeRateDTO dualCurrency = multiExchangeRateMyBatisMapper.findMultiExchangeRate(workgroupId, buWorkgroupId, currencyId, "SUPPLIER");
        return dualCurrency != null ? dualCurrency.getRate() : null;
    }

    public Double getClientDualCurrency(Long workgroupId, Long buWorkgroupId, Long currencyId) {
        MultiExchangeRateDTO dualCurrency = multiExchangeRateMyBatisMapper.findMultiExchangeRate(workgroupId, buWorkgroupId, currencyId, "CLIENT");
        return dualCurrency != null ? dualCurrency.getRate() : null;
    }

    public Double getSupplierDualCurrencyByTargetGroupId(Long workgroupId, Long targetWorkgroupId) {
        MultiExchangeRateDTO dualCurrency = multiExchangeRateMyBatisMapper.findSupplierDualCurrencyByTargetGroupId(workgroupId, targetWorkgroupId);
        return dualCurrency != null ? dualCurrency.getRate() : null;
    }

    public Double getClientDualCurrencyByTargetGroupId(Long workgroupId, Long buWorkgroupId, Long targetWorkgroupId) {
        MultiExchangeRateDTO dualCurrency = multiExchangeRateMyBatisMapper.findClientDualCurrencyByTargetGroupId(workgroupId, buWorkgroupId, targetWorkgroupId);
        return dualCurrency != null ? dualCurrency.getRate() : null;
    }

    public DualCurrencyListVOWithTarget getDualCurrencyList(Long groupId, String target, PageVO page, String activeDateFrom, String activeDateTo) {
        boolean supportSellSideMultiCurrency = preferenceService.check(PreferenceID.WORKGROUP_OPTION_SELL_SIDE_MULTIPLE_CURRENCY, groupId);
        DualCurrencyListVOWithTarget listVO = new DualCurrencyListVOWithTarget();
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        pageInfo.setUnsafeOrderBy(page.getOrderBy());
        Boolean isVendor = false, isClient = false;
        String targetType = "";
        List<MultiExchangeRateDTO> multiExchangeRateDTOList = new ArrayList<>();
        if (target.equalsIgnoreCase("SUPPLIER") || target.equalsIgnoreCase("VENDOR")) {
            targetType = "SUPPLIER";
            listVO.setTarget("Vendor");
            isVendor = true;
            multiExchangeRateDTOList = multiExchangeRateMyBatisMapper.findAllMultiExchangeRatesWithoutOrder(groupId, "supplier", supportSellSideMultiCurrency, activeDateFrom, activeDateTo);
        } else if (target.equalsIgnoreCase("CLIENT")) {
            targetType = "CLIENT";
            listVO.setTarget("Client");
            isClient = true;
            multiExchangeRateDTOList = multiExchangeRateMyBatisMapper.findAllMultiExchangeRatesWithoutOrder(groupId, "client", supportSellSideMultiCurrency, activeDateFrom, activeDateTo);
        }
        page.setTotal(pageInfo.getTotal());

        List<DualCurrencyVO> currencyList = new ArrayList<>();
        if (!multiExchangeRateDTOList.isEmpty()) {
            for (MultiExchangeRateDTO dto : multiExchangeRateDTOList) {
                DualCurrencyVO vo = new DualCurrencyVO();
                vo.setMultiExchangeRateId(dto.getId());
                if (isVendor) {
                    vo.setCurrency(dto.getFromCurrency().getCurrency());
                    vo.setCurrencyId(dto.getFromCurrency().getId());
                    vo.setCurrencySymbol(Money.getCurrencySymbol(dto.getFromCurrency().getCurrency()));
                }
                if (isClient) {
                    if (supportSellSideMultiCurrency && dto.getClientWorkgroup() != null) {
                        vo.setBuClientWorkgroupId(dto.getClientWorkgroup().getId());
                        if (dto.getClientWorkgroup().getIsNoosh() != null && dto.getClientWorkgroup().getIsNoosh()) {
                            vo.setClient(dto.getClientWorkgroup().getClientWorkgroup().getName());
                        } else {
                            vo.setClient(dto.getClientWorkgroup().getName());
                        }
                    }
                    vo.setCurrency(dto.getToCurrency().getCurrency());
                    vo.setCurrencyId(dto.getToCurrency().getId());
                    vo.setCurrencySymbol(Money.getCurrencySymbol(dto.getToCurrency().getCurrency()));
                }
                vo.setExchangeRate(dto.getRate());
                vo.setDateToActivate(dto.getActivateDate());
                currencyList.add(vo);
            }
        }
        listVO.setCurrencyList(currencyList);

        Boolean isShowDefault = multiExchangeRateMyBatisMapper.findAllMultiExchangeRates(groupId, targetType, supportSellSideMultiCurrency).isEmpty();
        listVO.setShowDefault(isShowDefault);
        List<DualCurrencyVO> defaultCurrencyList = new ArrayList<>();
        if (isVendor) {
            List<CurrencyDTO> defaultVendorCurrencyList = currencyMyBatisMapper.findDefaultVendorDualCurrencies(groupId);
            if (!defaultVendorCurrencyList.isEmpty()) {
                for (CurrencyDTO currencyDTO : defaultVendorCurrencyList) {
                    DualCurrencyVO vo = new DualCurrencyVO();
                    vo.setCurrency(currencyDTO.getCurrency());
                    vo.setCurrencyId(currencyDTO.getId());
                    vo.setCurrencySymbol(Money.getCurrencySymbol(currencyDTO.getCurrency()));
                    defaultCurrencyList.add(vo);
                }
            }
        }
        if (isClient) {
            List<CurrencyDTO> defaultClientCurrencyList = currencyMyBatisMapper.findDefaultClientDualCurrencies(groupId);
            if (!defaultClientCurrencyList.isEmpty()) {
                for (CurrencyDTO currencyDTO : defaultClientCurrencyList) {
                    DualCurrencyVO vo = new DualCurrencyVO();
                    vo.setCurrency(currencyDTO.getCurrency());
                    vo.setCurrencyId(currencyDTO.getId());
                    vo.setCurrencySymbol(Money.getCurrencySymbol(currencyDTO.getCurrency()));
                    defaultCurrencyList.add(vo);
                }
            }
        }
        listVO.setDefaultCurrencyList(defaultCurrencyList);
        return listVO;
    }

    @Transactional
    public void updateDualCurrencyFilter(Long workgroupId, Long userId, Integer pageSize, String target, String sort, String order) {
        Map<String, String> preferences = new HashMap<>();
        if (target.equalsIgnoreCase("SUPPLIER") || target.equalsIgnoreCase("VENDOR")) {
            preferences.put(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.VENDOR_PAGE_SIZE, pageSize.toString());
            preferences.put(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.VENDOR_SORT, sort);
            preferences.put(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.VENDOR_SORT_ORDER, order);
        }
        if (target.equalsIgnoreCase("CLIENT")) {
            preferences.put(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.CLIENT_PAGE_SIZE, pageSize.toString());
            preferences.put(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.CLIENT_SORT, sort);
            preferences.put(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.CLIENT_SORT_ORDER, order);
        }
        preferenceService.saveUserPreference(workgroupId, userId, preferences);
    }

    public DualCurrencySearchFilterVO getDualCurrencyFilter(Long workgroupId, Long userId) {
        Map<String, String> preferences = preferenceService.findUserPrefs(workgroupId, userId);
        DualCurrencySearchFilterVO filterVO = new DualCurrencySearchFilterVO();

        if (preferences.containsKey(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.VENDOR_PAGE_SIZE)) {
            filterVO.setVendorPageSize(Integer.valueOf(preferences.get(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.VENDOR_PAGE_SIZE)));
        } else {
            filterVO.setVendorPageSize(DualCurrencySearchFilterVO.PAGE_SIZE_DEFAULT_VALUE);
        }
        if (preferences.containsKey(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.VENDOR_SORT)) {
            filterVO.setVendorSort(preferences.get(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.VENDOR_SORT));
        } else {
            filterVO.setVendorSort(DualCurrencySearchFilterVO.VENDOR_SORT_DEFAULT_VALUE);
        }
        if (preferences.containsKey(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.CLIENT_PAGE_SIZE)) {
            filterVO.setClientPageSize(Integer.valueOf(preferences.get(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.CLIENT_PAGE_SIZE)));
        } else {
            filterVO.setClientPageSize(DualCurrencySearchFilterVO.PAGE_SIZE_DEFAULT_VALUE);
        }
        if (preferences.containsKey(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.CLIENT_SORT)) {
            filterVO.setClientSort(preferences.get(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.CLIENT_SORT));
        } else {
            filterVO.setClientSort(DualCurrencySearchFilterVO.CLIENT_SORT_DEFAULT_VALUE);
        }
        if (preferences.containsKey(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.VENDOR_SORT_ORDER)) {
            //If exists vendor's sort order, then get it
            filterVO.setVendorSortOrder(preferences.get(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.VENDOR_SORT_ORDER));
        } else{
            //If vendor's sort is currency, then set default value to currency sort order to asc
            //If vendor's sort is not currency, then set default value to activate date sort order to desc
            if(filterVO.getVendorSort().toUpperCase().contains("CURRENCY")){
                filterVO.setVendorSortOrder(DualCurrencySearchFilterVO.CURRENCY_SORT_ORDER_DEFAULT_VALUE);
            }else{
                filterVO.setVendorSortOrder(DualCurrencySearchFilterVO.ACTIVATE_DATE_SORT_ORDER_DEFAULT_VALUE);
            }
        }
        if (preferences.containsKey(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.CLIENT_SORT_ORDER)) {
            //If exists client's sort order, then get it
            filterVO.setClientSortOrder(preferences.get(DualCurrencySearchFilterVO.DUAL_CURRENCY_LIST_FILTER_PREFIX + DualCurrencySearchFilterVO.CLIENT_SORT_ORDER));
        } else{
            //If vendor's sort is currency, then set default value to currency sort order to asc
            //If vendor's sort is not currency, then set default value to activate date sort order to desc
            if(filterVO.getClientSort().toUpperCase().contains("CURRENCY")){
                filterVO.setClientSortOrder(DualCurrencySearchFilterVO.CURRENCY_SORT_ORDER_DEFAULT_VALUE);
            }else{
                filterVO.setClientSortOrder(DualCurrencySearchFilterVO.ACTIVATE_DATE_SORT_ORDER_DEFAULT_VALUE);
            }
        }
        return filterVO;
    }

    public DualCurrencyVO getDualCurrencyDetail(Long multiExchangeRateId, Long groupId) {
        DualCurrencyVO vo = new DualCurrencyVO();
        boolean supportSellSideMultiCurrency = preferenceService.check(PreferenceID.WORKGROUP_OPTION_SELL_SIDE_MULTIPLE_CURRENCY, groupId);
        MultiExchangeRateDTO dto = multiExchangeRateMyBatisMapper.getMultiExchangeRateById(multiExchangeRateId);
        vo.setMultiExchangeRateId(dto.getId());
        vo.setTarget(dto.getTargetWorkgroupType());
        if (dto.getTargetWorkgroupType().equalsIgnoreCase("SUPPLIER")) {
            vo.setCurrency(dto.getFromCurrency().getCurrency());
            vo.setCurrencyId(dto.getFromCurrency().getId());
            vo.setCurrencySymbol(Money.getCurrencySymbol(dto.getFromCurrency().getCurrency()));
        }
        if (dto.getTargetWorkgroupType().equalsIgnoreCase("CLIENT")) {
            if (supportSellSideMultiCurrency && dto.getClientWorkgroup() != null) {
                vo.setBuClientWorkgroupId(dto.getClientWorkgroup().getId());
                if (dto.getClientWorkgroup().getIsNoosh() != null && dto.getClientWorkgroup().getIsNoosh()) {
                    vo.setClient(dto.getClientWorkgroup().getClientWorkgroup().getName());
                } else {
                    vo.setClient(dto.getClientWorkgroup().getName());
                }
            }
            vo.setCurrency(dto.getToCurrency().getCurrency());
            vo.setCurrencyId(dto.getToCurrency().getId());
            vo.setCurrencySymbol(Money.getCurrencySymbol(dto.getToCurrency().getCurrency()));
        }
        vo.setExchangeRate(dto.getRate());
        vo.setDateToActivate(dto.getActivateDate());
        return vo;
    }

    public List<String> getUnconfiguredCurrencyListForSupplier(Long workgroupId) {
        List<String> currencyList = new ArrayList<>();
        boolean supportSellSideMultiCurrency = preferenceService.check(PreferenceID.WORKGROUP_OPTION_SELL_SIDE_MULTIPLE_CURRENCY, workgroupId);
        List<CurrencyDTO> defaultVendorCurrencyList = currencyMyBatisMapper.findDefaultVendorDualCurrencies(workgroupId);
        if (!defaultVendorCurrencyList.isEmpty()) {
            for (CurrencyDTO currencyDTO : defaultVendorCurrencyList) {
                currencyList.add(currencyDTO.getCurrency());
            }
        }
        List<MultiExchangeRateDTO> exchangeRateDTOS = multiExchangeRateMyBatisMapper.findAllMultiExchangeRates(workgroupId, "SUPPLIER", supportSellSideMultiCurrency);
        if (!exchangeRateDTOS.isEmpty()) {
            for (MultiExchangeRateDTO exchangeRateDTO : exchangeRateDTOS) {
                if (!currencyList.isEmpty() && currencyList.contains(exchangeRateDTO.getFromCurrency().getCurrency())) {
                    currencyList.remove(exchangeRateDTO.getFromCurrency().getCurrency());
                }
            }
        }
        return currencyList;
    }

    public List<String> getUnconfiguredCurrencyListForClient(Long workgroupId) {
        List<String> currencyList = new ArrayList<>();
        boolean supportSellSideMultiCurrency = preferenceService.check(PreferenceID.WORKGROUP_OPTION_SELL_SIDE_MULTIPLE_CURRENCY, workgroupId);
        List<CurrencyDTO> defaultClientCurrencyList = currencyMyBatisMapper.findDefaultClientDualCurrencies(workgroupId);
        if (!defaultClientCurrencyList.isEmpty()) {
            for (CurrencyDTO currencyDTO : defaultClientCurrencyList) {
                currencyList.add(currencyDTO.getCurrency());
            }
        }
        List<MultiExchangeRateDTO> exchangeRateDTOS = multiExchangeRateMyBatisMapper.findAllMultiExchangeRates(workgroupId, "CLIENT", supportSellSideMultiCurrency);
        if (!exchangeRateDTOS.isEmpty()) {
            for (MultiExchangeRateDTO exchangeRateDTO : exchangeRateDTOS) {
                if (!currencyList.isEmpty() && currencyList.contains(exchangeRateDTO.getToCurrency().getCurrency())) {
                    currencyList.remove(exchangeRateDTO.getToCurrency().getCurrency());
                }
            }
        }
        return currencyList;
    }
}
