package com.noosh.app.service.order;

import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.address.AddressDTO;
import com.noosh.app.commons.dto.breakout.BreakoutTypeDTO;
import com.noosh.app.commons.dto.order.OrderItemDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import com.noosh.app.commons.dto.rfe.RfeSupplierDTO;
import com.noosh.app.commons.dto.shipment.UofmDTO;
import com.noosh.app.commons.dto.spec.SpecDTO;
import com.noosh.app.commons.entity.collaboration.SyContainable;
import com.noosh.app.commons.entity.estimate.Estimate;
import com.noosh.app.commons.entity.estimate.EstimateItem;
import com.noosh.app.commons.entity.estimate.EstimateItemPrice;
import com.noosh.app.commons.entity.job.PcJob;
import com.noosh.app.commons.entity.property.Property;
import com.noosh.app.commons.entity.rfe.Rfe;
import com.noosh.app.commons.entity.shipment.Shipment;
import com.noosh.app.commons.entity.spec.Spec;
import com.noosh.app.commons.entity.spec.SpecNode;
import com.noosh.app.commons.entity.spec.SpecReference;
import com.noosh.app.commons.entity.terms.AcTerms;
import com.noosh.app.commons.entity.uofm.Uofm;
import com.noosh.app.commons.vo.breakout.BreakoutTypeVO;
import com.noosh.app.commons.vo.breakout.BreakoutVO;
import com.noosh.app.commons.vo.order.OrderEditVO;
import com.noosh.app.commons.vo.order.OrderGeneralInfoVO;
import com.noosh.app.commons.vo.order.OrderItemSpecsVO;
import com.noosh.app.commons.vo.order.OrderItemVO;
import com.noosh.app.commons.vo.property.PropertyAttributeVO;
import com.noosh.app.commons.vo.shipment.ShipmentVO;
import com.noosh.app.commons.vo.spec.SpecVO;
import com.noosh.app.commons.vo.uofm.UofmVO;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.feign.ProjectOpenFeignClient;
import com.noosh.app.mapper.OrderMapper;
import com.noosh.app.mapper.account.AddressMapper;
import com.noosh.app.mapper.breakout.BreakoutTypeMapper;
import com.noosh.app.mapper.property.PropertyAttributeMapper;
import com.noosh.app.mapper.shipment.ShipmentMapper;
import com.noosh.app.mapper.spec.SpecMapper;
import com.noosh.app.mapper.terms.AcTermsMapper;
import com.noosh.app.mapper.uofm.UofmMapper;
import com.noosh.app.repository.collaboration.CollaborationRepository;
import com.noosh.app.repository.estimate.EstimateItemPriceRepository;
import com.noosh.app.repository.estimate.EstimateRepository;
import com.noosh.app.repository.jpa.job.JobRepository;
import com.noosh.app.repository.jpa.property.PropertyRepository;
import com.noosh.app.repository.jpa.spec.SpecNodeRepository;
import com.noosh.app.repository.jpa.spec.SpecRepository;
import com.noosh.app.repository.mybatis.address.AddressMyBatisMapper;
import com.noosh.app.repository.mybatis.breakouttype.BreakoutTypeMyBatisMapper;
import com.noosh.app.repository.mybatis.property.PropertyMyBatisMapper;
import com.noosh.app.repository.mybatis.shipment.ShipmentMyBatisMapper;
import com.noosh.app.repository.mybatis.spec.SpecMyBatisMapper;
import com.noosh.app.repository.shipment.ShipmentRepository;
import com.noosh.app.repository.terms.AcTermsRepository;
import com.noosh.app.repository.uofm.UofmRepository;
import com.noosh.app.service.OrderService;
import com.noosh.app.service.breakout.BreakoutService;
import com.noosh.app.service.permission.ordering.EditOrderPermission;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.rfe.RfeService;
import com.noosh.app.service.routing.RoutingService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.terms.TermsService;
import com.noosh.app.service.util.NooshOneUrlUtil;
import com.noosh.app.service.vat.VatService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrderEditService {
    @Inject
    private ProjectService projectService;
    @Inject
    private OrderService orderService;
    @Inject
    private PreferenceService preferenceService;
    @Inject
    private PermissionService permissionService;
    @Inject
    private RfeService rfeService;
    @Inject
    private BreakoutService breakoutService;
    @Inject
    private EditOrderPermission editOrderPermission;
    @Inject
    private OrderMapper orderMapper;
    @Inject
    private ProjectOpenFeignClient projectOpenFeignClient;
    @Inject
    private JobRepository jobRepository;
    @Inject
    private SpecMyBatisMapper specMyBatisMapper;
    @Inject
    private SpecMapper specMapper;
    @Inject
    private UofmRepository uofmRepository;
    @Inject
    private UofmMapper uofmMapper;
    @Inject
    private AcTermsRepository acTermsRepository;
    @Inject
    private AcTermsMapper acTermsMapper;
    @Inject
    private CollaborationRepository collaborationRepository;
    @Inject
    private RoutingService routingService;
    @Inject
    private EstimateRepository estimateRepository;
    @Inject
    private EstimateItemPriceRepository estimateItemPriceRepository;
    @Inject
    private SpecRepository specRepository;
    @Inject
    private SpecNodeRepository specNodeRepository;
    @Inject
    private ShipmentRepository shipmentRepository;
    @Inject
    private ShipmentMapper shipmentMapper;
    @Inject
    private BreakoutTypeMyBatisMapper breakoutTypeMyBatisMapper;
    @Inject
    private BreakoutTypeMapper breakoutTypeMapper;
    @Inject
    private PropertyMyBatisMapper propertyMyBatisMapper;
    @Inject
    private AddressMapper addressMapper;
    @Inject
    private AddressMyBatisMapper addressMyBatisMapper;
    @Inject
    private ShipmentMyBatisMapper shipmentMyBatisMapper;
    @Inject
    private TermsService termsService;
    @Inject
    private VatService vatService;
    @Inject
    private PropertyAttributeMapper propertyAttributeMapper;
    @Inject
    private PropertyRepository propertyRepository;

    public OrderEditVO getOrderEditDetail(Long orderId, Long projectId, Long currentWorkgroupId,
                                          Long currentUserId, boolean isReorder) throws Exception{
        OrderEditVO orderEditVO = new OrderEditVO();
        OrderGeneralInfoVO orderGeneralInfoVO = new OrderGeneralInfoVO();
        OrderVersionDTO orderVersionDTO = orderService.findOrderVersionById(orderId, projectId, currentUserId, currentWorkgroupId, true);
        if (!editOrderPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)) {
            throw new NoPermissionException("Your don't have permission to edit order!");
        }

        Long originalOrderId = orderVersionDTO.getOrderId();
        //retrive order and orderItems
        if (orderVersionDTO != null) {
            orderMapper.toOrderGeneralInfoVO(orderVersionDTO, orderGeneralInfoVO, true);
        }

        Map<String, String> buyerPrefs = preferenceService.findGroupPrefs(orderVersionDTO.getBuyerWorkgroupId());
        orderGeneralInfoVO.setContractPricingEnabled(preferenceService.check(PreferenceID.WORKGROUP_OPTION_CONTRACT_PRICING, buyerPrefs));
        boolean isDatePrefsEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROCUREMENT_DATE_PREFERENCES, buyerPrefs);
        boolean isComplDateRequired = !isDatePrefsEnabled || preferenceService.check(PreferenceID.PS_ORDER_COMPLETION_DATE_REQUIRED, buyerPrefs);
        boolean itemizedTaxAndShippingEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ITEMIZED_TAX_SHIPPING, buyerPrefs);
        boolean bypassReAcceptanceEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_BYPASS_RE_ACCEPTANCE, buyerPrefs);
        orderGeneralInfoVO.setIsCompletionDateRequired(isComplDateRequired);

        String percentStr = preferenceService.getString(PreferenceID.PC_TAX_PERCENTAGE, buyerPrefs, "0");
        Double taxPercent = Double.valueOf(percentStr);
        if (orderVersionDTO.getSubTotal() != null && orderVersionDTO.getSubTotal().equals(BigDecimal.ZERO)) {
            orderGeneralInfoVO.setTaxPercent(taxPercent);
        }
        boolean enableSupplierAddPaperDetails = orderGeneralInfoVO.getEnableSupplierAddPaperDetails();
        boolean enableLogistics = orderGeneralInfoVO.getEnableLogistics();
        boolean enableWarehouse = preferenceService.check(PreferenceID.WORKGROUP_OPTION_WAREHOUSE, currentWorkgroupId);
        boolean canViewShipment = permissionService.checkAll(PermissionID.VIEW_SHIPMENT, currentWorkgroupId, currentUserId, projectId);
        boolean isEnableComplexVAT = orderVersionDTO.getIsEnableComplexVAT();
        if (isEnableComplexVAT) {
            orderGeneralInfoVO.setVatsInfo(vatService.findVATList(buyerPrefs));
        }

        // create reason/other dropdown should NOT visible to supplier
        //if is not quick order, disable additem
        //if is not draft,disable additem
        //If all specs have been selected, disable 'Add Items Button'.
        List<OrderItemVO> orderItemVOS = orderGeneralInfoVO.getOrderItems();
        //spec parent
        List<Long> projectTreeIds = projectOpenFeignClient.getProjectTreeIds(projectId, currentUserId, currentWorkgroupId, true);
        List<Long> selectedSpecIds = new ArrayList<>();
        for (OrderItemVO orderItemVO : orderItemVOS) {
            selectedSpecIds.add(orderItemVO.getSpec().getId());
        }

        if(orderGeneralInfoVO.getOrderTypeId() != OrderTypeID.ORDER) {
            Map<Long, Long> specProjectMap = getSpecProjectIdMap(selectedSpecIds, projectTreeIds);
            //Handle spec permission
            Map<String, Boolean> specPermissions = getSpecPermissions(specProjectMap, selectedSpecIds, currentWorkgroupId, currentUserId);
            for (OrderItemVO orderItemVO : orderItemVOS) {
                // find the appropriate parent for the specs
                Long parentProjectId = specProjectMap.get(orderItemVO.getSpec().getId());
                PcJob job = jobRepository.findById(orderItemVO.getJobId()).orElse(null);
                List<SpecDTO> specDTOS = specMyBatisMapper.findSpecsBySpecRefId(parentProjectId, job.getSpSpecReferenceId());
                List<SpecVO> specVOS = new ArrayList<>();
                for (SpecDTO specDTO : specDTOS) {
                    SpecVO specVO = specMapper.toVOForOrderItem(specDTO.getSpecNode().getId(), specDTO, projectId, null);
                    specVO.setNodeId(specDTO.getSpecNode().getId());
                    boolean canEditSpec = permissionService.check(PermissionID.EDIT_SPEC, parentProjectId, specPermissions);
                    boolean isImmutable = (specDTO.getIsLocked() != null && specDTO.getIsLocked())
                            || (specDTO.getLockCount() != null && specDTO.getLockCount().longValue() > 0);
                    if (canEditSpec && !isImmutable && !specDTO.getIsLocked()) {
                        specVO.setEditSpecExternalLink(NooshOneUrlUtil.composeEditSpecLinkForOrderItemToEnterprise(specDTO.getSpecNode().getId(), specDTO.getId(), parentProjectId));
                    }
                    boolean canCreateSpec = permissionService.check(PermissionID.CREATE_SPEC, parentProjectId, specPermissions);
                    if (canCreateSpec) {
                        specVO.setCopySpecExternalLink(NooshOneUrlUtil.composeCopySpecLinkForOrderItemToEnterprise(specDTO.getSpecNode().getId(), specDTO.getId(), parentProjectId));
                    }
                    specVOS.add(specVO);
                }
                orderItemVO.setSpecs(specVOS);
            }
        }

        for (OrderItemVO orderItemVO: orderItemVOS) {
            if (orderItemVO.getId() > 0 && orderItemVO.getAllowBreakouts() && orderGeneralInfoVO.getIsDraft()
                    && !orderGeneralInfoVO.getIsReordered() && orderItemVO.getIsOverridingBreakouts()) {
                Long rootBreakoutTypeId = orderItemVO.getBreakoutTypeId();
                if (rootBreakoutTypeId != null) {
                    List<BreakoutTypeDTO> breakoutTypeDTOS = breakoutTypeMyBatisMapper.findAllByRootBreakoutTypeId(rootBreakoutTypeId);
                    breakoutTypeDTOS = breakoutTypeDTOS.stream().filter(bt -> bt.isDummyRootNode() == false).collect(Collectors.toList());
                    if (breakoutTypeDTOS != null && breakoutTypeDTOS.size() > 0) {
                        Map<Long, BreakoutTypeDTO> typeMap = breakoutTypeDTOS.stream().filter(bt -> bt.getParentTypeId() != null && bt.getParentTypeId() > 0).collect(Collectors.toMap(BreakoutTypeDTO::getId, BreakoutTypeDTO -> BreakoutTypeDTO));
                        breakoutService.buildBreakoutTypeTree(breakoutTypeDTOS, typeMap);
                        List<BreakoutTypeVO> breakoutTypeVOS = breakoutTypeDTOS.stream().filter(bt -> bt.getLevel() == 1).map(breakoutTypeMapper::toVOWithDescendents).collect(Collectors.toList());
                        orderItemVO.setBreakoutTypes(breakoutTypeVOS);
                    }
                }
            }

            //uofms
            List<Uofm> uofms = uofmRepository.findBySpecTypeId(orderItemVO.getSpec().getSpSpecTypeId());
            if (uofms != null && uofms.size() > 0) {
                List<UofmVO> uofmVOS = uofms.stream().map(uofmMapper::toVO).collect(Collectors.toList());
                if (uofmVOS != null && uofmVOS.size() > 0) {
                    orderItemVO.setUofms(uofmVOS);
                }
            }

            if (itemizedTaxAndShippingEnabled && orderItemVO.getValue() != null && orderItemVO.getValue().equals(BigDecimal.ZERO)) {
                orderItemVO.setTaxPercent(BigDecimal.valueOf(taxPercent));
            }

            // Get spec paper details for supplier edit order init if have no paper details saved yet
            if (enableSupplierAddPaperDetails && orderVersionDTO.isUserSupplier() && orderItemVO.getPaperDetails() == null) {
                List<PropertyAttributeDTO> specPaperFields = new ArrayList<PropertyAttributeDTO>();
                SpecVO spec = orderItemVO.getSpec();
                List<Long> paperPropertyIdList = propertyMyBatisMapper.getPaperDetailCount(spec.getId(), "STOCKS_AND_INKS");
                int paperSize = paperPropertyIdList.size();
                if (paperSize > 0) {
                    specPaperFields = propertyMyBatisMapper.getSpecPaperFields(paperPropertyIdList, "STOCKS_AND_INKS");
                } else {
                    paperPropertyIdList = propertyMyBatisMapper.getPaperDetailCount(spec.getId(), "INKS_AND_PAPER");
                    paperSize = paperPropertyIdList.size();
                    specPaperFields = propertyMyBatisMapper.getSpecPaperFields(paperPropertyIdList, "INKS_AND_PAPER");
                }
                orderItemVO.setPaperDetailSize(paperSize);
                List<PropertyAttributeVO> specPaperFieldsVOs = new ArrayList<PropertyAttributeVO>();
                if (specPaperFields != null && specPaperFields.size() > 0) {
                    for (PropertyAttributeDTO propertyAttributeDTO : specPaperFields) {
                        PropertyAttributeVO propertyAttributeVO = new PropertyAttributeVO(
                                propertyAttributeDTO.getPrPropertyAttributeId(),
                                propertyAttributeDTO.getValue(), propertyAttributeDTO.getParamName());
                        specPaperFieldsVOs.add(propertyAttributeVO);
                    }
                }
                orderItemVO.setSpecPaperFields(specPaperFieldsVOs);
            }
            if (enableSupplierAddPaperDetails && orderItemVO.getPaperDetails() == null) {
                orderItemVO.setPaperDetails(Collections.emptyList());
            }

            if (enableLogistics && orderVersionDTO.isUserSupplier()) {
                // Load request ids
                if (enableLogistics && canViewShipment) {
                    List<Long> requestIds = shipmentMyBatisMapper.findRequestIdsByJobs(new ArrayList<Long>(Arrays.asList(orderItemVO.getJobId())));
                    if (requestIds != null) {
                        orderItemVO.setRequestIds(requestIds);
                    }
                }
            }
        }

        if (enableLogistics && orderVersionDTO.isUserSupplier()) {
            List<AddressDTO> addressDTOs = addressMyBatisMapper.findWorkgroupMainAddress(currentWorkgroupId, enableWarehouse);
            orderEditVO.setOwnerWorkgroupMainAddress(addressMapper.toVOs(addressDTOs));
        }
        orderEditVO.setOrderGeneral(orderGeneralInfoVO);
        orderEditVO.setBuyerTerms(termsService.findTermAndCondition(orderVersionDTO.getBuyerTermsId(), TermsTypeID.BUYER_PURCHASE, orderVersionDTO.getBuyerWorkgroupId()));
        orderEditVO.setSupplierTerms(termsService.findTermAndCondition(orderVersionDTO.getSupplierTermsId(), TermsTypeID.SUPPLIER_SELL, orderVersionDTO.getSupplierWorkgroupId()));

        //buttons check
        //ACTION_EDIT_AND_SUBMIT_ORDER
        if (!orderVersionDTO.isDraft() && !routingService.requiresApproval(orderVersionDTO, buyerPrefs) &&
                !routingService.requiresManagerApproval(orderVersionDTO, buyerPrefs) && !orderVersionDTO.isPendingSubmission() ||
                orderVersionDTO.isUserSupplier()) {
            orderEditVO.setCanSaveAndAccept(Boolean.TRUE);
        }
        //ACTION_UPDATE_ORDER_DRAFT, ACTION_UPDATE_AND_SUBMIT_ORDER_DRAFT
        if (orderVersionDTO.isUserBuyer() && orderVersionDTO.isDraft()) {
            orderEditVO.setCanSaveDraft(Boolean.TRUE);
        }
        //ACTION_EDIT_ORDER
        orderEditVO.setBypassReAcceptanceEnabled(bypassReAcceptanceEnabled);
        if ((orderVersionDTO.isUserBuyer() && orderVersionDTO.isPendingBuyerAcceptance()) ||
                (orderVersionDTO.isUserBuyer() && orderVersionDTO.isPendingSubmission()) ||
                (orderVersionDTO.isUserSupplier() && orderVersionDTO.isPendingSupplierAcceptance())) {
            orderEditVO.setCanSaveOrder(Boolean.TRUE);
            orderEditVO.setCanByPassReAcceptance(bypassReAcceptanceEnabled && orderVersionDTO.getReAcceptanceStatusId() != ReAcceptanceStatusID.NEED_RE_ACCEPTANCE);
        } else {
            orderEditVO.setCanSaveOrder(Boolean.FALSE);
            orderEditVO.setCanByPassReAcceptance(Boolean.FALSE);
        }
        Boolean canAddItem;
        if (!orderVersionDTO.isDraft() || !orderVersionDTO.isQuickOrder()) {
            canAddItem = Boolean.FALSE;
        } else {
            canAddItem = canAddItem(projectTreeIds, selectedSpecIds.size());
        }
        orderEditVO.setCanAddItem(canAddItem);

        // dismiss control
        if (orderVersionDTO.isDraft() && orderVersionDTO.getOrderTypeId() == OrderTypeID.ORDER) {
            List<OrderItemVO> orderItems = orderGeneralInfoVO.getOrderItems();
            List<Long> estimateItemPriceIds = orderItems.stream()
                    .map(OrderItemVO::getEstimateItemPriceId).filter(Objects::nonNull).collect(Collectors.toList());
            List<EstimateItemPrice> estimateItemPrices = estimateItemPriceRepository.findByIdIn(estimateItemPriceIds);
            if (estimateItemPrices != null && estimateItemPrices.size() > 0) {
                EstimateItemPrice estimateItemPrice = estimateItemPrices.get(0);
                EstimateItem estimateItem = estimateItemPrice.getEstimateItem();
                Estimate estimate = estimateRepository.findById(estimateItem.getEstimateId()).get();
                Rfe rfe = estimate.getRfe();
                orderGeneralInfoVO.setRfeId(rfe.getId());
                boolean dismissPref = preferenceService.check(PreferenceID.BU_WORKGROUP_ORDER_DISMISS_SUPPLIER_PREF, buyerPrefs);
                boolean isCloseRfe = rfe.isOpen() ? true : false;
                List<RfeSupplierDTO> rfeSupplierDTOS = rfeService.findDismissableSupplier(rfe.getId(), projectId);
                boolean hasSupplierGroups = rfeSupplierDTOS != null && rfeSupplierDTOS.size() > 1;
                orderEditVO.setIsCloseRfe(isCloseRfe);
                orderEditVO.setDismissPrefValue(dismissPref);
                orderEditVO.setIsDismissUnselectedSupplier(hasSupplierGroups);
                Map<String, Boolean> permissionMap = permissionService.getPermissionMap(Arrays.asList(PermissionID.DISMISS_SUPPLIER, PermissionID.CLOSE_RFE), orderVersionDTO.getBuyerWorkgroupId(), currentUserId, Arrays.asList(projectId));
                orderEditVO.setCanDismissSupplier(permissionService.check(PermissionID.DISMISS_SUPPLIER, projectId, permissionMap));
                orderEditVO.setCanCloseRfe(permissionService.check(PermissionID.CLOSE_RFE, projectId, permissionMap));

                Map<Long, Long> estimateItemPriceIdToOptionIndexMap = estimateItemPrices.stream()
                        .collect(Collectors.toMap(
                                eip -> eip.getId(),
                                eip -> eip.getItemOption().getOptionIndex()
                        ));
                orderItems.forEach(orderItem -> {
                    Long optionIndex = estimateItemPriceIdToOptionIndexMap.get(orderItem.getEstimateItemPriceId());
                    orderItem.setEstItemOptionIndex(optionIndex);
                });
            }
        }

        return orderEditVO;
    }

    /**
     * map spec to projectId, in case the current project has sub projects
     * If no sub projects, then just return a map
     *
     * @param selectedSpecIds
     * @param projectTreeIds
     * @return
     */
    private Map<Long, Long> getSpecProjectIdMap(List<Long> selectedSpecIds, List<Long> projectTreeIds) {
        Map<Long, Long> specProjectMap = new HashMap<>(selectedSpecIds.size());
        if (projectTreeIds.size() > 1) {
            List<SyContainable> containableBeans = collaborationRepository
                    .findByObjectIdInAndParentObjectIdIn(selectedSpecIds,
                            projectTreeIds,
                            ObjectClassID.OBJECT_CLASS_SPEC,
                            ObjectClassID.OBJECT_CLASS_PROJECT);

            containableBeans.forEach(containableBean -> {
                specProjectMap.put(containableBean.getObjectId(), containableBean.getParentObjectId());
            });
        } else {
            Long projectId = projectTreeIds.get(0);
            selectedSpecIds.forEach(specId -> {
                specProjectMap.put(specId, projectId);
            });
        }

        return specProjectMap;
    }

    /**
     *  get Spec Permissions of parent project ids
     *
     * @param specProjectMap
     * @param selectedSpecIds
     * @param currentWorkgroupId
     * @param currentUserId
     * @return
     */
    private Map<String, Boolean> getSpecPermissions(Map<Long, Long> specProjectMap, List<Long> selectedSpecIds,
                                                    Long currentWorkgroupId, Long currentUserId) {
        List<Long> parentProjectIds = new ArrayList<>();
        for (Long specId : selectedSpecIds) {
            Long parentProjectId = specProjectMap.get(specId);
            parentProjectIds.add(parentProjectId);
        }
        List<Long> permissionIds = new ArrayList<>();
        permissionIds.add(PermissionID.CREATE_SPEC);
        permissionIds.add(PermissionID.EDIT_SPEC);
        return permissionService.getPermissionMap(permissionIds, currentWorkgroupId, currentUserId, parentProjectIds);
    }

    /**
     * add Items support
     *
     * @param projectId
     *
     * @param workgroupId
     * @param selectedSpecNodeIds
     * @param supplierWgId
     * @param index
     * @return
     */
    public List<OrderItemVO> addItems(Long projectId, Long userId, Long workgroupId, List<Long> selectedSpecNodeIds, Long supplierWgId, Long index) {
        ProjectDTO parent = projectService.findProjectById(projectId);
        List<Long> permissionIds = new ArrayList<>();
        permissionIds.add(PermissionID.CREATE_SPEC);
        permissionIds.add(PermissionID.EDIT_SPEC);
        List<Long> projectIds = new ArrayList<>();
        projectIds.add(projectId);
        Map<String, Boolean> specPermissions = permissionService.getPermissionMap(permissionIds, workgroupId, userId, projectIds);
        boolean canCreateSpec = permissionService.check(PermissionID.CREATE_SPEC, projectId, specPermissions);
        boolean canEditSpec = permissionService.check(PermissionID.EDIT_SPEC, projectId, specPermissions);
        boolean isPaperFlow = parent.getIsPaperFlow();

        List<OrderItemVO> orderItemVOS = new ArrayList<>();
        for (Long specNodeId : selectedSpecNodeIds) {
            OrderItemVO orderItemVO = new OrderItemVO();
            orderItemVO.setId(-1L);
            SpecNode specNode = specNodeRepository.findById(specNodeId).get();
            orderItemVO.setSpecNodeId(specNode.getId());
            Spec spec = specNode.getSpec();
            SpecDTO specDTO = specMapper.convertToSpecDTO(spec);
            boolean isTimeMaterials = specDTO.getSpecType().getIsTimeMaterials() != null && specDTO.getSpecType().getIsTimeMaterials() == (short)1;
            orderItemVO.setTimeMaterials(isTimeMaterials);
            SpecVO specVO = specMapper.toVOForOrderItem(specNode.getId(), specDTO, projectId, "");

            SpecReference specReference = spec.getSpecReference();
            List<PcJob> jobs;
            if (!specDTO.getIsItemVersion()) {
                jobs = jobRepository.findBySpSpecReferenceId(specReference.getSpSpecReferenceId());
            } else {
                jobs = jobRepository.findJobBySpecRefIdAndProjectId(projectId, specReference.getSpSpecReferenceId());
            }
            if (jobs != null && jobs.size() > 0) {
                Long jobId = jobs.get(jobs.size() - 1).getPcJobId();
                orderItemVO.setJobId(jobId);
            }

            if (canCreateSpec) {
                specVO.setCopySpecExternalLink(NooshOneUrlUtil.composeCopySpecLinkForOrderItemToEnterprise(specNode.getId(), spec.getId(), projectId));
            }
            orderItemVO.setSpec(specVO);

            //spec info for paper workflow
            if (isPaperFlow) {
                List<PropertyAttributeDTO> paperSelections = new ArrayList<>();
                PropertyAttributeDTO paperSelection = propertyAttributeMapper.getPaperSelection(spec.getProperty());
                if (paperSelection != null) {
                    paperSelections.add(paperSelection);
                }
                List<Property> childrenProperties = propertyRepository.findByParentPropertyIdAndPropertyName(spec.getProperty().getId(), "INKS_AND_PAPER");
                if (childrenProperties != null && !childrenProperties.isEmpty()) {
                    for (Property childProperty : childrenProperties) {
                        PropertyAttributeDTO childPaperSelection = propertyAttributeMapper.getPaperSelection(childProperty);
                        if (childPaperSelection != null) {
                            paperSelections.add(childPaperSelection);
                        }
                    }
                }
                orderItemVO.setPaperSelections(propertyAttributeMapper.toVOs(paperSelections));
            }

            List<SpecDTO> specDTOS = specMyBatisMapper.findSpecsBySpecRefId(projectId, spec.getSpSpecReferenceId());
            if (specDTOS != null && specDTOS.size() > 0) {
                List<SpecVO> specVOS = new ArrayList<>();
                for (SpecDTO specOption : specDTOS) {
                    SpecVO specOptionVO = specMapper.toVOForOrderItem(specOption.getSpecNode().getId(), specOption, projectId, null);
                    specOptionVO.setNodeId(specOption.getSpecNode().getId());
                    boolean isImmutable = (specOption.getIsLocked() != null && specOption.getIsLocked())
                            || (specOption.getLockCount() != null && specOption.getLockCount().longValue() > 0);
                    if (canEditSpec && !isImmutable && !specOption.getIsLocked()) {
                        specOptionVO.setEditSpecExternalLink(NooshOneUrlUtil.composeEditSpecLinkForOrderItemToEnterprise(specOption.getSpecNode().getId(), specOption.getId(), projectId));
                    }
                    if (canCreateSpec) {
                        specOptionVO.setCopySpecExternalLink(NooshOneUrlUtil.composeCopySpecLinkForOrderItemToEnterprise(specOption.getSpecNode().getId(), specOption.getId(), projectId));
                    }
                    specVOS.add(specOptionVO);
                }
                orderItemVO.setSpecs(specVOS);
            }

            orderItemVO.setItemIndex(++index);
            //quantity
            if (spec.getQuantity1() != null && spec.getQuantity1().doubleValue() > 0) {
                orderItemVO.setQuantity(spec.getQuantity1());
            }
            //uofm
            UofmDTO defaultUofmDTO = uofmMapper.toDTO(uofmRepository.findDefaultBySpecTypeId(spec.getSpSpecTypeId()));
            if (defaultUofmDTO != null) {
                orderItemVO.setUofm(uofmMapper.toVO(defaultUofmDTO));
                if (isTimeMaterials) {
                    orderItemVO.setBreakoutRatesStrId(defaultUofmDTO.getBreakoutRatesStrId());
                    orderItemVO.setBreakoutUnitsStrId(defaultUofmDTO.getBreakoutUnitsStrId());
                }
            }
            //uofms
            List<Uofm> uofms = uofmRepository.findBySpecTypeId(spec.getSpSpecTypeId());
            if (uofms != null && uofms.size() > 0) {
                List<UofmVO> uofmVOS = uofms.stream().map(uofmMapper::toVO).collect(Collectors.toList());
                if (uofmVOS != null && uofmVOS.size() > 0) {
                    orderItemVO.setUofms(uofmVOS);
                }
            }

            long buyerWorkgroupId = parent.getOwnerWorkgroupId();
            Map<String, String> buyerPrefs = preferenceService.findGroupPrefs(buyerWorkgroupId);

            //default tax percent
            boolean itemizedTaxAndShippingEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ITEMIZED_TAX_SHIPPING, buyerPrefs);
            if (itemizedTaxAndShippingEnabled) {
                String percentStr = preferenceService.getString(PreferenceID.PC_TAX_PERCENTAGE, buyerPrefs, "0");
                Double taxPercent = Double.valueOf(percentStr);
                orderItemVO.setTaxPercent(BigDecimal.valueOf(taxPercent));
            }

            //breakouts
            boolean isOverridingBreakouts = preferenceService.check(PreferenceID.PC_ALLOW_BREAKOUTS_OVERRIDE, buyerPrefs);
            boolean calTotalFromBreakouts = preferenceService.check(PreferenceID.PC_CALC_TOTAL_FROM_BREAKOUTS, buyerPrefs);
            boolean isBreakoutsForOrders = preferenceService.check(PreferenceID.PC_PRICE_BREAKOUTS_FOR_ORDERS, buyerPrefs);

            orderItemVO.setIsOverridingBreakouts(isOverridingBreakouts);
            orderItemVO.setTotalFromBreakouts(calTotalFromBreakouts);
            orderItemVO.setAllowBreakouts(isBreakoutsForOrders);
            if (isBreakoutsForOrders) {
                long rootBreakoutTypeId = -1;
                List<BreakoutTypeDTO> breakoutTypeDTOS = breakoutTypeMyBatisMapper.findByWorkgroupAndSpecTypeId(buyerWorkgroupId, specDTO.getSpSpecTypeId());
                breakoutTypeDTOS = breakoutTypeDTOS.stream().filter(bt -> bt.isDummyRootNode() == false).collect(Collectors.toList());
                List<BreakoutVO> breakoutVOS = new ArrayList<BreakoutVO>();
                if (breakoutTypeDTOS != null && breakoutTypeDTOS.size() > 0) {
                    rootBreakoutTypeId = breakoutTypeDTOS.get(0).getRootTypeId();
                }
                if (supplierWgId != null) {
                    breakoutTypeDTOS = breakoutTypeMyBatisMapper.findAllWithRootBreakoutTypeIdAndSpecTypeIdAndSupplierGroupId(rootBreakoutTypeId, specDTO.getSpSpecTypeId(), supplierWgId);
                }
                if (breakoutTypeDTOS != null && breakoutTypeDTOS.size() > 0) {
                    // all breakout types are initially set to be included by default
                    for (BreakoutTypeDTO breakoutTypeDTO : breakoutTypeDTOS) {
                        breakoutTypeDTO.setIsIncluded(true);
                        BreakoutTypeVO breakoutTypeVO = breakoutTypeMapper.toVO(breakoutTypeDTO);
                        if (breakoutTypeDTO.getIsQuantity()) {
                            BreakoutVO breakoutVO = new BreakoutVO();
                            breakoutVO.setBreakoutType(breakoutTypeVO);
                            breakoutVO.setNestingLevel(breakoutTypeDTO.getLevel().longValue());
                            breakoutVO.setHasQuantity(breakoutTypeDTO.getIsQuantity());
                            breakoutVO.setObjectId(-1L);// orderitem.getid()
                            breakoutVO.setObjectClassId(ObjectClassID.ORDER_ITEM);
                            breakoutVOS.add(breakoutVO);
                        }
                    }
                    orderItemVO.setBreakouts(breakoutVOS);
                    orderItemVO.setBreakoutTypeId(rootBreakoutTypeId);
                    Map<Long, BreakoutTypeDTO> typeMap = breakoutTypeDTOS.stream().filter(bt -> bt.getParentTypeId() != null && bt.getParentTypeId() > 0).collect(Collectors.toMap(BreakoutTypeDTO::getId, BreakoutTypeDTO -> BreakoutTypeDTO));
                    breakoutService.buildBreakoutTypeTree(breakoutTypeDTOS, typeMap);
                    List<BreakoutTypeVO> breakoutTypeVOS = breakoutTypeDTOS.stream().filter(bt -> bt.getLevel() == 1).map(breakoutTypeMapper::toVOWithDescendents).collect(Collectors.toList());
                    orderItemVO.setBreakoutTypes(breakoutTypeVOS);

                }
            }
            //shipment
            if (orderItemVO.getJobId() != null) {
                Shipment shipment = shipmentRepository.findByPcJobId(orderItemVO.getJobId());
                if (shipment != null) {
                    ShipmentVO shipmentVO = shipmentMapper.toVO(shipment);
                    orderItemVO.setShipment(shipmentVO);
                }
            }
            orderItemVOS.add(orderItemVO);
        }
        return orderItemVOS;
    }

    public OrderItemSpecsVO getSpecsOfOrderItem(Long projectId, Long specId, Long jobId, Long currentUserId, Long currentWorkgroupId) {
        //spec parent
        List<Long> projectTreeIds = projectOpenFeignClient.getProjectTreeIds(projectId, currentUserId, currentWorkgroupId, true);
        List<Long> selectedSpecIds = new ArrayList<>();
        selectedSpecIds.add(specId);
        Map<Long, Long> specProjectMap = getSpecProjectIdMap(selectedSpecIds, projectTreeIds);

        //Handle spec permission
        Map<String, Boolean> specPermissions = getSpecPermissions(specProjectMap, selectedSpecIds, currentWorkgroupId, currentUserId);
        // find the appropriate parent for the specs
        Long parentProjectId = specProjectMap.get(specId);
        List<SpecDTO> specDTOS;
        if (jobId != null && -1 != jobId.longValue()) {
            PcJob job = jobRepository.findById(jobId).orElse(null);
            specDTOS = specMyBatisMapper.findSpecsBySpecRefId(parentProjectId, job.getSpSpecReferenceId());
        } else {
            Spec spec = specRepository.findById(specId).orElse(null);
            specDTOS = specMyBatisMapper.findSpecsBySpecRefId(parentProjectId, spec.getSpSpecReferenceId());
        }

        List<SpecVO> specVOS = new ArrayList<>();
        for (SpecDTO specDTO : specDTOS) {
            SpecVO specVO = specMapper.toVOForOrderItem(specDTO.getSpecNode().getId(), specDTO, projectId, null);
            specVO.setNodeId(specDTO.getSpecNode().getId());
            boolean canEditSpec = permissionService.check(PermissionID.EDIT_SPEC, parentProjectId, specPermissions);
            boolean isImmutable = (specDTO.getIsLocked() != null && specDTO.getIsLocked())
                    || (specDTO.getLockCount() != null && specDTO.getLockCount().longValue() > 0);
            if (canEditSpec && !isImmutable && !specDTO.getIsLocked()) {
                specVO.setEditSpecExternalLink(NooshOneUrlUtil.composeEditSpecLinkForOrderItemToEnterprise(specDTO.getSpecNode().getId(), specDTO.getId(), parentProjectId));
            }
            boolean canCreateSpec = permissionService.check(PermissionID.CREATE_SPEC, parentProjectId, specPermissions);
            if (canCreateSpec) {
                specVO.setCopySpecExternalLink(NooshOneUrlUtil.composeCopySpecLinkForOrderItemToEnterprise(specDTO.getSpecNode().getId(), specDTO.getId(), parentProjectId));
            }
            specVOS.add(specVO);
        }
        OrderItemSpecsVO orderItemSpecsVO = new OrderItemSpecsVO();
        orderItemSpecsVO.setJobId(jobId);
        orderItemSpecsVO.setSpecs(specVOS);
        return orderItemSpecsVO;
    }

    private boolean canAddItem(List<Long> projectIds, int itemSize) {
        int specRefSize = specMyBatisMapper.findTotalSpecReferenceCountByProjectIds(projectIds);
        return specRefSize > itemSize;
    }

}
