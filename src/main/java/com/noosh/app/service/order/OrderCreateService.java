package com.noosh.app.service.order;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.OrderTypeID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.constant.TermsTypeID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.account.AccountUserDTO;
import com.noosh.app.commons.dto.breakout.BreakoutTypeDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import com.noosh.app.commons.dto.rfe.RfeSupplierDTO;
import com.noosh.app.commons.dto.shipment.UofmDTO;
import com.noosh.app.commons.dto.spec.SpecDTO;
import com.noosh.app.commons.entity.account.AccountUser;
import com.noosh.app.commons.entity.estimate.Estimate;
import com.noosh.app.commons.entity.estimate.EstimateItem;
import com.noosh.app.commons.entity.estimate.EstimateItemPrice;
import com.noosh.app.commons.entity.job.PcJob;
import com.noosh.app.commons.entity.property.Property;
import com.noosh.app.commons.entity.rfe.Rfe;
import com.noosh.app.commons.entity.security.Workgroup;
import com.noosh.app.commons.entity.shipment.Shipment;
import com.noosh.app.commons.entity.spec.Spec;
import com.noosh.app.commons.entity.spec.SpecNode;
import com.noosh.app.commons.entity.spec.SpecReference;
import com.noosh.app.commons.entity.uofm.Uofm;
import com.noosh.app.commons.vo.account.AccountUserVO;
import com.noosh.app.commons.vo.breakout.BreakoutTypeVO;
import com.noosh.app.commons.vo.breakout.BreakoutVO;
import com.noosh.app.commons.vo.order.OrderConfirmVO;
import com.noosh.app.commons.vo.order.OrderDetailVO;
import com.noosh.app.commons.vo.order.OrderItemVO;
import com.noosh.app.commons.vo.order.TermsVO;
import com.noosh.app.commons.vo.paper.PaperDetailVO;
import com.noosh.app.commons.vo.pricing.PricingVO;
import com.noosh.app.commons.vo.shipment.ShipmentVO;
import com.noosh.app.commons.vo.uofm.UofmVO;
import com.noosh.app.commons.vo.workgroup.WorkgroupVO;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.feign.WorkgroupOpenFeignClient;
import com.noosh.app.mapper.OrderMapper;
import com.noosh.app.mapper.ProjectMapper;
import com.noosh.app.mapper.account.AccountUserMapper;
import com.noosh.app.mapper.breakout.BreakoutTypeMapper;
import com.noosh.app.mapper.property.PropertyAttributeMapper;
import com.noosh.app.mapper.shipment.ShipmentMapper;
import com.noosh.app.mapper.spec.SpecMapper;
import com.noosh.app.mapper.uofm.UofmMapper;
import com.noosh.app.repository.estimate.EstimateItemPriceRepository;
import com.noosh.app.repository.estimate.EstimateItemRepository;
import com.noosh.app.repository.estimate.EstimateRepository;
import com.noosh.app.repository.jpa.account.AccountUserRepository;
import com.noosh.app.repository.jpa.job.JobRepository;
import com.noosh.app.repository.jpa.property.PropertyRepository;
import com.noosh.app.repository.jpa.security.WorkgroupRepository;
import com.noosh.app.repository.jpa.spec.SpecNodeRepository;
import com.noosh.app.repository.mybatis.breakouttype.BreakoutTypeMyBatisMapper;
import com.noosh.app.repository.mybatis.order.OrderMyBatisMapper;
import com.noosh.app.repository.mybatis.property.PropertyMyBatisMapper;
import com.noosh.app.repository.shipment.ShipmentRepository;
import com.noosh.app.repository.uofm.UofmRepository;
import com.noosh.app.service.account.AccountService;
import com.noosh.app.service.account.SupplierWorkgroupService;
import com.noosh.app.service.breakout.BreakoutService;
import com.noosh.app.service.permission.ordering.CreateOrderPermission;
import com.noosh.app.service.permission.ordering.CreateQuickOrderPermission;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.rating.RatingService;
import com.noosh.app.service.rfe.RfeService;
import com.noosh.app.service.routing.RoutingService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.terms.TermsService;
import com.noosh.app.service.util.DateUtil;
import com.noosh.app.service.util.OrderUtil;
import com.noosh.app.service.util.PaperUtil;
import com.noosh.app.service.vat.VatService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Collections;
import java.util.stream.Collectors;

@Service
public class OrderCreateService {
    private final Logger log = LoggerFactory.getLogger(OrderCreateService.class);

    @Inject
    private UofmRepository uofmRepository;
    @Inject
    private BreakoutTypeMyBatisMapper breakoutTypeMyBatisMapper;
    @Inject
    private BreakoutTypeMapper breakoutTypeMapper;
    @Inject
    private UofmMapper uofmMapper;
    @Inject
    private ProjectService projectService;
    @Inject
    private PreferenceService preferenceService;
    @Inject
    private VatService vatService;
    @Inject
    private AccountUserRepository accountUserRepository;
    @Inject
    private ShipmentRepository shipmentRepository;
    @Inject
    private EstimateRepository estimateRepository;
    @Inject
    private EstimateItemRepository estimateItemRepository;
    @Inject
    private EstimateItemPriceRepository estimateItemPriceRepository;
    @Inject
    private WorkgroupRepository workgroupRepository;
    @Inject
    private CreateQuickOrderPermission createQuickOrderPermission;
    @Inject
    private CreateOrderPermission createOrderPermission;
    @Inject
    private SpecNodeRepository specNodeRepository;
    @Inject
    private SpecMapper specMapper;
    @Inject
    private JobRepository jobRepository;
    @Inject
    private ShipmentMapper shipmentMapper;
    @Inject
    private TermsService termsService;
    @Inject
    private ProjectMapper projectMapper;
    @Inject
    private AccountUserMapper accountUserMapper;
    @Inject
    private RoutingService routingService;
    @Inject
    private RfeService rfeService;
    @Inject
    private PermissionService permissionService;
    @Inject
    private AccountService accountService;
    @Inject
    private BreakoutService breakoutService;
    @Inject
    private PropertyMyBatisMapper propertyMyBatisMapper;
    @Inject
    private OrderMyBatisMapper orderMyBatisMapper;
    @Inject
    private SupplierWorkgroupService supplierWorkgroupService;
    @Inject
    private RatingService ratingService;
    @Inject
    private WorkgroupOpenFeignClient workgroupOpenFeignClient;
    @Inject
    private OrderMapper orderMapper;
    @Inject
    private PropertyAttributeMapper propertyAttributeMapper;
    @Inject
    private PropertyRepository propertyRepository;

    public OrderDetailVO getOrderCreateDetail(Long projectId, Long currentWorkgroupId,Long currentUserId,
                                              String localeCode, List<Long> selectedSpecNodeIds, String fromPricing,
                                              Long supplierUserId, Long supplierWgId, Double pricingQty, Double pricingAmount, String subtotals) throws Exception {
        OrderDetailVO orderDetailVO = new OrderDetailVO();
        ProjectDTO parent = projectService.findProjectById(projectId);
        if (!createQuickOrderPermission.check(parent, currentWorkgroupId, currentUserId, projectId)) {
            throw new NoPermissionException("Your don't have permission to create quick order!");
        }
        orderDetailVO.setOrderTypeId(OrderTypeID.QUICK_ORDER);
        orderDetailVO.setParent(projectMapper.toVO(parent));
        //set buyer workgroup and buyer
        long buyerWorkgroupId = parent.getOwnerWorkgroupId();
        AccountUser buyerUser = accountUserRepository.findById(currentUserId).orElse(null);
        Workgroup buyerWorkgroup = workgroupRepository.findById(buyerWorkgroupId).orElse(null);
        WorkgroupVO buyerWorkgroupVO = new WorkgroupVO();
        buyerWorkgroupVO.setId(buyerWorkgroup.getId());
        buyerWorkgroupVO.setName(buyerWorkgroup.getName());
        buyerWorkgroupVO.setDecimalPlaces(buyerWorkgroup.getDecimalPlaces());
        orderDetailVO.setBuyerWorkgroup(buyerWorkgroupVO);
        AccountUserVO buyerUserVO = new AccountUserVO();
        buyerUserVO.setId(buyerUser.getId());
        if (buyerUser.getPerson() != null) {
            buyerUserVO.setFirstName(buyerUser.getPerson().getFirstName());
            buyerUserVO.setLastName(buyerUser.getPerson().getLastName());
            buyerUserVO.setName(buyerUser.getPerson().getFullName());
            buyerUserVO.setProfileImg(buyerUser.getPerson().getProfileImg());
        }
        orderDetailVO.setBuyer(buyerUserVO);

        boolean isFromPricing = "1".equals(fromPricing);
        if (supplierUserId != null && !supplierUserId.equals(-1L)) {
            AccountUser supplierUser = accountUserRepository.findById(supplierUserId).orElse(null);
            AccountUserVO supplierUserVO = new AccountUserVO();
            supplierUserVO.setId(supplierUser.getId());
            if (supplierUser.getPerson() != null) {
                supplierUserVO.setFirstName(supplierUser.getPerson().getFirstName());
                supplierUserVO.setLastName(supplierUser.getPerson().getLastName());
                supplierUserVO.setName(supplierUser.getPerson().getFullName());
                supplierUserVO.setProfileImg(supplierUser.getPerson().getProfileImg());
            }
            orderDetailVO.setSupplier(supplierUserVO);
        }
        boolean isDualCurrency = false;
        Long exCurrencyId = null;
        if (supplierWgId != null && !supplierWgId.equals(-1L)) {
            Workgroup supplierWorkgroup = workgroupRepository.findById(supplierWgId).orElse(null);
            WorkgroupVO supplierWorkgroupVO = new WorkgroupVO();
            supplierWorkgroupVO.setId(supplierWorkgroup.getId());
            supplierWorkgroupVO.setName(supplierWorkgroup.getName());
            supplierWorkgroupVO.setDecimalPlaces(supplierWorkgroup.getDecimalPlaces());
            supplierWorkgroupVO.setSupplierFlag(supplierWorkgroupService.getSupplierFlagVO(true, -1, ObjectClassID.ORDER, buyerWorkgroupId, supplierWorkgroup.getId()));
//          supplierWorkgroupVO.setSupplierScore(ratingService.findSupplierScore(parent.getOwnerWorkgroupId(), supplierWorkgroup.getId()));
            orderDetailVO.setSupplierWorkgroup(supplierWorkgroupVO);
            //dual currency
            Map<String, String> supplierPrefs = preferenceService.findGroupPrefs(supplierWgId, Arrays.asList(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY));
            boolean enableDualCurrency = preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY, supplierPrefs);
            Double rate = workgroupOpenFeignClient.getSupplierDualCurrencyByTargetGroupId(buyerWorkgroupId, supplierWgId);
            isDualCurrency = enableDualCurrency && rate != null && new BigDecimal(rate).compareTo(BigDecimal.ZERO) != 0;
            orderDetailVO.setIsDualCurrency(isDualCurrency);
            if (isDualCurrency) {
                orderDetailVO.setRate(rate != null ? new BigDecimal(rate) : null);
                exCurrencyId = supplierWorkgroup.getTransCurrencyId();
                orderDetailVO.setExCurrencyId(exCurrencyId);
            }
        }

        //completionDate
        LocalDateTime completionDate = null;
        if (DateUtil.isValidCompletionDate(parent.getCompletionDate())) {
            completionDate = parent.getCompletionDate();
        }
        orderDetailVO.setCompletionDate(completionDate);

        Map<String, String> buyerPrefs = preferenceService.findGroupPrefs(buyerWorkgroupId);
        // initialize tax, miscCost and shipping to zero
        orderDetailVO.setMiscCost(BigDecimal.ZERO.doubleValue());
        orderDetailVO.setMiscCostCurrencyId(buyerWorkgroup.getDefaultCurrencyId());
        orderDetailVO.setTax(BigDecimal.ZERO.doubleValue());
        orderDetailVO.setTaxCurrencyId(buyerWorkgroup.getDefaultCurrencyId());
        orderDetailVO.setShipping(BigDecimal.ZERO.doubleValue());
        orderDetailVO.setShippingCurrencyId(buyerWorkgroup.getDefaultCurrencyId());
        orderDetailVO.setDiscountOrSurcharge(BigDecimal.ZERO.doubleValue());
        orderDetailVO.setDiscountOrSurchargeCurrencyId(buyerWorkgroup.getDefaultCurrencyId());
        if (isDualCurrency) {
            orderDetailVO.setExMiscCost(BigDecimal.ZERO);
            orderDetailVO.setExMiscCostCurrencyId(exCurrencyId);
            orderDetailVO.setExTax(BigDecimal.ZERO);
            orderDetailVO.setExTaxCurrencyId(exCurrencyId);
            orderDetailVO.setExShipping(BigDecimal.ZERO);
            orderDetailVO.setExShippingCurrencyId(exCurrencyId);
            orderDetailVO.setExDiscountOrSurcharge(BigDecimal.ZERO);
            orderDetailVO.setExDiscountOrSurchargeCurrencyId(exCurrencyId);
        }
        boolean itemizedTaxAndShippingEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ITEMIZED_TAX_SHIPPING, buyerPrefs);
        orderDetailVO.setItemizedTaxAndShippingEnabled(itemizedTaxAndShippingEnabled);
        boolean isEnableComplexVAT = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ENABLE_VAT, buyerPrefs) && vatService.isEnableVAT(buyerPrefs);
        orderDetailVO.setIsEnableComplexVAT(isEnableComplexVAT);
        if (isEnableComplexVAT) {
            orderDetailVO.setVatsInfo(vatService.findVATList(buyerPrefs));
        }
        // tax percentage and label
        String percentStr = preferenceService.getString(PreferenceID.PC_TAX_PERCENTAGE, buyerPrefs,"0");
        Double taxPercent = Double.valueOf(percentStr);
        orderDetailVO.setTaxPercent(taxPercent);
        String taxLabel = preferenceService.getString(PreferenceID.PC_TAX_LABEL_STRING, buyerPrefs,"");
        orderDetailVO.setTaxLabel(taxLabel);
        orderDetailVO.setContractPricingEnabled(preferenceService.check(PreferenceID.WORKGROUP_OPTION_CONTRACT_PRICING, buyerPrefs));
        boolean isDatePrefsEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROCUREMENT_DATE_PREFERENCES, buyerPrefs);
        boolean isComplDateRequired = !isDatePrefsEnabled || preferenceService.check(PreferenceID.PS_ORDER_COMPLETION_DATE_REQUIRED, buyerPrefs);
        orderDetailVO.setIsCompletionDateRequired(isComplDateRequired);

        // initialize the allowed overs & unders using buyer's workgroup defaults
        boolean closeOrderNegotiation = preferenceService.check(PreferenceID.WORKGROUP_OPTION_CLOSE_ORDER_NEGOTIATION, buyerPrefs);
        boolean hideOversAndUnders = preferenceService.check(PreferenceID.WORKGROUP_OPTION_HIDE_OVERS_UNDERS, buyerPrefs);
        if (closeOrderNegotiation) {
            String oversPercentStr = preferenceService.getString(PreferenceID.PC_ORDER_OVERS_PERCENTAGE, buyerPrefs);
            String undersPercentStr = preferenceService.getString(PreferenceID.PC_ORDER_UNDERS_PERCENTAGE, buyerPrefs);
            double oversPercent = oversPercentStr == null ? 0.0 : Double.valueOf(oversPercentStr);
            double undersPercent = undersPercentStr == null ? 0.0 : Double.valueOf(undersPercentStr);
            orderDetailVO.setOversPercent(oversPercent);
            orderDetailVO.setUndersPercent(undersPercent);
        } else{
            orderDetailVO.setOversPercent(0);
            orderDetailVO.setUndersPercent(0);
        }
        orderDetailVO.setCloseOrderNegotiation(closeOrderNegotiation);
        orderDetailVO.setHideOversAndUnders(hideOversAndUnders);

        boolean sentToSecureSupplierEnabled = preferenceService.check(PreferenceID.PC_ORDER_SENT_TO_SECURE_SUPPLIER_ONLY, buyerPrefs);
        orderDetailVO.setIsSensitive(sentToSecureSupplierEnabled);

        boolean isOverridingBreakouts = preferenceService.check(PreferenceID.PC_ALLOW_BREAKOUTS_OVERRIDE, buyerPrefs);
        boolean isPricingEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_CONTRACT_PRICING, buyerPrefs);
        boolean calTotalFromBreakouts = preferenceService.check(PreferenceID.PC_CALC_TOTAL_FROM_BREAKOUTS, buyerPrefs);
        boolean isBreakoutsForOrders = preferenceService.check(PreferenceID.PC_PRICE_BREAKOUTS_FOR_ORDERS, buyerPrefs);
        boolean isPaperFlow = parent.getIsPaperFlow();
        orderDetailVO.setIsPaperFlow(isPaperFlow);

        //handle spec node ids
        List<SpecNode> specNodes = specNodeRepository.findAllById(selectedSpecNodeIds);
        specNodes.sort((a,b)-> a.getId().compareTo(b.getId()));
        long i = 0;
        List<OrderItemVO> orderItemVOS = new ArrayList<OrderItemVO>();
        for (SpecNode specNode:specNodes) {
            OrderItemVO orderItemVO = new OrderItemVO();
            orderItemVO.setSpecNodeId(specNode.getId());
            Spec spec = specNode.getSpec();
            SpecDTO specDTO = specMapper.convertToSpecDTO(spec);
            //TODO: need to confirm with frontend.
            orderItemVO.setSpec(specMapper.toVOForOrderItem(specNode.getId(), specDTO, projectId, ""));
            boolean isTimeMaterials = specDTO.getSpecType().getIsTimeMaterials() != null && specDTO.getSpecType().getIsTimeMaterials() == (short)1;
            orderItemVO.setTimeMaterials(isTimeMaterials);
            SpecReference specReference = spec.getSpecReference();
            List<PcJob> jobs;
            if (!specDTO.getIsItemVersion()){
                jobs = jobRepository.findBySpSpecReferenceId(specReference.getSpSpecReferenceId());
            } else {
                jobs = jobRepository.findJobBySpecRefIdAndProjectId(projectId, specReference.getSpSpecReferenceId());
            }
            if (jobs != null && jobs.size() > 0) {
                Long jobId = jobs.get(jobs.size() - 1).getPcJobId();
                orderItemVO.setJobId(jobId);
            }
            orderItemVO.setItemIndex(++i);
            //quantity
            if (spec.getQuantity1() != null && spec.getQuantity1().doubleValue() > 0){
                orderItemVO.setQuantity(spec.getQuantity1());
            }
            //ratecard case
            if (isFromPricing) {
                orderItemVO.setQuantity(BigDecimal.valueOf(pricingQty));
            }
            //tax percent init
            if (itemizedTaxAndShippingEnabled) {
                orderItemVO.setTaxPercent(BigDecimal.valueOf(taxPercent));
            }
            //uofm
            UofmDTO defaultUofmDTO = uofmMapper.toDTO(uofmRepository.findDefaultBySpecTypeId(spec.getSpSpecTypeId()));
            if (defaultUofmDTO != null) {
                orderItemVO.setUofm(uofmMapper.toVO(defaultUofmDTO));
                if (isTimeMaterials) {
                    orderItemVO.setBreakoutRatesStrId(defaultUofmDTO.getBreakoutRatesStrId());
                    orderItemVO.setBreakoutUnitsStrId(defaultUofmDTO.getBreakoutUnitsStrId());
                }
            }

            //uofms
            List<Uofm> uofms = uofmRepository.findBySpecTypeId(spec.getSpSpecTypeId());
            if (uofms != null && uofms.size() > 0) {
                List<UofmVO> uofmVOS = uofms.stream().map(uofmMapper::toVO).collect(Collectors.toList());
                if (uofmVOS != null && uofmVOS.size() > 0) {
                    orderItemVO.setUofms(uofmVOS);
                }
            }
            orderItemVO.setCompletionDate(completionDate);

            //spec info for paper workflow
            if (isPaperFlow) {
                List<PropertyAttributeDTO> paperSelections = new ArrayList<>();
                PropertyAttributeDTO paperSelection = propertyAttributeMapper.getPaperSelection(spec.getProperty());
                if (paperSelection != null) {
                    paperSelections.add(paperSelection);
                }
                List<Property> childrenProperties = propertyRepository.findByParentPropertyIdAndPropertyName(spec.getProperty().getId(), "INKS_AND_PAPER");
                if (childrenProperties != null && childrenProperties.size() > 0) {
                    for (Property childProperty : childrenProperties) {
                        PropertyAttributeDTO childPaperSelection = propertyAttributeMapper.getPaperSelection(childProperty);
                        if (childPaperSelection != null) {
                            paperSelections.add(childPaperSelection);
                        }
                    }
                }
                orderItemVO.setPaperSelections(propertyAttributeMapper.toVOs(paperSelections));
            }

            //breakouts
            orderItemVO.setIsOverridingBreakouts(isOverridingBreakouts);
            orderItemVO.setTotalFromBreakouts(calTotalFromBreakouts);
            orderItemVO.setAllowBreakouts(isBreakoutsForOrders);
            if (isBreakoutsForOrders) {
                long rootBreakoutTypeId = -1;
                List<BreakoutTypeDTO> breakoutTypeDTOS = breakoutTypeMyBatisMapper.findByWorkgroupAndSpecTypeId(buyerWorkgroupId, specDTO.getSpSpecTypeId());
                breakoutTypeDTOS = breakoutTypeDTOS.stream().filter(bt->bt.isDummyRootNode() == false).collect(Collectors.toList());
                List<BreakoutVO> breakoutVOS = new ArrayList<BreakoutVO>();
                if (breakoutTypeDTOS != null && breakoutTypeDTOS.size() > 0) {
                    rootBreakoutTypeId = breakoutTypeDTOS.get(0).getRootTypeId();
                }
                //handle breakout types with supplier case.
                if (supplierWgId != null && !supplierWgId.equals(-1L)) {
                    breakoutTypeDTOS = breakoutTypeMyBatisMapper.findAllWithRootBreakoutTypeIdAndSpecTypeIdAndSupplierGroupId(rootBreakoutTypeId, specDTO.getSpSpecTypeId(), supplierWgId);
                }
                if (breakoutTypeDTOS != null && breakoutTypeDTOS.size() > 0) {
                    // all breakout types are initially set to be included by default
                    for (BreakoutTypeDTO breakoutTypeDTO : breakoutTypeDTOS) {
                        breakoutTypeDTO.setIsIncluded(true);
                        BreakoutTypeVO breakoutTypeVO = breakoutTypeMapper.toVO(breakoutTypeDTO);
                        if (breakoutTypeDTO.getIsQuantity()) {
                            BreakoutVO breakoutVO = new BreakoutVO();
                            breakoutVO.setBreakoutType(breakoutTypeVO);
                            breakoutVO.setNestingLevel(breakoutTypeDTO.getLevel().longValue());
                            breakoutVO.setHasQuantity(breakoutTypeDTO.getIsQuantity());
                            breakoutVO.setObjectId(-1L);// orderitem.getid()
                            breakoutVO.setObjectClassId(ObjectClassID.ORDER_ITEM);
                            breakoutVOS.add(breakoutVO);
                        }
                    }
                    orderItemVO.setBreakouts(breakoutVOS);
                    orderItemVO.setBreakoutTypeId(rootBreakoutTypeId);
                    Map<Long,BreakoutTypeDTO> typeMap = breakoutTypeDTOS.stream().filter(bt -> bt.getParentTypeId() != null && bt.getParentTypeId() > 0).collect(Collectors.toMap(BreakoutTypeDTO::getId, BreakoutTypeDTO->BreakoutTypeDTO));
                    breakoutService.buildBreakoutTypeTree(breakoutTypeDTOS, typeMap);
                    List<BreakoutTypeVO> breakoutTypeVOS = breakoutTypeDTOS.stream().filter(bt -> bt.getLevel() == 1).map(breakoutTypeMapper::toVOWithDescendents).collect(Collectors.toList());
                    orderItemVO.setBreakoutTypes(breakoutTypeVOS);

                }
            }
            if (isFromPricing && supplierWgId != null && !supplierWgId.equals(-1L)) {
                PricingVO pricingVO = new PricingVO();
                pricingVO.setValue(pricingAmount);
                pricingVO.composeSubTotals(subtotals);
                orderItemVO.setPricingVO(pricingVO);
                orderItemVO.setIsFromRatecard(Boolean.TRUE);
            }
            //shipment
            if (orderItemVO.getJobId() != null) {
                Shipment shipment = shipmentRepository.findByPcJobId(orderItemVO.getJobId());
                if (shipment != null) {
                    ShipmentVO shipmentVO = shipmentMapper.toVO(shipment);
                    orderItemVO.setShipment(shipmentVO);
                }
            }

            orderItemVOS.add(orderItemVO);
        }
        orderDetailVO.setOrderItems(orderItemVOS);
        return orderDetailVO;

    }

    public OrderDetailVO getOrderCreateFromEstDetail(Long projectId, Long currentWorkgroupId,
                                                     Long currentUserId, String localeCode, List<Long> estimateItemPriceIds) throws Exception {
        OrderDetailVO orderDetailVO = new OrderDetailVO();
        ProjectDTO parent = projectService.findProjectById(projectId);
        if (!createOrderPermission.check(parent, currentWorkgroupId, currentUserId, projectId)) {
            throw new NoPermissionException("Your don't have permission to create order!");
        }
        orderDetailVO.setOrderTypeId(OrderTypeID.ORDER);
        //set buyer workgroup and buyer
        long buyerWorkgroupId = parent.getOwnerWorkgroupId();
        AccountUser buyerUser = accountUserRepository.findById(currentUserId).orElse(null);
        Workgroup buyerWorkgroup = workgroupRepository.findById(buyerWorkgroupId).orElse(null);
        WorkgroupVO buyerWorkgroupVO = new WorkgroupVO();
        buyerWorkgroupVO.setId(buyerWorkgroup.getId());
        buyerWorkgroupVO.setName(buyerWorkgroup.getName());
        buyerWorkgroupVO.setDecimalPlaces(buyerWorkgroup.getDecimalPlaces());
        orderDetailVO.setBuyerWorkgroup(buyerWorkgroupVO);
        AccountUserVO buyerUserVO = new AccountUserVO();
        buyerUserVO.setId(buyerUser.getId());
        if (buyerUser.getPerson() != null) {
            buyerUserVO.setFirstName(buyerUser.getPerson().getFirstName());
            buyerUserVO.setLastName(buyerUser.getPerson().getLastName());
            buyerUserVO.setName(buyerUser.getPerson().getFullName());
            buyerUserVO.setProfileImg(buyerUser.getPerson().getProfileImg());
        }

        orderDetailVO.setBuyer(buyerUserVO);

        Map<String, String> buyerPrefs = preferenceService.findGroupPrefs(buyerWorkgroupId);
        boolean itemizedTaxAndShippingEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ITEMIZED_TAX_SHIPPING, buyerPrefs);
        orderDetailVO.setItemizedTaxAndShippingEnabled(itemizedTaxAndShippingEnabled);
        boolean isDatePrefsEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROCUREMENT_DATE_PREFERENCES, buyerPrefs);
        boolean isComplDateRequired = !isDatePrefsEnabled || preferenceService.check(PreferenceID.PS_ORDER_COMPLETION_DATE_REQUIRED, buyerPrefs);
        orderDetailVO.setIsCompletionDateRequired(isComplDateRequired);

        // tax percentage and label (tax calculate by frond-end)
        String percentStr = preferenceService.getString(PreferenceID.PC_TAX_PERCENTAGE, buyerPrefs,"0");
        Double taxPercent = Double.valueOf(percentStr);
        orderDetailVO.setTaxPercent(taxPercent);
        String taxLabel = preferenceService.getString(PreferenceID.PC_TAX_LABEL_STRING, buyerPrefs,"");
        orderDetailVO.setTaxLabel(taxLabel);

        // initialize the allowed overs & unders using buyer's workgroup defaults
        boolean closeOrderNegotiation = preferenceService.check(PreferenceID.WORKGROUP_OPTION_CLOSE_ORDER_NEGOTIATION, buyerPrefs);
        boolean hideOversAndUnders = preferenceService.check(PreferenceID.WORKGROUP_OPTION_HIDE_OVERS_UNDERS, buyerPrefs);
        if (closeOrderNegotiation) {
            String oversPercentStr = preferenceService.getString(PreferenceID.PC_ORDER_OVERS_PERCENTAGE, buyerPrefs);
            String undersPercentStr = preferenceService.getString(PreferenceID.PC_ORDER_UNDERS_PERCENTAGE, buyerPrefs);
            double oversPercent = oversPercentStr == null ? 0.0 : Double.valueOf(oversPercentStr);
            double undersPercent = undersPercentStr == null ? 0.0 : Double.valueOf(undersPercentStr);
            orderDetailVO.setOversPercent(oversPercent);
            orderDetailVO.setUndersPercent(undersPercent);
        } else{
            orderDetailVO.setOversPercent(0);
            orderDetailVO.setUndersPercent(0);
        }
        orderDetailVO.setCloseOrderNegotiation(closeOrderNegotiation);
        orderDetailVO.setHideOversAndUnders(hideOversAndUnders);

        boolean isEnableComplexVAT = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ENABLE_VAT, buyerPrefs) && vatService.isEnableVAT(buyerPrefs);
        orderDetailVO.setIsEnableComplexVAT(isEnableComplexVAT);
        if (isEnableComplexVAT) {
            orderDetailVO.setVatsInfo(vatService.findVATList(buyerPrefs));
        }

        List<EstimateItemPrice> estimateItemPrices = estimateItemPriceRepository.findByIdIn(estimateItemPriceIds);
        EstimateItem estimateItem = estimateItemPrices.get(0).getEstimateItem();
        Estimate estimate = estimateRepository.findById(estimateItem.getEstimateId()).get();
        //TODO: call by frond-end to show estimate custom attribute
        Rfe rfe = estimate.getRfe();
        LocalDateTime completionDate = null;
        if (DateUtil.isValidCompletionDate(rfe.getProposedCompletionDate())) {
            completionDate = rfe.getProposedCompletionDate();
        }
        orderDetailVO.setCompletionDate(completionDate);
        orderDetailVO.setRfeId(rfe.getId());

        Long valueCurrencyId = estimateItemPrices.get(0).getPriceCurrencyId();
        // initialize tax, miscCost and shipping to zero
        orderDetailVO.setMiscCost(BigDecimal.ZERO.doubleValue());
        orderDetailVO.setMiscCostCurrencyId(valueCurrencyId);
        orderDetailVO.setTax(BigDecimal.ZERO.doubleValue());
        orderDetailVO.setTaxCurrencyId(valueCurrencyId);
        orderDetailVO.setShipping(BigDecimal.ZERO.doubleValue());
        orderDetailVO.setShippingCurrencyId(valueCurrencyId);
        orderDetailVO.setDiscountOrSurcharge(BigDecimal.ZERO.doubleValue());
        orderDetailVO.setDiscountOrSurchargeCurrencyId(valueCurrencyId);

        // RFE title (set order title to RFE title)
        String rfeTitle = rfe.getTitle();
        if (rfeTitle!= null && !rfeTitle.equals(String.valueOf(rfe.getId()))) {
            orderDetailVO.setTitle(rfeTitle);
        }
        //dual currency
        long supplierWorkgroupId = estimate.getOwnerWorkgroupId();
        Map<String, String> supplierPrefs = preferenceService.findGroupPrefs(supplierWorkgroupId, Arrays.asList(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY));
        boolean enableDualCurrency = preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY, supplierPrefs);
        BigDecimal rate = estimate.getRate();
        Long exCurrencyId = estimate.getExCurrencyId();
        boolean isDualCurrency = enableDualCurrency && rate != null && rate.compareTo(BigDecimal.ZERO) != 0 && exCurrencyId != null;
        orderDetailVO.setIsDualCurrency(isDualCurrency);
        if (isDualCurrency) {
            orderDetailVO.setRate(rate);
            orderDetailVO.setExCurrencyId(exCurrencyId);
            orderDetailVO.setExMiscCost(BigDecimal.ZERO);
            orderDetailVO.setExMiscCostCurrencyId(exCurrencyId);
            orderDetailVO.setExShipping(BigDecimal.ZERO);
            orderDetailVO.setExShippingCurrencyId(exCurrencyId);
            orderDetailVO.setExDiscountOrSurcharge(BigDecimal.ZERO);
            orderDetailVO.setExDiscountOrSurchargeCurrencyId(exCurrencyId);
        }

        boolean dismissPref = preferenceService.check(PreferenceID.BU_WORKGROUP_ORDER_DISMISS_SUPPLIER_PREF, buyerPrefs);
        boolean isCloseRfe = rfe.isOpen() ? true : false;
        List<RfeSupplierDTO> rfeSupplierDTOS = rfeService.findDismissableSupplier(rfe.getId(), projectId);
        boolean hasSupplierGroups = rfeSupplierDTOS != null && rfeSupplierDTOS.size() > 1;
        orderDetailVO.setIsCloseRfe(isCloseRfe);
        orderDetailVO.setDismissPrefValue(dismissPref);
        orderDetailVO.setIsDismissUnselectedSupplier(hasSupplierGroups);
        Map<String, Boolean> permissionMap = permissionService.getPermissionMap(Arrays.asList(PermissionID.DISMISS_SUPPLIER, PermissionID.CLOSE_RFE), buyerWorkgroupId, currentUserId, Arrays.asList(projectId));
        orderDetailVO.setCanDismissSupplier(permissionService.check(PermissionID.DISMISS_SUPPLIER, projectId, permissionMap));
        orderDetailVO.setCanCloseRfe(permissionService.check(PermissionID.CLOSE_RFE, projectId, permissionMap));
        orderDetailVO.setIsSensitive(rfe.getIsSensitive());

        //set supplier info
        long supplierUserId = estimate.getSubmitUserId();
        if (estimate.getAutoGenerated()) {
            supplierUserId = estimate.getOwnerUserId();
            if (estimate.isInactive()) {
                //adjust the supplier to be the behalf user
                supplierUserId = estimate.getBehalfUserId();
                supplierWorkgroupId = accountUserRepository.findById(supplierUserId).get().getWorkgroupId();
            }
        }
        AccountUser supplierUser = accountUserRepository.findById(supplierUserId).orElse(null);
        Workgroup supplierWorkgroup = workgroupRepository.findById(supplierWorkgroupId).orElse(null);
        WorkgroupVO supplierWorkgroupVO = new WorkgroupVO();
        supplierWorkgroupVO.setId(supplierWorkgroup.getId());
        supplierWorkgroupVO.setName(supplierWorkgroup.getName());
        supplierWorkgroupVO.setDecimalPlaces(supplierWorkgroup.getDecimalPlaces());
        supplierWorkgroupVO.setSupplierFlag(supplierWorkgroupService.getSupplierFlagVO(true, -1, ObjectClassID.ORDER, buyerWorkgroupId, supplierWorkgroupId));
//      supplierWorkgroupVO.setSupplierScore(ratingService.findSupplierScore(parent.getOwnerWorkgroupId(), supplierWorkgroup.getId()));
        orderDetailVO.setSupplierWorkgroup(supplierWorkgroupVO);
        AccountUserVO supplierUserVO = new AccountUserVO();
        supplierUserVO.setId(supplierUser.getId());
        if (supplierUser.getPerson() != null) {
            supplierUserVO.setFirstName(supplierUser.getPerson().getFirstName());
            supplierUserVO.setLastName(supplierUser.getPerson().getLastName());
            supplierUserVO.setName(supplierUser.getPerson().getFullName());
            supplierUserVO.setProfileImg(supplierUser.getPerson().getProfileImg());
        }

        orderDetailVO.setSupplier(supplierUserVO);

        // If the form contains the job and specNode, set the orderItem to the specified job/specNode
        // The form's job/specNode can be different from the estimateItem's job/specNode
        // This can happen when the outsourcer "buy order" from a quote with estimate that had been already ordered
        // see NKB137365

        List<EstimateItem> estimateItems = estimateItemRepository.findByEstimateIdOrderByIdAsc(estimate.getId());
        List<Long> specNodeIds = estimateItems.stream().map(EstimateItem::getSpecNodeId).collect(Collectors.toList());
        //handle spec node ids
        List<SpecNode> specNodes = specNodeRepository.findAllById(specNodeIds);
        specNodes.sort((a,b)-> a.getId().compareTo(b.getId()));
        Map<Long, SpecNode> specNodeMap = new HashMap<>();
        for (SpecNode specNode: specNodes) {
            specNodeMap.put(specNode.getId(), specNode);
        }

        long i = 0;
        List<OrderItemVO> orderItemVOS = new ArrayList<>();
        boolean isOverridingBreakouts = preferenceService.check(PreferenceID.PC_ALLOW_BREAKOUTS_OVERRIDE, buyerPrefs);
        boolean isBreakoutsForOrders = preferenceService.check(PreferenceID.PC_PRICE_BREAKOUTS_FOR_ORDERS, buyerPrefs);

        //handle paper and logistics
        boolean enableSupplierAddPaperDetails = preferenceService.check(PreferenceID.WORKGROUP_OPTION_SUPPLIER_ADD_PAPER_DETAILS, buyerPrefs);
        boolean enableLogistics = preferenceService.check(PreferenceID.WORKGROUP_OPTION_SUSTAINABILITY_LOGISTICS, buyerPrefs);
        //carry over estimate/item user field values to order/item
        boolean userFieldEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_USER_FIELDS, buyerPrefs);
        orderDetailVO.setEnableSupplierAddPaperDetails(enableSupplierAddPaperDetails);
        orderDetailVO.setEnableLogistics(enableLogistics);
        boolean isCommunisis = accountService.isCommunisisPortal(parent, currentWorkgroupId);
        List<Long> propertyIds = estimateItems.stream().filter(ei -> ei.getPropertyId() != null && ei.getPropertyId() > 0).map(EstimateItem::getPropertyId).collect(Collectors.toList());
        for (EstimateItemPrice estItemPrice : estimateItemPrices) {
            OrderItemVO orderItemVO = new OrderItemVO();
            orderItemVO.setEstimateItemPriceId(estItemPrice.getId());
            orderItemVO.setQuantity(BigDecimal.valueOf(estItemPrice.getItemOption().getValueInDouble()));
            orderItemVO.setEstItemOptionIndex(estItemPrice.getItemOption().getOptionIndex());
            EstimateItem estItem = estItemPrice.getEstimateItem();
            if (estItem.getIsEstimated() && estItem.getPropertyId() != null && estItem.getPropertyId() > 0) {
                orderItemVO.setCustomPropertyId(estItem.getPropertyId());
            }
            Long specNodeId = estItem.getSpecNodeId();
            SpecNode specNode = specNodeMap.get(specNodeId);
            Spec spec = specNode.getSpec();
            SpecDTO specDTO = specMapper.convertToSpecDTO(spec);
            boolean isTimeMaterials = specDTO.getSpecType().getIsTimeMaterials() != null && specDTO.getSpecType().getIsTimeMaterials() == (short)1;
            orderItemVO.setTimeMaterials(isTimeMaterials);
            orderItemVO.setSpec(specMapper.toVOForOrderItem(specNodeId, specDTO, projectId,""));
            orderItemVO.setSpecNodeId(specNodeId);
            orderItemVO.setValue(estItemPrice.getPrice());
            orderItemVO.setValueCurrencyId(estItemPrice.getPriceCurrencyId());
            orderItemVO.setComments(estItem.getComments());
            orderItemVO.setVatRate(estimateItem.getVatRate());
            orderItemVO.setVatCode(estimateItem.getVatCode());
            //orderItemVO.setValueCurrency(estItemPrice.getPriceCurrencyId());
            orderItemVO.setItemIndex(++i);
            orderItemVO.setJobId(estItem.getPcJobId());
            if (estItemPrice.getBuUOFMId() != null) {
                Uofm uofm = uofmRepository.findById(estItemPrice.getBuUOFMId()).get();
                orderItemVO.setUofm(uofmMapper.toVO(uofm));
                if (isTimeMaterials) {
                    UofmDTO uofmDTO = uofmMapper.toDTO(uofm);
                    orderItemVO.setBreakoutRatesStrId(uofmDTO.getBreakoutRatesStrId());
                    orderItemVO.setBreakoutUnitsStrId(uofmDTO.getBreakoutUnitsStrId());
                }
            }
            //uofms
            List<Uofm> uofms = uofmRepository.findBySpecTypeId(spec.getSpSpecTypeId());
            if (uofms != null && uofms.size() > 0) {
                List<UofmVO> uofmVOS = uofms.stream().map(uofmMapper::toVO).collect(Collectors.toList());
                if (uofmVOS != null && uofmVOS.size() > 0) {
                    orderItemVO.setUofms(uofmVOS);
                }
            }
            if (estItemPrice.getAddBuUOFMId() != null) {
                Uofm uofm = uofmRepository.findById(estItemPrice.getAddBuUOFMId()).get();
                orderItemVO.setAdditionalUofm(uofmMapper.toVO(uofm));
            }
            orderItemVO.setAdditionalPrice(estItemPrice.getAddPrice());
            orderItemVO.setAdditionalPriceCurrencyId(estItemPrice.getAddPriceCurrencyId());
            if (itemizedTaxAndShippingEnabled) {
                BigDecimal tax = estItemPrice.getTax();
                orderItemVO.setTax(tax);
                orderItemVO.setTaxCurrencyId(estItemPrice.getTaxCurrencyId());
                BigDecimal itemAmount = orderItemVO.getValue();
                if (tax != null && itemAmount != null && itemAmount.compareTo(BigDecimal.ZERO) > 0 && tax.compareTo(BigDecimal.ZERO) > 0) {
                    // compute the percent if there are any amount
                    BigDecimal itemTaxPercent = tax.multiply(BigDecimal.valueOf(100)).divide(itemAmount, 5, RoundingMode.HALF_UP);
                    orderItemVO.setTaxPercent(itemTaxPercent);
                } else {
                    orderItemVO.setTaxPercent(BigDecimal.valueOf(taxPercent));
                }
                orderItemVO.setShipping(estItemPrice.getShipping());
                orderItemVO.setShippingCurrencyId(estItemPrice.getShippingCurrencyId());
            } else {
                orderItemVO.setTax(BigDecimal.ZERO);
                orderItemVO.setTaxCurrencyId(orderItemVO.getValueCurrencyId());
                orderItemVO.setShipping(BigDecimal.ZERO);
                orderItemVO.setShippingCurrencyId(orderItemVO.getValueCurrencyId());
            }
            if (isDualCurrency) {
                orderItemVO.setExValue(estItemPrice.getExPrice());
                orderItemVO.setExValueCurrencyId(estItemPrice.getExPriceCurrencyId());
                orderItemVO.setExAdditionalPrice(estItemPrice.getExAddPrice());
                orderItemVO.setExAdditionalPriceCurrencyId(estItemPrice.getExAddPriceCurrencyId());
                if (itemizedTaxAndShippingEnabled) {
                    BigDecimal exTax = estItemPrice.getExTax();
                    orderItemVO.setExTax(exTax);
                    orderItemVO.setExTaxCurrencyId(estItemPrice.getExTaxCurrencyId());
                    orderItemVO.setExShipping(estItemPrice.getExShipping());
                    orderItemVO.setExShippingCurrencyId(estItemPrice.getExShippingCurrencyId());
                } else {
                    orderItemVO.setExTax(BigDecimal.ZERO);
                    orderItemVO.setExTaxCurrencyId(orderItemVO.getExValueCurrencyId());
                    orderItemVO.setExShipping(BigDecimal.ZERO);
                    orderItemVO.setExShippingCurrencyId(orderItemVO.getExValueCurrencyId());
                }
            }
            //TODO: call by frond-end to show estimate item and itemprice custom attribute
            orderItemVO.setCompletionDate(completionDate);

            //TODO: paper workflow
            // price breakouts
            orderItemVO.setIsOverridingBreakouts(isOverridingBreakouts);
            orderItemVO.setAllowBreakouts(isBreakoutsForOrders);
            if (isBreakoutsForOrders) {
                orderItemVO.setBreakoutTypeId(estItem.getBreakoutTypeId());
                List<BreakoutVO> breakoutVOS = breakoutService.getBreakouts(estItemPrice);
                orderItemVO.setBreakouts(breakoutVOS);
                orderItemVO.setTotalFromBreakouts(estItem.getTotalFromBreakouts());
            }
            //shipment
            if (orderItemVO.getJobId() != null){
                Shipment shipment = shipmentRepository.findByPcJobId(orderItemVO.getJobId());
                if (shipment != null) {
                    ShipmentVO shipmentVO = shipmentMapper.toVO(shipment);
                    orderItemVO.setShipment(shipmentVO);
                }
            }

            // Display of Environmental Paper Impact
            if (estItem.getIsEstimated() && enableSupplierAddPaperDetails && estItem.getPropertyId() != null) {
                List<PropertyAttributeDTO> propertyAttributeDTOList = new ArrayList<>();
                if (estItem.getPropertyId() != null) {
                    propertyAttributeDTOList = propertyMyBatisMapper.getEstItemPaperDetailProperty(estItem.getPropertyId());
                }
                boolean containsPaperInfo = PaperUtil.containsPaperInfo(propertyAttributeDTOList);
                if (containsPaperInfo) {
                    int paperNumber = propertyMyBatisMapper.getPaperDetailCount(spec.getId(), "STOCKS_AND_INKS").size();
                    if (paperNumber == 0) {
                        paperNumber = propertyMyBatisMapper.getPaperDetailCount(spec.getId(), "INKS_AND_PAPER").size();
                    }
                    List<PaperDetailVO> paperDetails;
                    if (isCommunisis) {
                        paperDetails = OrderUtil.getPaperDetailForCommunisis(propertyAttributeDTOList, paperNumber);
                    } else {
                        paperDetails = OrderUtil.getPaperDetail(propertyAttributeDTOList, paperNumber);
                    }
                    orderItemVO.setPaperDetails(paperDetails);
                }
            }
            if (enableSupplierAddPaperDetails && orderItemVO.getPaperDetails() == null) {
                orderItemVO.setPaperDetails(Collections.emptyList());
            }
            orderItemVOS.add(orderItemVO);
        }

        // carry over estimate/item custom/user field values to order item
        Long estimateCustomPropertyId = estimate.getCustomPropertyId();
        if (estimateCustomPropertyId != null && estimateCustomPropertyId > 0) {
            propertyIds.add(estimateCustomPropertyId);
        }
        if (enableLogistics || userFieldEnabled) {
            if (!propertyIds.isEmpty()) {
                Map<Long, Map<String, Object>> customAttributesMap = workgroupOpenFeignClient.findCustomAttribute(propertyIds);
                if (customAttributesMap.containsKey(estimateCustomPropertyId)) {
                    orderDetailVO.setCustomAttributes(customAttributesMap.get(estimateCustomPropertyId));
                }
                for (OrderItemVO orderItemVO : orderItemVOS) {
                    if (orderItemVO.getCustomPropertyId() != null) {
                        Map<String, Object> customAttributes = customAttributesMap.get(orderItemVO.getCustomPropertyId());
                        if (customAttributes != null) {
                            orderItemVO.setCustomAttributes(customAttributes);
                            if (enableLogistics) {
                                orderMapper.setEnvironmentalLogistics(enableLogistics, orderItemVO);
                            }
                        }
                    }
                }
            }
        }
        orderDetailVO.setOrderItems(orderItemVOS);

        // terms & conditions
        TermsVO buyerTerms = termsService.findCurrentTermsAndConditions(buyerWorkgroup, TermsTypeID.BUYER_PURCHASE);
        if (buyerTerms != null) {
            orderDetailVO.setBuyerTerms(buyerTerms);
        }
        TermsVO supplierTerms = termsService.findCurrentTermsAndConditions(supplierWorkgroup, TermsTypeID.SUPPLIER_SELL);
        if (supplierTerms != null) {
            orderDetailVO.setSupplierTerms(supplierTerms);
        }

        return orderDetailVO;
    }

    public OrderConfirmVO getOrderConfirmView(OrderVersionDTO orderVersionDTO, Long projectId) throws Exception {
        OrderConfirmVO orderConfirmVO = new OrderConfirmVO();
        ProjectDTO parentProject = projectService.findProjectById(projectId);
        orderVersionDTO.setParent(parentProject);
        Map<String, String> buyerWorkgroupPreference = preferenceService.findGroupPrefs(orderVersionDTO.getBuyerWorkgroupId());

        orderConfirmVO.setRequiresApproval(false);
        orderConfirmVO.setRequiresManagerApproval(false);
        if (routingService.requiresApproval(orderVersionDTO, buyerWorkgroupPreference)) {
            if (orderVersionDTO.isUserBuyer()) {
                if (routingService.requiresQuickApproval(orderVersionDTO, buyerWorkgroupPreference)) {
                    orderConfirmVO.setIsAllAtOnce(true);
                    orderConfirmVO.setAutoTimeOut(false);
                } else {
                    Boolean isAllAtOnce = preferenceService.check(PreferenceID.PS_ALL_AT_ONCE, buyerWorkgroupPreference);
                    Boolean autoTimeOut = preferenceService.check(PreferenceID.PS_TIMEOUT_PERIOD_ENABLED, buyerWorkgroupPreference);
                    orderConfirmVO.setIsAllAtOnce(isAllAtOnce);
                    orderConfirmVO.setAutoTimeOut(autoTimeOut);
                }
                List<AccountUserDTO> accountUserDTOS = orderVersionDTO.getApproverRecipients();
                List<AccountUserVO> accountUserVOS = accountUserDTOS.stream().map(accountUserMapper::toVO).collect(Collectors.toList());
                orderConfirmVO.setApprovers(accountUserVOS);
                orderConfirmVO.setRequiresApproval(orderVersionDTO.getRequiresApproval());
            }
        } else  if (routingService.requiresManagerApproval(orderVersionDTO, buyerWorkgroupPreference)) {
            if (orderVersionDTO.isUserBuyer()) {
                Boolean allMgrAtOnce = preferenceService.check(PreferenceID.PS_M_ALL_AT_ONCE, buyerWorkgroupPreference);
                Boolean autoMgrTimeOut = preferenceService.check(PreferenceID.PS_M_TIMEOUT_PERIOD_ENABLED, buyerWorkgroupPreference);
                List<AccountUserDTO> accountUserDTOS = orderVersionDTO.getManagerRecipients();
                List<AccountUserVO> accountUserVOS = accountUserDTOS.stream().map(accountUserMapper::toVO).collect(Collectors.toList());
                orderConfirmVO.setManagerApprovers(accountUserVOS);
                orderConfirmVO.setIsAllAtOnce(allMgrAtOnce);
                orderConfirmVO.setAutoTimeOut(autoMgrTimeOut);
                orderConfirmVO.setRequiresManagerApproval(orderVersionDTO.getRequiresManagerApproval());
            }
        }

        boolean uniqueOrderTitle = preferenceService.check(PreferenceID.WORKGROUP_OPTION_UNIQUE_ORDER_TITLE, buyerWorkgroupPreference);
        if (uniqueOrderTitle) {
            Long parentOrderId = orderVersionDTO.getOrderId() == null ? -1L : orderVersionDTO.getOrderId();
            if (orderMyBatisMapper.checkOrderTitleExists(projectId, orderVersionDTO.getOrderTitle(), parentOrderId) > 0) {
                throw new NoPermissionException("There is an existing Order of the same title");
            }
        }
        return orderConfirmVO;
    }
}
