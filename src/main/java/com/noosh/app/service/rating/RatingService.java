package com.noosh.app.service.rating;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.dto.accounts.RatingAverageDTO;
import com.noosh.app.commons.dto.accounts.RatingQuestionDTO;
import com.noosh.app.commons.dto.accounts.RatingQuestionnaireDTO;
import com.noosh.app.commons.entity.accounts.*;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.commons.vo.rating.RatingPreferenceVO;
import com.noosh.app.commons.vo.rating.RatingQuestionVO;
import com.noosh.app.commons.vo.rating.RatingQuestionnaireVO;
import com.noosh.app.commons.vo.rating.RatingSectionVO;
import com.noosh.app.mapper.rating.SupplierRatingMapper;
import com.noosh.app.repository.jpa.accounts.*;
import com.noosh.app.repository.mybatis.accounts.RatingAverageMyBatisMapper;
import com.noosh.app.repository.mybatis.accounts.RatingQuestionnaireMyBatisMapper;
import com.noosh.app.service.preference.PreferenceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * author: Yang
 * Date: 10/17/2022
 */
@Service
public class RatingService {

    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private RatingQuestionnaireRepository ratingQuestionnaireRepository;
    @Autowired
    private RatingSectionRepository ratingSectionRepository;
    @Autowired
    private RatingRepository ratingRepository;
    @Autowired
    private RatingQuestionRepository ratingQuestionRepository;
    @Autowired
    private RatingAverageRepository ratingAverageRepository;
    @Autowired
    private RatingQuestionnaireMyBatisMapper ratingQuestionnaireMyBatisMapper;
    @Autowired
    private RatingAverageMyBatisMapper ratingAverageMyBatisMapper;
    @Autowired
    private SupplierRatingMapper ratingMapper;


    private Map<Long, String> stateToDescriptionMapping;

    @PostConstruct
    public void init() {
        stateToDescriptionMapping = new HashMap(5);
        stateToDescriptionMapping.put(ObjectStateID.ORDER_ACCEPTED, "Accepted");
        stateToDescriptionMapping.put(ObjectStateID.ORDER_SHIPPED, "Shipped");
        stateToDescriptionMapping.put(ObjectStateID.ORDER_DELIVERED, "Delivered");
        stateToDescriptionMapping.put(ObjectStateID.ORDER_COMPLETED, "Closed");
        stateToDescriptionMapping.put(-1L, "Created");
    }

    public RatingPreferenceVO getRatingPreferences(long workgroupId) {
        Map<String, String> preferences = preferenceService.findGroupPrefs(workgroupId);
        RatingPreferenceVO preferenceVO = new RatingPreferenceVO();

        boolean supplierRatingEnabled = preferenceService.check("SR_ACTIVE", preferences);
        preferenceVO.setSupplierRatingEnabled(supplierRatingEnabled);

        int averageRatingNumber = preferenceService.getInt("SR_RATING_AVERAGE_NUMBER", preferences, 0);
        preferenceVO.setAverageRatingNumber(averageRatingNumber);

        boolean canSupplierViewRating = preferenceService.check("SR_RATING_PUBLIC", preferences);
        preferenceVO.setCanSupplierViewRating(canSupplierViewRating);

        boolean canSupplierEditRating = preferenceService.check("SR_SUPPLIERS_CAN_EDIT", preferences);
        preferenceVO.setCanSupplierEditRating(canSupplierEditRating);

        long activeQuestionnaireId = preferenceService.getLong("SR_ACTIVE_QUESTIONNAIRE", preferences, -1L);
        if (activeQuestionnaireId != -1) {
            Optional<RatingQuestionnaire> optional = ratingQuestionnaireRepository.findById(activeQuestionnaireId);
            if (optional.isPresent()) {
                RatingQuestionnaire ratingQuestionnaireEntity = optional.get();
                preferenceVO.setActiveQuestionnaire(ratingQuestionnaireEntity.getTitle());
                preferenceVO.setActiveQuestionnaireId(ratingQuestionnaireEntity.getId());
            }
        }

        Long ratingTriggerId = preferenceService.getLong("SR_RATING_TRIGGER", preferences, ObjectStateID.ORDER_ACCEPTED);
        preferenceVO.setRatingTriggerId(ratingTriggerId);
        String ratingTrigger = stateToDescriptionMapping.get(ratingTriggerId);
        preferenceVO.setRatingTrigger(ratingTrigger);

        int ratingGranularity = preferenceService.getInt("SR_RATING_GRANULARITY", preferences, 3);
        preferenceVO.setRatingLevels(ratingGranularity);

        boolean ratingNaAllowed = preferenceService.check("SR_RATING_NA_ALLOWED", preferences);
        preferenceVO.setIsNaAllowed(ratingNaAllowed);

        boolean ratingRequired = preferenceService.check("SR_RATING_REQUIRED", preferences);
        preferenceVO.setIsRatingRequired(ratingRequired);

        return preferenceVO;
    }

    public RatingPreferenceVO getEditableRatingPreferences(long workgroupId) {
        RatingPreferenceVO preferenceVO = getRatingPreferences(workgroupId);

        List<DropdownVO<Long>> activeQuestionnaireList = new ArrayList<>();
        List<RatingQuestionnaireDTO> ratingQuestionnaireDTOList = ratingQuestionnaireMyBatisMapper.findByWorkGroupId(workgroupId);
        ratingQuestionnaireDTOList.forEach(dto -> activeQuestionnaireList.add(new DropdownVO<>(dto.getId(), dto.getTitle())));
        preferenceVO.setActiveQuestionnaireList(activeQuestionnaireList);

        List<DropdownVO<Long>> ratingTriggerIdList = new ArrayList<>();
        stateToDescriptionMapping.forEach((k, v) -> ratingTriggerIdList.add(new DropdownVO<>(k, v)));
        preferenceVO.setRatingTriggerIdList(ratingTriggerIdList);

        List<DropdownVO<Integer>> ratingLevelsList = new ArrayList<>();
        ratingLevelsList.add(new DropdownVO<>(3, "3"));
        ratingLevelsList.add(new DropdownVO<>(5, "5"));
        preferenceVO.setRatingLevelsList(ratingLevelsList);

        return preferenceVO;
    }
    @Transactional
    public RatingPreferenceVO editRatingPreferences(long userId, long workgroupId, RatingPreferenceVO ratingPreferenceVO) {
        Map<String, String> oldPreferences = preferenceService.findGroupPrefs(workgroupId, Collections.singletonList("SR_RATING_AVERAGE_NUMBER"));
        int previousSpanCount = preferenceService.getInt("SR_RATING_AVERAGE_NUMBER", oldPreferences, 50);

        Map<String, String> preferences = new HashMap<>();
        preferenceService.setBoolean("SR_ACTIVE", ratingPreferenceVO.getSupplierRatingEnabled(), preferences);
        preferenceService.setNumber("SR_RATING_AVERAGE_NUMBER", ratingPreferenceVO.getAverageRatingNumber(), preferences);
        preferenceService.setBoolean("SR_RATING_PUBLIC", ratingPreferenceVO.getCanSupplierViewRating(), preferences);
        preferenceService.setBoolean("SR_SUPPLIERS_CAN_EDIT", ratingPreferenceVO.getCanSupplierEditRating(), preferences);
        Long activeQuestionnaireId = ratingPreferenceVO.getActiveQuestionnaireId();
        if (null != activeQuestionnaireId) {
            Optional<RatingQuestionnaire> optional = ratingQuestionnaireRepository.findById(activeQuestionnaireId);
            if (!optional.isPresent() || optional.get().getWorkGroupId() != workgroupId) {
                throw new IllegalArgumentException("activeQuestionnaireId is not valid " + activeQuestionnaireId);
            }
        }
        preferenceService.setNumber("SR_ACTIVE_QUESTIONNAIRE", activeQuestionnaireId, preferences);
        if (null != ratingPreferenceVO.getRatingTriggerId() && !stateToDescriptionMapping.containsKey(ratingPreferenceVO.getRatingTriggerId())) {
            throw new IllegalArgumentException("ratingTriggerId is not valid " + ratingPreferenceVO.getRatingTriggerId());
        }
        preferenceService.setNumber("SR_RATING_TRIGGER", ratingPreferenceVO.getRatingTriggerId(), preferences);
        preferenceService.setNumber("SR_RATING_GRANULARITY", ratingPreferenceVO.getRatingLevels(), preferences);
        preferenceService.setBoolean("SR_RATING_NA_ALLOWED", ratingPreferenceVO.getIsNaAllowed(), preferences);
        preferenceService.setBoolean("SR_RATING_REQUIRED", ratingPreferenceVO.getIsRatingRequired(), preferences);
        preferenceService.updatePreferencesForWorkgroup(workgroupId, userId, preferences);
        int newSpanCount = ratingPreferenceVO.getAverageRatingNumber() == null ? 0 : ratingPreferenceVO.getAverageRatingNumber();
        if (previousSpanCount != newSpanCount) {
            createOrUpdateAverageRating(newSpanCount, workgroupId);
        }
        return getRatingPreferences(workgroupId);
    }

    private void createOrUpdateAverageRating(int newSpanCount, long byWorkgroupId) {
        List<RatingAverageDTO> ratingAverageDTOS;
        if (newSpanCount == 0) {
            ratingAverageDTOS = ratingAverageMyBatisMapper.calculateAvgFromAllRatings(byWorkgroupId);
        } else {
            ratingAverageDTOS = ratingAverageMyBatisMapper.calculateAvgFromRatings(byWorkgroupId, newSpanCount);
        }
        for (int i = 0; i < ratingAverageDTOS.size(); i++) {
            RatingAverageDTO calculatedAvg = ratingAverageDTOS.get(i);
            long forWorkgroupId = calculatedAvg.getRatedForWorkGroupId();
            Optional<RatingAverage> ratingAverageOptional = ratingAverageRepository.findByRatedByWorkGroupIdAndRatedForWorkGroupIdAndObjectClassId(byWorkgroupId, forWorkgroupId, ObjectClassID.ORDER);
            RatingAverage averageRating = ratingAverageOptional.orElse(new RatingAverage());
            averageRating.setRatedByWorkGroupId(byWorkgroupId);
            averageRating.setRatedForWorkGroupId(forWorkgroupId);
            averageRating.setObjectClassId(ObjectClassID.ORDER);
            averageRating.setAverageGrade(calculatedAvg.getAverageGrade());
            averageRating.setCount(calculatedAvg.getCount());
            averageRating.setComputedDate(LocalDateTime.now());
            ratingAverageRepository.save(averageRating);
        }
    }

    public List<RatingQuestionnaireVO> getRatingQuestionnaireList(long workgroupId) {
        List<RatingQuestionnaireDTO> ratingQuestionnaireDTOList = ratingQuestionnaireMyBatisMapper.findByWorkGroupId(workgroupId);
        List<RatingQuestionnaireVO> list = new ArrayList<>();
        if (!ratingQuestionnaireDTOList.isEmpty()) {
            Map<String, String> preferences = preferenceService.findGroupPrefs(workgroupId, Arrays.asList("SR_ACTIVE_QUESTIONNAIRE"));
            long activeQuestionnaireId = preferenceService.getLong("SR_ACTIVE_QUESTIONNAIRE", preferences, -1L);
            for (RatingQuestionnaireDTO ratingQuestionnaireDTO : ratingQuestionnaireDTOList) {
                list.add(ratingMapper.toRatingQuestionnaireVO(ratingQuestionnaireDTO, activeQuestionnaireId, canQuestionnaireDelete(ratingQuestionnaireDTO.getId())));
            }
        }
        return list;
    }

    public RatingQuestionnaireVO getRatingQuestionnaireDetail(long questionnaireId, long workgroupId) {
        RatingQuestionnaireDTO ratingQuestionnaireDTO = ratingQuestionnaireMyBatisMapper.findWithQuestions(questionnaireId);
        if (null == ratingQuestionnaireDTO) {throw new IllegalArgumentException("can't find questionnaire by " + questionnaireId);}
        List<RatingSection> ratingSections = ratingSectionRepository.findAllByOrderByDisplayOrder();
        Map<String, String> preferences = preferenceService.findGroupPrefs(workgroupId, Arrays.asList("SR_ACTIVE_QUESTIONNAIRE"));
        long activeQuestionnaireId = preferenceService.getLong("SR_ACTIVE_QUESTIONNAIRE", preferences, -1L);
        return ratingMapper.toRatingQuestionnaireVO(ratingQuestionnaireDTO, ratingSections, activeQuestionnaireId, canQuestionnaireDelete(questionnaireId));
    }

    public List<RatingSectionVO> getRatingSectionList() {
        List<RatingSectionVO> ratingSectionList = new ArrayList<>();
        List<RatingSection> ratingSections = ratingSectionRepository.findAllByOrderByDisplayOrder();
        for (RatingSection ratingSection: ratingSections) {
            RatingSectionVO ratingSectionVO = ratingMapper.toRatingSectionVO(ratingSection);
            ratingSectionList.add(ratingSectionVO);
        }
        return ratingSectionList;
    }

    @Transactional
    public long editRatingQuestionnaireWithQuestions(RatingQuestionnaireVO ratingQuestionnaireVO, long workgroupId, long userId) {
        //clone or update questionnaire
        Long questionnaireId = editRatingQuestionnaire(ratingQuestionnaireVO, workgroupId);

        //update active preferences
        if (null != ratingQuestionnaireVO.getIsActive() && ratingQuestionnaireVO.getIsActive()) {
            setQuestionnaireActive(workgroupId, userId, questionnaireId);
        }
        return questionnaireId;
    }

    private Long editRatingQuestionnaire(RatingQuestionnaireVO ratingQuestionnaireVO, long workgroupId) {
        //pre-check
        Long questionnaireId = ratingQuestionnaireVO.getQuestionnaireId();
        Optional<RatingQuestionnaire> questionnaireOptional = ratingQuestionnaireRepository.findById(questionnaireId);
        if (questionnaireOptional.isEmpty()) {throw new IllegalArgumentException("Can't find questionnaire by " + questionnaireId);}
        RatingQuestionnaire questionnaire = questionnaireOptional.get();
        if (!questionnaire.isCurrent) {throw new IllegalArgumentException("Can only edit current questionnaire.");}

        check(ratingQuestionnaireVO);

        //update questionnaire
        boolean isCreate = !canQuestionnaireDelete(questionnaireId);
        if (isCreate) {
            //there are already ratings created for it, clone it
            questionnaire.setIsCurrent(false);
            RatingQuestionnaire clonedQuestionnaire = createRatingQuestionnaire(ratingQuestionnaireVO, workgroupId);
            questionnaireId = clonedQuestionnaire.getId();
        } else {
            //nobody uses, update it
            questionnaire.setTitle(ratingQuestionnaireVO.getTitle());
            ratingQuestionnaireRepository.save(questionnaire);
        }
        //update questions
        List<RatingSectionVO> ratingSectionVOS = ratingQuestionnaireVO.getRatingSectionList();
        for (int i = 0; i < ratingSectionVOS.size(); i++) {
            RatingSectionVO  ratingSectionVO = ratingSectionVOS.get(i);
            if (null != ratingSectionVO.getRatingQuestionList() && !ratingSectionVO.getRatingQuestionList().isEmpty()) {
                if (isCreate) {
                    createRatingQuestions(ratingSectionVO.getRatingQuestionList(), ratingSectionVO.getRatingSectionId(), questionnaireId);
                } else {
                    freshRatingQuestions(ratingSectionVO.getRatingQuestionList(), ratingSectionVO.getRatingSectionId(), questionnaireId);
                }

            }
        }

        return questionnaireId;
    }

    private void check(RatingQuestionnaireVO ratingQuestionnaireVO) {
        //check questionnaire
        checkQuestionnaire(ratingQuestionnaireVO);
        //check questions
        List<RatingSectionVO> ratingSectionVOS = ratingQuestionnaireVO.getRatingSectionList();
        Set<Long> questionIdSet = new HashSet<>();
        if (null != ratingQuestionnaireVO.getQuestionnaireId()) {
            RatingQuestionnaireDTO ratingQuestionnaireDTO = ratingQuestionnaireMyBatisMapper.findWithQuestions(ratingQuestionnaireVO.getQuestionnaireId());
            questionIdSet = ratingQuestionnaireDTO.getQuestions().stream().map(RatingQuestionDTO :: getId).collect(Collectors.toSet());
        }
        checkSectionsAndQuestions(ratingSectionVOS, questionIdSet);
    }

    private void checkQuestionnaire(RatingQuestionnaireVO ratingQuestionnaireVO) {
        if (ratingQuestionnaireVO.getTitle() == null || ratingQuestionnaireVO.getTitle().isEmpty()) {
            throw new IllegalArgumentException("The field Questionnaire name is required");
        }
    }

    private void freshRatingQuestions(List<RatingQuestionVO> ratingQuestionList, Long ratingSectionId, Long questionnaireId) {
        //no ratings created
        for (RatingQuestionVO ratingQuestionVO : ratingQuestionList) {
            if (ratingQuestionVO.getRatingQuestionId() == null) {
                if (!ratingQuestionVO.getIsDeleted()) {
                    //create
                    createRatingQuestion(ratingQuestionVO, ratingSectionId, questionnaireId);
                }
            } else {
                if (ratingQuestionVO.getIsDeleted()) {
                    //delete
                    ratingQuestionRepository.deleteById(ratingQuestionVO.getRatingQuestionId());
                } else {
                    //edit
                    editRatingQuestion(ratingQuestionVO, questionnaireId);
                }
            }
        }
    }

    private void editRatingQuestion(RatingQuestionVO ratingQuestionVO, long questionnaireId) {
        if (ratingQuestionVO.getRatingQuestionId() == null) {throw new IllegalArgumentException("There is not a valid ratingQuestionId.");}
        Optional<RatingQuestion> ratingQuestionOptional = ratingQuestionRepository.findById(ratingQuestionVO.getRatingQuestionId());
        if (ratingQuestionOptional.isEmpty() ) {throw new IllegalArgumentException("Can't find ratingQuestion by " + ratingQuestionVO.getRatingQuestionId());}
        RatingQuestion ratingQuestion = ratingQuestionOptional.get();
        if (ratingQuestion.getRatingQuestionnaireId() != questionnaireId) {throw new IllegalArgumentException("Invalid ratingQuestionId " + ratingQuestionVO.getRatingQuestionId());}
        ratingQuestion.setText(ratingQuestionVO.getText());
        ratingQuestion.setWeight(ratingQuestionVO.getWeight());
        ratingQuestion.setOrdinal(ratingQuestionVO.getOrdinal());
        ratingQuestionRepository.save(ratingQuestion);
    }

    @Transactional
    public void deleteRatingQuestionnaire(long questionnaireId, long workgroupId) {
        RatingQuestionnaireDTO ratingQuestionnaireDTO = ratingQuestionnaireMyBatisMapper.findWithQuestions(questionnaireId);
        if (null == ratingQuestionnaireDTO) {throw new IllegalArgumentException("There is not a valid ratingQuestionId.");}
        if (ratingQuestionnaireDTO.getWorkGroupId() != workgroupId) {throw new IllegalArgumentException("You can only delete your own workgroup questionnaire.");}
        boolean canDelete = canQuestionnaireDelete(questionnaireId);
        if (canDelete) {
            List<RatingQuestionDTO> ratingQuestionDTOS = ratingQuestionnaireDTO.getQuestions();
            List<Long> questionIds = new ArrayList<>();
            ratingQuestionDTOS.stream().forEach(ratingQuestionDTO -> questionIds.add(ratingQuestionDTO.getId()));
            ratingQuestionRepository.deleteAllById(questionIds);
            ratingQuestionnaireRepository.deleteById(questionnaireId);
        } else {
            throw new IllegalArgumentException("Questionnaire is already in use.");
        }
    }

    private boolean canQuestionnaireDelete(long questionnaireId) {
        List<Rating> ratings = ratingRepository.findByQuestionnaireId(questionnaireId);
        return null == ratings || ratings.isEmpty();
    }

    @Transactional
    public long createRatingQuestionnaireWithQuestions(RatingQuestionnaireVO ratingQuestionnaireVO, long workgroupId, long userId) {
        //pre-check
        check(ratingQuestionnaireVO);
        //create questionnaire
        RatingQuestionnaire ratingQuestionnaire = createRatingQuestionnaire(ratingQuestionnaireVO, workgroupId);
        Long questionnaireId = ratingQuestionnaire.getId();
        //create questions
        List<RatingSectionVO> ratingSectionVOS = ratingQuestionnaireVO.getRatingSectionList();
        ratingSectionVOS.forEach(ratingSectionVO -> {
            if (null != ratingSectionVO.getRatingQuestionList() && !ratingSectionVO.getRatingQuestionList().isEmpty()) {
                createRatingQuestions(ratingSectionVO.getRatingQuestionList(), ratingSectionVO.getRatingSectionId(), questionnaireId);
            }
        });

        //update active preferences
        if (null != ratingQuestionnaireVO.getIsActive() && ratingQuestionnaireVO.getIsActive()) {
            setQuestionnaireActive(workgroupId, userId, questionnaireId);
        }
        return questionnaireId;
    }

    private RatingQuestionnaire createRatingQuestionnaire(RatingQuestionnaireVO ratingQuestionnaireVO, long workgroupId) {
        RatingQuestionnaire ratingQuestionnaire = new RatingQuestionnaire();
        ratingQuestionnaire.setTitle(ratingQuestionnaireVO.getTitle());
        ratingQuestionnaire.setIsCurrent(true);
        ratingQuestionnaire.setWorkGroupId(workgroupId);
        ratingQuestionnaireRepository.save(ratingQuestionnaire);
        return ratingQuestionnaire;
    }

    private void setQuestionnaireActive(long workgroupId, long userId, Long questionnaireId) {
        Map<String, String> preferences = new HashMap<>();
        preferenceService.setNumber("SR_ACTIVE_QUESTIONNAIRE", questionnaireId, preferences);
        preferenceService.updatePreferencesForWorkgroup(workgroupId, userId, preferences);
    }

    private void checkSectionsAndQuestions(List<RatingSectionVO> ratingSectionVOS, Set<Long> questionIdSet) {
        long totalWeight = 100L;
        for (int i = 0; i < ratingSectionVOS.size(); i++) {
            RatingSectionVO ratingSectionVO = ratingSectionVOS.get(i);
            List<RatingQuestionVO> ratingQuestionVOS = ratingSectionVO.getRatingQuestionList();
            if (null != ratingQuestionVOS && !ratingQuestionVOS.isEmpty()) {
                checkQuestionOrdinal(ratingQuestionVOS);
                if (ratingSectionVO.getRatingSectionId() == null) {
                    throw new IllegalArgumentException("Can't find ratingSectionId");
                }
                for (int j = 0; j < ratingQuestionVOS.size(); j++) {
                    RatingQuestionVO ratingQuestionVO = ratingSectionVO.getRatingQuestionList().get(j);
                    if (ratingQuestionVO.getRatingQuestionId() != null) {
                        Optional<RatingQuestion> ratingQuestionOptional = ratingQuestionRepository.findById(ratingQuestionVO.getRatingQuestionId());
                        if (ratingQuestionOptional.isEmpty()) {throw new IllegalArgumentException("Invalid ratingQuestionId " + ratingQuestionVO.getRatingQuestionId());}
                    }
                    if (!ratingQuestionVO.getIsDeleted()) {
                        checkQuestion(ratingQuestionVO);
                        totalWeight -= ratingQuestionVO.getWeight();
                    }
                    if (ratingQuestionVO.getRatingQuestionId() != null && !questionIdSet.isEmpty()) {
                        questionIdSet.remove(ratingQuestionVO.getRatingQuestionId());
                    }
                }
            }
        }
        if (totalWeight != 0) {
            throw new IllegalArgumentException("The total of the weights should be 100%");
        }
        if (!questionIdSet.isEmpty()) {
            throw new IllegalArgumentException("You should pass all the questions");
        }
    }

    private void checkQuestionOrdinal(List<RatingQuestionVO> ratingQuestionVOS) {
        List<RatingQuestionVO> list = ratingQuestionVOS.stream().filter(ratingQuestionVO -> !ratingQuestionVO.getIsDeleted())
                .sorted(Comparator.comparing(RatingQuestionVO :: getOrdinal)).toList();
        for (int i = 1; i <= list.size(); i++) {
            RatingQuestionVO ratingQuestionVO = list.get(i - 1);
            if (ratingQuestionVO.getOrdinal() == null) {
                throw new IllegalArgumentException("Should set ordinal for every non-deleted question");
            }
            if (ratingQuestionVO.getOrdinal() != i) {
                throw new IllegalArgumentException("Incorrect Ordinal");
            }
        }
    }

    private void checkQuestion(RatingQuestionVO ratingQuestionVO) {
        if (ratingQuestionVO.getText() == null || ratingQuestionVO.getText().isEmpty()) {
            throw new IllegalArgumentException("One or more questions are not specified. Please specify all questions.");
        }
        if (ratingQuestionVO.getWeight() == null || ratingQuestionVO.getWeight() > 100 || ratingQuestionVO.getWeight() < 0) {
            throw new IllegalArgumentException("Invalid question weight.");
        }
    }

    private void createRatingQuestions(List<RatingQuestionVO> ratingQuestionList, long ratingSectionId, long questionnaireId) {
        ratingQuestionList.stream().filter(ratingQuestionVO -> !ratingQuestionVO.getIsDeleted()).forEach(ratingQuestionVO -> createRatingQuestion(ratingQuestionVO, ratingSectionId, questionnaireId));
    }

    private void createRatingQuestion(RatingQuestionVO ratingQuestionVO, long ratingSectionId, long questionnaireId) {
            RatingQuestion ratingQuestion = new RatingQuestion();
            ratingQuestion.setRatingQuestionnaireId(questionnaireId);
            ratingQuestion.setRatingSectionId(ratingSectionId);
            ratingQuestion.setText(ratingQuestionVO.getText());
            ratingQuestion.setWeight(ratingQuestionVO.getWeight());
            ratingQuestion.setOrdinal(ratingQuestionVO.getOrdinal());
            ratingQuestionRepository.save(ratingQuestion);
    }

}
