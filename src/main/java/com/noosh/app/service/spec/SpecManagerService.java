package com.noosh.app.service.spec;

import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.account.WorkgroupAttributeDTO;
import com.noosh.app.commons.dto.job.JobDTO;
import com.noosh.app.commons.dto.project.ProjectStatusValueDTO;
import com.noosh.app.commons.dto.property.PropertyParamDTO;
import com.noosh.app.commons.dto.spec.SpecQueryParam;
import com.noosh.app.commons.dto.spec.SpecQueryResultDTO;
import com.noosh.app.commons.dto.spec.SpecTypeDTO;
import com.noosh.app.commons.entity.property.PropertyParam;
import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.commons.entity.spec.*;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.commons.vo.job.JobStatusVO;
import com.noosh.app.commons.vo.job.JobVO;
import com.noosh.app.commons.vo.spec.*;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.mapper.account.WorkgroupAttributeMapper;
import com.noosh.app.mapper.job.JobMapper;
import com.noosh.app.mapper.job.JobStatusMapper;
import com.noosh.app.mapper.project.ProjectStatusMapper;
import com.noosh.app.mapper.spec.SpecQueryMapper;
import com.noosh.app.mapper.spec.SpecTypeMapper;
import com.noosh.app.repository.jpa.property.PropertyParamRepository;
import com.noosh.app.repository.jpa.security.ObjectStateRepository;
import com.noosh.app.repository.jpa.spec.SpecQueryRepository;
import com.noosh.app.repository.jpa.spec.SpecReferenceRepository;
import com.noosh.app.repository.jpa.spec.SpecRepository;
import com.noosh.app.repository.jpa.spec.SpecTypeRepository;
import com.noosh.app.repository.mybatis.account.WorkgroupAttributeMyBatisMapper;
import com.noosh.app.repository.mybatis.property.PropertyMyBatisMapper;
import com.noosh.app.repository.mybatis.spec.SpecMyBatisMapper;
import com.noosh.app.repository.mybatis.spec.SpecTypeMyBatisMapper;
import com.noosh.app.service.job.JobService;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.project.ProjectStatusService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.util.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Transactional(readOnly = false)
public class SpecManagerService {

    private static final Long[] jobStatusIds = {ObjectStateID.JOB_NEW, ObjectStateID.JOB_IN_ESTIMATING, ObjectStateID.JOB_IN_QUOTING, ObjectStateID.JOB_ORDER_PENDING, ObjectStateID.JOB_ORDER_ACCEPTED, ObjectStateID.JOB_COMPLETED};
    private static final Long[] userObjectStateIds = {ObjectStateID.JOB_SPEC_USER_STATE_ACTIVE, ObjectStateID.JOB_SPEC_USER_STATE_ARCHIVED};
    private static final Long[] projectObjectStateIds = {ObjectStateID.PROJECT_ACTIVATED, ObjectStateID.PROJECT_INACTIVATED};

    @Autowired
    private SpecQueryMapper specQueryMapper;
    @Autowired
    private ProjectStatusMapper projectStatusMapper;
    @Autowired
    private WorkgroupAttributeMapper workgroupAttributeMapper;
    @Autowired
    private SpecTypeMapper specTypeMapper;
    @Autowired
    private JobMapper jobMapper;
    @Autowired
    private JobStatusMapper jobStatusMapper;
    @Autowired
    private SpecTypeMyBatisMapper specTypeMyBatisMapper;
    @Autowired
    private WorkgroupAttributeMyBatisMapper workgroupAttributeMyBatisMapper;
    @Autowired
    private SpecMyBatisMapper specMyBatisMapper;
    @Autowired
    private PropertyMyBatisMapper propertyMyBatisMapper;
    @Autowired
    private SpecQueryRepository specQueryRepository;
    @Autowired
    private ObjectStateRepository objectStateRepository;
    @Autowired
    private SpecRepository specRepository;
    @Autowired
    private SpecTypeRepository specTypeRepository;
    @Autowired
    private SpecReferenceRepository specReferenceRepository;
    @Autowired
    private PropertyParamRepository propertyParamRepository;
    @Autowired
    private ProjectStatusService projectStatusService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private JobService jobService;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private I18NUtils i18NUtils;

    private final Map<String, String> SORT_FIELD_MAP = new HashMap<>();

    @PostConstruct
    public void init() {
        SORT_FIELD_MAP.put("projectname", "UPPER(projectName)");
        SORT_FIELD_MAP.put("nooshid", "nooshId");
        SORT_FIELD_MAP.put("projectnumber", "UPPER(projectNumber)");
        SORT_FIELD_MAP.put("clientaccount", "UPPER(clientAccount)");
        SORT_FIELD_MAP.put("projectstatus", "projectStatus");
        SORT_FIELD_MAP.put("isactive", "isActive");
        SORT_FIELD_MAP.put("completiondate", "completionDate");
        SORT_FIELD_MAP.put("specname", "UPPER(specName)");
        SORT_FIELD_MAP.put("specreference", "UPPER(specReference)");
        SORT_FIELD_MAP.put("qty1", "qty1");
        SORT_FIELD_MAP.put("producttype", "productType");
        SORT_FIELD_MAP.put("spectype", "specType");
    }

    public boolean isSortFieldExist(String sort) {
        return SORT_FIELD_MAP.containsKey(sort.toLowerCase());
    }

    public SpecFilterVO updateFilterSettings(SpecFilter specFilter, long workgroupId, long userId) throws Exception {
        // save or update spec filter params
        SpecQuery specQuery;
        if(specFilter.getFilterSettingsId() != null) {
            Optional<SpecQuery> specQueryOptional = specQueryRepository.findById(specFilter.getFilterSettingsId());
            if (specQueryOptional.isPresent()) {
                specQuery = specQueryOptional.get();
                specQuery.setParams(SpecFilterUtil.mapToQueryString(specFilter));
            } else {
                throw new NotFoundException("filterSettingsId not found");
            }
        } else {
            SpecQuery specQueryByName = specQueryRepository.findAllByNameAndOwnerWorkgroupId(specFilter.getFilterSettingsName(), workgroupId);
            if(specQueryByName != null) {
                specFilter.setFilterSettingsId(specQueryByName.getId());
            }
            specQuery = specQueryMapper.toEntity(specFilter, workgroupId);
            if (specQuery.getCreateUserId() == null) {
                specQuery.setCreateUserId(userId);
            }
        }

        SpecQuery query = specQueryRepository.save(specQuery);

        SpecFilterVO specFilterVO = new SpecFilterVO();
        specFilterVO.setFilterSettingsId(query.getId());
        specFilterVO.setFilterSettingsName(query.getName());
        return specFilterVO;
    }

    public SpecFilter getFilterSettingsData(Long filterSettingsId) throws Exception {
        SpecFilter specFilter;

        Optional<SpecQuery> specQueryOptional = specQueryRepository.findById(filterSettingsId);
        if(specQueryOptional.isPresent()) {
            SpecQuery specQuery = specQueryOptional.get();
            specFilter = specQueryMapper.toVO(specQuery);
        } else {
            throw new NotFoundException("filterSettingsId not found");
        }

        return specFilter;
    }

    public void deleteFilterSettings(Long filterSettingsId) {
        Optional<SpecQuery> specQueryOptional = specQueryRepository.findById(filterSettingsId);
        if(specQueryOptional.isPresent()) {
            specQueryRepository.deleteById(filterSettingsId);
        } else {
            throw new NotFoundException("filterSettingsId not found");
        }
    }

    public List<DropdownVO<Long>> getFilterSettingsList(long workgroupId) {
        List<SpecQuery> savedSpecQueries = specQueryRepository.findByOwnerWorkgroupIdOrderByName(workgroupId);
        List<DropdownVO<Long>> savedSpecQueryDTOs = savedSpecQueries.stream().map(specQueryMapper::toDropdownVO).collect(Collectors.toList());
        return savedSpecQueryDTOs;
    }

    public List<DropdownVO<Long>> getFieldOptionsList(String keyName, long workgroupId, Long specTypeId) throws Exception {
        if(KeyNameEnum.PROJECT_STATUS.getValue().equals(keyName)) {
            List<ProjectStatusValueDTO> projectStatusDTOs = projectStatusService.findProjectStatus(workgroupId);
            List<DropdownVO<Long>> projectStatus = projectStatusDTOs.stream().map(projectStatusMapper::toDropdownVO).collect(Collectors.toList());
            return projectStatus;
        } else if(KeyNameEnum.IS_ACTIVE.getValue().equals(keyName)) {
            List<ObjectState> projectObjectStateList = objectStateRepository.findAllById(Arrays.asList(projectObjectStateIds));
            return getObjectStateDropdown(projectObjectStateList);
        }else if(KeyNameEnum.SPEC_STATUS.getValue().equals(keyName)) {
            List<ObjectState> specStatus = objectStateRepository.findAllById(Arrays.asList(jobStatusIds));
            return getObjectStateDropdown(specStatus);
        } else if(KeyNameEnum.USER_STATE.getValue().equals(keyName)) {
            List<ObjectState> userStates = objectStateRepository.findAllById(Arrays.asList(userObjectStateIds));
            return getObjectStateDropdown(userStates);
        } else if(KeyNameEnum.PRODUCT_TYPE.getValue().equals(keyName)) {
            List<WorkgroupAttributeDTO> productTypeDTOs = workgroupAttributeMyBatisMapper.findWorkgroupAttributes(WgAttributeTypeID.SPEC_PRODUCT_TYPE, null, true, "LABEL");
            List<DropdownVO<Long>> productTypes = productTypeDTOs.stream().map(workgroupAttributeMapper::toDropdownVO).collect(Collectors.toList());
            // Alphabetize the "Product Type"
            List<DropdownVO<Long>> productTypeList = productTypes.stream().filter(productType -> productType.getLabel() != null).sorted(Comparator.comparing(DropdownVO::getLabel)).collect(Collectors.toList());
            return productTypeList;
        } else if(KeyNameEnum.SPEC_TYPE.getValue().equals(keyName)) {
            if (specTypeId == null) {
                List<SpecTypeDTO> specTypeDTOS = specTypeMyBatisMapper.findSpecTypes(workgroupId, true, false, false);
                List<DropdownVO<Long>> specTypes = specTypeDTOS.stream().map(specTypeMapper::toDropdownVO).collect(Collectors.toList());
                // Alphabetize the "Spec Type"
                List<DropdownVO<Long>> specTypeList = specTypes.stream().filter(specType -> specType.getLabel() != null).sorted(Comparator.comparing(DropdownVO::getLabel)).collect(Collectors.toList());
                return specTypeList;
            } else {
                SpecType specType = specTypeRepository.findById(specTypeId).orElse(null);
                if (specType == null) {
                    throw new NotFoundException(specTypeId + " spec type id not found");
                }
                List<PropertyParamDTO> propertyParamDTOList = propertyMyBatisMapper.findPropertyParams(specType.getPrPropertyTypeId());
                propertyParamDTOList.stream().forEach(propertyParamDTO -> {

                });
                List<DropdownVO<Long>> subSpecTypes = propertyParamDTOList.stream().map(specTypeMapper::toDropdownVO).collect(Collectors.toList());
                // Alphabetize the "Spec Type"
                List<DropdownVO<Long>> subSpecTypeList = subSpecTypes.stream().sorted(Comparator.comparing(DropdownVO::getLabel)).collect(Collectors.toList());
                return subSpecTypeList;
            }
        } else {
            throw new NotFoundException(keyName + " not found");
        }
    }

    private List<DropdownVO<Long>> getObjectStateDropdown(List<ObjectState> objectStates) {
        return objectStates.stream().map(objectState -> {
            DropdownVO<Long> vo = new DropdownVO<>();
            vo.setId(objectState.getId());
            vo.setLabel(i18NUtils.getMessage(objectState.getDescriptionStrId()));
            vo.setLabelStrId(objectState != null && objectState.getDescriptionStrId() != null ? objectState.getDescriptionStrId().toString() : null);
            return vo;
        }).collect(Collectors.toList());
    }

    public SpecPageSizeVO getPageSize(long workgroupId, long userId) {
        List<String> preferenceIds = new ArrayList<>(3);
        preferenceIds.add(SpecManagerFilterPreferenceID.SPEC_MANAGER_PAGESIZE);
        preferenceIds.add(SpecManagerFilterPreferenceID.REACT_SPEC_MANAGER_SORT);
        preferenceIds.add(SpecManagerFilterPreferenceID.REACT_SPEC_MANAGER_SORT_ORDER);
        Map<String, String> prefs = preferenceService.findUserPrefs(workgroupId, userId, preferenceIds);
        SpecPageSizeVO pageSizeVO = new SpecPageSizeVO();
        String pageSizeStr = preferenceService.getString(
                SpecManagerFilterPreferenceID.SPEC_MANAGER_PAGESIZE, prefs,
                String.valueOf(10));
        pageSizeVO.setPageSize(Integer.valueOf(pageSizeStr));
        String sort = preferenceService.getString(
                SpecManagerFilterPreferenceID.REACT_SPEC_MANAGER_SORT, prefs,
                "projectName");
        pageSizeVO.setSort(sort);
        String order = preferenceService.getString(
                SpecManagerFilterPreferenceID.REACT_SPEC_MANAGER_SORT_ORDER, prefs,
                "desc");
        pageSizeVO.setOrder(order);
        return pageSizeVO;
    }

    public void updateSpecInfo(SpecInfoVO specInfoVO, Long userId) throws Exception {
        Optional<Spec> specOptional = specRepository.findById(specInfoVO.getSpecId());
        if(specOptional.isPresent()) {
            // spec
            Spec spec = specOptional.get();
            spec.setId(specInfoVO.getSpecId());
            spec.setSpecName(specInfoVO.getSpecName());
            spec.setModUserId(userId);
            specRepository.save(spec);
            // spec reference
            SpecReference specReference = spec.getSpecReference();
            specReference.setSpecName(specInfoVO.getSpecName());
            specReference.setModUserId(userId);
            specReferenceRepository.save(specReference);
        } else {
            throw new NotFoundException("spec id not found");
        }
    }

    public List<SpecQueryResultDTO> findSpecs(PageVO page, SpecFilter specFilter, long userId, long workgroupId) throws Exception {

        // 1. get query params
        boolean noSort = false;
        List<SpecQueryParam> generalQueryParams = getGeneralQueryParams(specFilter);
        // if projectName Column Index is null, removed default sorting
        if ("projectName".equals(generalQueryParams.get(0).getFieldName())
                && !(generalQueryParams.get(0).getColumnIndex() != null && generalQueryParams.get(0).getColumnIndex() > 0)) {
            noSort = true;
        }

        List<SpecQueryParam> propertyQueryParams = getPropertyQueryParams(specFilter.getSpecTypeDetails());
        List<SpecQueryParam> outputParams = getOutputParams(generalQueryParams, propertyQueryParams);

        // 2. sql
        String outputParamsSql = getOutputParamsSql(outputParams);
        String propertyQueryParamsSql = getPropertyQueryParamsSql(propertyQueryParams);
        String generalQueryParamsSql = getGeneralQueryParamsSql(propertyQueryParams, generalQueryParams);
        boolean isPropertyQueryParams = propertyQueryParams.size() > 0 ? true : false;

        // 3. find specs
        String index = "/*+ index (spec sp_spec_pr_property_id_x) */";
        boolean isViewProject = permissionService.checkAll(PermissionID.VIEW_WORKGROUP_PROJECTS, workgroupId, userId, Long.valueOf(-1));
        List<SpecQueryResultDTO> results = specMyBatisMapper.findSpecQueryResult(
                index, userId, workgroupId,
                isViewProject,
                outputParamsSql,
                propertyQueryParamsSql,
                generalQueryParamsSql,
                isPropertyQueryParams,
                PageUtil.toPageDTO(page, SORT_FIELD_MAP, noSort));

        List<WorkgroupAttributeDTO> productTypeDTOs = workgroupAttributeMyBatisMapper.findWorkgroupAttributes(WgAttributeTypeID.SPEC_PRODUCT_TYPE, null, true, "LABEL");
        List<DropdownVO<Long>> productTypes = productTypeDTOs.stream().map(workgroupAttributeMapper::toDropdownVO).collect(Collectors.toList());
        Map<Long, DropdownVO> productTypeMap = productTypes.stream().filter(productType -> productType.getLabel() != null).collect(Collectors.toMap(DropdownVO::getId, Function.identity()));

        List<SpecTypeDTO> specTypeDTOS = specTypeMyBatisMapper.findSpecTypes(workgroupId, true, false, false);
        List<DropdownVO<Long>> specTypes = specTypeDTOS.stream().map(specTypeMapper::toDropdownVO).collect(Collectors.toList());
        Map<Long, DropdownVO> specTypeMap = specTypes.stream().filter(specType -> specType.getLabel() != null).collect(Collectors.toMap(DropdownVO::getId, Function.identity()));

        // get jobStatus
        if (!results.isEmpty()) {
            page.setTotal(results.get(0).getMaxRownum());

            List<JobDTO> jobs = specMyBatisMapper.findJobsForSepcQueryResult(results);
            jobService.setJobStatuses(workgroupId, jobs);
            results.forEach(result -> {
                getQueryParams(result, productTypeMap, specTypeMap);
                result.setSpecExternalLink(NooshOneUrlUtil.composeGotoViewSpecLinkToEnterprise(Long.valueOf(result.getNodeId()), result.getProjectId()));
                result.setProjectExternalLink(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(result.getProjectId()));
                result.setIsHotProject(projectService.getIsHotForUser(result.getProjectId(), userId));
                result.setIsActive(result.getIsActive()!=null?i18NUtils.getObjectStateMessage(Long.valueOf(result.getIsActive())):null);
                for(JobDTO job: jobs){
                    if(job.getSpecReferenceId().longValue() == result.getSpecRefId().longValue()
                            && job.getProjectId().longValue() == result.getProjectId().longValue()
                            && job.getJobStatusDTOList() != null) {
                        List<JobStatusVO> jobStatusVOs = job.getJobStatusDTOList().stream().map(jobStatusMapper::toVO).collect(Collectors.toList());
                        if(result.getSpecStatus() != null) {
                            result.setSpecStatuses(jobStatusVOs);
                            result.setSpecStatus(null);
                        }

                    }
                }
            });
        }
        return results;
    }

    public void updatePageSize(Long workgroupId, Long userId, Integer pageSize, String sort, String order) throws Exception {
        Map<String, String> prefs = new HashMap<>();
        prefs.put( SpecManagerFilterPreferenceID.SPEC_MANAGER_PAGESIZE,
                Integer.toString(pageSize));
        prefs.put( SpecManagerFilterPreferenceID.REACT_SPEC_MANAGER_SORT,
                sort);
        prefs.put( SpecManagerFilterPreferenceID.REACT_SPEC_MANAGER_SORT_ORDER,
                order);
        preferenceService.saveUserPreference(workgroupId, userId, prefs);
    }

    private void getQueryParams(SpecQueryResultDTO result, Map<Long, DropdownVO> productTypeMap, Map<Long, DropdownVO> specTypeMap) {
        if(result.getUserState() != null) {
            result.setUserState(i18NUtils.getObjectStateMessage(result.getSpecUserStateId()));
            ObjectState objectState = objectStateRepository.findById(result.getSpecUserStateId()).orElse(null);
            result.setUserStateStrId(objectState != null && objectState.getDescriptionStrId() != null ? objectState.getDescriptionStrId().toString() : null);
        }
        if(result.getProductType() != null) {
            DropdownVO dropdownVO = productTypeMap.get(Long.valueOf(result.getProductType()));
            result.setProductType(dropdownVO.getLabel());
            result.setProductTypeStr(dropdownVO.getLabelStr());
            result.setProductTypeStrId(dropdownVO.getLabelStrId());
        }
        if(result.getSpecType() != null) {
            DropdownVO dropdownVO = specTypeMap.get(Long.valueOf(result.getSpecType()));
            result.setSpecType(dropdownVO.getLabel());
            result.setSpecTypeStr(dropdownVO.getLabelStr());
            result.setSpecTypeStrId(dropdownVO.getLabelStrId());
        }
    }

    private List<SpecQueryParam> getGeneralQueryParams(SpecFilter specFilter) {
        List<SpecQueryVO> projects = specFilter.getProject();
        List<SpecQueryVO> specs = specFilter.getSpec();

        List<SpecQueryVO> specFilterParams = new ArrayList<>();
        specFilterParams.addAll(projects);
        specFilterParams.addAll(specs);

        List<SpecQueryParam> specQueryParams = new ArrayList<>();
        if(!specFilterParams.isEmpty()) {
            specFilterParams.stream().forEach(param -> {
                specQueryParams.add(this.getSpecQueryParam(param));
            });
        }
        return specQueryParams;
    }

    private List<SpecQueryParam> getPropertyQueryParams(List<SpecQueryVO> specFilterParams) {
        List<SpecQueryParam> propertyQueryParams = new ArrayList<>();
        if (specFilterParams == null) {
            return propertyQueryParams;
        }
        // add all spec (property) parameters
        for (SpecQueryVO specQueryVO : specFilterParams) {
            if (specQueryVO.getFilterName().startsWith(SpecQueryEnum.PARAM_PREFIX.getValue())) {
                Long propertyParamId = specQueryVO.getFilterNameId();

                if (propertyParamId == null) {
                    continue;
                }
                String index = specQueryVO.getFilterName().substring(SpecQueryEnum.PARAM_PREFIX.getValue().length(), specQueryVO.getFilterName().length());

                Optional<PropertyParam> propertyParamOptional = propertyParamRepository.findById(Long.valueOf(propertyParamId));
                PropertyParam propertyParam = propertyParamOptional.get();

                // determine column name (depends on datatype)
                long dataTypeId = propertyParam.getPrDataTypeId();
                String dataType;
                String columnName;
                if (dataTypeId == DataTypeID.STRING) {
                    columnName = "STRING_VALUE";
                    dataType = SpecQueryEnum.DATATYPE_STRING.getValue();
                } else if (dataTypeId == DataTypeID.DATE) {
                    columnName = "DATE_VALUE";
                    dataType = SpecQueryEnum.DATATYPE_DATE.getValue();
                } else {
                    columnName = "NUMBER_VALUE";
                    dataType = SpecQueryEnum.DATATYPE_NUMBER.getValue();
                }

                SpecQueryParam param = new SpecQueryParam(SpecQueryEnum.PARAM_PREFIX.getValue() + index, columnName, propertyParam.getDescription(), dataType,
                        specQueryVO.getFilterIndex(), specQueryVO.getStringValue1(), null, SpecFilterUtil.getFilterType(specQueryVO.getFilterType()));

                param.setPropertyParam(propertyParam);

                if (param.getComparator() != null || param.getColumnIndex() > 0) {
                    propertyQueryParams.add(param);
                }
            }
        }

        return propertyQueryParams;
    }

    /**
     * @return a sorted array of all parameters that should be part of the output
     * of the query
     */
    private List<SpecQueryParam> getOutputParams(List<SpecQueryParam> generalQueryParams, List<SpecQueryParam> propertyQueryParams) {
        List<SpecQueryParam> params = new ArrayList<>();
        params.addAll(propertyQueryParams);
        params.addAll(generalQueryParams);
        return params.stream()
                .filter(param -> param.getColumnIndex() !=null && param.getColumnIndex() > 0)
                .sorted((o1, o2) -> (o1.getColumnIndex() > o2.getColumnIndex()) ? 1 : -1)
                .collect(Collectors.toList());
    }

    private String getOutputParamsSql(List<SpecQueryParam> outputParams) {
        StringBuilder sql = new StringBuilder(1024);
        for (SpecQueryParam param : outputParams) {
            if (param.getColumnIndex() <= 0) {
                continue;
            }
            sql.append(',');
            sql.append((param.getPropertyParam() == null) ? param.getColumnName() : param.getFieldName());
            sql.append(" as ").append(param.getFieldName());
        }
        return sql.toString();
    }

    private String getPropertyQueryParamsSql(List<SpecQueryParam> propertyQueryParams) {
        StringBuilder sql = new StringBuilder(2048);
        // FROM (inline view for properties) ----------------------------------
        if (propertyQueryParams.size() > 0) {
            sql.append(", (SELECT ");
            for (SpecQueryParam param : propertyQueryParams) {
                sql.append(" MAX(").append(param.getFieldName()).append(") AS ");
                sql.append(param.getFieldName());
                sql.append(',');
            }
            sql.append(" PR_PROPERTY_ID FROM (");

            // sql for each parameter
            for (int i = 0; i < propertyQueryParams.size(); i++) {
                SpecQueryParam param = propertyQueryParams.get(i);
                if (i > 0) {
                    sql.append(" UNION ALL ");
                }
                sql.append(" SELECT ");
                for (int j = 0; j < propertyQueryParams.size(); j++) {
                    SpecQueryParam thisParam = propertyQueryParams.get(j);
                    if (j == i) {
                        sql.append(thisParam.getColumnName()).append(" AS ");
                        sql.append(thisParam.getFieldName());
                    } else {
                        sql.append(" NULL AS ").append(thisParam.getFieldName());
                    }
                    sql.append(',');
                }
                sql.append(" nvl(pr_property.parent_property_id, pr_property.pr_property_id) pr_property_id "
                        + " from pr_property_attribute, pr_property_param, pr_property "
                        + " where pr_property_param.pr_property_param_id = " + param.getPropertyParam().getPrPropertyParamId()
                        + " and pr_property_param.pr_property_param_id = pr_property_attribute.pr_property_param_id"
                        + " and pr_property.pr_property_id = pr_property_attribute.pr_property_id");
                if (!param.getComparator().equals(SpecQueryEnum.COMPARATOR_NONE.getValue()) && StringUtils.isNotEmpty(param.getFilterValue())) {
                    sql.append(" AND ").append(param.getFilter());
                }
            }

            // end of wrapped sql
            sql.append(" ) GROUP BY PR_PROPERTY_ID) x ");
        }
        return sql.toString();
    }

    private String getGeneralQueryParamsSql(List<SpecQueryParam> propertyQueryParams,
                                            List<SpecQueryParam> generalQueryParams) {
        StringBuilder sql = new StringBuilder(2048);

        // WHERE (filters for property fields) ----------------------------
        for (SpecQueryParam param : propertyQueryParams) {
            if (StringUtils.isNotEmpty(param.getFilterValue()) && !param.getComparator().equals(SpecQueryEnum.COMPARATOR_NONE.getValue())) {
                sql.append(" and " + param.getFilter("x." + param.getFieldName(), param.getComparator()));
            }
        }

        // WHERE (filters for property fields to correct some issues related to NOT_EQUALS and NOT_CONTAINS)
        for (SpecQueryParam param : propertyQueryParams) {
            if (param.getComparator().equals(SpecQueryEnum.COMPARATOR_NOT_CONTAINS.getValue())) {
                sql.append("  and not exists (select 'x' from pr_property_attribute, pr_property_param, pr_property ");
                sql.append(" where pr_property_param.pr_property_param_id = " + param.getPropertyParam().getPrPropertyParamId());
                sql.append(" and pr_property_param.pr_property_param_id = pr_property_attribute.pr_property_param_id");
                sql.append(" and pr_property.pr_property_id = pr_property_attribute.pr_property_id");
                sql.append(" and nvl(pr_property.parent_property_id, pr_property.pr_property_id) = spec.pr_property_id ");
                sql.append(" and " + param.getFilter(param.getColumnName(), SpecQueryEnum.COMPARATOR_CONTAINS.getValue()));
                sql.append(" )");

            } else if (param.getComparator().equals(SpecQueryEnum.COMPARATOR_NOT_EQUALS.getValue())) {
                sql.append("  and not exists (select 'x' from pr_property_attribute, pr_property_param, pr_property ");
                sql.append(" where pr_property_param.pr_property_param_id = " + param.getPropertyParam().getPrPropertyParamId());
                sql.append(" and pr_property_param.pr_property_param_id = pr_property_attribute.pr_property_param_id");
                sql.append(" and pr_property.pr_property_id = pr_property_attribute.pr_property_id");
                sql.append(" and nvl(pr_property.parent_property_id, pr_property.pr_property_id) = spec.pr_property_id ");
                sql.append(" and " + param.getFilter(param.getColumnName(), SpecQueryEnum.COMPARATOR_EQUALS.getValue()));
                sql.append(" )");
            }
        }

        // WHERE (filters for non-property fields) ----------------------------
        for (SpecQueryParam param : generalQueryParams) {
            if (param.getFieldName().equals("specStatus")) {
                long jobStatusId = (param.getFilterValue() == null) ? -1 : Long.parseLong(param.getFilterValue());
                if (jobStatusId == 2000020) {
                    sql.append(" and not exists (select 'x' from pc_job_status, pc_job where owner_ac_workgroup_id = PROJECT.owner_ac_workgroup_id");
                    sql.append("     and pc_job_status.pc_job_id = pc_job.pc_job_id");
                    sql.append("     and pc_job.sp_spec_reference_id = sref.sp_spec_reference_id");
                    sql.append("     and pc_job_status.oc_object_state_id != 2000020)");
                } else if (jobStatusId > 0) {
                    sql.append(" and exists (select 'x' from pc_job_status, pc_job where owner_ac_workgroup_id = PROJECT.owner_ac_workgroup_id");
                    sql.append("     and pc_job_status.pc_job_id = pc_job.pc_job_id");
                    sql.append("     and pc_job.sp_spec_reference_id = sref.sp_spec_reference_id");
                    sql.append("     and pc_job_status.oc_object_state_id = " + jobStatusId + ")");
                }

            } else if (param.getFieldName().equals("userState")) {
                long jobStateId = (param.getFilterValue() == null) ? -1 : Long.parseLong(param.getFilterValue());
                if (jobStateId == 2000130) {
                    sql.append(" AND exists( ");
                    sql.append("(SELECT 'x' FROM pc_job, sy_containable j_cont");
                    sql.append(" WHERE pc_job.SP_SPEC_REFERENCE_ID = SREF.SP_SPEC_REFERENCE_ID");
                    sql.append(" AND j_cont.OBJECT_ID= pc_job.PC_JOB_ID");
                    sql.append(" AND j_cont.OBJECT_CLASS_ID=1000020");
                    sql.append(" AND j_cont.PARENT_OBJECT_CLASS_ID=1000000");
                    sql.append(" AND j_cont.PARENT_OBJECT_ID=PROJECT.PM_PROJECT_ID");
                    sql.append(" AND SREF.IS_MASTER=1 ");
                    sql.append(" AND (pc_job.OC_OBJECT_STATE_ID is null OR pc_job.OC_OBJECT_STATE_ID=2000130))");
                    sql.append("UNION ");
                    sql.append("(SELECT 'x' FROM sy_containable s_cont");
                    sql.append(" WHERE s_cont.OBJECT_ID= SPEC.SP_SPEC_ID ");
                    sql.append(" AND s_cont.OBJECT_CLASS_ID=1000114");
                    sql.append(" AND s_cont.PARENT_OBJECT_CLASS_ID=1000000");
                    sql.append(" AND s_cont.PARENT_OBJECT_ID=" + "PROJECT.PM_PROJECT_ID");
                    sql.append(" AND (SPEC.IS_ITEM_VERSION=0 OR SPEC.IS_ITEM_VERSION is null) ");
                    sql.append(" AND (SPEC.SPEC_USER_STATE_ID is null OR SPEC.SPEC_USER_STATE_ID=2000130))");
                    sql.append(") ");
                } else if (jobStateId > 0) {
                    sql.append(" AND exists( ");
                    sql.append("(SELECT 'x' FROM pc_job, sy_containable j_cont ");
                    sql.append(" WHERE pc_job.SP_SPEC_REFERENCE_ID = SREF.SP_SPEC_REFERENCE_ID");
                    sql.append(" AND j_cont.OBJECT_ID= pc_job.PC_JOB_ID");
                    sql.append(" AND j_cont.OBJECT_CLASS_ID=1000020");
                    sql.append(" AND j_cont.PARENT_OBJECT_CLASS_ID=1000000");
                    sql.append(" AND j_cont.PARENT_OBJECT_ID=PROJECT.PM_PROJECT_ID");
                    sql.append(" AND SREF.IS_MASTER=1 ");
                    sql.append(" AND pc_job.OC_OBJECT_STATE_ID=" + jobStateId + ") ");
                    sql.append("UNION ");
                    sql.append("(SELECT 'x' FROM sy_containable s_cont ");
                    sql.append(" WHERE s_cont.OBJECT_ID= SPEC.SP_SPEC_ID");
                    sql.append(" AND s_cont.OBJECT_CLASS_ID=1000114");
                    sql.append(" AND s_cont.PARENT_OBJECT_CLASS_ID=1000000");
                    sql.append(" AND s_cont.PARENT_OBJECT_ID=PROJECT.PM_PROJECT_ID");
                    sql.append(" AND (SPEC.IS_ITEM_VERSION=0 OR SPEC.IS_ITEM_VERSION is null) ");
                    sql.append(" AND (SPEC.SPEC_USER_STATE_ID=" + jobStateId + ")) ");
                    sql.append(") ");
                }
            } else if (!(Util.isNullOrEmpty(param.getFilterValue()) && Util.isNullOrEmpty(param.getFilterValue2())) && !param.getComparator().equals(SpecQueryEnum.COMPARATOR_NONE.getValue())) {
                sql.append(" AND ").append(param.getFilter());
            }
        }
        return sql.toString();
    }

    private SpecQueryParam getSpecQueryParam(SpecQueryVO param) {
        if("projectName".equals(param.getFilterName())) {
            return new SpecQueryParam(param.getFilterName(), "PROJECT.NAME", "Project Name", "STRING",
                    param.getFilterIndex(), param.getStringValue1(), null, SpecFilterUtil.getFilterType(param.getFilterType()));
        } else if("nooshId".equals(param.getFilterName())) {
            return new SpecQueryParam(param.getFilterName(), "PROJECT.REFERENCE", "Noosh ID", "STRING",
                    param.getFilterIndex(), param.getStringValue1(), null, SpecFilterUtil.getFilterType(param.getFilterType()));
        } else if("projectNumber".equals(param.getFilterName())) {
            return new SpecQueryParam(param.getFilterName(), "PROJECT.PROJECT_NUMBER", "Project Number", "STRING",
                    param.getFilterIndex(), param.getStringValue1(), null, SpecFilterUtil.getFilterType(param.getFilterType()));
        } else if("clientAccount".equals(param.getFilterName())) {
            return new SpecQueryParam(param.getFilterName(), "PROJECT.CLIENT_ACCOUNT", "Client Account", "STRING",
                    param.getFilterIndex(), param.getStringValue1(), null, SpecFilterUtil.getFilterType(param.getFilterType()));
        } else if("projectStatus".equals(param.getFilterName())) {
            return new SpecQueryParam(param.getFilterName(), "PRJS.NAME_STR", "Project Status", "NUMBER",
                    param.getFilterIndex(), param.getStringValue1(), null, SpecFilterUtil.getFilterType(param.getFilterType()));
        } else if("isActive".equals(param.getFilterName())) {
            return new SpecQueryParam(param.getFilterName(), "PROJECT.OC_OBJECT_STATE_ID", "Active/Inactive", "NUMBER",
                    param.getFilterIndex(), param.getStringValue1(), null, SpecFilterUtil.getFilterType(param.getFilterType()));
        } else if("completionDate".equals(param.getFilterName())) {
            return new SpecQueryParam(param.getFilterName(), "PROJECT.COMPLETION_DATE", "Completion Date", "DATE",
                    param.getFilterIndex(), param.getStringValue1(), param.getStringValue2(), SpecFilterUtil.getFilterType(param.getFilterType()));
        } else if("specName".equals(param.getFilterName())) {
            return new SpecQueryParam(param.getFilterName(), "SPEC.SPEC_NAME", "Spec Name", "STRING",
                    param.getFilterIndex(), param.getStringValue1(), null, SpecFilterUtil.getFilterType(param.getFilterType()));
        } else if("specReference".equals(param.getFilterName())) {
            return new SpecQueryParam(param.getFilterName(), "SREF.REF_NUMBER", "Reference", "STRING",
                    param.getFilterIndex(), param.getStringValue1(), null, SpecFilterUtil.getFilterType(param.getFilterType()));
        } else if("specStatus".equals(param.getFilterName())) {
            return new SpecQueryParam(param.getFilterName(), "'x'", "Spec Status", "NUMBER",
                    param.getFilterIndex(), param.getStringValue1(), null, SpecFilterUtil.getFilterType(param.getFilterType()));
        } else if("userState".equals(param.getFilterName())) {
            return new SpecQueryParam(param.getFilterName(), "'x'", "User State", "NUMBER",
                     param.getFilterIndex(), param.getStringValue1(), null, SpecFilterUtil.getFilterType(param.getFilterType()));
        } else if("sku".equals(param.getFilterName())) {
            return new SpecQueryParam(param.getFilterName(), "SREF.SKU", "SKU", "STRING",
                    param.getFilterIndex(), param.getStringValue1(), null, SpecFilterUtil.getFilterType(param.getFilterType()));
        } else if("qty1".equals(param.getFilterName())) {
            return new SpecQueryParam(param.getFilterName(), "SPEC.QUANTITY1", "Quantity 1", "NUMBER",
                    param.getFilterIndex(), param.getStringValue1(), param.getStringValue2(), SpecFilterUtil.getFilterType(param.getFilterType()));
        } else if("productType".equals(param.getFilterName())) {
            return new SpecQueryParam(param.getFilterName(), "SPEC.AC_ATTR_PRODUCT_TYPE_ID", "Product Type", "NUMBER",
                    param.getFilterIndex(), param.getStringValue1(), null, SpecFilterUtil.getFilterType(param.getFilterType()));
        } else if("specType".equals(param.getFilterName())) {
            return new SpecQueryParam(param.getFilterName(), "SPEC.sp_spec_type_id", "Spec Type", "NUMBER",
                    param.getFilterIndex(), param.getStringValue1(), null, SpecFilterUtil.getFilterType(param.getFilterType()));
        }
        return null;
    }
}
