package com.noosh.app.service.workgroup;

import com.noosh.app.commons.dto.security.*;
import com.noosh.app.commons.entity.address.Country;
import com.noosh.app.commons.entity.security.AccountUser;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.commons.vo.fedex.FedexInitVO;
import com.noosh.app.repository.jpa.address.CountryRepository;
import com.noosh.app.repository.jpa.security.AccountUserRepository;
import com.noosh.app.repository.mybatis.CurrencyMyBatisMapper;
import com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper;
import com.noosh.app.service.custom.CustomAttributeService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.*;

/**
 * <AUTHOR>
 * @since 6/29/2022
 */
@Service
public class WorkgroupService {

    @Autowired
    private WorkgroupMyBatisMapper workgroupMyBatisMapper;
    @Autowired
    private CurrencyMyBatisMapper currencyMyBatisMapper;
    @Autowired
    private CustomAttributeService customAttributeService;
    @Autowired
    private CountryRepository countryRepository;

    public WorkgroupDTO findWorkgroup(Long workgroupId) {
        WorkgroupDTO workgroupWithAllData = workgroupMyBatisMapper.findWorkgroupWithAllData(workgroupId);
        if (workgroupWithAllData.getParentWorkgroupId() != null) {
            WorkgroupDTO parentWorkgroup = workgroupMyBatisMapper.findWorkgroup(workgroupWithAllData.getParentWorkgroupId());
            workgroupWithAllData.setParentWorkgroup(parentWorkgroup);
        }

        workgroupWithAllData.setDefaultCurrency(currencyMyBatisMapper.findCurrency(workgroupWithAllData.getDefaultCurrencyId()));
        if (workgroupWithAllData.getTransactionalCurrencyId() != null) {
            workgroupWithAllData.setTransactionalCurrency(currencyMyBatisMapper.findCurrency(workgroupWithAllData.getTransactionalCurrencyId()));
        }

        workgroupWithAllData.setCustomAttributes(customAttributeService.findCustomAttributes(workgroupWithAllData.getCustomPropertyId(), null));

        return workgroupWithAllData;
    }

    public WorkgroupDTO findWorkgroupByGuid(String guid) {
        return workgroupMyBatisMapper.findWorkgroupByGuid(guid);
    }

    public List<DropdownVO<Long>> getCountryListDropdown() {
        List<DropdownVO<Long>> countryListDropdown = new ArrayList<DropdownVO<Long>>();
        List<Country> countryList = countryRepository.findAll();
        countryList.forEach(country -> countryListDropdown.add(
                new DropdownVO<>(country.getId(), null, country.getNameStrId())));
        return countryListDropdown;
    }

    public List<DropdownVO<Long>> getCurrencyListDropdown() {
        List<DropdownVO<Long>> currencyListDropdown = new ArrayList<DropdownVO<Long>>();
        List<CurrencyDTO> currencyList = currencyMyBatisMapper.findAllCurrencies();
        currencyList.forEach(currency -> currencyListDropdown.add(
                new DropdownVO<>(currency.getId(), currency.getCurrency(), null)));
        return currencyListDropdown;
    }


}
