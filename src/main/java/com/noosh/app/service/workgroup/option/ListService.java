package com.noosh.app.service.workgroup.option;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import com.noosh.app.commons.constant.StringID;
import com.noosh.app.commons.dto.accounts.WorkgroupAttributeDTO;
import com.noosh.app.commons.dto.accounts.WorkgroupAttributeTypeDTO;
import com.noosh.app.commons.entity.accounts.WorkgroupAttribute;
import com.noosh.app.commons.entity.accounts.WorkgroupAttributeRegistration;
import com.noosh.app.commons.entity.accounts.WorkgroupAttributeType;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.commons.vo.workgroup.option.customdata.lists.AddOptionsVO;
import com.noosh.app.commons.vo.workgroup.option.customdata.lists.ListVO;
import com.noosh.app.commons.vo.workgroup.option.customdata.lists.ListsVO;
import com.noosh.app.commons.vo.workgroup.option.customdata.lists.OptionVO;
import com.noosh.app.commons.vo.workgroup.option.customdata.lists.UpdateListVO;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.repository.jpa.accounts.WorkgroupAttributeRegistrationRepository;
import com.noosh.app.repository.jpa.accounts.WorkgroupAttributeRepository;
import com.noosh.app.repository.jpa.accounts.WorkgroupAttributeTypeRepository;
import com.noosh.app.repository.mybatis.accounts.WorkgroupAttributeMyBatisMapper;
import com.noosh.app.repository.mybatis.accounts.WorkgroupAttributeRegistrationMyBatisMapper;
import com.noosh.app.repository.mybatis.accounts.WorkgroupAttributeTypeMyBatisMapper;
import com.noosh.app.service.util.URLUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Custom Data - List
 *
 * <AUTHOR> Shan
 * @since 8/10/2022
 */
@Service
public class ListService {

    @Autowired
    private WorkgroupAttributeTypeRepository workgroupAttributeTypeRepository;
    @Autowired
    private WorkgroupAttributeRepository workgroupAttributeRepository;
    @Autowired
    private WorkgroupAttributeRegistrationRepository workgroupAttributeRegistrationRepository;
    @Autowired
    private WorkgroupAttributeRegistrationMyBatisMapper workgroupAttributeRegistrationMyBatisMapper;
    @Autowired
    private WorkgroupAttributeMyBatisMapper workgroupAttributeMyBatisMapper;
    @Autowired
    private WorkgroupAttributeTypeMyBatisMapper workgroupAttributeTypeMyBatisMapper;

    /**
     * get list names and ids.
     *
     * @param workgroupId current workgroup id
     * @return a list of names and ids
     */
    public ListsVO getLists(Long workgroupId) {
        ListsVO listsVO = new ListsVO();

        List<WorkgroupAttributeTypeDTO> attributeTypeDTOS = workgroupAttributeTypeMyBatisMapper.findUnrestrictedTypesByWgId(workgroupId);
        if (attributeTypeDTOS != null && !attributeTypeDTOS.isEmpty()) {
            List<DropdownVO<Long>> lists = attributeTypeDTOS.stream()
                    .map(attributeTypeDTO -> new DropdownVO<>(attributeTypeDTO.getId(), attributeTypeDTO.getLabelStr()))
                    .collect(Collectors.toList());
            listsVO.setLists(lists);
        } else {
            listsVO.setLists(new ArrayList<>());
        }

        return listsVO;
    }

    /**
     * get a list
     *
     * @param workgroupId current workgroup id
     * @param listId      current selected list id
     * @return a list
     * @throws Exception exception
     */
    public ListVO getList(Long workgroupId, Long listId) throws Exception {
        Optional<WorkgroupAttributeType> optionalAttributeType = workgroupAttributeTypeRepository.findById(listId);
        if (optionalAttributeType.isPresent()) {
            ListVO listVO = new ListVO();
            String typeOptions = optionalAttributeType.get().getOptions();
            listVO.setDefaultOptionId(CustomLists.getDefaultValueId(typeOptions));
            listVO.setHasBlank(CustomLists.hasBlankOption(typeOptions));
            List<OptionVO> options = this.getOptions(workgroupId, listId);
            listVO.setOptions(options);
            return listVO;
        } else {
            throw new IllegalArgumentException("listId is not exist.");
        }
    }

    /**
     * get a list with options by name
     *
     * @param workgroupId current workgroup id
     * @param listName      current selected list name
     * @return a list
     * @throws Exception exception
     */
    public ListVO getListWithOptionsByName(Long workgroupId, String listName, Boolean activeOnly) throws Exception {
        WorkgroupAttributeTypeDTO workgroupAttributeTypeDTO = this.getListByName(workgroupId, listName);
        if (workgroupAttributeTypeDTO != null) {
            ListVO listVO = new ListVO();
            String typeOptions = workgroupAttributeTypeDTO.getOptions();
            listVO.setDefaultOptionId(CustomLists.getDefaultValueId(typeOptions));
            listVO.setHasBlank(CustomLists.hasBlankOption(typeOptions));
            List<OptionVO> options = this.getOptions(workgroupId, workgroupAttributeTypeDTO.getId(), activeOnly);
            listVO.setOptions(options);
            return listVO;
        } else {
            throw new IllegalArgumentException("listId is not exist.");
        }
    }

    public WorkgroupAttributeTypeDTO getListByName(Long workgroupId, String listName) {
        return workgroupAttributeTypeMyBatisMapper.findTypesByWgIdAndLabel(workgroupId, listName);
    }

    /**
     * get options from a list
     *
     * @param workgroupId current workgroup id
     * @param typeId      type id is list id
     * @return options
     */
    public List<OptionVO> getOptions(Long workgroupId, Long typeId) {
        return getOptions(workgroupId, typeId, true);
    }

    /**
     * get options from a list
     *
     * @param workgroupId current workgroup id
     * @param typeId      type id is list id
     * @param activeOnly  active only
     * @return options
     */
    public List<OptionVO> getOptions(Long workgroupId, Long typeId, Boolean activeOnly) {
        List<WorkgroupAttributeDTO> optionDTOS = workgroupAttributeMyBatisMapper.findWorkgroupAttributes(workgroupId, typeId, "LABEL", activeOnly);

        if (optionDTOS != null && !optionDTOS.isEmpty()) {
            return optionDTOS.stream()
                    .map(optionDTO -> {
                        OptionVO optionVO = new OptionVO();
                        optionVO.setId(optionDTO.getId());
                        optionVO.setLabel(optionDTO.getLabelStr());
                        optionVO.setCode(optionDTO.getCode());
                        optionVO.setDefaultId(optionDTO.getAcWgAttributeRegId());
                        return optionVO;
                    })
                    .collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * Add options for a list.
     *
     * @param workgroupId  current workgroup id
     * @param userId       user id
     * @param addOptionsVO add options vo
     */
    @Transactional(rollbackFor = Exception.class)
    public void addOptions(Long workgroupId, Long userId, AddOptionsVO addOptionsVO) {
        Long nextOrdinalNumber = workgroupAttributeRegistrationMyBatisMapper.findNextOrdinalNumberByWgIdAndTypeId(workgroupId, addOptionsVO.getListId());

        for (OptionVO option : addOptionsVO.getOptions()) {
            WorkgroupAttribute attr = new WorkgroupAttribute();
            attr.setLabelStr(option.getLabel());
            attr.setCode(option.getCode());

            // Verify that neither the label or code is empty
            // if one exists, populate the other one with the same value
            if (attr.getLabelStr() == null || attr.getCode() == null) {
                if (attr.getLabelStr() == null && attr.getCode() != null) {
                    attr.setLabelStr(attr.getCode());
                } else if (attr.getLabelStr() != null && attr.getCode() == null) {
                    attr.setCode(option.getLabel());
                } else {
                    throw new IllegalArgumentException(String.valueOf(StringID.WORKGROUP_ATTRIBUTE_ERROR_MESSAGE));
                }
            }
            attr.setTypeId(addOptionsVO.getListId());
            attr.setCreateUserId(userId);
            attr = workgroupAttributeRepository.save(attr);

            WorkgroupAttributeRegistration regBean = new WorkgroupAttributeRegistration();
            regBean.setOwnerWorkgroupId(workgroupId);
            regBean.setWorkgroupAttributeId(attr.getId());
            regBean.setIsActive(true);
            nextOrdinalNumber = nextOrdinalNumber + 1;
            regBean.setOrdinalNumber(nextOrdinalNumber);
            regBean.setCreateUserId(userId);
            workgroupAttributeRegistrationRepository.save(regBean);
        }

    }

    /**
     * Update a list
     *
     * @param userId       current user id
     * @param updateListVO vo
     * @throws Exception exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateList(Long userId, UpdateListVO updateListVO) throws Exception {
        // Verify that none of the ids, labels or codes are empty
        for (OptionVO optionVO : updateListVO.getOptions()) {
            if (optionVO.getId() == null || optionVO.getLabel() == null || optionVO.getCode() == null) {
                throw new IllegalArgumentException("Option ID, label, code must not be null");
            }
        }

        // update Options
        updateListVO.getOptions().forEach(optionVO -> {
            Optional<WorkgroupAttribute> optionalOption = workgroupAttributeRepository.findById(optionVO.getId());
            if (optionalOption.isPresent()) {
                WorkgroupAttribute option = optionalOption.get();
                option.setLabelStr(optionVO.getLabel());
                option.setCode(optionVO.getCode());
                option.setModUserId(userId);
                workgroupAttributeRepository.save(option);
            } else {
                throw new NotFoundException("List Option Id " + optionVO.getId() + " is not exist");
            }
        });

        // update List
        Optional<WorkgroupAttributeType> optionalList = workgroupAttributeTypeRepository.findById(updateListVO.getListId());
        if (optionalList.isPresent()) {
            WorkgroupAttributeType list = optionalList.get();
            String options = CustomLists.setHasBlankOption(list.getOptions(), updateListVO.getHasBlank() != null && updateListVO.getHasBlank());
            options = CustomLists.setDefaultValueId(options, updateListVO.getDefaultOptionId() == null ? -1L : updateListVO.getDefaultOptionId());
            list.setOptions(options);
            list.setModUserId(userId);
            workgroupAttributeTypeRepository.save(list);
        } else {
            throw new NotFoundException("List Id " + updateListVO.getListId() + " is not exist");
        }

    }

    /**
     * move an option up or down
     *
     * @param workgroupId current workgroup id
     * @param userId      current user id
     * @param listId      current list id
     * @param optionId    current option id
     * @param promoting   true is up, false is down
     */
    @Transactional(rollbackFor = Exception.class)
    public void reorderOptions(Long workgroupId, Long userId, Long listId, Long optionId, Boolean promoting) {
        WorkgroupAttributeRegistration curOption = workgroupAttributeRegistrationRepository.findByGroupAndAttributeId(workgroupId, optionId);
        if (curOption == null) {
            return;
        }

        List<WorkgroupAttributeRegistration> options = workgroupAttributeRegistrationRepository
                .findAllWithWgIdAndTypeId(curOption.getOwnerWorkgroupId(), listId, true);
        if (options == null || options.isEmpty()) {
            return;
        }

        WorkgroupAttributeRegistration adjacentOption = findAdjacentRegistration(options, curOption.getId(), promoting);
        if (adjacentOption == null) {
            return;
        }

        long ordinalNumber = curOption.getOrdinalNumber();
        curOption.setOrdinalNumber(adjacentOption.getOrdinalNumber());
        workgroupAttributeRegistrationRepository.save(curOption);

        adjacentOption.setOrdinalNumber(ordinalNumber);
        adjacentOption.setModUserId(userId);
        workgroupAttributeRegistrationRepository.save(adjacentOption);
    }

    /**
     * This function finds and returns a registration that belongs to an option in the list
     * that is adjacent to an input option's registration.
     * This code assumes that if someone is looking for a registration above one exists.
     * This code assumes that if someone is looking for a registration below one exists.
     *
     * @param options     List options
     * @param curOptionId current option Id
     * @param above       above, ture is move up, false is move down.
     * @return WorkgroupAttributeRegistration
     */
    private WorkgroupAttributeRegistration findAdjacentRegistration(List<WorkgroupAttributeRegistration> options,
                                                                    long curOptionId,
                                                                    boolean above) {
        for (int i = 0; i < options.size(); i++) {
            WorkgroupAttributeRegistration tempRegistration = options.get(i);
            if (tempRegistration.getId() == curOptionId) {
                if (above) {
                    if (i == 0) {
                        return null;
                    }
                    return options.get(i - 1);
                } else {
                    if (i == options.size() - 1) {
                        return null;
                    }
                    return options.get(i + 1);
                }
            }
        }
        return null;
    }

    /**
     * sort a list alphabetically
     *
     * @param workgroupId current workgroup id
     * @param userId      current user id
     * @param listId      current list id
     */
    @Transactional(rollbackFor = Exception.class)
    public void arrangeOptionsByAlphabet(Long workgroupId, Long userId, Long listId) {
        List<WorkgroupAttributeRegistration> registrations = workgroupAttributeRegistrationRepository.findAllWithWgIdAndTypeId(workgroupId, listId, true);
        List<WorkgroupAttributeRegistration> registrationsOrderByLabel = workgroupAttributeRegistrationRepository.findAllWithWgIdAndTypeIdOrderBYLabel(workgroupId, listId, true);
        if (null != registrations && !registrations.isEmpty()) {
            // save the original order
            long[] ordinalNumbers = new long[registrations.size()];
            for (int i = 0; i < registrations.size(); i++) {
                ordinalNumbers[i] = registrations.get(i).getOrdinalNumber();
            }

            // reset the ordinal number as the original order
            for (int i = 0; i < registrationsOrderByLabel.size(); i++) {
                registrationsOrderByLabel.get(i).setOrdinalNumber(ordinalNumbers[i]);
                registrationsOrderByLabel.get(i).setModUserId(userId);
            }
            workgroupAttributeRegistrationRepository.saveAllAndFlush(registrations);
        }
    }

    /**
     * delete a selected option
     *
     * @param workgroupId current workgroup id
     * @param userId      current user id
     * @param listId      current list id
     * @param optionId    current option id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteOption(Long workgroupId, Long userId, Long listId, Long optionId) throws Exception {
        WorkgroupAttributeType list = workgroupAttributeTypeRepository.findByIdAndWorkgroupId(listId, workgroupId);
        WorkgroupAttribute option = workgroupAttributeRepository.findByIdAndWorkgroupId(optionId, workgroupId);
        List<WorkgroupAttribute> options = new ArrayList<>();
        options.add(option);
        deactivateOptions(userId, list, options);
    }

    /**
     * delete selected options
     *
     * @param workgroupId current workgroup id
     * @param userId      current user id
     * @param listId      current list id
     * @param optionIds   selected option ids
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteOptions(Long workgroupId, Long userId, Long listId, List<Long> optionIds) throws Exception {
        WorkgroupAttributeType list = workgroupAttributeTypeRepository.findByIdAndWorkgroupId(listId, workgroupId);
        List<WorkgroupAttribute> options = workgroupAttributeRepository.findByIdsAndWorkgroupId(optionIds, workgroupId);
        deactivateOptions(userId, list, options);
    }

    /**
     * delete all options from a list
     *
     * @param workgroupId current workgroup id
     * @param userId      current user id
     * @param listId      current list id
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteAllOptions(Long workgroupId, Long userId, Long listId) throws Exception {
        WorkgroupAttributeType list = workgroupAttributeTypeRepository.findByIdAndWorkgroupId(listId, workgroupId);
        List<WorkgroupAttribute> options = workgroupAttributeRepository.findByListIdAndWorkgroupId(listId, workgroupId);
        deactivateOptions(userId, list, options);
    }

    /**
     * deactivate options, which is a logic delete
     *
     * @param userId current user id
     * @param list   list
     * @param attrs  options
     * @throws Exception exception
     */
    private void deactivateOptions(Long userId, WorkgroupAttributeType list,
                                   List<WorkgroupAttribute> attrs) throws Exception {
        List<Long> attrIds = new ArrayList<>();
        for (WorkgroupAttribute attr : attrs) {
            attrIds.add(attr.getId());
        }
        List<WorkgroupAttributeRegistration> attrRegs = workgroupAttributeRegistrationRepository.findByWorkgroupAttributeIdIn(attrIds);


        for (WorkgroupAttributeRegistration attrReg : attrRegs) {
            // if we are deleting an option that is marked as default
            // set the default option to -1
            if (isDeletingDefaultOption(list, attrReg)) {
                String options = CustomLists.setDefaultValueId(list.getOptions(), -1L);
                list.setOptions(options);
                list.setModUserId(userId);
                workgroupAttributeTypeRepository.save(list);
            }

            // We don't actually delete, we deactivate the record (ie. isActive = false).
            // This is because the TimeCard feature uses that values it retrieves from this
            // table to display it's dropdowns to the user in realtime...it does not copy the
            // values into it's own structure.  Therefore, these records need to remain after
            // they've been created.
            attrReg.setIsActive(false);
        }
        workgroupAttributeRegistrationRepository.saveAll(attrRegs);

    }

    private boolean isDeletingDefaultOption(WorkgroupAttributeType list, WorkgroupAttributeRegistration optionReg) throws Exception {
        final Long defaultValueId = CustomLists.getDefaultValueId(list.getOptions());
        return defaultValueId != null && defaultValueId == optionReg.getId().longValue();
    }

    private static final class CustomLists {

        private static final String BLANK = "blank";
        private static final String NO = "no";
        private static final String YES = "yes";
        private static final String DEFAULT_VALUE_ID = "default_value_id";

        private CustomLists() {
        }

        public static boolean hasBlankOption(String options) throws Exception {
            String blankStr = getOptionsParam(options, BLANK);
            return blankStr != null && !blankStr.equals(NO);
        }

        public static String setHasBlankOption(String options, boolean hasBlankOption) throws Exception {
            if (hasBlankOption) {
                return setOptionsParam(options, BLANK, YES);
            } else {
                return setOptionsParam(options, BLANK, NO);
            }
        }

        public static Long getDefaultValueId(String options) throws Exception {
            String defaultValueIdStr = getOptionsParam(options, DEFAULT_VALUE_ID);
            if (defaultValueIdStr == null) {
                return null;
            }
            return Long.valueOf(defaultValueIdStr);
        }

        public static String setDefaultValueId(String options, long id) throws Exception {
            String defaultValueIdStr = String.valueOf(id);
            return setOptionsParam(options, DEFAULT_VALUE_ID, defaultValueIdStr);
        }

        private static String getOptionsParam(String options, String param) throws Exception {
            return getOptionsParams(options).get(param);
        }

        private static String setOptionsParam(String options, String param, String value) throws Exception {
            Map<String, String> optionsParams = getOptionsParams(options);
            optionsParams.put(param, value);
            return setOptionsParams(options, optionsParams);
        }

        private static Map<String, String> getOptionsParams(String options) throws Exception {
            Map<String, String> optionsParams = new HashMap<>();
            return URLUtil.queryStringToMap(options, optionsParams);
        }

        private static String setOptionsParams(String options, Map<String, String> params) throws Exception {
            if (params == null || params.isEmpty()) {
                return null;
            }

            Map<String, String> optionsParams = getOptionsParams(options);
            optionsParams.putAll(params);
            return URLUtil.mapToQueryString(optionsParams);
        }
    }

}
