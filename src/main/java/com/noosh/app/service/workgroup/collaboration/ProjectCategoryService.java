package com.noosh.app.service.workgroup.collaboration;

import com.noosh.app.commons.dto.collaboration.CategoryDTO;
import com.noosh.app.repository.mybatis.collaboration.CategoryMyBatisMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * User: leilaz
 * Date: 7/28/22
 */
@Service
@Transactional
public class ProjectCategoryService {
    @Autowired
    private CategoryMyBatisMapper categoryMyBatisMapper;

    public List<CategoryDTO> getProjectCategoryList(Long workgroupId, Long userId) {
        return categoryMyBatisMapper.getCategoryList(workgroupId, userId);
    }
}
