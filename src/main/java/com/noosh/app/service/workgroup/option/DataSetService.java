package com.noosh.app.service.workgroup.option;

import com.noosh.app.commons.constant.StringID;
import com.noosh.app.commons.dto.workgroup.option.CustomDataSetDTO;
import com.noosh.app.commons.dto.workgroup.option.CustomDataSetTypeDTO;
import com.noosh.app.commons.entity.workgroup.option.CustomDataSet;
import com.noosh.app.commons.entity.workgroup.option.CustomDataSetType;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.exception.UnexpectedException;
import com.noosh.app.mapper.workgroup.option.CustomDataSetMapper;
import com.noosh.app.repository.jpa.workgroup.option.CustomDataSetTypeRepository;
import com.noosh.app.repository.jpa.workgroup.option.DataSetRepository;
import com.noosh.app.service.util.I18NUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationTargetException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * User: leilaz
 * Date: 3/29/22
 */
@Service
@Transactional
public class DataSetService {
    @Autowired
    private DataSetRepository dataSetRepository;
    @Autowired
    private CustomDataSetMapper customDataSetMapper;
    @Autowired
    private CustomDataSetTypeRepository customDataSetTypeRepository;
    @Autowired
    private I18NUtils i18NUtils;

    private static int maxIndex = 30;

    public CustomDataSetDTO findDataSetById (Long dataSetId) {
        if (dataSetId == null) {
            throw new IllegalArgumentException("Please pass the correct custom data set id!");
        }
        CustomDataSet customDataSet = dataSetRepository.findById(dataSetId).orElse(null);
        if (customDataSet == null) {
            throw new NotFoundException("Can't find the custom data set by id:" + dataSetId);
        }
        return customDataSetMapper.toDTO(customDataSet);
    }

    public List<CustomDataSetTypeDTO> findDataSetTypeByWgId(Long workgroupId) {
        List<DropdownVO> dataSetTypeList = new ArrayList<DropdownVO>();
        List<CustomDataSetType> dataSetTypes = customDataSetTypeRepository.findByWorkgroupId(workgroupId);
        return customDataSetMapper.toTypeDTOs(dataSetTypes);
    }

    public List<CustomDataSetDTO> findDataSets(Long workgroupId, String name) {
        return findDataSets(workgroupId, name, null);
    }

    public List<CustomDataSetDTO> findDataSets(Long workgroupId, String name, String filters) {
        CustomDataSetType dataSetType = customDataSetTypeRepository.findByWorkgroupIdAndName(workgroupId, name);
        List<CustomDataSet> customDataSets = dataSetType.getCustomDataSets();
        if (filters != null && !filters.isEmpty()) {
            //filters is like "attribute1=value1,attribute2=value2..."
            //filter the attributes in customDataSets
            String[] filterArray = filters.split(",");
            customDataSets = customDataSets.stream()
                    .filter(dataSet -> {
                        BeanWrapper wrapper = new BeanWrapperImpl(dataSet);
                        for (String filter : filterArray) {
                            String[] keyValue = filter.split("=");
                            if (keyValue.length != 2) continue;

                            String attributeName = keyValue[0];
                            String expectedValue = keyValue[1];
                            try {
                                Object actualValueObj = wrapper.getPropertyValue(attributeName);
                                String actualValue = (actualValueObj != null) ? actualValueObj.toString() : null;
                                if (!Objects.equals(actualValue, expectedValue)) {
                                    return false;
                                }
                            } catch (Exception e) {
                                return false;
                            }
                        }
                        return true;
                    })
                    .collect(Collectors.toList());
        }
        return customDataSetMapper.toDTOs(customDataSets);
    }

    public void updateCustomDataSet(CustomDataSetTypeDTO customDataSetTypeDTO, Long currentUserId) {
        if (customDataSetTypeDTO == null || customDataSetTypeDTO.getId() == null
                || customDataSetTypeDTO.getCustomDataSets() == null || customDataSetTypeDTO.getCustomDataSets().size() == 0) {
            throw new IllegalArgumentException("Please pass the valid data set type!");
        }
        CustomDataSetType customDataSetType = customDataSetTypeRepository.findById(customDataSetTypeDTO.getId()).orElse(null);
        try {
            if (customDataSetType != null) {
                List<CustomDataSet> existingDataSets = customDataSetType.getCustomDataSets();
                List<CustomDataSetDTO> currentDataSets = customDataSetTypeDTO.getCustomDataSets();
                int maxValidIndex = 1;
                for (int j = 1; j <= maxIndex; j++) {
                    if (customDataSetType.getClass().getMethod("getAttribute" + j + "Name").invoke(customDataSetType) == null) {
                        maxValidIndex = j - 1;
                        break;
                    }
                }
                int n = 0;
                for (CustomDataSetDTO customDataSetDTO : currentDataSets) {
                    if (currentDataSets.stream().filter(s -> s.getId() != null
                            && s.getId().longValue() == customDataSetDTO.getId()).count() > 1) {
                        throw new IllegalArgumentException(i18NUtils.getMessage(StringID.AC_DATA_SET_DUPLICATED_IDS, new String[] {String.valueOf(n)}));
                    }

                    for (int k = 1; k <= maxValidIndex; k++) {
                        if (customDataSetDTO.getClass().getMethod("getAttribute" + k).invoke(customDataSetDTO) != null) {
                            throw new IllegalArgumentException(i18NUtils.getMessage(StringID.AC_DATA_SET_TOO_MANY_ATTRIBUTES, new String[] {String.valueOf(n)}));
                        }
                    }

                    if (customDataSetDTO.getId() == null) {
                        throw new IllegalArgumentException(i18NUtils.getMessage(StringID.AC_DATA_SET_ID_REQUIRED, new String[] {String.valueOf(n)}));
                    }

                    n ++;

                    if (customDataSetDTO.getId() == (long) -1) {
                        // Do insert
                        CustomDataSet newDataSet = customDataSetMapper.toEntity(customDataSetDTO);
                        newDataSet.setId(null);
                        newDataSet.setCreateUserId(currentUserId);
                        newDataSet.setCreateDate(LocalDateTime.now());
                        newDataSet.setDataSetTypeId(customDataSetTypeDTO.getId());
                        dataSetRepository.save(newDataSet);
                    } else {
                        if (existingDataSets != null) {
                            boolean isDelete = true;
                            CustomDataSet existing = existingDataSets.stream().filter(e -> e.getId()
                                    == customDataSetDTO.getId().longValue()).findFirst().orElse(null);
                            for (int i = 1; i <= maxIndex; i++) {
                                if (customDataSetDTO.getClass().getMethod("getAttribute" + i).invoke(customDataSetDTO) != null) {
                                    // Means update, because if all attributes are null means delete
                                    isDelete = false;
                                    break;
                                }
                            }
                            if (existing == null) {
                                // Means incorrect id
                                if (isDelete) {
                                    throw new IllegalArgumentException(i18NUtils.getMessage(StringID.AC_DATA_SET_INVALID_IDS_REMOVE,
                                            new String[] {String.valueOf(customDataSetDTO.getId())}));
                                } else {
                                    throw new IllegalArgumentException(i18NUtils.getMessage(StringID.AC_DATA_SET_INVALID_IDS_UPDATE,
                                            new String[] {String.valueOf(customDataSetDTO.getId())}));
                                }
                            } else {
                                if (!isDelete) {
                                    // DO update
                                    CustomDataSet newUpdated = customDataSetMapper.toEntityOnlyAttributes(customDataSetDTO);
                                    newUpdated.setDataSetTypeId(existing.getDataSetTypeId());
                                    newUpdated.setId(existing.getId());
                                    newUpdated.setCreateUserId(existing.getCreateUserId());
                                    newUpdated.setModUserId(currentUserId);
                                    newUpdated.setCreateDate(existing.getCreateDate());
                                    newUpdated.setModDate(LocalDateTime.now());
                                    dataSetRepository.save(newUpdated);
                                } else {
                                    dataSetRepository.delete(existing);
                                }

                            }
                        }
                    }
                }

            } else {
                throw new NotFoundException("Can't find the data set type by id :" + customDataSetTypeDTO.getId());
            }
        } catch (NoSuchMethodException e) {
            throw new UnexpectedException(e.getMessage());
        } catch (IllegalAccessException e) {
            throw new UnexpectedException(e.getMessage());
        } catch (InvocationTargetException e) {
            throw new UnexpectedException(e.getMessage());
        }

    }
}
