package com.noosh.app.service.workgroup.option;

import com.noosh.app.commons.dto.collaboration.ProjectStatusDTO;
import com.noosh.app.commons.dto.collaboration.ProjectStatusUpdateDTO;
import com.noosh.app.commons.entity.collaboration.ProjectStatus;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.mapper.collaboration.ProjectStatusMapper;
import com.noosh.app.repository.jpa.collaboration.ProjectStatusRepository;
import com.noosh.app.repository.mybatis.collaboration.ProjectStatusMyBatisMapper;
import com.noosh.app.service.util.I18NUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * User: leilaz
 * Date: 7/11/22
 */
@Service
@Transactional
public class ProjectStatusService {
    @Autowired
    private ProjectStatusRepository projectStatusRepository;

    @Autowired
    private ProjectStatusMapper projectStatusMapper;

    @Autowired
    private ProjectStatusMyBatisMapper projectStatusMyBatisMapper;

    @Autowired
    private I18NUtils i18NUtils;

    public List<ProjectStatusDTO> getProjectStatusList(Long workgroupId) {
        List<ProjectStatus> projectStatuses = projectStatusRepository.findByWorkgroupIdOrderByStatusOrder(workgroupId);
        return projectStatusMapper.toDTOs(projectStatuses);
    }

    public void createOrUpdateProjectStatus(Long workgroupId, Long userId, ProjectStatusUpdateDTO projectStatusUpdateDTO) {
        if (!StringUtils.hasLength(projectStatusUpdateDTO.getName())) {
            throw new IllegalArgumentException("Please pass the valid project status name!");
        }
        //Check if the name is already in use
        isNameInUse(workgroupId, projectStatusUpdateDTO.getId(), projectStatusUpdateDTO.getName());
        ProjectStatus projectStatus = new ProjectStatus();
        boolean isNew = false;
        if (projectStatusUpdateDTO.getId() == null) {
            isNew = true;
            projectStatus.setCreateUserId(userId);
            projectStatus.setCreateDate(LocalDateTime.now());
            projectStatus.setIsDefault(false); // Hard code false for new created
            projectStatus.setWorkgroupId(workgroupId);
        } else {
            projectStatus = projectStatusRepository.findById(projectStatusUpdateDTO.getId()).orElse(new ProjectStatus());
            projectStatus.setModUserId(userId);
            projectStatus.setModDate(LocalDateTime.now());
        }

        projectStatus.setNameStr(projectStatusUpdateDTO.getName());
        projectStatus.setDescription(projectStatusUpdateDTO.getDescription());


        projectStatusRepository.saveAndFlush(projectStatus);
        if (isNew) {
            projectStatus.setStatusOrder(projectStatus.getId());
            projectStatusRepository.save(projectStatus);
        }
    }

    public void setDefaultProjectStatus(Long workgroupId, Long userId, ProjectStatusUpdateDTO projectStatusDTO) {
        if (projectStatusDTO.getId() == null) {
            throw new IllegalArgumentException("The status id is null!");
        }
        //First find the default one
        ProjectStatus defaultStatus = projectStatusRepository.findDefaultByWorkgroupId(workgroupId);
        if (defaultStatus == null || defaultStatus.getId() == null) {
            throw new NotFoundException("Can't find the default project status in current workgroup: " + workgroupId);
        }
        defaultStatus.setIsDefault(false);
        defaultStatus.setModDate(LocalDateTime.now());
        defaultStatus.setModUserId(userId);
        projectStatusRepository.save(defaultStatus);

        // Load the current project status
        ProjectStatus currentStatus = projectStatusRepository.findById(projectStatusDTO.getId()).orElse(null);
        if (currentStatus == null) {
            throw new NotFoundException("Can't find the project status with: " + projectStatusDTO.getId());
        }
        currentStatus.setIsDefault(true);
        currentStatus.setModDate(LocalDateTime.now());
        currentStatus.setModUserId(userId);
        projectStatusRepository.save(currentStatus);
    }

    public boolean isProjectStatusInUsed(Long projectStatusId) {
        if (projectStatusId == null) {
            throw new IllegalArgumentException("Please pass the valid project status id!");
        }
        Long totalCount = projectStatusMyBatisMapper.getTotalProjectStatusInUsed(projectStatusId);
        return totalCount != null && totalCount > 0;
    }

    public void deleteProjectStatus(Long workgroupId, Long userId, Long deleteStatusId,
                                    Long assignToStatusId, Long defaultStatusId) {
        if (deleteStatusId == null) {
            throw new IllegalArgumentException("Please pass the valid deleted project status id!");
        }
        ProjectStatus deleteProjectStatus = projectStatusRepository.findById(deleteStatusId).orElse(null);
        if (deleteProjectStatus == null) {
            throw new NotFoundException("Can't find the project status with: " + deleteStatusId);
        }
        if (deleteProjectStatus.getIsDefault() && defaultStatusId == null) {
            throw new IllegalArgumentException("The deleted project status is a default one, please pass the new default status id!");
        }
        if (isProjectStatusInUsed(deleteStatusId) && assignToStatusId == null) {
            throw new IllegalArgumentException("The deleted project status is used in projects, please pass the new replaced status id!");
        }
        if (assignToStatusId != null) {
            projectStatusMyBatisMapper.updateProjectStatus(assignToStatusId, deleteStatusId);
        }
        if (defaultStatusId != null) {
            ProjectStatus defaultProjectStatus = projectStatusRepository.findById(defaultStatusId).orElse(null);
            if (defaultProjectStatus == null) {
                throw new NotFoundException("Can't find the project status with: " + defaultStatusId);
            }
            defaultProjectStatus.setIsDefault(true);
        }
        projectStatusRepository.delete(deleteProjectStatus);
    }

    private void isNameInUse(Long workgroupId, Long projectStatusId, String name) {
        List<ProjectStatus> projectStatuses = projectStatusRepository.findByWorkgroupIdOrderByStatusOrder(workgroupId);
        if (projectStatuses != null && projectStatuses.size() > 0) {
            for (ProjectStatus status : projectStatuses) {
                if (projectStatusId != null && status.getId() == projectStatusId.longValue()) {
                    continue;
                } else if (getI18nStatusName(status).equals(name)) {
                    throw new IllegalArgumentException("The status name you entered is already in use. Please choose another.");
                }
            }
        }
    }

    public String getI18nStatusName(ProjectStatus projectStatus) {
        if (StringUtils.hasLength(projectStatus.getNameStr())) {
            return projectStatus.getNameStr();
        } else if (projectStatus.getNameStrId() != null) {
            return i18NUtils.getMessage(projectStatus.getNameStrId());
        }
        return "";
    }

}
