package com.noosh.app.service.workgroup.option;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.security.AccountUserDTO;
import com.noosh.app.commons.dto.security.PersonDTO;
import com.noosh.app.commons.dto.security.RoleDTO;
import com.noosh.app.commons.dto.security.WorkgroupDTO;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.commons.vo.workgroup.option.members.*;
import com.noosh.app.repository.mybatis.security.AccountUserMyBatisMapper;
import com.noosh.app.repository.mybatis.security.PersonMyBatisMapper;
import com.noosh.app.repository.mybatis.security.RoleMyBatisMapper;
import com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper;
import com.noosh.app.service.permission.PermissionService;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.util.NooshOneUrlUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

/**
 * <AUTHOR> Shan
 * @since 8/3/2022
 */
@Service
public class MemberService {

    @Autowired
    private RoleMyBatisMapper roleMyBatisMapper;
    @Autowired
    private AccountUserMyBatisMapper accountUserMyBatisMapper;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private PersonMyBatisMapper personMyBatisMapper;
    @Autowired
    private WorkgroupMyBatisMapper workgroupMyBatisMapper;

    private static final Map<String, String> SORT_MAP = new HashMap<>();
    
    @PostConstruct
    public void init(){
        SORT_MAP.put("p_last_name", "UPPER(P_LAST_NAME),UPPER(P_FIRST_NAME)");
        SORT_MAP.put("title", "UPPER(P_PS_TITLE)");
        SORT_MAP.put("phone", "P_PS_PHONE_NUMBER");
        SORT_MAP.put("email", "UPPER(P_E_EMAIL_ADDRESS)");
        SORT_MAP.put("role_name_str", "R_ROLE_NAME_STR");
    }

    public String getSort(String sort) {
        return SORT_MAP.get(sort);
    }

    public MemberListVO initMemberList(Long userId, Long workgroupId) {
        MemberListVO memberListVO = new MemberListVO();
        RoleDTO roleDTO = roleMyBatisMapper.findWorkgroupRoleByUserId(userId);
        memberListVO.setRoleNameStrId(roleDTO.getRoleNameStrId());
        memberListVO.setRoleNameStr(roleDTO.getRoleNameStr());
        memberListVO.setInviteExternalUrl(getInviteMemberExternalUrl());
        memberListVO.setBackToOptionsExternalUrl(getBackToOptionsExternalUrl());
        final boolean canViewWorkgroupPrivs = permissionService.checkAll(PermissionID.VIEW_WORKGROUP_ROLE_PRIVILEGES, workgroupId, userId, -1L);
        memberListVO.setCanViewWorkgroupPrivs(canViewWorkgroupPrivs);
        return memberListVO;
    }

    public List<MemberVO> getMemberList(Long userId, Long workgroupId, String listType, PageVO page) {

        if (ListTypeEnum.ACTIVE.name().equalsIgnoreCase(listType)) {
            return getActiveMemberList(userId, workgroupId, page);
        } else if (ListTypeEnum.INACTIVE.name().equalsIgnoreCase(listType)) {
            return getInactiveMemberList(userId, workgroupId, page);
        } else if (ListTypeEnum.INVITED.name().equalsIgnoreCase(listType)) {
            return getInvitedMemberList(userId, workgroupId, page);
        } else if (ListTypeEnum.SUSPENDED.name().equalsIgnoreCase(listType)) {
            return getSuspendedMemberList(userId, workgroupId, page);
        } else {
            throw new IllegalArgumentException("invalid listType");
        }
    }

    public List<MemberVO> getActiveMemberList(Long userId, Long workgroupId, PageVO page) {
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        pageInfo.setUnsafeOrderBy(page.getOrderBy());
        List<AccountUserDTO> accountUserDTOS = accountUserMyBatisMapper.findWithAllData(workgroupId, ObjectStateID.ACCOUNT_USER_ACTIVATED);
        page.setTotal(pageInfo.getTotal());

        final boolean allowPasswordResets = preferenceService.check(PreferenceID.WORKGROUP_OPTION_PASSWORD_ALLOW_RESETS, workgroupId);
        final boolean canResetUserPassword = permissionService.checkAll(PermissionID.RESET_USER_PASSWORD, workgroupId, userId, -1L);
        final boolean canEditMember = permissionService.checkAll(PermissionID.EDIT_ACCOUNT_USER, workgroupId, userId, -1L);
        List<MemberVO> memberVOS = new ArrayList<>();
        if (accountUserDTOS != null) {
            accountUserDTOS.forEach(accountUserDTO -> {
                MemberVO memberVO = toMemberVO(accountUserDTO);

                memberVO.setViewExternalUrl(getViewExternalUrl(accountUserDTO.getPersonId()));

                if (canEditMember) {
                    memberVO.setEditExternalUrl(getEditExternalUrl(accountUserDTO.getId()));
                    memberVO.setDeactiveExternalUrl(getDeactiveExternalUrl(accountUserDTO.getId()));
                }

                if (canEditMember && accountUserDTO.getIsLocked() != null && accountUserDTO.getIsLocked()) {
                    memberVO.setUnlockExternalUrl(getUnlockExternalUrl(accountUserDTO.getId()));
                }

                if (allowPasswordResets && canResetUserPassword) {
                    memberVO.setResetPasswordExternalUrl(getResetPasswordExternalUrl(accountUserDTO.getId()));
                }

                memberVO.setDeactiveExternalUrl(getDeactiveExternalUrl(accountUserDTO.getId()));

                memberVOS.add(memberVO);
            });
        }

        return memberVOS;
    }

    public List<MemberVO> getInactiveMemberList(Long userId, Long workgroupId, PageVO page) {
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        pageInfo.setUnsafeOrderBy(page.getOrderBy());
        List<AccountUserDTO> accountUserDTOS = accountUserMyBatisMapper.findWithAllData(workgroupId, ObjectStateID.ACCOUNT_USER_INACTIVATED);
        page.setTotal(pageInfo.getTotal());

        final boolean canEditMember = permissionService.checkAll(PermissionID.EDIT_ACCOUNT_USER, workgroupId, userId, -1L);
        List<MemberVO> memberVOS = new ArrayList<>();
        if (accountUserDTOS != null) {
            accountUserDTOS.forEach(accountUserDTO -> {
                MemberVO memberVO = toMemberVO(accountUserDTO);

                memberVO.setViewExternalUrl(getViewExternalUrl(accountUserDTO.getPersonId()));

                if (canEditMember) {
                    memberVO.setEditExternalUrl(getEditExternalUrl(accountUserDTO.getId()));
                    memberVO.setActiveExternalUrl(getActiveExternalUrl(accountUserDTO.getId()));
                }

                memberVO.setActiveExternalUrl(getActiveExternalUrl(accountUserDTO.getId()));

                memberVOS.add(memberVO);
            });
        }

        return memberVOS;
    }

    public List<MemberVO> getInvitedMemberList(Long userId, Long workgroupId, PageVO page) {
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        pageInfo.setUnsafeOrderBy(page.getOrderBy());
        List<AccountUserDTO> accountUserDTOS = accountUserMyBatisMapper.findWithAllData(workgroupId, ObjectStateID.ACCOUNT_USER_INVITED);
        page.setTotal(pageInfo.getTotal());

        final boolean canEditMember = permissionService.checkAll(PermissionID.EDIT_ACCOUNT_USER, workgroupId, userId, -1L);
        List<MemberVO> memberVOS = new ArrayList<>();
        if (accountUserDTOS != null) {
            accountUserDTOS.forEach(accountUserDTO -> {
                MemberVO memberVO = toMemberVO(accountUserDTO);

                if (canEditMember) {
                    memberVO.setEditExternalUrl(getEditExternalUrl(accountUserDTO.getId()));
                    memberVO.setActiveExternalUrl(getActiveExternalUrl(accountUserDTO.getId()));
                    memberVO.setUninviteExternalUrl(getUninviteExternalUrl(accountUserDTO.getId()));
                }
                memberVO.setUninviteExternalUrl(getUninviteExternalUrl(accountUserDTO.getId()));

                memberVOS.add(memberVO);
            });
        }

        return memberVOS;
    }

    public List<MemberVO> getSuspendedMemberList(Long userId, Long workgroupId, PageVO page) {
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        pageInfo.setUnsafeOrderBy(page.getOrderBy());
        List<AccountUserDTO> accountUserDTOS = accountUserMyBatisMapper.findWithAllData(workgroupId, ObjectStateID.ACCOUNT_USER_SUSPENDED);
        page.setTotal(pageInfo.getTotal());

        final boolean canEditMember = permissionService.checkAll(PermissionID.EDIT_ACCOUNT_USER, workgroupId, userId, -1L);
        List<MemberVO> memberVOS = new ArrayList<>();
        if (accountUserDTOS != null) {
            accountUserDTOS.forEach(accountUserDTO -> {
                MemberVO memberVO = toMemberVO(accountUserDTO);

                memberVO.setViewExternalUrl(getViewExternalUrl(accountUserDTO.getPersonId()));

                if (canEditMember) {
                    memberVO.setEditExternalUrl(getEditExternalUrl(accountUserDTO.getId()));
                    memberVO.setUnsuspendedExternalUrl(getUnsuspendedExternalUrl(accountUserDTO.getId()));
                }

                memberVO.setUnsuspendedExternalUrl(getUnsuspendedExternalUrl(accountUserDTO.getId()));

                memberVOS.add(memberVO);
            });
        }

        return memberVOS;
    }

    private String getBackToOptionsExternalUrl() {
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/home");
    }

    private String getInviteMemberExternalUrl() {
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/manageMembers");
    }

    private String getViewExternalUrl(Long personId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("personId", String.valueOf(personId));
        params.put("back", "/noosh/accounts/workgroup/listMembers");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/personal/personalInfo", params);
    }

    private String getEditExternalUrl(Long userId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("userId", String.valueOf(userId));
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/editAccountUser", params);
    }

    private String getUnlockExternalUrl(Long userId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("userId", String.valueOf(userId));
        params.put("acName", "ACTION_UNLOCK_USER");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/listMembers", params);
    }

    private String getResetPasswordExternalUrl(Long userId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("userId", String.valueOf(userId));
        params.put("acName", "ACTION_RESET_PASSWORD");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/listMembers", params);
    }

    private String getDeactiveExternalUrl(Long userId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("userId", String.valueOf(userId));
        params.put("acName", "ACTION_DEACTIVATE_USER");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/listMembers", params);
    }

    private String getActiveExternalUrl(Long userId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("userId", String.valueOf(userId));
        params.put("acName", "ACTION_ACTIVATE_USER");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/listMembers", params);
    }

    private String getUninviteExternalUrl(Long userId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("userId", String.valueOf(userId));
        params.put("acName", "ACTION_UNINVITE_USER");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/listMembers", params);
    }

    private String getUnsuspendedExternalUrl(Long userId) {
        HashMap<String, String> params = new HashMap<>();
        params.put("userId", String.valueOf(userId));
        params.put("acName", "ACTION_UNSPENDED_USER");
        return NooshOneUrlUtil.composeLinkToEnterprise("/noosh/accounts/workgroup/listMembers", params);
    }

    private MemberVO toMemberVO(AccountUserDTO accountUserDTO) {
        MemberVO memberVO = new MemberVO();
        memberVO.setName(accountUserDTO.getPerson().getLastName() + ", " + accountUserDTO.getPerson().getFirstName());
        memberVO.setTitle(accountUserDTO.getPerson().getPsd().getTitle());
        memberVO.setPhone(accountUserDTO.getPerson().getPsd().getPhoneNumber());
        memberVO.setEmail(accountUserDTO.getPerson().getDefaultPersonEmail().getEmailAddress());
        memberVO.setRoleId(accountUserDTO.getWorkgroupRole().getId());
        memberVO.setRoleNameStrId(accountUserDTO.getWorkgroupRole().getRoleNameStrId());
        memberVO.setRoleNameStr(accountUserDTO.getWorkgroupRole().getRoleNameStr());
        return memberVO;
    }

    enum ListTypeEnum {
        ACTIVE, INACTIVE, INVITED, SUSPENDED;
    }


    public InviteMemberVO initInviteAndAssignMember(Long workgroupId) {
        InviteMemberVO vo = new InviteMemberVO();

        List<RoleDTO> roleDTOs = roleMyBatisMapper.findRolesByWorkgroup(workgroupId, RoleClassID.ACCOUNT_ROLES);
        List<DropdownVO<Long>> workgroupRoles = new ArrayList<>();
        if (roleDTOs != null && roleDTOs.size() > 0) {
            roleDTOs.forEach(roleDTO -> {
                if (roleDTO.getBaseRoleId() != RoleID.WORKGROUP_ADMIN) {
                    workgroupRoles.add(new DropdownVO<>(roleDTO.getId(), roleDTO.getRoleNameStr(), roleDTO.getRoleNameStrId()));
                }
            });
        }
        vo.setInviteWorkgroupRoles(workgroupRoles);
        vo.setAssignWorkgroupRoles(workgroupRoles);

        WorkgroupDTO workgroupDTO = workgroupMyBatisMapper.findWorkgroup(workgroupId);
        List<PersonDTO> personDTOS = personMyBatisMapper.findAllPersonsInCompanyNotInWorkgroup(workgroupDTO.getId(), workgroupDTO.getCompanyId());
        List<DropdownVO<Long>> assignMembers = new ArrayList<>();
        if (personDTOS != null && personDTOS.size() > 0) {
            personDTOS.forEach(personDTO -> {
                assignMembers.add(new DropdownVO<>(personDTO.getId(), personDTO.getFirstName() + " " + personDTO.getLastName()));
            });
        }
        vo.setAssignMembers(assignMembers);
        return vo;
    }

    public InviteNewMemberConfirmVO inviteNewMemberConfirmView(Long workgroupId, String email) {
        InviteNewMemberConfirmVO vo = new InviteNewMemberConfirmVO();

        List<PersonDTO> personDTOS = personMyBatisMapper.findByEmailAddress(email);

        List<InviteNewMemberVO> members = new ArrayList<>();
        if (personDTOS != null && personDTOS.size() > 0) {
            for (PersonDTO personDTO : personDTOS) {
                InviteNewMemberVO memberVO = new InviteNewMemberVO();
                memberVO.setPersonId(personDTO.getId());

                String personName = personDTO.getFirstName() + " " + personDTO.getLastName();
                memberVO.setPersonName(personName);

                AccountUserDTO user = accountUserMyBatisMapper.findByPersonIdAndWorkgroupId(personDTO.getId(), workgroupId);
                memberVO.setIsPersonAlreadyAMember(isPersonAlreadyAMember(user));
                members.add(memberVO);
            }
        }
        vo.setMembers(members);
        return vo;
    }

    public boolean isPersonAlreadyAMember(AccountUserDTO user) {
        return user != null && user.getStatusId() != null && user.getStatusId() != ObjectStateID.ACCOUNT_USER_UNINVITED;
    }
}
