package com.noosh.app.service.workgroup.collaboration;

import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.workgroup.collaboration.MilestonesDTO;
import com.noosh.app.commons.dto.workgroup.collaboration.MilestonesDetailDTO;
import com.noosh.app.commons.dto.workgroup.collaboration.MilestonesUpdateDTO;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.repository.jpa.userfield.CustomFieldRepository;
import com.noosh.app.repository.jpa.task.TaskTypeRepository;
import com.noosh.app.commons.entity.userfield.CustomField;
import com.noosh.app.commons.entity.task.TaskType;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.permission.PermissionService;
import com.noosh.app.service.util.I18NUtils;
import com.noosh.app.service.util.Util;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * User: leilaz
 * Date: 4/20/22
 */
@Service
@Transactional
public class ProductionMatrixService {
    @Autowired
    private CustomFieldRepository customFieldRepository;
    @Autowired
    private TaskTypeRepository taskTypeRepository;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private I18NUtils i18NUtils;


    private static final String WORKGROUP_PRODUCTION_MATRIX_FIELDS = "WORKGROUP_PRODUCTION_MATRIX_FIELDS";

    public MilestonesDTO getMilestones(Long currentWorkgroupId, Long currentUserId) {
        MilestonesDTO milestonesDTO = new MilestonesDTO();

        Map<String, String> projectFieldsToDisplayMapping = new HashMap<String, String>();
        Map<String, String> projectFieldsToOutputMapping = new HashMap<String, String>();

        projectFieldsToDisplayMapping.put(ProductionMatrixConstants.KEY_PROJECT_NAME, i18NUtils.getMessage(StringID.PO_PROJECT_NAME));
        projectFieldsToDisplayMapping.put(ProductionMatrixConstants.KEY_PROJECT_CATEGORY, i18NUtils.getMessage(StringID.PO_PROJECT_CATEGORY));
        projectFieldsToDisplayMapping.put(ProductionMatrixConstants.KEY_PROJECT_ID, i18NUtils.getMessage(StringID.PO_PROJECT_ID));
        projectFieldsToDisplayMapping.put(ProductionMatrixConstants.KEY_PROJECT_NUMBER, i18NUtils.getMessage(StringID.PO_PROJECT_NUMBER));
        projectFieldsToDisplayMapping.put(ProductionMatrixConstants.KEY_PROJECT_CLIENT_ACCOUNT, i18NUtils.getMessage(StringID.PO_CLIENT_ACCOUNT));
        projectFieldsToDisplayMapping.put(ProductionMatrixConstants.KEY_PROJECT_CREATE_DATE, i18NUtils.getMessage(StringID.PO_PROJECT_CREATE_DATE));
        projectFieldsToDisplayMapping.put(ProductionMatrixConstants.KEY_PROJECT_COMPLETION_DATE, i18NUtils.getMessage(StringID.PO_PROJECT_COMPLETION_DATE));
        projectFieldsToDisplayMapping.put(ProductionMatrixConstants.KEY_PROJECT_DESCRIPTION, i18NUtils.getMessage(StringID.PO_DESCRIPTION));
        projectFieldsToDisplayMapping.put(ProductionMatrixConstants.KEY_PROJECT_COMMENTS, i18NUtils.getMessage(StringID.PO_COMMENTS));
        projectFieldsToDisplayMapping.put(ProductionMatrixConstants.KEY_PROJECT_STATE, i18NUtils.getMessage(StringID.PO_PROJECT_STATE));
        projectFieldsToDisplayMapping.put(ProductionMatrixConstants.KEY_PROJECT_STATUS, i18NUtils.getMessage(StringID.PO_PROJECT_STATUS));

        List<CustomField> customFields = customFieldRepository.findByOwnerWorkgroupIdAndCustomFieldClassId(
                currentWorkgroupId, CustomFieldClassID.PROJECT_HOME);
        if (customFields != null && customFields.size() > 0) {
            for (CustomField customField : customFields) {
                projectFieldsToDisplayMapping.put(ProductionMatrixConstants.PCUSTOM_PREFIX + "@"
                        + customField.getPropertyParam().getParamName() + "[" + customField.getLabel() + "]",
                        customField.getLabel());
            }
        }

        List<TaskType> milestoneList = taskTypeRepository.findMilestonesByWorkgroupId(currentWorkgroupId);
        if (milestoneList != null && milestoneList.size() > 0) {
            for (TaskType taskType : milestoneList) {
                projectFieldsToDisplayMapping.put(taskType.getTaTaskTypeId().toString(),
                        taskType.getNameStrId() != null ? String.valueOf(taskType.getNameStrId()) : taskType.getNameStr());
            }
        }

        List<String> preferenceIds = Arrays.asList(WORKGROUP_PRODUCTION_MATRIX_FIELDS);
        Map<String, String> preferenceMap = preferenceService.findGroupPrefs(
                currentWorkgroupId, preferenceIds);

        if (!preferenceMap.isEmpty()) {
            String[] selectedFieldArray = Util.split(preferenceMap.get(WORKGROUP_PRODUCTION_MATRIX_FIELDS), ":");
            for (int i = 0; i < selectedFieldArray.length; i++) {
                String selectedField = selectedFieldArray[i];

                // BACKWARD COMPATIBLITY
                // adjust the selected field for backward compatibility
                // new format is P_<name>, PS_<name>, PC_<name>[<display name>]
                // old format P_<name>[<display name>] for attrs
                // P_<@name_type>[<display name>] for project custom attrs
                if (!selectedField.startsWith(ProductionMatrixConstants.PCUSTOM_PREFIX) && selectedField.indexOf("[") > 0) {
                    if (selectedField.startsWith(ProductionMatrixConstants.P_PREFIX+"@") ) {
                        // old format for project custom attrs
                        //P_<@name_type>[<label name>] -> PC_<@name_type>[<label name>]
                        selectedField = ProductionMatrixConstants.PCUSTOM_PREFIX + selectedField.substring(ProductionMatrixConstants.P_PREFIX.length());
                    } else {
                        // old format for project atts
                        // P_<name>[<display name>] -> P_<name>
                        // PS_<name>[<display name>] -> PS_<name>
                        selectedField = selectedField.substring(0, selectedField.indexOf("[")) ;
                    }
                }

                //deal with project fields: use the i18n version which is based on user locale
                // deal with project custom fields: use the name in [] directly
                //deal with milestones: get the task type name
                if (projectFieldsToDisplayMapping.containsKey(selectedField))   {
                    projectFieldsToOutputMapping.put(selectedField, projectFieldsToDisplayMapping.get(selectedField));
                    projectFieldsToDisplayMapping.remove(selectedField);
                }
            }
        }

        List<MilestonesDetailDTO> inputList = new ArrayList<MilestonesDetailDTO>();
        for (String key : projectFieldsToDisplayMapping.keySet()) {
            inputList.add(new MilestonesDetailDTO(key, projectFieldsToDisplayMapping.get(key)));
        }

        List<MilestonesDetailDTO> outputList = new ArrayList<MilestonesDetailDTO>();
        for (String key : projectFieldsToOutputMapping.keySet()) {
            outputList.add(new MilestonesDetailDTO(key, projectFieldsToOutputMapping.get(key)));
        }
        milestonesDTO.setInputFields(inputList);
        milestonesDTO.setOutputFields(outputList);
        milestonesDTO.setCanUpdate(!permissionService.checkWorkgroupLevelCannotPrivilege(
                PermissionID.MANAGE_PROJECT_MILESTONES, currentWorkgroupId, currentUserId));

        return milestonesDTO;
    }

    public void setMilestones(MilestonesUpdateDTO milestonesUpdateDTO, Long currentWorkgroupId, Long currentUserId) {
        if(permissionService.checkWorkgroupLevelCannotPrivilege(
                PermissionID.MANAGE_PROJECT_MILESTONES, currentWorkgroupId, currentUserId)) {
            throw new NoPermissionException("You don't have permission to update the project milestones!");
        }
        if (milestonesUpdateDTO.getOutputFields() != null) {
            String preferenceValues = "";
            for (String key : milestonesUpdateDTO.getOutputFields()) {
                preferenceValues += key + ":";
            }
            Map mileStones = new HashMap();
            mileStones.put(WORKGROUP_PRODUCTION_MATRIX_FIELDS, preferenceValues.length() > 0
                    ? preferenceValues.substring(0, preferenceValues.length() - 1) : "");
            preferenceService.updatePreferencesForWorkgroup(currentWorkgroupId, currentUserId, mileStones);
        }
    }
}
