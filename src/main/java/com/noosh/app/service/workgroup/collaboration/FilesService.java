package com.noosh.app.service.workgroup.collaboration;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.document.TgTagDTO;
import com.noosh.app.commons.dto.workgroup.collaboration.FilesDTO;
import com.noosh.app.commons.entity.document.TgTag;
import com.noosh.app.mapper.document.TgTagMapper;
import com.noosh.app.repository.jpa.document.TgTagRepository;
import com.noosh.app.service.preference.PreferenceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.noosh.app.exception.ForbiddenException;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.commons.constant.StringID;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 4/12/22
 */
@Service
@Transactional
public class FilesService {
    @Autowired
    private TgTagRepository tgTagRepository;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private TgTagMapper tgTagMapper;

    public FilesDTO getFilesConfig(Long currentWorkgroupId, Long currentUserId) {
        List<String> preferenceIds = new ArrayList<String>();
        FilesDTO filesDTO = new FilesDTO();

        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_DEFAULT_SHARING);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_DEFAULT_DOCUMENT_PRIVILEGES);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_ENABLE_FTP_FILE_UPLOAD);
        preferenceIds.add(PreferenceID.FTP_DEFAULT_SETTINGS_URL);
        preferenceIds.add(PreferenceID.FTP_DEFAULT_SETTINGS_PROTOCOL);
        preferenceIds.add(PreferenceID.FTP_DEFAULT_SETTINGS_HOST);
        preferenceIds.add(PreferenceID.FTP_DEFAULT_SETTINGS_PORT);
        preferenceIds.add(PreferenceID.FTP_DEFAULT_SETTINGS_PATH);
        preferenceIds.add(PreferenceID.FTP_DEFAULT_SETTINGS_LOGINNAME);
        preferenceIds.add(PreferenceID.FTP_DEFAULT_SETTINGS_PASSWORD);
        preferenceIds.add(PreferenceID.FTP_DEFAULT_IS_ENABLE_OVERRIDE_PL_HT_PT);
        preferenceIds.add(PreferenceID.FTP_DEFAULT_IS_ENABLE_OVERRIDE_PH);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_ENABLE_NGE_FILE_UI);

        Map<String, String> preferenceMap = preferenceService.findGroupPrefs(
                currentWorkgroupId, preferenceIds);
        if (!preferenceMap.isEmpty()) {
            filesDTO.setSharingSetting(preferenceMap.containsKey(PreferenceID.WORKGROUP_OPTION_DEFAULT_SHARING)
                    ? preferenceMap.get(PreferenceID.WORKGROUP_OPTION_DEFAULT_SHARING) : "self");
            filesDTO.setPrivilegesSetting(preferenceMap.containsKey(PreferenceID.WORKGROUP_OPTION_DEFAULT_DOCUMENT_PRIVILEGES)
                    ? preferenceMap.get(PreferenceID.WORKGROUP_OPTION_DEFAULT_DOCUMENT_PRIVILEGES) : "1");
            filesDTO.setIsFtpFileUpload(preferenceMap.containsKey(PreferenceID.WORKGROUP_OPTION_ENABLE_FTP_FILE_UPLOAD)
                    ? "1".equalsIgnoreCase(preferenceMap.get(PreferenceID.WORKGROUP_OPTION_ENABLE_FTP_FILE_UPLOAD)) : false);
            filesDTO.setFtpURL(preferenceMap.get(PreferenceID.FTP_DEFAULT_SETTINGS_URL));
            filesDTO.setFtpProtocol(preferenceMap.get(PreferenceID.FTP_DEFAULT_SETTINGS_PROTOCOL));
            filesDTO.setFtpHost(preferenceMap.get(PreferenceID.FTP_DEFAULT_SETTINGS_HOST));
            filesDTO.setFtpPort(preferenceMap.get(PreferenceID.FTP_DEFAULT_SETTINGS_PORT));
            filesDTO.setFtpPath(preferenceMap.get(PreferenceID.FTP_DEFAULT_SETTINGS_PATH));
            filesDTO.setFtpLoginName(preferenceMap.get(PreferenceID.FTP_DEFAULT_SETTINGS_LOGINNAME));
            filesDTO.setFtpLoginPassword(preferenceMap.get(PreferenceID.FTP_DEFAULT_SETTINGS_PASSWORD));
            filesDTO.setIsEnableForPLHTPT(preferenceMap.containsKey(PreferenceID.FTP_DEFAULT_IS_ENABLE_OVERRIDE_PL_HT_PT)
                    ? "1".equalsIgnoreCase(preferenceMap.get(PreferenceID.FTP_DEFAULT_IS_ENABLE_OVERRIDE_PL_HT_PT)) : false);
            filesDTO.setIsEnableForPH(preferenceMap.containsKey(PreferenceID.FTP_DEFAULT_IS_ENABLE_OVERRIDE_PH)
                    ? "1".equalsIgnoreCase(preferenceMap.get(PreferenceID.FTP_DEFAULT_IS_ENABLE_OVERRIDE_PH)) : false);
            filesDTO.setIsNewFileUI(preferenceMap.containsKey(PreferenceID.WORKGROUP_OPTION_ENABLE_NGE_FILE_UI)
                    ? "1".equalsIgnoreCase(preferenceMap.get(PreferenceID.WORKGROUP_OPTION_ENABLE_NGE_FILE_UI)) : false);
            if (filesDTO.getIsNewFileUI()) {
                List<TgTag> tags = tgTagRepository.findByOwnerWgIdAndObjectClassId(
                        currentWorkgroupId, ObjectClassID.DOCUMENT, true);
                if (tags != null && tags.size() > 0) {
                    filesDTO.setTgTags(tgTagMapper.toDtos(tags));
                } else {
                    filesDTO.setTgTags(new ArrayList<TgTagDTO>());
                }
            }
        }

        return filesDTO;
    }

    public void saveFilesConfig(FilesDTO filesDTO, Long currentWorkgroupId, Long currentUserId) {
        Map<String, String> preferenceMap = new HashMap<String, String>();
        if (filesDTO.getSharingSetting() != null && filesDTO.getSharingSetting().trim().length() > 0) {
            preferenceMap.put(PreferenceID.WORKGROUP_OPTION_DEFAULT_SHARING, filesDTO.getSharingSetting());
        }
        if (filesDTO.getPrivilegesSetting() != null && filesDTO.getPrivilegesSetting().trim().length() > 0) {
            preferenceMap.put(PreferenceID.WORKGROUP_OPTION_DEFAULT_DOCUMENT_PRIVILEGES, filesDTO.getPrivilegesSetting());
        }

        List<String> preferenceIds = new ArrayList<String>();
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_ENABLE_FTP_FILE_UPLOAD);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_ENABLE_NGE_FILE_UI);

        Map<String, String> resultsMap = preferenceService.findUserPrefs(
                currentWorkgroupId, currentUserId, preferenceIds);
        if (resultsMap.containsKey(PreferenceID.WORKGROUP_OPTION_ENABLE_FTP_FILE_UPLOAD)
                && resultsMap.get(PreferenceID.WORKGROUP_OPTION_ENABLE_FTP_FILE_UPLOAD) != null
                && "1".equals(resultsMap.get(PreferenceID.WORKGROUP_OPTION_ENABLE_FTP_FILE_UPLOAD))) {
            if (filesDTO.getFtpURL() != null) {
                preferenceMap.put(PreferenceID.FTP_DEFAULT_SETTINGS_URL, filesDTO.getFtpURL());
            }
            if (filesDTO.getFtpProtocol() != null) {
                if (filesDTO.getFtpProtocol().length() == 0) {
                    throw new IllegalArgumentException("Protocol is required");
                }
                if (filesDTO.getFtpProtocol().length() > 4000) {
                    throw new IllegalArgumentException("Protocol is larger than 4000");
                }
                preferenceMap.put(PreferenceID.FTP_DEFAULT_SETTINGS_PROTOCOL, filesDTO.getFtpProtocol());
            }
            if (filesDTO.getFtpHost() != null) {
                if (filesDTO.getFtpHost().length() == 0) {
                    throw new IllegalArgumentException("Host is required");
                }
                if (filesDTO.getFtpHost().length() > 4000) {
                    throw new IllegalArgumentException("Host is larger than 4000");
                }
                preferenceMap.put(PreferenceID.FTP_DEFAULT_SETTINGS_HOST, filesDTO.getFtpHost());
            }
            if (filesDTO.getFtpPort() != null) {
                if (filesDTO.getFtpPort().length() == 0) {
                    throw new IllegalArgumentException("Port is required");
                }
                if (filesDTO.getFtpPort().length() > 4000) {
                    throw new IllegalArgumentException("Port is larger than 4000");
                }
                preferenceMap.put(PreferenceID.FTP_DEFAULT_SETTINGS_PORT, filesDTO.getFtpPort());
            }
            if (filesDTO.getFtpPath() != null) {
                if (filesDTO.getFtpPath().length() == 0) {
                    throw new IllegalArgumentException("Path is required");
                }
                if (filesDTO.getFtpPath().length() > 4000) {
                    throw new IllegalArgumentException("Path is larger than 4000");
                }
                preferenceMap.put(PreferenceID.FTP_DEFAULT_SETTINGS_PATH, filesDTO.getFtpPath());
            }
            if (filesDTO.getFtpLoginName() != null) {
                preferenceMap.put(PreferenceID.FTP_DEFAULT_SETTINGS_LOGINNAME, filesDTO.getFtpLoginName());
            }
            if (filesDTO.getFtpLoginPassword() != null) {
                preferenceMap.put(PreferenceID.FTP_DEFAULT_SETTINGS_PASSWORD, filesDTO.getFtpLoginPassword());
            }
            if (filesDTO.getIsEnableForPLHTPT() != null) {
                preferenceMap.put(PreferenceID.FTP_DEFAULT_IS_ENABLE_OVERRIDE_PL_HT_PT, filesDTO.getIsEnableForPLHTPT() ? "1" : "0");
            }
            if (filesDTO.getIsEnableForPH() != null) {
                preferenceMap.put(PreferenceID.FTP_DEFAULT_IS_ENABLE_OVERRIDE_PH, filesDTO.getIsEnableForPH() ? "1" : "0");
            }
        }

        preferenceService.updatePreferencesForWorkgroup(currentWorkgroupId, currentUserId, preferenceMap);

//        if (resultsMap.containsKey(PreferenceID.WORKGROUP_OPTION_ENABLE_NGE_FILE_UI)
//                && resultsMap.get(PreferenceID.WORKGROUP_OPTION_ENABLE_NGE_FILE_UI) != null
//                && "1".equals(resultsMap.get(PreferenceID.WORKGROUP_OPTION_ENABLE_NGE_FILE_UI))) {
//            if (filesDTO.getTgTags() != null && filesDTO.getTgTags().size() > 0) {
//                List<TgTag> tags = tgTagRepository.findByOwnerWgIdAndObjectClassId(
//                        currentWorkgroupId, ObjectClassID.DOCUMENT, true);
//                List<Long> tagIds = new ArrayList<Long>();
//                for (TgTagDTO tgTagDTO : filesDTO.getTgTags()) {
//                    if (tgTagDTO.getId() != null && tgTagDTO.getId() > 0) {
//                        List<TgTag> tgTagList = tgTagRepository.findByNameAndWorkgroupId(
//                                currentWorkgroupId, ObjectClassID.DOCUMENT, true, tgTagDTO.getName());
//                        if(tgTagList != null && tgTagList.size() > 0) {
//                            if ((!(tgTagDTO.getId() != null && tgTagList.stream().filter(
//                                    t -> t.getId() == tgTagDTO.getId().longValue()).findAny().isPresent()))
//                                    || tgTagDTO.getId() == null) {
//                                throw new ForbiddenException(String.valueOf(StringID.PM_DUPLICATE_TAG_NAME));
//                            }
//                        }
//                        tagIds.add(tgTagDTO.getId());
//                        TgTag tgTag = tgTagRepository.findById(tgTagDTO.getId()).orElse(null);
//                        if (tgTag == null) {
//                            throw new NotFoundException("No Tag found");
//                        }
//                        tgTag.setName(tgTagDTO.getName());
//                        tgTag.setIsActive(tgTagDTO.getIsActive());
//                        tgTag.setModDate(LocalDateTime.now());
//                        tgTag.setModUserId(currentUserId);
//                        tgTagRepository.save(tgTag);
//                    } else {
//                        TgTag tgTag = tgTagMapper.toEntity(tgTagDTO);
//                        tgTag.setId(null);
//                        tgTag.setCreateDate(LocalDateTime.now());
//                        tgTag.setCreateUserId(currentUserId);
//                        tgTag.setModDate(LocalDateTime.now());
//                        tgTag.setModUserId(currentUserId);
//                        tgTagRepository.save(tgTag);
//                    }
//                }
//                if (tagIds.size() > 0 && tags != null && tags.size() > 0) {
//                    for (TgTag tgTag : tags) {
//                        if (!tagIds.contains(tgTag.getId())) {
//                            tgTag.setModDate(LocalDateTime.now());
//                            tgTag.setModUserId(currentUserId);
//                            tgTag.setIsActive(false);
//                            tgTagRepository.save(tgTag);
//                        }
//                    }
//                }
//            }
//        }

    }

    public void fileTagsInlineEdit(TgTagDTO tagDTO, Long currentWorkgroupId, Long currentUserId) {
        if (preferenceService.check(PreferenceID.WORKGROUP_OPTION_ENABLE_NGE_FILE_UI, currentWorkgroupId)) {
            // Do delete
            if (tagDTO.getId() != null && (!tagDTO.getIsActive())) {
                TgTag tgTag = tgTagRepository.findById(tagDTO.getId()).orElse(null);
                if (tgTag == null) {
                    throw new NotFoundException("No Tag found");
                }
                tgTag.setModDate(LocalDateTime.now());
                tgTag.setModUserId(currentUserId);
                tgTag.setIsActive(false);
                tgTagRepository.save(tgTag);
            } else {
                List<TgTag> tags = tgTagRepository.findByOwnerWgIdAndObjectClassId(
                        currentWorkgroupId, ObjectClassID.DOCUMENT, true);
                if (tags.stream().filter(
                        t -> t.getName().equalsIgnoreCase(tagDTO.getName()) && (tagDTO.getId() == null || t.getId().longValue() != tagDTO.getId())).findAny().isPresent()) {
                    throw new ForbiddenException(String.valueOf(StringID.PM_DUPLICATE_TAG_NAME));
                }
                // Do add new
                if (tagDTO.getId() == null) {
                    TgTag tgTag = tgTagMapper.toEntity(tagDTO);
                    tgTag.setId(null);
                    tgTag.setCreateDate(LocalDateTime.now());
                    tgTag.setCreateUserId(currentUserId);
                    tgTag.setModDate(LocalDateTime.now());
                    tgTag.setModUserId(currentUserId);
                    tgTag.setOwnerWorkgroupId(currentWorkgroupId);
                    tgTag.setObjectClassId(ObjectClassID.DOCUMENT);
                    tgTagRepository.save(tgTag);
                } else {
                    // Do update
                    TgTag tgTag = tgTagRepository.findById(tagDTO.getId()).orElse(null);
                    if (tgTag == null) {
                        throw new NotFoundException("No Tag found");
                    }
                    tgTag.setName(tagDTO.getName());
                    tgTag.setModDate(LocalDateTime.now());
                    tgTag.setModUserId(currentUserId);
                    tgTagRepository.save(tgTag);
                }
            }
        }
    }
}
