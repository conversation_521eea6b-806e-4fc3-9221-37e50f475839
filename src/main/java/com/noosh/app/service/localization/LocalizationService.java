package com.noosh.app.service.localization;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.noosh.app.WorkgroupResourceApplication;
import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.RoleID;
import com.noosh.app.commons.constant.TeamID;
import com.noosh.app.commons.constant.TrackingTypeID;
import com.noosh.app.commons.dto.localization.LocalizationDTO;
import com.noosh.app.commons.dto.localization.LocalizationListDTO;
import com.noosh.app.commons.dto.localization.LocalizationUpdatedDTO;
import com.noosh.app.commons.dto.localization.PortalDTO;
import com.noosh.app.commons.dto.tracking.WGTrackingDTO;
import com.noosh.app.commons.entity.localization.Localization;
import com.noosh.app.commons.entity.localization.Portal;
import com.noosh.app.commons.entity.team.TeamMember;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.localization.PortalConfirmedVO;
import com.noosh.app.config.portal.service.PortalService;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.exception.UnexpectedException;
import com.noosh.app.feign.FileResourceFeignClient;
import com.noosh.app.mapper.localization.LocalizationMapper;
import com.noosh.app.mapper.localization.PortalMapper;
import com.noosh.app.repository.jpa.localization.LocalizationRepository;
import com.noosh.app.repository.jpa.localization.PortalRepository;
import com.noosh.app.repository.jpa.team.TeamMemberRepository;
import com.noosh.app.repository.mybatis.localization.LocalizationMyBatisMapper;
import com.noosh.app.resource.localization.LocalizationResource;
import com.noosh.app.service.tracking.LocalizationTrackingService;
import org.apache.commons.collections4.map.LinkedMap;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;

/**
 * User: leilaz
 * Date: 11/22/23
 */
@Service
public class LocalizationService {
    private final Logger logger = LoggerFactory.getLogger(LocalizationService.class);
    @Autowired
    private LocalizationMapper localizationMapper;
    @Autowired
    private LocalizationRepository localizationRepository;
    @Autowired
    private TeamMemberRepository teamMemberRepository;
    @Autowired
    private LocalizationTrackingService localizationTrackingService;
    @Autowired
    private LocalizationMyBatisMapper localizationMyBatisMapper;
    @Autowired
    private PortalRepository portalRepository;
    @Autowired
    private PortalMapper portalMapper;
    @Autowired
    private FileResourceFeignClient fileResourceFeignClient;
    @Autowired
    private PortalService portalService;

    public LocalizationListDTO saveLocalization(LocalizationUpdatedDTO updatedDTO, Long currentUserId, Long currentWorkgroupId) {
        //First check the permission for current user, it should be admin manager
        TeamMember teamMember = teamMemberRepository.findByTeamIdAndUserId(TeamID.ADMIN_TEAM, currentUserId);
        if (!(teamMember != null && teamMember.getRoleId() == RoleID.ADMIN_MANAGER)) {
            throw new NoPermissionException("You don't have the permission to view the localization!");
        }
        LocalizationListDTO localizationListDTO = new LocalizationListDTO();
        localizationListDTO.setIsDuplicated(false);

        if (updatedDTO == null || updatedDTO.getLanguage() == null
                || updatedDTO.getResourceName() == null || updatedDTO.getResourceValue() == null) {
            throw new IllegalArgumentException("Please pass the valid parameter!");
        }

        if (updatedDTO.getPortal() == null) {
            updatedDTO.setPortal("default");
        }

        Localization savedLocalization = null;
        if (updatedDTO.getId() != null && updatedDTO.getId() > 0) {
            savedLocalization = localizationRepository.findById(updatedDTO.getId()).orElse(null);
            if (savedLocalization == null) {
                throw new IllegalArgumentException("Please pass the correct id");
            }
        }
        List<Localization> localizationList = localizationRepository.findByResourceNameAndPortalAndLanguage(
                updatedDTO.getResourceName(), updatedDTO.getPortal(), updatedDTO.getLanguage());
        if (localizationList != null && localizationList.size() > 0) {

            // Skip check if user just save without change
            if ((!updatedDTO.getIsConfirmed()) && !(localizationList.size() == 1 && savedLocalization != null
                    && savedLocalization.getId().longValue() == localizationList.get(0).getId())) {
                localizationListDTO.setIsDuplicated(true);
                localizationListDTO.setDuplicateList(localizationMapper.toDtos(localizationList));
            } else {
                for (Localization localization : localizationList) {
                    String oldResourceName = localization.getResourceName();
                    String oldResourceValue = localization.getResourceValue();
                    localization.setModDate(LocalDateTime.now());
                    localization.setModUserId(currentUserId);
                    localization.setLanguage(updatedDTO.getLanguage());
                    localization.setPortal(updatedDTO.getPortal());
                    localization.setResourceName(updatedDTO.getResourceName());
                    localization.setResourceValue(updatedDTO.getResourceValue());
                    localizationRepository.save(localization);
                    Map<String, String> i18nMap = new HashMap<String, String>();
                    i18nMap.put("oldResourceName", oldResourceName);
                    i18nMap.put("newResourceName", updatedDTO.getResourceName());
                    i18nMap.put("oldResourceValue", oldResourceValue);
                    i18nMap.put("newResourceValue", updatedDTO.getResourceValue());
                    i18nMap.put("language", updatedDTO.getLanguage());
                    i18nMap.put("portal", updatedDTO.getPortal());
                    localizationTrackingService.buildAndSaveTracking(TrackingTypeID.LOCALIZATION_FIELD_UPDATED,
                            currentUserId, currentWorkgroupId, ObjectClassID.WORKGROUP, i18nMap, currentUserId, null);
                }
            }
        } else {
            if (savedLocalization != null) {
                //Do update
                String oldResourceName = savedLocalization.getResourceName();
                String oldResourceValue = savedLocalization.getResourceValue();
                savedLocalization.setModDate(LocalDateTime.now());
                savedLocalization.setModUserId(currentUserId);
                savedLocalization.setLanguage(updatedDTO.getLanguage());
                savedLocalization.setPortal(updatedDTO.getPortal());
                savedLocalization.setResourceName(updatedDTO.getResourceName());
                savedLocalization.setResourceValue(updatedDTO.getResourceValue());
                localizationRepository.save(savedLocalization);
                Map<String, String> i18nMap = new HashMap<String, String>();
                i18nMap.put("oldResourceName", oldResourceName);
                i18nMap.put("newResourceName", updatedDTO.getResourceName());
                i18nMap.put("oldResourceValue", oldResourceValue);
                i18nMap.put("newResourceValue", updatedDTO.getResourceValue());
                i18nMap.put("language", updatedDTO.getLanguage());
                i18nMap.put("portal", updatedDTO.getPortal());
                localizationTrackingService.buildAndSaveTracking(TrackingTypeID.LOCALIZATION_FIELD_UPDATED,
                        currentUserId, currentWorkgroupId, ObjectClassID.WORKGROUP, i18nMap, currentUserId, null);
            } else {
                // Do create
                Localization localization = localizationMapper.toEntity(updatedDTO);
                localization.setCreateDate(LocalDateTime.now());
                localization.setCreateUserId(currentUserId);
                if (localization != null) {
                    localizationRepository.save(localization);
                    Map<String, String> i18nMap = new HashMap<String, String>();
                    i18nMap.put("resourceName", updatedDTO.getResourceName());
                    i18nMap.put("resourceValue", updatedDTO.getResourceValue());
                    i18nMap.put("language", updatedDTO.getLanguage());
                    i18nMap.put("portal", updatedDTO.getPortal());
                    localizationTrackingService.buildAndSaveTracking(TrackingTypeID.LOCALIZATION_FIELD_CREATED,
                            currentUserId, currentWorkgroupId, ObjectClassID.WORKGROUP, i18nMap, currentUserId, null);
                }
            }
        }
        if ((!localizationListDTO.getIsDuplicated()) && (localizationListDTO.getDuplicateList() == null || localizationListDTO.getDuplicateList().size() == 0)) {
//            List<LocalizationDTO> localizationDTOs = localizationMyBatisMapper.getFullLocalization(updatedDTO.getPortal(), updatedDTO.getLanguage(), true, null);
            List<Localization> currentLocalizations = localizationRepository.findByPortalAndLanguage(updatedDTO.getPortal(), updatedDTO.getLanguage());
            if (currentLocalizations != null && currentLocalizations.size() > 0) {
                JSONObject jsonObject = new JSONObject();
                for (Localization localization : currentLocalizations) {
                    jsonObject.put(localization.getResourceName(), localization.getResourceValue());
                }
                if (jsonObject != null) {
                    String jsonString = jsonObject.toString();
                    fileResourceFeignClient.uploadFileWithBytes(jsonString.getBytes(), WorkgroupResourceApplication.getInstance() + "/"
                            + updatedDTO.getPortal() + "/" + updatedDTO.getLanguage(), WorkgroupResourceApplication.getLocalizationBucketName(),
                            "locale.json", WorkgroupResourceApplication.getLocalizationRegion());
                }
            }
        }
        return localizationListDTO;
    }

    public LocalizationListDTO getLocalizationList(String portal, String language, String resourceName,
                                                   String resourceValue, PageVO page, Long currentUserId) {
        //First check the permission for current user, it should be admin manager
        TeamMember teamMember = teamMemberRepository.findByTeamIdAndUserId(TeamID.ADMIN_TEAM, currentUserId);
        if (!(teamMember != null && teamMember.getRoleId() == RoleID.ADMIN_MANAGER)) {
            throw new NoPermissionException("You don't have the permission to view the localization!");
        }

        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        pageInfo.setUnsafeOrderBy(page.getOrderByNoNull());
        List<LocalizationDTO> localizationDTOs = localizationMyBatisMapper.getLocalizationList(
                portal, language, resourceName, resourceValue, null);
        page.setTotal(pageInfo.getTotal());

        LocalizationListDTO localizationListDTO = new LocalizationListDTO();
        localizationListDTO.setTotalList(localizationDTOs);
        return localizationListDTO;
    }

    public LocalizationListDTO saveExcel(Long userId, String portal, String language, MultipartFile file, boolean isConfirmed, Long workgroupId) {
        if (portal == null || language == null) {
            throw new IllegalArgumentException("Please pass the valid parameter!");
        }
        //First check the permission for current user, it should be admin manager
        TeamMember teamMember = teamMemberRepository.findByTeamIdAndUserId(TeamID.ADMIN_TEAM, userId);
        if (!(teamMember != null && teamMember.getRoleId() == RoleID.ADMIN_MANAGER)) {
            throw new NoPermissionException("You don't have the permission to view the localization!");
        }
        List<String> fieldsList = new ArrayList<String>();

        Workbook workbook = null;
        List<Localization> newLocalizations = new ArrayList<Localization>();
        List<Localization> existingLocalizations = new ArrayList<Localization>();
        LocalizationListDTO localizationListDTO = new LocalizationListDTO();

        //First load the whole existing resource items
        List<Localization> currentLocalizations = localizationRepository.findByPortalAndLanguage(portal, language);

        //Read the excel
        try {
            //Get the workbook instance for XLS file
            workbook = WorkbookFactory.create(file.getInputStream());

            //Get first sheet from the workbook
            Sheet sheet = workbook.getSheetAt(0);

            //Get all the rows in current sheet
            int rowSize = sheet.getPhysicalNumberOfRows();
            int cellSize = 0;
            for ( int r = 0; r < rowSize; r ++) {
                Row row = sheet.getRow(r);
                if (row == null) continue;
                //Go through each columns
                if (r == 0) {
                    cellSize = row.getPhysicalNumberOfCells();
                }
                if (cellSize < 2) {
                    throw new IllegalArgumentException("Please pass the correct translate file!");
                }
                Cell cellResourceName = row.getCell(0);
                Cell cellResourceValue = row.getCell(1);
                if (!StringUtils.hasLength(getCellValue(cellResourceName).toString())) {
                    continue;
                }
                if (r == 0) {
                    if (!StringUtils.hasLength(getCellValue(cellResourceValue).toString())) {
                        throw new IllegalArgumentException("Please make sure the resource value is in the second column!");
                    }
                    //Check if the first column is resource name, second column is resource value
                    if (!"resource name".equalsIgnoreCase(getCellValue(cellResourceName).toString())) {
                        throw new IllegalArgumentException("Please make sure the resource name is in the first column!");
                    }
                    if (!"resource value".equalsIgnoreCase(getCellValue(cellResourceValue).toString())) {
                        throw new IllegalArgumentException("Please make sure the resource value is in the second column!");
                    }
                }
                if (r > 0) {
                    String resourceName = getCellValue(cellResourceName).toString();
                    String resourceValue = getCellValue(cellResourceValue).toString();
                    if (!StringUtils.hasLength(resourceValue)) {
                        continue;
                    }
                    // Before save, need to check if there has duplicate field
                    if (fieldsList.contains(resourceName + "," + language + "," + portal)) {
                        throw new IllegalArgumentException("There has duplicate field: Resource Name: " + resourceName
                                + ", Resource Value: " + resourceValue + ", Language: " + language + ", Portal: " + portal);
                    } else {
                        fieldsList.add(resourceName + "," + language + "," + portal);
                    }
                    Localization current = currentLocalizations.stream().filter(c -> c.getResourceName().equals(resourceName)).findAny().orElse(null);
                    if (current != null) {
                        if (isConfirmed) {
                            current.setModUserId(userId);
                            current.setModDate(LocalDateTime.now());
                            current.setResourceValue(resourceValue);
                            current.setResourceName(resourceName);
                        }
                        existingLocalizations.add(current);
                    } else {
                        Localization localization = new Localization();
                        localization.setLanguage(language);
                        localization.setPortal(portal);
                        localization.setCreateUserId(userId);
                        localization.setModDate(LocalDateTime.now());
                        localization.setModUserId(userId);
                        localization.setCreateDate(LocalDateTime.now());
                        localization.setResourceValue(resourceValue);
                        localization.setResourceName(resourceName);
                        newLocalizations.add(localization);
                    }
                }
            }
            boolean isNeedTracking = false;
            if (!isConfirmed) {
                if (newLocalizations.size() > 0) {
                    localizationListDTO.setNewResourceCount((long)newLocalizations.size());
                } else {
                    localizationListDTO.setNewResourceCount(0L);
                }
                if (existingLocalizations.size() > 0) {
                    localizationListDTO.setIsDuplicated(true);
                    localizationListDTO.setDuplicateList(localizationMapper.toDtos(existingLocalizations));
                }
            } else {
                isNeedTracking = true;
                if(newLocalizations.size() > 0) {
                    localizationRepository.saveAll(newLocalizations);
                }
                if (existingLocalizations.size() > 0) {
                    localizationRepository.saveAll(existingLocalizations);
                }
            }
            if (isNeedTracking) {
                String fileName = file.getOriginalFilename().substring(0, file.getOriginalFilename().lastIndexOf(".")).replaceAll(" ", "_");
                String responseData = fileResourceFeignClient.uploadFile(file, "localization", fileName + "-" + System.currentTimeMillis());
                if (StringUtils.hasLength(responseData)) {
                    JSONObject jsonObject = new JSONObject(responseData);
                    if (jsonObject.has("data")) {
                        Map<String, String> i18nMap = new HashMap<String, String>();
                        i18nMap.put("resourceDownloadPath", jsonObject.getString("data"));
                        i18nMap.put("resourceFileRowCount", rowSize + "");
                        i18nMap.put("language", language);
                        i18nMap.put("portal", portal);
                        i18nMap.put("newResourceCount", "" + (newLocalizations != null ? newLocalizations.size() : 0));
                        i18nMap.put("duplicatedCount", "" + (existingLocalizations != null ? existingLocalizations.size() : 0));
                        localizationTrackingService.buildAndSaveTracking(TrackingTypeID.LOCALIZATION_FILE_UPLOAD,
                                userId, workgroupId, ObjectClassID.WORKGROUP, i18nMap, userId, null);
                    }
                }
                //Load latest localization and store it into the s3 as json
//                List<LocalizationDTO> localizationDTOs = localizationMyBatisMapper.getFullLocalization(portal, language, true, null);
                JSONObject jsonObject = new JSONObject();
                for (Localization localization : currentLocalizations) {
                    jsonObject.put(localization.getResourceName(), localization.getResourceValue());
                }
                for (Localization localization : newLocalizations) {
                    jsonObject.put(localization.getResourceName(), localization.getResourceValue());
                }
                if (jsonObject != null) {
                    String jsonString = jsonObject.toString();
                    fileResourceFeignClient.uploadFileWithBytes(jsonString.getBytes(), WorkgroupResourceApplication.getInstance() + "/"
                            + portal + "/" + language, WorkgroupResourceApplication.getLocalizationBucketName(),
                            "locale.json", WorkgroupResourceApplication.getLocalizationRegion());
                }
            }
        } catch (NotFoundException e) {
            throw e;
        } catch (IllegalArgumentException e) {
            throw e;
        } catch (Exception ioe) {
            throw new IllegalArgumentException("Error when read excel!");
        } finally {
            try {
                if (workbook != null) {
                    workbook.close();
                }
            } catch (IOException io) {
                throw new UnexpectedException(io.getMessage());
            }
        }
        return localizationListDTO;
    }

    public HSSFWorkbook exportLocalization(Long userId, String portal, String language, List<Long> ids, boolean isFromSchedule) {
        //First check the permission for current user, it should be admin manager
        if (!isFromSchedule) {
            TeamMember teamMember = teamMemberRepository.findByTeamIdAndUserId(TeamID.ADMIN_TEAM, userId);
            if (!(teamMember != null && teamMember.getRoleId() == RoleID.ADMIN_MANAGER)) {
                throw new NoPermissionException("You don't have the permission to view the localization!");
            }
        }

        List<LocalizationDTO> localizationDTOs = localizationMyBatisMapper.getFullLocalization(portal, language, false, ids);

        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet();
        sheet.autoSizeColumn(1);

        HSSFFont font = workbook.createFont();
        font.setBold(true);
        HSSFCellStyle cellStyle1 = workbook.createCellStyle();
        cellStyle1.setFont(font);
        cellStyle1.setAlignment(HorizontalAlignment.LEFT);

        HSSFCellStyle cellStyle2 = workbook.createCellStyle();
        cellStyle2.setAlignment(HorizontalAlignment.LEFT);

        // Handle for title, resource name, resource value
        HSSFRow row = sheet.createRow(0);
        setCellValue(row, cellStyle1, "Resource Name", 0) ;
        setCellValue(row, cellStyle1, "Resource Value", 1) ;

        if (localizationDTOs != null && localizationDTOs.size() > 0) {
            for (int i = 0; i < localizationDTOs.size(); i++) {
                if (localizationDTOs.get(i) != null) {
                    row = sheet.createRow(i+1);
                    setCellValue(row, cellStyle2, localizationDTOs.get(i).getResourceName(), 0) ;
                    setCellValue(row, cellStyle2, localizationDTOs.get(i).getResourceValue(), 1) ;
                }
            }
        }
        return workbook;
    }

    public void setCellValue(HSSFRow row, HSSFCellStyle cellStyle, Object value, int num) {
        HSSFCell cell = row.createCell(num);
        cell.setCellStyle(cellStyle);
        cell.setCellValue(value != null ? value.toString() : "");
    }

    public Object getCellValue(Cell cell) {
        if (cell != null) {
            switch (cell.getCellType()) {
                case BOOLEAN:
                    return cell.getBooleanCellValue();
                case NUMERIC:
                    return format(cell.getNumericCellValue() + "");
                case STRING:
                    return cell.getStringCellValue();
                case BLANK:
                    return "";
                case ERROR:
                    return cell.getErrorCellValue();
                default:
                    return "";
            }
        }
        return "";
    }

    public static String format(String val){
        String stringVal = String.valueOf(val);
        String[] number = stringVal.split( "[.]" );
        if(number.length > 1 && number[1].equalsIgnoreCase("0")){
            return number[0];
        } else {
            return val;
        }
    }

    public void deleteLocalizations(List<Long> ids, Long currentUserId, Long currentWorkgroupId) {
        //First check the permission for current user, it should be admin manager
        TeamMember teamMember = teamMemberRepository.findByTeamIdAndUserId(TeamID.ADMIN_TEAM, currentUserId);
        if (!(teamMember != null && teamMember.getRoleId() == RoleID.ADMIN_MANAGER)) {
            throw new NoPermissionException("You don't have the permission to view the localization!");
        }

        if (ids == null || ids.size() == 0) {
            throw new IllegalArgumentException("Please pass the valid ids");
        }
        //Load deleted name and value first
        List<Localization> localizations = localizationRepository.findAllById(ids);
        List<String> portalLanguageList = new ArrayList<>();
        if (localizations != null) {
            for (Localization localization : localizations) {
                Map<String, String> i18nMap = new HashMap<String, String>();
                i18nMap.put("resourceName", localization.getResourceName());
                i18nMap.put("resourceValue", localization.getResourceValue());
                String language = localization.getLanguage();
                String portal = localization.getPortal();
                i18nMap.put("language", language);
                i18nMap.put("portal", portal);
                localizationTrackingService.buildAndSaveTracking(TrackingTypeID.LOCALIZATION_FIELD_DELETED,
                        currentUserId, currentWorkgroupId, ObjectClassID.WORKGROUP, i18nMap, currentUserId, null);
                String portalLanguage = localization.getPortal() + ";" + localization.getLanguage();
                if (!portalLanguageList.contains(portalLanguage)) {
                    portalLanguageList.add(portalLanguage);
                }
            }
        }
        localizationMyBatisMapper.deleteByIds(ids);
        if (portalLanguageList != null && portalLanguageList.size() > 0) {
            for (String portalLanguage : portalLanguageList) {
                String[] portalLanguageArray = portalLanguage.split(";");
                if (portalLanguageArray.length > 1) {
//                    List<LocalizationDTO> localizationDTOs = localizationMyBatisMapper.getFullLocalization(portalLanguageArray[0], portalLanguageArray[1], true, null);
                    List<Localization> currentLocalizations = localizationRepository.findByPortalAndLanguage(portalLanguageArray[0], portalLanguageArray[1]);
                    JSONObject jsonObject = new JSONObject();
                    if (currentLocalizations != null && currentLocalizations.size() > 0) {
                        for (Localization localization : currentLocalizations) {
                            jsonObject.put(localization.getResourceName(), localization.getResourceValue());
                        }
                    }
                    fileResourceFeignClient.uploadFileWithBytes(jsonObject.toString().getBytes(), WorkgroupResourceApplication.getInstance() + "/"
                            + portalLanguageArray[0] + "/" + portalLanguageArray[1], WorkgroupResourceApplication.getLocalizationBucketName(),
                            "locale.json", WorkgroupResourceApplication.getLocalizationRegion());
                }
            }
        }

    }

    public Map getMergedLocalizationList(String portal, String language) {
        long start1=System.nanoTime();
        if (language == null) {
            throw new IllegalArgumentException("Please pass the valid language!");
        }
        List<LocalizationDTO> localizationDTOs = localizationMyBatisMapper.getFullLocalization(portal, language, true, null);
        logger.info("Get mergedList Size: " + localizationDTOs.size());
        long end1=System.nanoTime();
        logger.info("Get mergedList API Sql time: " + (end1-start1)/1000000 + "ms");
        long start2=System.nanoTime();
        Map resultMap = new HashMap();
        if (localizationDTOs != null && localizationDTOs.size() > 0) {
            for (LocalizationDTO dto : localizationDTOs) {
                resultMap.put(dto.getResourceName(), dto.getResourceValue());
            }
        }
        long end2=System.nanoTime();
        logger.info("Get mergedList API Loop time: " + (end2-start2)/1000000 + "ms");
        return resultMap;
    }

    public List<PortalDTO> getPortalListWithSearch(String name, PageVO page, Long currentUserId) {
        //First check the permission for current user, it should be admin manager
        TeamMember teamMember = teamMemberRepository.findByTeamIdAndUserId(TeamID.ADMIN_TEAM, currentUserId);
        if (!(teamMember != null && teamMember.getRoleId() == RoleID.ADMIN_MANAGER)) {
            throw new NoPermissionException("You don't have the permission to view the localization!");
        }
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        pageInfo.setUnsafeOrderBy(page.getOrderByNoNull());
        List<PortalDTO> portalDTOs = localizationMyBatisMapper.getPortalList(name);
        page.setTotal(pageInfo.getTotal());
        return portalDTOs;
    }

    public List<PortalDTO> getPortalList(Long currentUserId) {
        //First check the permission for current user, it should be admin manager
        TeamMember teamMember = teamMemberRepository.findByTeamIdAndUserId(TeamID.ADMIN_TEAM, currentUserId);
        if (!(teamMember != null && teamMember.getRoleId() == RoleID.ADMIN_MANAGER)) {
            throw new NoPermissionException("You don't have the permission to view the localization!");
        }

        List<Portal> portals = portalRepository.findAllByOrderByNameAsc();
        return portalMapper.toDTOs(portals);
    }

    public void editPortal(PortalDTO portalDTO, Long currentUserId, Long currentWorkgroupId) {
        //First check the permission for current user, it should be admin manager
        TeamMember teamMember = teamMemberRepository.findByTeamIdAndUserId(TeamID.ADMIN_TEAM, currentUserId);
        if (!(teamMember != null && teamMember.getRoleId() == RoleID.ADMIN_MANAGER)) {
            throw new NoPermissionException("You don't have the permission to view the localization!");
        }

        if (portalDTO == null || portalDTO.getName() == null) {
            throw new IllegalArgumentException("Please pass the valid portal name!");
        }
        //First check if there has the same portal
        Portal existPortal = portalRepository.findByName(portalDTO.getName());
        if (existPortal != null && (portalDTO.getId() == null || (portalDTO.getId() != null && portalDTO.getId().longValue() != existPortal.getId()))) {
            throw new IllegalArgumentException("The portal name is already exist!");
        }
        if (portalDTO.getId() == null) {
            Portal newPortal = new Portal();
            newPortal.setName(portalDTO.getName());
            newPortal.setCreateDate(LocalDateTime.now());
            newPortal.setModDate(LocalDateTime.now());
            newPortal.setCreateUserId(currentUserId);
            newPortal.setModUserId(currentUserId);
            portalRepository.save(newPortal);
            Map<String, String> i18nMap = new HashMap<String, String>();
            i18nMap.put("name", portalDTO.getName());
            localizationTrackingService.buildAndSaveTracking(TrackingTypeID.PORTAL_CREATED,
                    currentUserId, currentWorkgroupId, ObjectClassID.WORKGROUP, i18nMap, currentUserId, null);
        } else {
            Portal existing = portalRepository.findById(portalDTO.getId()).orElse(null);
            if (existing == null) {
                throw new NotFoundException("Can't find the portal!");
            }
            String existingName = existing.getName();
            existing.setModDate(LocalDateTime.now());
            existing.setModUserId(currentUserId);
            if (!existingName.equals(portalDTO.getName())) {
                //Update localization
                localizationMyBatisMapper.updateByPortal(portalDTO.getName(), existingName);
                //Update portal
                existing.setName(portalDTO.getName());
            }
            portalRepository.save(existing);
            Map<String, String> i18nMap = new HashMap<String, String>();
            i18nMap.put("name", existingName);
            i18nMap.put("newName", portalDTO.getName());
            localizationTrackingService.buildAndSaveTracking(TrackingTypeID.PORTAL_UPDATED,
                    currentUserId, currentWorkgroupId, ObjectClassID.WORKGROUP, i18nMap, currentUserId, null);

        }
    }

    public PortalConfirmedVO deletePortal(Long portalId, Long currentUserId, boolean isConfirmed, Long currentWorkgroupId) {
        PortalConfirmedVO portalConfirmedVO = new PortalConfirmedVO();
        //First check the permission for current user, it should be admin manager
        TeamMember teamMember = teamMemberRepository.findByTeamIdAndUserId(TeamID.ADMIN_TEAM, currentUserId);
        if (!(teamMember != null && teamMember.getRoleId() == RoleID.ADMIN_MANAGER)) {
            throw new NoPermissionException("You don't have the permission to view the localization!");
        }
        Portal deletedPortal = portalRepository.findById(portalId).orElse(null);
        if (deletedPortal == null) {
            throw new IllegalArgumentException("Please pass the valid portal id");
        }
        List<LocalizationDTO> localizationDTOS = localizationMyBatisMapper.getLocalizationList(deletedPortal.getName(), null, null, null, null);
        if (isConfirmed || localizationDTOS == null || localizationDTOS.size() == 0) {
            portalConfirmedVO.setIsNeedConfirmed(false);
            portalConfirmedVO.setTotalResourceCount(0);
            //Delete localization which under portal first
            localizationMyBatisMapper.deleteByPortal(deletedPortal.getName());
            //Delete portal
            portalRepository.delete(deletedPortal);
            Map<String, String> i18nMap = new HashMap<String, String>();
            i18nMap.put("name", deletedPortal.getName());
            i18nMap.put("num", localizationDTOS == null ? "0" : localizationDTOS.size() + "");
            localizationTrackingService.buildAndSaveTracking(TrackingTypeID.PORTAL_DELETED,
                    currentUserId, currentWorkgroupId, ObjectClassID.WORKGROUP, i18nMap, currentUserId, null);
        } else {
            portalConfirmedVO.setIsNeedConfirmed(true);
            portalConfirmedVO.setTotalResourceCount(localizationDTOS.size());
        }
        return portalConfirmedVO;
    }


}
