package com.noosh.app.service.preference;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.entity.preference.Preference;
import com.noosh.app.commons.entity.property.PropertyAttribute;
import com.noosh.app.feign.AccountOpenFeignClient;
import com.noosh.app.repository.jpa.preference.PreferenceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <b>preference logic from NE/PreferenceBeanHome.java</b>
 * <p>
 * Group preference = systemUserPrefs + groupPrefs <br/>
 * User preference = systemUserPrefs + groupPrefs + userPrefs <br/>
 * userPrefs overrides groupPrefs, groupPrefs overrides systemUserPrefs <br/>
 * <p>
 * User: <PERSON> Shan
 * Date: 5/2/2018
 */
@Service
public class PreferenceService {

    public static final long SYSTEM_UID = 1;
    /**
     * Based on the current size of the SYSTEM user preference set.
     */
    public static final int DEFAULT_CAPACITY = 250;
    public static final int GROUP_CAPACITY = PreferenceService.DEFAULT_CAPACITY + (PreferenceService.DEFAULT_CAPACITY / 2);
    public static final int USER_CAPACITY = PreferenceService.GROUP_CAPACITY;

    @Autowired
    private PreferenceRepository preferenceRepository;

    @Autowired
    private AccountOpenFeignClient accountResourceFeignClient;

    public Map<String, String> findGroupPrefs(long groupId) {
        return accountResourceFeignClient.getPreferencesForGroup(groupId, null);
    }

    public Map<String, String> findGroupPrefs(long groupId, List<String> preferenceIds) {
        return accountResourceFeignClient.getPreferencesForGroup(groupId, preferenceIds);
    }

    public Map<String, String> findUserPrefs(long groupId, long userId) {
        return accountResourceFeignClient.getPreferencesForUser(groupId, userId, null);
    }

    public Map<String, String> findUserPrefs(long groupId, long userId, List<String> preferenceIds) {
        return accountResourceFeignClient.getPreferencesForUser(groupId, userId, preferenceIds);
    }

    @Deprecated // to be removed after we move notification to scheduledApi
    public Map<String, PropertyAttribute> getOwnerAttributesByDefaultValueForUser(Long userId, String defaultValue) {
        Preference userPreference = preferenceRepository.findByObjectClassIdAndObjectId(ObjectClassID.ACCOUNT_USER,
                userId);
        return userPreference != null ? userPreference.getProperty().getPropertyAttributeSet().stream()
                .filter(p -> p.getStringValue() != null && p.getStringValue().equalsIgnoreCase(defaultValue))
                .collect(Collectors.toMap((p) -> p.getPropertyParam().getParamName(), (p) -> p)) : new HashMap<>();
    }

    public Map<String, PropertyAttribute> getOwnerAttributesByDefaultValueForWorkgroup(Long workgroupId, String defaultValue) {
        Preference workgroupPreference = preferenceRepository.findByObjectClassIdAndObjectId(ObjectClassID.WORKGROUP,
                workgroupId);
        return workgroupPreference.getProperty().getPropertyAttributeSet().stream()
                .filter(p -> p.getStringValue() != null && p.getStringValue().equalsIgnoreCase(defaultValue))
                .collect(Collectors.toMap((p) -> p.getPropertyParam().getParamName(), (p) -> p));
    }

    @Deprecated // to be removed after we move notification to scheduledApi
    public String getValueForUser(String preferenceId, Long userId, String defaultValue) {
        Preference userPreference = preferenceRepository.findByObjectClassIdAndObjectId(ObjectClassID.ACCOUNT_USER,
                userId);
        return userPreference != null ? userPreference.getValue(preferenceId, defaultValue) : defaultValue;
    }

    public String getValueForWorkgroup(String preferenceId, Long workgroupId) {
        Preference workgroupPreference = preferenceRepository.findByObjectClassIdAndObjectId(ObjectClassID.WORKGROUP,
                workgroupId);
        return workgroupPreference.getValue(preferenceId);
    }

    public String getValueForWorkgroup(String preferenceId, Long workgroupId, String defaultValue) {
        Preference workgroupPreference = preferenceRepository.findByObjectClassIdAndObjectId(ObjectClassID.WORKGROUP,
                workgroupId);
        return workgroupPreference.getValue(preferenceId, defaultValue);
    }

    public Map<String, PropertyAttribute> getOwnerAttributesForWorkgroup(Long workgroupId) {
        Preference workgroupPreference = preferenceRepository.findByObjectClassIdAndObjectId(ObjectClassID.WORKGROUP,
                workgroupId);
        return workgroupPreference.getProperty().getPropertyAttributeSet().stream()
                .collect(Collectors.toMap((p) -> p.getPropertyParam().getParamName(), (p) -> p));
    }

    public String getString(String preferenceName, Map<String, String> prefs) {
        if (prefs.containsKey(preferenceName)) {
            return prefs.get(preferenceName);
        }
        return "0";
    }

    public String getString(String preferenceName, Map<String, String> prefs, String defaultValue) {
        if (prefs.containsKey(preferenceName)) {
            return prefs.get(preferenceName);
        }
        return defaultValue;
    }

    public boolean check(String preferenceName, Map<String, String> prefs) {
        return ("1").equals(getString(preferenceName, prefs));
    }

    public boolean check(String preferenceName, Long groupId) {
        return ("1").equals(accountResourceFeignClient.getPreferenceForGroup(preferenceName, groupId));
    }

    public boolean check(String preferenceName, Long groupId, Long userId) {
        return ("1").equals(accountResourceFeignClient.getPreferenceForUser(preferenceName, groupId, userId));
    }

    public void saveUserPreference(Long workgroupId, Long userId,Map<String, String> preferences) {
        accountResourceFeignClient.updatePreferencesForUser(workgroupId, userId, preferences);
    }

}
