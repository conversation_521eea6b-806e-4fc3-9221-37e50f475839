package com.noosh.app.service.preference;

import com.noosh.app.feign.AccountResourceFeignClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <b>preference logic from NE/PreferenceBeanHome.java</b>
 * <p>
 * Group preference = systemUserPrefs + groupPrefs <br/>
 * User preference = systemUserPrefs + groupPrefs + userPrefs <br/>
 * userPrefs overrides groupPrefs, groupPrefs overrides systemUserPrefs <br/>
 * <p>
 * User: <PERSON>
 * Date: 5/2/2018
 */
@Service
public class PreferenceService {

    public static final long SYSTEM_UID = 1;
    /**
     * Based on the current size of the SYSTEM user preference set.
     */
    public static final int DEFAULT_CAPACITY = 250;
    public static final int GROUP_CAPACITY = PreferenceService.DEFAULT_CAPACITY + (PreferenceService.DEFAULT_CAPACITY / 2);
    public static final int USER_CAPACITY = PreferenceService.GROUP_CAPACITY;

    @Autowired
    private AccountResourceFeignClient accountResourceFeignClient;

    public Map<String, String> findGroupPrefs(long groupId) {
        return accountResourceFeignClient.getPreferencesForGroup(groupId, null);
    }

    public Map<String, String> findGroupPrefs(long groupId, List<String> preferenceIds) {
        return accountResourceFeignClient.getPreferencesForGroup(groupId, preferenceIds);
    }

    public Map<Long, Map<String, String>> findGroupsPrefs(List<Long> groupIds, List<String> preferenceIds) {
        return accountResourceFeignClient.getPreferencesForGroups(groupIds, preferenceIds);
    }


    public Map<String, String> findUserPrefs(long groupId, long userId) {
        return accountResourceFeignClient.getPreferencesForUser(groupId, userId, null);
    }

    public Map<String, String> findUserPrefs(long groupId, long userId, List<String> preferenceIds) {
        return accountResourceFeignClient.getPreferencesForUser(groupId, userId, preferenceIds);
    }

    public String getString(String preferenceName, Map<String, String> prefs) {
        return getString(preferenceName, prefs, "0");
    }

    public String getString(String preferenceName, Map<String, String> prefs, String defaultValue) {
        if (prefs.containsKey(preferenceName)) {
            return prefs.get(preferenceName);
        }
        return defaultValue;
    }

    public boolean check(String preferenceName, Map<String, String> prefs) {
        return ("1").equals(getString(preferenceName, prefs));
    }

    public boolean check(String preferenceName, Long groupId) {
        return ("1").equals(accountResourceFeignClient.getPreferenceForGroup(preferenceName, groupId));
    }

    public boolean check(String preferenceName, Long groupId, Long userId) {
        return ("1").equals(accountResourceFeignClient.getPreferenceForUser(preferenceName, groupId, userId));
    }

    public boolean check(String preferenceName, Long groupId, Map<Long, Map<String, String>> groupsPrefs) {
        Map<String, String> prefs = groupsPrefs.get(groupId);
        if (prefs != null) {
            return check(preferenceName, prefs);
        }
        return false;
    }

    public int getInt(String preferenceName, Map<String, String> prefs, int defaultValue) {
        if (prefs.containsKey(preferenceName)) {
            return Integer.parseInt(prefs.get(preferenceName));
        }
        return defaultValue;
    }
    public long getLong(String preferenceName, Map<String, String> prefs, long defaultValue) {
        if (prefs.containsKey(preferenceName)) {
            return Long.parseLong(prefs.get(preferenceName));
        }
        return defaultValue;
    }

    public String getString(String preferenceName, Long groupId, Long userId) {
        return accountResourceFeignClient.getPreferenceForUser(preferenceName, groupId, userId);
    }

    public String getString(String preferenceName, Long groupId, Long userId, String defaultValue) {
        String value = accountResourceFeignClient.getPreferenceForUser(preferenceName, groupId, userId);
        if (value == null) {
            return defaultValue;
        }
        return value;
    }

    public void saveUserPreference(Long workgroupId, Long userId,Map<String, String> preferences) {
        accountResourceFeignClient.updatePreferencesForUser(workgroupId, userId, preferences);
    }

    public void updatePreferencesForWorkgroup(Long workgroupId, Long userId, Map<String, String> preferences) {
        accountResourceFeignClient.updatePreferencesForWorkgroup(workgroupId, userId, preferences);
    }

    public void setBoolean(String preferenceName, Boolean value, Map<String, String> prefs) {
        if (value == null) {
            prefs.put(preferenceName, null);
        } else {
            prefs.put(preferenceName, value ? "1" : "0");
        }
    }
    public void setNumber(String preferenceName, Number value, Map<String, String> prefs) {
        if (value == null) {
            prefs.put(preferenceName, null);
        } else {
            prefs.put(preferenceName, String.valueOf(value));
        }
    }
}
