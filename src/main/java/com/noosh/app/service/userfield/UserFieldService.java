package com.noosh.app.service.userfield;

import com.noosh.app.commons.dto.userfield.CustomFieldDTO;
import com.noosh.app.commons.entity.userfield.CustomField;
import com.noosh.app.mapper.userfield.UserFieldMapper;
import com.noosh.app.repository.jpa.userfield.CustomFieldRepository;
import com.noosh.app.service.custom.CustomAttributeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: <PERSON><PERSON>yu Hu
 * @Date: 9/9/2022
 */

@Service
public class UserFieldService {

    @Autowired
    private CustomFieldRepository customFieldRepository;

    @Autowired
    private UserFieldMapper userFieldMapper;

    @Autowired
    private CustomAttributeService customAttributeService;

    public List<CustomFieldDTO> findUserFieldDefs(Long ownerWorkgroupId, Long customFieldClassId) {
        List<CustomField> customFields = customFieldRepository.findByOwnerWorkgroupIdAndCustomFieldClassIdOrderByOrdinalNumber(ownerWorkgroupId, customFieldClassId);
        List<CustomFieldDTO> customFieldDTOs;
        customFieldDTOs = userFieldMapper.toDTOs(customFields);
        return customFieldDTOs;
    }

    public Map<Long, Map<String, Object>> findUserFieldValues(Long workgroupId, Long fieldClassId, List<Long> propertyIds, Boolean isSupplierView) {
        Map<Long, Map<String, Object>> result = new HashMap<>();
        List<CustomFieldDTO> customFieldDTOs = findUserFieldDefs(workgroupId, fieldClassId);
        if (!customFieldDTOs.isEmpty()) {
            customAttributeService.findCustomAttributes(propertyIds, null).forEach((propertyId, customAttributes) -> {
                Map<String, Object> values = new LinkedHashMap<>();
                customFieldDTOs.forEach(customFieldDTO -> {
                    String paramName = customFieldDTO.getPropertyParamName();
                    if (customAttributes.containsKey(paramName)) {
                       if (isSupplierView && customFieldDTO.getInvisibleToSupplier() != null && customFieldDTO.getInvisibleToSupplier()) return;
                        values.put(paramName, customAttributes.get(paramName));
                        if (paramName.endsWith("_money")) {
                            String moneyCurrencyIdParamName = paramName + "CurrencyId";
                            values.put(moneyCurrencyIdParamName, customAttributes.get(moneyCurrencyIdParamName));
                        }
                    }
                });
                result.put(propertyId, values);
            });
        }
        return result;
    }

}
