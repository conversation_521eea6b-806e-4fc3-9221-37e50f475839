package com.noosh.app.service.taylor;

import com.noosh.app.commons.dto.accounts.SupplierWorkgroupDTO;
import com.noosh.app.commons.dto.workgroup.option.CustomDataSetDTO;
import com.noosh.app.commons.vo.taylor.TaylorSpecVO;
import com.noosh.app.commons.vo.taylor.TaylorSupplierVO;
import com.noosh.app.service.supplier.SupplierFlagService;
import com.noosh.app.service.supplier.SupplierService;
import com.noosh.app.service.util.BusinessUtil;
import com.noosh.app.service.workgroup.option.DataSetService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * author: Yang
 * Date: 3/6/2025
 */
@Service
public class TaylorService {
    @Autowired
    private DataSetService dataSetService;
    @Autowired
    private SupplierService supplierService;
    @Autowired
    private SupplierFlagService supplierFlagService;

    private static final String DATA_SET_NAME = "RFE AutoInvite Suppliers";

    public List<TaylorSupplierVO> getTaylorSuppliers(Long groupId, List<TaylorSpecVO> taylorSpecVOS) {
        List<CustomDataSetDTO> customDataSetDTOS = dataSetService.findDataSets(groupId, DATA_SET_NAME);
        List<TaylorSupplierVO> taylorSupplierVOS = new ArrayList<>();
        if (customDataSetDTOS == null || customDataSetDTOS.isEmpty()) {
            return taylorSupplierVOS;
        }
        for (TaylorSpecVO taylorSpecVO : taylorSpecVOS) {
            String specTypeName = taylorSpecVO.getSpecTypeName();
            customDataSetDTOS.stream().filter(customDataSetDTO -> isValid(customDataSetDTO, specTypeName)).forEach(
                    customDataSetDTO -> {
                        SupplierWorkgroupDTO supplierWorkgroupDTO = supplierService.findSupplier(groupId, customDataSetDTO.getAttribute2().trim());
                        if (supplierWorkgroupDTO != null && supplierWorkgroupDTO.getDefaultSupplierUser() != null) {
                            TaylorSupplierVO taylorSupplierVO = new TaylorSupplierVO();
                            taylorSupplierVO.setWorkgroup(supplierWorkgroupDTO.getSupplierWorkgroup().getName());
                            taylorSupplierVO.setUserId(supplierWorkgroupDTO.getDefaultSupplierUserId());
                            taylorSupplierVO.setFirstName(supplierWorkgroupDTO.getDefaultSupplierUser().getPerson().getFirstName());
                            taylorSupplierVO.setLastName(supplierWorkgroupDTO.getDefaultSupplierUser().getPerson().getLastName());
                            taylorSupplierVO.setFullName(BusinessUtil.getFullName(supplierWorkgroupDTO.getDefaultSupplierUser().getPerson()));
                            taylorSupplierVO.setMin(Long.valueOf(customDataSetDTO.getAttribute3()));
                            taylorSupplierVO.setMax(Long.valueOf(customDataSetDTO.getAttribute4()));
                            taylorSupplierVO.setSpecTypeName(specTypeName);
                            taylorSupplierVO.setSpecNodeId(taylorSpecVO.getSpecNodeId());
                            /*taylorSupplierVO.setSupplierFlag(supplierFlagService.getSupplierFlagVO(true, -1L, -1L,
                                    supplierWorkgroupDTO.getOwnerWorkgroupId(),
                                    supplierWorkgroupDTO.getSupplierWorkgroupId()));*/
                            taylorSupplierVOS.add(taylorSupplierVO);
                        }
                    }
            );
        }
        return taylorSupplierVOS;
    }

    private boolean isValid(CustomDataSetDTO customDataSetDTO, String specTypeName) {
        boolean str1Valid = StringUtils.isNotBlank(customDataSetDTO.getAttribute1()) && customDataSetDTO.getAttribute1().trim().equalsIgnoreCase(specTypeName);
        boolean str2Valid = StringUtils.isNotBlank(customDataSetDTO.getAttribute2());
        boolean str3Valid = isValidLong(customDataSetDTO.getAttribute3());
        boolean str4Valid = isValidLong(customDataSetDTO.getAttribute4());
        return str1Valid && str2Valid && str3Valid && str4Valid;
    }

    private boolean isValidLong(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        try {
            Long.parseLong(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

}
