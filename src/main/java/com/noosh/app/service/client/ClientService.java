package com.noosh.app.service.client;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.noosh.app.commons.vo.client.*;
import com.noosh.app.mapper.address.AddressMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.noosh.app.commons.constant.EnterpriseLinkDestination;
import com.noosh.app.commons.constant.PaymentMethodID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.constant.StringID;
import com.noosh.app.commons.constant.WgAttributeTypeID;
import com.noosh.app.commons.dto.accounts.ClientWorkgroupDTO;
import com.noosh.app.commons.dto.accounts.WorkgroupAttributeDTO;
import com.noosh.app.commons.dto.security.AddressDTO;
import com.noosh.app.commons.dto.security.CountryDTO;
import com.noosh.app.commons.dto.security.WorkgroupDTO;
import com.noosh.app.commons.entity.address.Address;
import com.noosh.app.commons.entity.address.Country;
import com.noosh.app.commons.entity.client.ClientAttrMarkup;
import com.noosh.app.commons.entity.client.PaymentMethod;
import com.noosh.app.commons.entity.client.Site;
import com.noosh.app.commons.entity.security.AccountUser;
import com.noosh.app.commons.entity.security.ClientWorkgroup;
import com.noosh.app.commons.entity.security.Currency;
import com.noosh.app.commons.entity.workgroup.Workgroup;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.account.WorkgroupAddressVO;
import com.noosh.app.commons.vo.address.AddressVO;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.commons.vo.workgroup.WgAttributeVO;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.exception.UnexpectedException;
import com.noosh.app.repository.jpa.accounts.PaymentMethodRepository;
import com.noosh.app.repository.jpa.accounts.SupplierWHLocationRepository;
import com.noosh.app.repository.jpa.address.AddressRepository;
import com.noosh.app.repository.jpa.address.CountryRepository;
import com.noosh.app.repository.jpa.client.ClientAttrMarkupRepository;
import com.noosh.app.repository.jpa.client.SiteRepository;
import com.noosh.app.repository.jpa.security.AccountUserRepository;
import com.noosh.app.repository.jpa.security.ClientWorkgroupRepository;
import com.noosh.app.repository.jpa.security.CurrencyRepository;
import com.noosh.app.repository.jpa.workgroup.SupplierWorkgroupRepository;
import com.noosh.app.repository.jpa.workgroup.WorkgroupRepository;
import com.noosh.app.repository.mybatis.accounts.ClientWorkgroupMyBatisMapper;
import com.noosh.app.repository.mybatis.accounts.WorkgroupAttributeMyBatisMapper;
import com.noosh.app.repository.mybatis.security.WorkgroupMyBatisMapper;
import com.noosh.app.service.apijob.WebClientService;
import com.noosh.app.service.custom.CustomAttributeService;
import com.noosh.app.service.permission.PermissionService;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.util.BusinessUtil;
import com.noosh.app.service.util.NooshOneUrlUtil;

/**
 * <AUTHOR>
 * @date 5/4/2022
 */
@Service
public class ClientService {

    private static final double DEFAULT_MARKUP_PERCENT = 10.0;


    @Autowired
    private SiteRepository siteRepository;
    @Autowired
    private PaymentMethodRepository paymentMethodRepository;
    @Autowired
    private AccountUserRepository accountUserRepository;
    @Autowired
    private WorkgroupRepository workgroupRepository;
    @Autowired
    private AddressRepository addressRepository;
    @Autowired
    private CountryRepository countryRepository;
    @Autowired
    private CurrencyRepository currencyRepository;
    @Autowired
    private ClientWorkgroupRepository clientWorkgroupRepository;
    @Autowired
    private ClientAttrMarkupRepository clientAttrMarkupRepository;
    @Autowired
    private SupplierWorkgroupRepository supplierWorkgroupRepository;
    @Autowired
    private SupplierWHLocationRepository supplierWHLocationRepository;
    @Autowired
    private WorkgroupMyBatisMapper workgroupMyBatisMapper;
    @Autowired
    private ClientWorkgroupMyBatisMapper clientWorkgroupMyBatisMapper;
    @Autowired
    private WorkgroupAttributeMyBatisMapper workgroupAttributeMyBatisMapper;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private WebClientService webClientService;
    @Autowired
    private CustomAttributeService customAttributeService;
    @Autowired
    private AddressMapper addressMapper;

    /**
     * get owner workgroup clients
     * @param userId
     * @param workgroupId
     * @param isProjectLevelActive true: active clients, false: inactive clients
     * @param page
     * @return
     */
    public ClientListVO listClients(Long userId, Long workgroupId, Boolean isProjectLevelActive, String searchStr, PageVO page) {
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        pageInfo.setUnsafeOrderBy(page.getOrderBy());
        searchStr = BusinessUtil.getEscapedSearchString(searchStr);
        List<ClientWorkgroupDTO> clients = clientWorkgroupMyBatisMapper.findClientsByProjectLevelActive(workgroupId, searchStr, isProjectLevelActive);
        page.setTotal(pageInfo.getTotal());
        Map<String, String> userPrefs = preferenceService.findUserPrefs(workgroupId, userId);
        boolean psfPricingEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_PSF_PRICING, userPrefs);
        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
        boolean isEnableMargin = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ENABLE_MARGIN, groupPrefs);
        boolean isMultiCurrency = preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY, groupPrefs);
        boolean canManageClient = permissionService.checkAll(PermissionID.MANAGE_CLIENT, workgroupId, userId, -1L);
        ClientListVO clientListVO = new ClientListVO();
        clientListVO.setIsEnableMargin(isEnableMargin);
        clientListVO.setIsMultiCurrency(isMultiCurrency);
        clientListVO.setPsfPricingEnabled(psfPricingEnabled);
        clientListVO.setCanManageClient(canManageClient);
        List<ClientVO> clientVOList = new ArrayList<>();
        for (ClientWorkgroupDTO client: clients) {
            ClientVO clientVO = toClientVO(client, isMultiCurrency);
            clientVOList.add(clientVO);
        }
        clientListVO.setClients(clientVOList);
        return clientListVO;
    }

    public List<ClientVO> listTransClients(Long workgroupId) {
        List<ClientWorkgroupDTO> clients = clientWorkgroupMyBatisMapper.findAllSimpleClientList(workgroupId);
        List<ClientVO> clientVOList = new ArrayList<>();
        for (ClientWorkgroupDTO client: clients) {
            if(!client.getIsInactive()){
                if(!client.getIsNoosh()){
                    if(client.getTransactionalCurrencyId() != null && client.getTransactionalCurrencyId() > 0){
                        ClientVO clientVO = toTransClientVO(client);
                        clientVOList.add(clientVO);
                    }
                }else{
                    WorkgroupDTO workgroupDTO = client.getClientWorkgroup();
                    if(workgroupDTO != null && workgroupDTO.getTransactionalCurrencyId() != null && workgroupDTO.getTransactionalCurrencyId() > 0){
                        ClientVO clientVO = toTransClientVO(client);
                        clientVOList.add(clientVO);
                    }
                }
            }
        }
        return clientVOList;
    }

    private ClientVO toTransClientVO(ClientWorkgroupDTO client) {
        ClientVO clientVO = new ClientVO();
        clientVO.setClientId(client.getId());
        clientVO.setIsNoosh(client.getIsNoosh());
        clientVO.setName(client.getName());
        if (null != client.getIsNoosh()) {
            clientVO.setIsNonNooshClient(!client.getIsNoosh());
            WorkgroupDTO workgroupDTO = client.getClientWorkgroup();
            if (client.getIsNoosh() && workgroupDTO != null) {
                clientVO.setName(workgroupDTO.getName());
            }
        }
        Long currencyId;
        if (clientVO.getIsNonNooshClient()) {
            currencyId = client.getTransactionalCurrencyId();
        } else {
            currencyId = client.getClientWorkgroup().getTransactionalCurrencyId();
        }
        if (null != currencyId) {
            Optional<Currency> currencyOptional = currencyRepository.findById(currencyId);
            if (currencyOptional.isPresent()) {
                clientVO.setTransCurrency(currencyOptional.get().getCurrency());
                clientVO.setTransCurrencyId(currencyOptional.get().getId());
            }
        }
        return clientVO;
    }


    /**
     * Client DTO to VO
     * @param client
     * @return
     */
    private ClientVO toClientVO(ClientWorkgroupDTO client, boolean isMultiCurrency) {
        ClientVO clientVO = new ClientVO();
        clientVO.setClientId(client.getId());
        clientVO.setIsNoosh(client.getIsNoosh());
        clientVO.setName(client.getName());
        if (null != client.getIsNoosh()) {
            clientVO.setIsNonNooshClient(!client.getIsNoosh());
            WorkgroupDTO workgroupDTO = client.getClientWorkgroup();
            if (client.getIsNoosh() && workgroupDTO != null) {
                clientVO.setName(workgroupDTO.getName());
            }
        }
        AddressDTO addressDTO = client.getAddress();
        if (null != addressDTO) {
            clientVO.setLine1(addressDTO.getLine1());
            clientVO.setLine2(addressDTO.getLine2());
            clientVO.setLine3(addressDTO.getLine3());
            clientVO.setCity(addressDTO.getCity());
            clientVO.setPostal(addressDTO.getPostal());
            clientVO.setState(addressDTO.getState());
            CountryDTO countryDTO = addressDTO.getCountry();
            if (null != countryDTO) {
                clientVO.setCountryStrId(countryDTO.getNameStrId());
            }
        }
        clientVO.setClientCode(client.getClientCode());
        clientVO.setMarginPercent(client.getMarginPercent());
        clientVO.setIsMarginVisible(client.getIsMarginVisible());
        if (isMultiCurrency) {
            Long currencyId;
            if (clientVO.getIsNonNooshClient()) {
                currencyId = client.getTransactionalCurrencyId();
            } else {
                currencyId = client.getClientWorkgroup().getTransactionalCurrencyId();
            }
            if (null != currencyId) {
                Optional<Currency> currencyOptional = currencyRepository.findById(currencyId);
                if (currencyOptional.isPresent()) {
                    clientVO.setTransCurrency(currencyOptional.get().getCurrency());
                }
            }
        }
        clientVO.setQuoteValidDays(client.getQuoteValidDays());
        clientVO.setMarkupPercent(client.getMarkupPercent());
        clientVO.setIsMarkupVisible(client.getIsMarkupVisible());
        clientVO.setMarkupCurrencyId(client.getMarkupCurrencyId());
        clientVO.setMarkup(client.getMarkup());
        if (client.getPaymentMethodId() != null) {
            PaymentMethod paymentMethod = paymentMethodRepository.findById(client.getPaymentMethodId()).orElse(new PaymentMethod());
            clientVO.setPaymentMethodStrId(paymentMethod.getDescriptionStrId());
        }
        clientVO.setRequiresPricing(client.getRequiresPricing());
        if (client.getDefaultClientUserId() != null) {
            AccountUser accountUser = accountUserRepository.findById(client.getDefaultClientUserId()).orElse(new AccountUser());
            clientVO.setDefaultClientUser(accountUser.getPerson().getFullName());
        }
        clientVO.setIsEnableDelete(isClientCreatedFromNP(client));
        HashMap<String, String> params = new HashMap<>();
        params.put("clientId", clientVO.getClientId().toString());
        clientVO.setClientEditExternalUrl(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.GOTO_EDIT_CLIENT, params));
        clientVO.setClientSupplierExternalUrl(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.GOTO_CLIENT_SUPPLIER, params));
        return clientVO;
    }

    public SimpleClientListVO listSimpleClients(Long workgroupId) {
        List<ClientWorkgroupDTO> clients = clientWorkgroupMyBatisMapper.findAllSimpleClientList(workgroupId);
        SimpleClientListVO clientListVO = new SimpleClientListVO();
        List<ClientSimpleVO> clientVOList = new ArrayList<>();
        clients.stream().forEach(client -> {
            ClientSimpleVO clientVO = toSimpleClientVO(client);
            clientVOList.add(clientVO);
        });
        clientListVO.setClients(clientVOList);
        return clientListVO;
    }
    private ClientSimpleVO toSimpleClientVO(ClientWorkgroupDTO client) {
        ClientSimpleVO clientVO = new ClientSimpleVO();
        clientVO.setClientId(client.getId());
        clientVO.setIsNoosh(client.getIsNoosh());
        clientVO.setName(client.getName());
        if (null != client.getIsNoosh()) {
            clientVO.setIsNonNooshClient(client.getIsNoosh() ? false : true);
            WorkgroupDTO workgroupDTO = client.getClientWorkgroup();
            if (client.getIsNoosh() && workgroupDTO != null) {
                clientVO.setName(workgroupDTO.getName());
            }
        }
        return clientVO;
    }

    /**
     * Whether to show the delete button
     * @param client
     * @return
     *  value is true: show delete button
     *  value is false: hide delete button
     */
    private boolean isClientCreatedFromNP(ClientWorkgroupDTO client){
        long buClientWorkgroupId = client.getId();
        Site site = siteRepository.findByBuClientWorkgroupId(buClientWorkgroupId);
        return site == null;
    }

    /**
     * Find client by workgroup name or first name or last name or default email
     *
     * @param workgroupId
     * @param workgroupName
     * @param firstName
     * @param lastName
     * @param defaultEmail
     * @param page
     * @return
     */
    public List<WorkgroupAddressVO> findClient(Long workgroupId, String workgroupName, String firstName, String lastName, String defaultEmail, PageVO page) {
        List<WorkgroupAddressVO> wgAddressVOList;
        if (workgroupName != null || firstName != null || lastName != null || defaultEmail !=null) {
            Workgroup workgroup = workgroupRepository.findById(workgroupId).orElse(new Workgroup());
            boolean isInternal = true;
            if (workgroup.getWorkgroupSd() != null) {
                isInternal = workgroup.getWorkgroupSd().getIsInternal();
            }
            if (workgroupName != null) {
                workgroupName = "%" + workgroupName + "%";
            }
            if (firstName != null) {
                firstName = "%" + firstName + "%";
            }
            if (lastName != null) {
                lastName = "%" + lastName + "%";
            }
            if (defaultEmail != null) {
                defaultEmail = "%" + defaultEmail + "%";
            }
            Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
            pageInfo.setUnsafeOrderBy(page.getOrderBy());
            List<WorkgroupDTO> workgroupDTOList = workgroupMyBatisMapper.findWithFirstNameAndLastNameAndDefaultEmail(workgroupId, isInternal,
                    workgroupName, firstName, lastName, defaultEmail, workgroup.getDefaultCurrencyId().longValue());
            page.setTotal(pageInfo.getTotal());
            List<Long> clientWorkgroupIdList = clientWorkgroupMyBatisMapper.findActiveClientWgIds(workgroupId);
            Set<Long> clientWorkgroupIdSet = new HashSet<>(clientWorkgroupIdList);
            wgAddressVOList = new ArrayList<>();
            workgroupDTOList.stream().forEach(client -> wgAddressVOList.add(toWorkgroupAddressVO(client, clientWorkgroupIdSet)));
        } else {
            throw new UnexpectedException(StringID.INCOMPLETED_SEARCH_CRITERIA);
        }
        return wgAddressVOList;
    }

    private WorkgroupAddressVO toWorkgroupAddressVO (WorkgroupDTO dto, Set<Long> clients) {
        WorkgroupAddressVO vo = new WorkgroupAddressVO();
        vo.setIsExistedClient(clients.contains(dto.getId()));
        vo.setWgId(dto.getId());
        vo.setName(dto.getName());
        AddressDTO address = dto.getMainAddress();
        if (null != address) {
            vo.setLine1(address.getLine1());
            vo.setLine2(address.getLine2());
            vo.setLine3(address.getLine3());
            vo.setCity(address.getCity());
            vo.setState(address.getState());
            vo.setPostal(address.getPostal());
            if (null != address.getCountry()) {
                vo.setCountryStrId(address.getCountry().getNameStrId());
            }
        }
        return vo;
    }

    /**
     * select client type
     * @param workgroupId
     * @return
     */
    public SelectClientTypeVO selectClientType(Long workgroupId) {
        // redirect to the next page if the user do not have both pref enable
        // for user with only proposal enable, they can only add non-noosh client
        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
        boolean outsourcingEnable = preferenceService.check(PreferenceID.WORKGROUP_OPTION_BROKERING, groupPrefs)
                || preferenceService.check(PreferenceID.WORKGROUP_OPTION_OUTSOURCING, groupPrefs);
        boolean proposalEnable = preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROPOSAL_WRITER, groupPrefs);

        SelectClientTypeVO vo = new SelectClientTypeVO();
        if (!outsourcingEnable && proposalEnable) {
            vo.setIsNonClient(true);
        } else {
            // user should only get here if their workgroup sign up to handle both noosh & non-noosh client
            vo.setIsClient(true);
            vo.setIsNonClient(true);
        }

        // country options
        List<DropdownVO<Long>> countryOptions = getCountryDropdown();
        vo.setCountryOptions(countryOptions);

        return vo;
    }

    private List<DropdownVO<Long>> getCountryDropdown() {
        List<DropdownVO<Long>> countryOptions = new ArrayList<>();
        List<Country> countryList = countryRepository.findAll();
        countryList.stream().forEach(country -> countryOptions.add(
                new DropdownVO<>(country.getId(), null, country.getNameStrId())));
        return countryOptions;
    }

    /**
     * Add noosh client
     * @param userId
     * @param workgroupId
     * @param clientWorkgroupIds
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void addClient(Long userId, Long workgroupId, List<Long> clientWorkgroupIds) {
        if(clientWorkgroupIds == null || clientWorkgroupIds.isEmpty()) {
            // StringID.CLIENT_SELECT_CONSTRAINT
            throw new NotFoundException("Please select at least 1 workgroup to add to client list");
        }

        //find existing clients for validation
        List<ClientWorkgroupDTO> existingClients = clientWorkgroupMyBatisMapper.findClients(workgroupId);

        Map<Long, Workgroup> clientWgMap = new HashMap<>();
        List<Workgroup> clientWorkgroups = workgroupRepository.findAllById(clientWorkgroupIds);
        clientWorkgroups.stream().forEach(clientWorkgroup -> clientWgMap.put(clientWorkgroup.getId(), clientWorkgroup));

        Workgroup ownerWorkgroup = workgroupRepository.findById(workgroupId).orElse(null);

        //create the clients
        List<ClientWorkgroup> clientGroups = new ArrayList<>();
        //For NKB152399 - Prevent Client linkage if client has different base currency
        StringBuilder preventClientName = new StringBuilder();
        int countPreventClient = 0;
        int errorCount = 0;
        if (errorCount==0) {
            for (Long clientGroupId : clientWorkgroupIds) {
                Workgroup clientWorkgroup = clientWgMap.get(clientGroupId);
                if (clientWorkgroup != null && clientWorkgroup.getDefaultCurrencyId() != null
                        && ownerWorkgroup.getDefaultCurrencyId().longValue() != clientWorkgroup.getDefaultCurrencyId()) {
                    countPreventClient++;
                    if (countPreventClient > 1) {
                        preventClientName.append(" | ");
                    }
                    preventClientName.append(clientWorkgroup.getName());
                }
                //check to ensure not already existed
                //shouldn't get here since the view already check
                //double check just in case user use the <back> button & create again
                for (ClientWorkgroupDTO existingClient : existingClients) {
                    if (existingClient.getClientWorkgroupId() !=null
                            && clientGroupId.longValue() == existingClient.getClientWorkgroupId()) {
                        // StringID.CLIENT_EXIST_CONSTRAINT
                        errorCount++;
                        break;
                    }
                }
                ClientWorkgroup client = new ClientWorkgroup();
                client.setOwnerAcWorkgroupId(workgroupId);
                client.setClientAcWorkgroupId(clientGroupId);
                client.setMarkupPercent(BigDecimal.valueOf(DEFAULT_MARKUP_PERCENT));
                client.setNoosh(true);
                client.setInactive(false);
                client.setReferenced(false);
                client.setMarkupVisible(false);
                client.setCreateUserId(userId);
                client.setModDate(null);

                clientGroups.add(client);
            }
        }

        //NKB152399 - Prevent Client linkage if client has different base currency
        if (countPreventClient > 0) {
            // StringID.INVITE_ClIENT_CURRENCY_CONSTRAINT
            throw new UnexpectedException("The client " + preventClientName + " you are trying to add has a different currency set up than your own workgroup. Therefore this client CANNOT be added.");
        }

        if (errorCount == 0) {
            //save client group
            clientWorkgroupRepository.saveAll(clientGroups);
        } else {
            // StringID.CLIENT_EXIST_CONSTRAINT
            throw new UnexpectedException("At least one of the selected workgroup(s) already exist in the client list.  Please select different workgroup.");
        }
    }

    /**
     * Add non noosh client
     * @param userId
     * @param workgroupId
     * @param vo
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void addNonNooshClient(Long userId, Long workgroupId, NonNooshClientVO vo) {
        ClientWorkgroup client = new ClientWorkgroup();
        if (vo.getName() != null) {
            // add address
            Address address = new Address();
            address.setLine1(vo.getLine1());
            address.setLine2(vo.getLine2());
            address.setLine3(vo.getLine3());
            address.setCity(vo.getCity());
            address.setPostal(vo.getPostal());
            address.setState(vo.getState());
            address.setCountryId(vo.getCountryId());
            address.setCreateUserId(userId);
            address.setModDate(null);
            Address saveAddress = addressRepository.save(address);

            // add non client
            client.setName(vo.getName());
            client.setAcAddressId(saveAddress.getId());
            client.setOwnerAcWorkgroupId(workgroupId);
            client.setMarkupPercent(BigDecimal.valueOf(DEFAULT_MARKUP_PERCENT));
            client.setNoosh(false);
            client.setInactive(false);
            client.setReferenced(false);
            client.setMarkupVisible(false);
            client.setCreateUserId(userId);
            client.setModDate(null);
            client.setTransactionalCurrencyId(vo.getTransactionalCurrencyId());
            clientWorkgroupRepository.save(client);
        } else {
            throw new UnexpectedException("client name cannot be empty");
        }
    }

    /**
     * go to client view page when click edit button
     * @param workgroupId
     * @param clientId
     */
    public ClientDetailVO viewClientInfo(Long userId, Long workgroupId, Long clientId) {
        Optional<ClientWorkgroup> optionalClientWorkgroup = clientWorkgroupRepository.findById(clientId);
        if (optionalClientWorkgroup.isPresent()) {
            ClientWorkgroup clientWorkgroup = optionalClientWorkgroup.get();
            // workgroup preferences
            Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
            boolean isEnableMargin = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ENABLE_MARGIN, groupPrefs);
            //user preferences
            Map<String, String> userPrefs = preferenceService.findUserPrefs(workgroupId, userId);
            boolean psfPricingEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_PSF_PRICING, userPrefs);
            boolean isMultiCurrency = preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY, userPrefs);
            Workgroup ownerWorkgroup = workgroupRepository.findById(workgroupId).get();
            ClientDetailVO viewVO = new ClientDetailVO();
            viewVO.setMarkupCurrencyId(ownerWorkgroup.getDefaultCurrencyId());
            boolean canManageClient = permissionService.checkAll(PermissionID.MANAGE_CLIENT, workgroupId, userId, -1L);
            viewVO.setCanManageClient(canManageClient);
            toClientViewVO(isEnableMargin, psfPricingEnabled, clientWorkgroup, isMultiCurrency, viewVO);
            viewVO.setWgAttributes(getWgAttributes(clientWorkgroup, isEnableMargin));
            Map<String, Object> customFields = customAttributeService.findCustomAttributes(clientWorkgroup.getCustomPrPropertyId(), null);
            viewVO.setCustomFields(customFields);
            return viewVO;
        } else {
            throw new NotFoundException("client id not found");
        }
    }

    /**
     * workgroup attributes
     * @param clientWorkgroup
     * @param isEnableMargin
     */
    private List<WgAttributeVO> getWgAttributes(ClientWorkgroup clientWorkgroup, boolean isEnableMargin) {
        List<WorkgroupAttributeDTO> wgAttributeDTOList = workgroupAttributeMyBatisMapper.findByType(WgAttributeTypeID.SPEC_PRODUCT_TYPE, "CODE");
        Map<Long, WgAttributeVO> allWgAttributeMap = new HashMap<>();
        // workgroup attributes
        List<WgAttributeVO> wgAttributeVOList = new ArrayList<>();
        wgAttributeDTOList.stream().forEach(dto -> {
            WgAttributeVO vo = new WgAttributeVO();
            vo.setWgAttributeId(dto.getId());
            vo.setLabelStrId(dto.getLabelStrId());
            wgAttributeVOList.add(vo);
            allWgAttributeMap.put(dto.getId(), vo);
        });
        // client attribute mark up
        List<ClientAttrMarkup> clientAttrMarkups = clientWorkgroup.getClientAttrMarkups();
        clientAttrMarkups.stream().forEach(clientAttrMarkup -> {
            if (allWgAttributeMap.containsKey(clientAttrMarkup.getWgAttributeId())) {
                WgAttributeVO vo = allWgAttributeMap.get(clientAttrMarkup.getWgAttributeId());
                vo.setIsOverride(true);
                if (isEnableMargin) {
                    vo.setMarginPercent(clientAttrMarkup.getMarginPercent());
                } else {
                    vo.setMarkup(clientAttrMarkup.getMarkup());
                    vo.setMarkupPercent(clientAttrMarkup.getMarkupPercent());
                }
            }
        });
        return wgAttributeVOList;
    }

    private void toClientViewVO(boolean isEnableMargin, boolean psfPricingEnabled,
                                ClientWorkgroup client, boolean isMultiCurrency, ClientDetailVO vo) {
        vo.setClientId(client.getId());
        if (client.getNoosh()) {
            vo.setIsNoosh(true);
            vo.setName(client.getWorkgroup().getName());
            WorkgroupDTO workgroupDTO = workgroupMyBatisMapper.findWorkgroupWithAllData(client.getClientAcWorkgroupId());
            AddressDTO addressDTO = workgroupDTO.getMainAddress();
            AddressVO addressVO = addressMapper.toVO(addressDTO);
            addressVO.setCountryStrId(addressDTO.getCountry().getNameStrId());
            vo.setAddress(addressVO);
        } else {
            // offline client
            vo.setIsNoosh(false);
            vo.setName(client.getName());
            // address
            Optional<Address> addressOptional = addressRepository.findById(client.getAcAddressId());
            if (addressOptional.isPresent()) {
                AddressVO addressVO = new AddressVO();
                Address address = addressOptional.get();
                addressVO.setLine1(address.getLine1());
                addressVO.setLine2(address.getLine2());
                addressVO.setLine3(address.getLine3());
                addressVO.setCity(address.getCity());
                addressVO.setState(address.getState());
                addressVO.setPostal(address.getPostal());
                addressVO.setCountryId(address.getCountryId());
                addressVO.setCountryStrId(address.getCountry().getNameStrId());
                vo.setAddress(addressVO);
            }
        }

        // payment method
        if (psfPricingEnabled) {
            vo.setIsEnablePsfPricing(true);
            vo.setPaymentMethodId(client.getBuPaymentMethodId());
            vo.setPaymentMethodDropdown(getPaymentDropdown());
            vo.setRequiresPricing(client.getPsfRequiresPricing());
            if(client.getNoosh() && client.getDefaultClientUserId() != null) {
                vo.setDefaultClientUserId(client.getDefaultClientUserId());
                Optional<AccountUser> accountUserOptional = accountUserRepository.findById(client.getDefaultClientUserId());
                if (accountUserOptional.isPresent()) {
                    vo.setDefaultClientUser(accountUserOptional.get().getPerson().getFullName());
                }
            }
        }

        vo.setClientCode(client.getClientCode());

        // margin
        if (isEnableMargin) {
            vo.setIsEnableMargin(true);
            vo.setMarginPercent(client.getMarginPercent());
            vo.setIsMarginVisible(client.getMarginVisible());
        } else {
            vo.setIsEnableMargin(false);
            vo.setMarkupPercent(client.getMarkupPercent());
            vo.setMarkup(client.getMarkup());
            vo.setIsMarkupVisible(client.getMarkupVisible());
        }
        //multi-currency
        if (!client.getNoosh() && isMultiCurrency) {
            vo.setIsEnableMultiCurrency(true);
            vo.setMultiCurrencyDropdown(getTransactionCurrencyDropdown(true));
            vo.setTransactionalCurrencyId(client.getTransactionalCurrencyId());
        }
        //quote valid days
        if (client.getNoosh()) {
            vo.setQuoteValidDays(client.getQuoteValidDays());
        }
        vo.setIsProjectLevelInactive(client.getIsProjectLevelInactive());
    }


    private List<DropdownVO<Long>> getPaymentDropdown() {
        List<DropdownVO<Long>> paymentDropdown = new ArrayList<>();
        List<Long> paymentMethodIds = new ArrayList<>(Arrays.asList(PaymentMethodID.PO, PaymentMethodID.CREDIT_CARD, PaymentMethodID.IN_CONTRACT));
        List<PaymentMethod> paymentMethods = paymentMethodRepository.findAllById(paymentMethodIds);
        paymentMethods.stream().forEach(paymentMethod -> paymentDropdown.add(new DropdownVO<>(paymentMethod.getId(), null, paymentMethod.getDescriptionStrId())));
        return paymentDropdown;
    }

    /***
     * generate current workgroup client option list
     * @param ownerWorkgroupId
     * @return
     */
    public List<ClientOptionVO> getClientDropdown(long ownerWorkgroupId) {
        Workgroup workgroup = workgroupRepository.findById(ownerWorkgroupId).orElse(new Workgroup());
        List<ClientOptionVO> clientOptionVOs = new ArrayList<>();
        clientOptionVOs.add(new ClientOptionVO(workgroup.getName(), null, 0L));
        List<ClientWorkgroupDTO> clientWorkgroupDTOS = clientWorkgroupMyBatisMapper.findClients(ownerWorkgroupId);
        clientWorkgroupDTOS.stream().filter(clientWorkgroupDTO -> null != clientWorkgroupDTO.getIsNoosh() && clientWorkgroupDTO.getIsNoosh())
                .sorted(Comparator.comparing(c -> c.getClientWorkgroup().getName().toUpperCase()))
                .forEach(clientWorkgroupDTO -> {
                WorkgroupDTO workgroupDTO = clientWorkgroupDTO.getClientWorkgroup();
                clientOptionVOs.add(new ClientOptionVO(workgroupDTO.getName(), StringID.CLIENT_OPTION_PREFIX, workgroupDTO.getId()));
        });
        return clientOptionVOs;
    }

    @Transactional
    public void updateClientFilter(Long workgroupId, Long userId, Integer pageSize, boolean isActive) {
        Map<String, String> preferences = new HashMap<>();
        if (isActive) {
            preferences.put(ClientFilterVO.CLIENT_LIST_FILTER_PREFIX + ClientFilterVO.PAGE_SIZE, pageSize.toString());
        } else {
            preferences.put(ClientFilterVO.CLIENT_LIST_FILTER_PREFIX + ClientFilterVO.INACTIVE_PAGE_SIZE, pageSize.toString());
        }
        preferenceService.saveUserPreference(workgroupId, userId, preferences);
    }

    public ClientFilterVO getClientFilter(Long workgroupId, Long userId) {
        Map<String, String> preferences = preferenceService.findUserPrefs(workgroupId, userId);
        ClientFilterVO clientFilterVO = new ClientFilterVO();
        if (preferences.containsKey(ClientFilterVO.CLIENT_LIST_FILTER_PREFIX + ClientFilterVO.PAGE_SIZE)) {
            clientFilterVO.setPageSize(Integer.valueOf(preferences.get(ClientFilterVO.CLIENT_LIST_FILTER_PREFIX + ClientFilterVO.PAGE_SIZE)));
        } else {
            clientFilterVO.setPageSize(ClientFilterVO.PAGE_SIZE_DEFAULT_VALUE);
        }
        if (preferences.containsKey(ClientFilterVO.CLIENT_LIST_FILTER_PREFIX + ClientFilterVO.INACTIVE_PAGE_SIZE)) {
            clientFilterVO.setInactivePageSize(Integer.valueOf(preferences.get(ClientFilterVO.CLIENT_LIST_FILTER_PREFIX + ClientFilterVO.INACTIVE_PAGE_SIZE)));
        } else {
            clientFilterVO.setInactivePageSize(ClientFilterVO.PAGE_SIZE_DEFAULT_VALUE);
        }
        return clientFilterVO;
    }

    public List<DropdownVO<Long>> getTransactionCurrencyDropdown(Long workgroupId, Long userId) {
        boolean isMultiCurrency = preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY, workgroupId, userId);
        return getTransactionCurrencyDropdown(isMultiCurrency);
    }
    private List<DropdownVO<Long>> getTransactionCurrencyDropdown(boolean isMultiCurrency) {
        List<DropdownVO<Long>> dropdownVOList = new ArrayList<>();
        if (isMultiCurrency) {
            List<Currency> currencyList = currencyRepository.findAll();
            currencyList.forEach(currency -> dropdownVOList.add(new DropdownVO<>(currency.getId(), currency.getCurrency())));
        }
        return dropdownVOList;
    }

    @Transactional
    public void editClientMargin(Long userId, Long workgroupId, Long clientId, List<WgAttributeVO> newRequestWgAttributes) {
        boolean canManageClient = permissionService.checkAll(PermissionID.MANAGE_CLIENT, workgroupId, userId, -1L);
        if (!canManageClient) {throw new IllegalArgumentException("no permission to edit client");}
        Optional<ClientWorkgroup> optionalClientWorkgroup = clientWorkgroupRepository.findById(clientId);
        if (optionalClientWorkgroup.isEmpty()) {throw new NotFoundException("client id not found");}
        ClientWorkgroup client = optionalClientWorkgroup.get();
        // workgroup preferences
        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
        boolean isEnableMargin = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ENABLE_MARGIN, groupPrefs);

        //new request
        Map<Long, WgAttributeVO> newRequestWgAttributeMap = new HashMap<>();
        newRequestWgAttributes.stream().forEach(newRequest -> newRequestWgAttributeMap.put(newRequest.getWgAttributeId(), newRequest));
        //existing
        List<ClientAttrMarkup> existingWgAttributes = client.getClientAttrMarkups();
        Map<Long, ClientAttrMarkup> existingWgAttributeMap = new HashMap<>();
        existingWgAttributes.stream().forEach(existing -> existingWgAttributeMap.put(existing.getWgAttributeId(), existing));

        List<ClientAttrMarkup> updateClientAttrs = new ArrayList<>();
        List<ClientAttrMarkup> removeClientAttrs = new ArrayList<>();
        List<ClientAttrMarkup> addClientAttrs = new ArrayList<>();

        Workgroup ownerWorkgroup = workgroupRepository.findById(workgroupId).get();
        Long currencyId = ownerWorkgroup.getDefaultCurrencyId();
        //if the existing attr is in the request map, should update. if not, should delete it.
        for (ClientAttrMarkup existingAttr : existingWgAttributes) {
            if (newRequestWgAttributeMap.containsKey(existingAttr.getWgAttributeId())) {
                WgAttributeVO wgAttribute = newRequestWgAttributeMap.get(existingAttr.getWgAttributeId());
                if (isEnableMargin) {
                    existingAttr.setMarginPercent(wgAttribute.getMarginPercent());
                } else {
                    existingAttr.setMarkup(wgAttribute.getMarkup());
                    if (wgAttribute.getMarkup() != null) {
                        existingAttr.setMarkupCurrencyId(currencyId);
                    }
                    existingAttr.setMarkupPercent(wgAttribute.getMarkupPercent());
                }
                updateClientAttrs.add(existingAttr);
            } else {
                removeClientAttrs.add(existingAttr);
            }
        }
        //the new request attr is not in the existing map, should add for it
        for (WgAttributeVO newRequestAttr : newRequestWgAttributes) {
            if (!existingWgAttributeMap.containsKey(newRequestAttr.getWgAttributeId())) {
                ClientAttrMarkup attrMarkup = new ClientAttrMarkup();
                attrMarkup.setClientWorkgroupId(client.getId());
                attrMarkup.setWgAttributeId(newRequestAttr.getWgAttributeId());
                if (isEnableMargin) {
                    attrMarkup.setMarginPercent(newRequestAttr.getMarginPercent());
                } else {
                    attrMarkup.setMarkup(newRequestAttr.getMarkup());
                    if (newRequestAttr.getMarkup() != null) {
                        attrMarkup.setMarkupCurrencyId(currencyId);
                    }
                    attrMarkup.setMarkupPercent(newRequestAttr.getMarkupPercent());
                }
                addClientAttrs.add(attrMarkup);
            }
        }
        clientAttrMarkupRepository.saveAll(updateClientAttrs);
        clientAttrMarkupRepository.deleteAllInBatch(removeClientAttrs);
        clientAttrMarkupRepository.saveAll(addClientAttrs);
        deleteNEClientCache(clientId);
    }

    public void inlineEditClientMargin(Long userId, Long workgroupId, Long clientId, Boolean isDelete, WgAttributeVO wgAttributeVO) {
        boolean canManageClient = permissionService.checkAll(PermissionID.MANAGE_CLIENT, workgroupId, userId, -1L);
        if (!canManageClient) {throw new IllegalArgumentException("no permission to edit client");}
        Optional<ClientWorkgroup> optionalClientWorkgroup = clientWorkgroupRepository.findById(clientId);
        if (optionalClientWorkgroup.isEmpty()) {throw new NotFoundException("client id not found");}
        ClientWorkgroup client = optionalClientWorkgroup.get();
        // workgroup preferences
        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
        boolean isEnableMargin = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ENABLE_MARGIN, groupPrefs);
        //existing
        List<ClientAttrMarkup> existingWgAttributes = client.getClientAttrMarkups();
        Map<Long, ClientAttrMarkup> existingWgAttributeMap = new HashMap<>();
        existingWgAttributes.stream().forEach(existing -> existingWgAttributeMap.put(existing.getWgAttributeId(), existing));
        if (isDelete) {
            if (existingWgAttributeMap.containsKey(wgAttributeVO.getWgAttributeId())) {
                clientAttrMarkupRepository.deleteAllById(Collections.singleton(wgAttributeVO.getWgAttributeId()));
            } else {
                throw new IllegalArgumentException(wgAttributeVO.getWgAttributeId() + " isn't in client margin list");
            }
        } else {
            ClientAttrMarkup clientAttrMarkup = existingWgAttributeMap.getOrDefault(wgAttributeVO.getWgAttributeId(), new ClientAttrMarkup());
            clientAttrMarkup.setClientWorkgroupId(client.getId());
            clientAttrMarkup.setWgAttributeId(wgAttributeVO.getWgAttributeId());
            if (isEnableMargin) {
                clientAttrMarkup.setMarginPercent(wgAttributeVO.getMarginPercent());
            } else {
                clientAttrMarkup.setMarkup(wgAttributeVO.getMarkup());
                if (wgAttributeVO.getMarkup() != null) {
                    Workgroup ownerWorkgroup = workgroupRepository.findById(workgroupId).get();
                    Long currencyId = ownerWorkgroup.getDefaultCurrencyId();
                    clientAttrMarkup.setMarkupCurrencyId(currencyId);
                }
                clientAttrMarkup.setMarkupPercent(wgAttributeVO.getMarkupPercent());
            }
            clientAttrMarkupRepository.save(clientAttrMarkup);
        }
        deleteNEClientCache(clientId);
    }

    private void deleteNEClientCache(long clientId) {
        // update enterprise cache
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Long> updateCacheMap = new HashMap<>(1);
        updateCacheMap.put("clientId", clientId);
        String jsonBody;
        try {
            jsonBody = objectMapper.writeValueAsString(updateCacheMap);
            webClientService.createWebApiWithJsonBody(
                    "http://",
                    webClientService.getEnterpriseHost(),
                    "/noosh/internal/api/deleteClientCache",
                    HttpMethod.POST,
                    null,
                    jsonBody
            );
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("delete client cache failed");
        }
    }
}
