package com.noosh.app.service;

import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.breakout.BreakoutDTO;
import com.noosh.app.commons.dto.collaboration.SyContainableDTO;
import com.noosh.app.commons.dto.costcenter.CostCenterAllocationDTO;
import com.noosh.app.commons.dto.costcenter.CostCenterAllocationUpdateDTO;
import com.noosh.app.commons.dto.costcenter.OrderCostCenterDTO;
import com.noosh.app.commons.dto.costcenter.OrderCostCenterDetailDTO;
import com.noosh.app.commons.dto.costcenter.ProjectCostCenterDetailDTO;
import com.noosh.app.commons.dto.custom.CustomFieldDTO;
import com.noosh.app.commons.dto.customfield.UserFieldDTO;
import com.noosh.app.commons.dto.order.*;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import com.noosh.app.commons.dto.property.PropertyAttributeUpdateDTO;
import com.noosh.app.commons.dto.reason.WorkgroupReasonDTO;
import com.noosh.app.commons.dto.rfe.RfeSupplierDTO;
import com.noosh.app.commons.dto.routing.RoutingRecipientDTO;
import com.noosh.app.commons.dto.routing.RoutingSlipDTO;
import com.noosh.app.commons.dto.shipment.UofmDTO;
import com.noosh.app.commons.dto.tracking.TrackingDTO;
import com.noosh.app.commons.entity.account.AccountUser;
import com.noosh.app.commons.entity.benchmark.BenchmarkItem;
import com.noosh.app.commons.entity.breakout.Breakout;
import com.noosh.app.commons.entity.costcenter.CostCenterAllocation;
import com.noosh.app.commons.entity.estimate.EstimateItemPrice;
import com.noosh.app.commons.entity.externalItem.ExternalItem;
import com.noosh.app.commons.entity.invoice.Invoice;
import com.noosh.app.commons.entity.order.*;
import com.noosh.app.commons.entity.property.CustomField;
import com.noosh.app.commons.entity.property.Property;
import com.noosh.app.commons.entity.property.PropertyAttribute;
import com.noosh.app.commons.entity.property.PropertyParam;
import com.noosh.app.commons.entity.reason.WorkgroupReason;
import com.noosh.app.commons.entity.routing.RoutingSlip;
import com.noosh.app.commons.entity.security.ClientWorkgroup;
import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.commons.entity.security.Workgroup;
import com.noosh.app.commons.entity.shipment.Shipment;
import com.noosh.app.commons.entity.terms.AcTerms;
import com.noosh.app.commons.entity.tracking.Tracking;
import com.noosh.app.commons.enums.order.OrderTypeEnum;
import com.noosh.app.commons.vo.order.OrderDeskoidVO;
import com.noosh.app.commons.vo.order.OrderGeneralInfoVO;
import com.noosh.app.commons.vo.project.ProjectVO;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.feign.ProjectOpenFeignClient;
import com.noosh.app.feign.TrackingOpenFeignClient;
import com.noosh.app.feign.WorkgroupOpenFeignClient;
import com.noosh.app.mapper.OrderItemMapper;
import com.noosh.app.mapper.OrderVersionMapper;
import com.noosh.app.mapper.RoutingSlipMapper;
import com.noosh.app.mapper.OrderStateMapper;
import com.noosh.app.mapper.breakout.BreakoutMapper;
import com.noosh.app.mapper.externalItem.ExternalItemMapper;
import com.noosh.app.mapper.payment.PaymentMethodMapper;
import com.noosh.app.mapper.property.CustomFieldMapper;
import com.noosh.app.mapper.reason.WorkgroupReasonMapper;
import com.noosh.app.mapper.terms.AcTermsMapper;
import com.noosh.app.mapper.uofm.UofmMapper;
import com.noosh.app.repository.benchmarkItem.BenchmarkItemRepository;
import com.noosh.app.repository.breakout.BreakoutRepository;
import com.noosh.app.repository.costCenter.CostCenterAllocationRepository;
import com.noosh.app.repository.estimate.EstimateItemPriceRepository;
import com.noosh.app.repository.externalItem.ExternalItemRepository;
import com.noosh.app.repository.jpa.account.AccountUserRepository;
import com.noosh.app.repository.jpa.invoice.InvoiceRepository;
import com.noosh.app.repository.jpa.order.*;
import com.noosh.app.repository.jpa.property.CustomFieldRepository;
import com.noosh.app.repository.jpa.security.ClientWorkgroupRepository;
import com.noosh.app.repository.jpa.security.WorkgroupRepository;
import com.noosh.app.repository.jpa.property.PropertyAttributeRepository;
import com.noosh.app.repository.jpa.property.PropertyParamRepository;
import com.noosh.app.repository.jpa.property.PropertyRepository;
import com.noosh.app.repository.jpa.security.ObjectStateRepository;
import com.noosh.app.repository.mybatis.costcenter.CostCenterMyBatisMapper;
import com.noosh.app.repository.mybatis.order.OrderItemMyBatisMapper;
import com.noosh.app.repository.mybatis.order.OrderMyBatisMapper;
import com.noosh.app.repository.mybatis.property.PropertyMyBatisMapper;
import com.noosh.app.repository.reason.WorkgroupReasonRepository;
import com.noosh.app.repository.routing.RoutingSlipRepository;
import com.noosh.app.repository.shipment.ShipmentRepository;
import com.noosh.app.repository.terms.AcTermsRepository;
import com.noosh.app.repository.tracking.TrackingRepository;
import com.noosh.app.repository.uofm.UofmRepository;
import com.noosh.app.service.customfield.CustomFieldService;
import com.noosh.app.service.account.SupplierWorkgroupService;
import com.noosh.app.service.collaboration.CollaborationService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.permission.budget.ViewBudgetPermission;
import com.noosh.app.service.permission.ordering.*;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.rating.RatingService;
import com.noosh.app.service.rfe.RfeService;
import com.noosh.app.service.routing.RoutingService;
import com.noosh.app.service.util.I18NUtils;
import com.noosh.app.service.util.JwtUtil;
import com.noosh.app.service.util.NooshOneUrlUtil;
import com.noosh.app.service.util.URLUtil;

import java.time.LocalDateTime;

import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import java.math.BigDecimal;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.noosh.app.service.order.OrderListService.ORDER_SELL;

/**
 * User: leilaz
 * Date: 9/24/19
 */
@Service
@Transactional
public class OrderService {
    private final Logger log = LoggerFactory.getLogger(OrderService.class);

    @Inject
    private OrderVersionRepository orderVersionRepository;
    @Inject
    private OrderStateRepository orderStateRepository;
    @Inject
    private OrderItemRepository orderItemRepository;
    @Inject
    private OrderRepository orderRepository;
    @Inject
    private PaymentMethodRepository paymentMethodRepository;
    @Inject
    private BreakoutRepository breakoutRepository;
    @Inject
    private UofmRepository uofmRepository;
    @Inject
    private InvoiceRepository invoiceRepository;
    @Inject
    private OrderVersionMapper orderVersionMapper;
    @Inject
    private OrderItemMapper orderItemMapper;
    @Inject
    private BreakoutMapper breakoutMapper;
    @Inject
    private PaymentMethodMapper paymentMethodMapper;
    @Inject
    private UofmMapper uofmMapper;
    @Inject
    private ProjectOpenFeignClient projectOpenFeignClient;
    @Inject
    private ProjectService projectService;
    @Inject
    private PreferenceService preferenceService;
    @Inject
    private RfeService rfeService;
    @Inject
    private ViewBudgetPermission viewBudgetPermission;
    @Inject
    private ManageShipmentPermission manageShipmentPermission;
    @Inject
    private I18NUtils i18NUtils;
    @Inject
    private RoutingSlipRepository routingSlipRepository;
    @Inject
    private TrackingRepository trackingRepository;
    @Inject
    private AcTermsRepository acTermsRepository;
    @Inject
    private AcTermsMapper acTermsMapper;
    @Inject
    private WorkgroupReasonMapper workgroupReasonMapper;
    @Inject
    private CustomFieldRepository customFieldRepository;
    @Inject
    private ShipmentRepository shipmentRepository;
    @Inject
    private EstimateItemPriceRepository estimateItemPriceRepository;
    @Inject
    private WorkgroupReasonRepository workgroupReasonRepository;
    @Inject
    private PropertyParamRepository propertyParamRepository;
    @Inject
    private PropertyAttributeRepository propertyAttributeRepository;
    @Inject
    private CustomFieldMapper customFieldMapper;
    @Inject
    private EditOrderPermission editOrderPermission;
    @Inject
    private AcceptOrderPermission acceptOrderPermission;
    @Inject
    private SubmitOrderPermission submitOrderPermission;
    @Inject
    private RejectOrderPermission rejectOrderPermission;
    @Inject
    private RetractOrderPermission retractOrderPermission;
    @Inject
    private CopyOrderPermission copyOrderPermission;
    @Inject
    private CancelOrderPermission cancelOrderPermission;
    @Inject
    private PermissionService permissionService;
    @Inject
    private CreateChangeOrderPermission createChangeOrderPermission;
    @Inject
    private AcceptNotYetShippedOrderPermission acceptNotYetShippedOrderPermission;
    @Inject
    private ShipOrderPermission shipOrderPermission;
    @Inject
    private PartiallyShipOrderPermission partiallyShipOrderPermission;
    @Inject
    private DeliverOrderPermission deliverOrderPermission;
    @Inject
    private EditSupplierRefPermission editSupplierRefPermission;
    @Inject
    private EditPaymentRefPermission editPaymentRefPermission;
    @Inject
    private CostCenterAllocationPermission costCenterAllocationPermission;
    @Inject
    private EditShipmentPermission editShipmentPermission;
    @Inject
    private ViewShipmentPermission viewShipmentPermission;
    @Inject
    private ViewSupplierRatingPermission viewSupplierRatingPermission;
    @Inject
    private ViewSourcingPermission viewSourcingPermission;
    @Inject
    private CompleteOrderPermission completeOrderPermission;
    @Inject
    private OrderMyBatisMapper orderMyBatisMapper;
    @Inject
    private SupplierWorkgroupService supplierWorkgroupService;
    @Inject
    private OrderItemMyBatisMapper orderItemMyBatisMapper;
    @Inject
    private ClientWorkgroupRepository clientWorkgroupRepository;
    @Inject
    private WorkgroupRepository workgroupRepository;
    @Inject
    private ExternalItemRepository externalItemRepository;
    @Inject
    private ExternalItemMapper externalItemMapper;
    @Inject
    private RatingService ratingService;
    @Inject
    private OrderStateMapper orderStateMapper;
    @Inject
    private RoutingSlipMapper routingSlipMapper;
    @Inject
    private TrackingOpenFeignClient trackingOpenFeignClient;
    @Inject
    private CollaborationService collaborationService;
    @Inject
    private RoutingService routingService;
    @Inject
    private CustomFieldService customFieldService;
    @Inject
    private BenchmarkItemRepository benchmarkItemRepository;
    @Inject
    private AccountUserRepository accountUserRepository;

    private static final List<Long> ACCEPT_COMPLETED_ORDER_STATE_IDS = new ArrayList<>(2);
    private static final List<Long> ACCEPTED_ORDER_STATE_IDS = new ArrayList<>(7);

    @PostConstruct
    public void init() {
        ACCEPT_COMPLETED_ORDER_STATE_IDS.add(ObjectStateID.ORDER_ACCEPTED);
        ACCEPT_COMPLETED_ORDER_STATE_IDS.add(ObjectStateID.ORDER_COMPLETED);

        ACCEPTED_ORDER_STATE_IDS.add(ObjectStateID.ORDER_ACCEPTED);
        ACCEPTED_ORDER_STATE_IDS.add(ObjectStateID.ORDER_PARTIALLY_SHIPPED);
        ACCEPTED_ORDER_STATE_IDS.add(ObjectStateID.ORDER_SHIPPED);
        ACCEPTED_ORDER_STATE_IDS.add(ObjectStateID.ORDER_DELIVERED);
        ACCEPTED_ORDER_STATE_IDS.add(ObjectStateID.ORDER_ACCEPTED_NOT_YET_SHIPPED);
        ACCEPTED_ORDER_STATE_IDS.add(ObjectStateID.ORDER_COMPLETED);
        ACCEPTED_ORDER_STATE_IDS.add(ObjectStateID.ORDER_FINALIZED);
    }

    @Transactional(readOnly = true)
    public OrderVersionDTO findOrderVersionById(Long orderId, Long projectId, Long currentUserId, Long currentWorkgroupId) {
        return findOrderVersionById(orderId, projectId, currentUserId, currentWorkgroupId, false);
    }

    @Transactional(readOnly = true)
    public OrderVersionDTO findOrderVersionById(Long orderId, Long projectId, Long currentUserId, Long currentWorkgroupId, Boolean isEdit) {
        ProjectDTO parentProject = projectService.findProjectById(projectId);
        if (parentProject == null) {
            throw new NotFoundException("Parent project not found");
        }


        OrderVersion orderVersion = orderVersionRepository.findFirstByOrderIdAndIsCurrent(orderId, true);
        if (orderVersion == null)
            throw new NotFoundException("Order not found");

        OrderVersionDTO orderVersionDTO = orderVersionMapper.toDTO(orderVersion);
        orderVersionDTO.setParent(parentProject);

        /*
         * Validation for if order is attached to project, except change order.
         * Change order is related to original order by parentOrderId.
         */
        if (orderVersion.getOrderTypeId() != OrderTypeID.CHANGE_ORDER) {
            try {
                String collaborationJson = projectOpenFeignClient.getContainableByParentId(
                        orderId, ObjectClassID.ORDER, projectId, ObjectClassID.OBJECT_CLASS_PROJECT);
            } catch (Exception e) {
                throw new NotFoundException("Collaboration not found!");
            }
        }

        // when Order Status is draft, supplier may is null, avoid NullPointerException
//        if (orderVersion.getSupplierWorkgroupId() == null)
//            orderVersion.setSupplierWorkgroupId((long) -1);
//        if (orderVersion.getSupplierUserId() == null)
//            orderVersion.setSupplierUserId((long) -1);
        OrderState currentOrderState = orderStateRepository.findByOrderIdAndIsCurrent(orderId, true);
        orderVersionDTO.setOrderState(orderStateMapper.toDTO(currentOrderState));

        orderVersionDTO.setBuyerProject(findBuyerProject(orderVersionDTO, projectId));
        boolean isDualCurrency = orderVersionDTO.getRate() != null && orderVersionDTO.getRate().compareTo(BigDecimal.ZERO) > 0 && orderVersionDTO.getExCurrencyId() != null;
        BigDecimal orderItemSubtotal = new BigDecimal(0);
        BigDecimal orderItemDiscountOrSurchargeTotal = new BigDecimal(0);
        BigDecimal exOrderItemSubtotal = new BigDecimal(0);
        BigDecimal exOrderItemDiscountOrSurchargeTotal = new BigDecimal(0);
        //NO 1876, will handel routing in NE
        orderVersionDTO.setCanRoutingSupplier(false);

        // Load order item
        orderVersionDTO.setOrderItemDTOs(getOrderItems(orderVersion.getId(), isEdit));
        if (orderVersionDTO.getOrderItemDTOs() != null) {
            for (OrderItemDTO o : orderVersionDTO.getOrderItemDTOs()) {
                orderItemSubtotal = orderItemSubtotal.add(o.getValue() != null ? o.getValue() : BigDecimal.ZERO);
                orderItemDiscountOrSurchargeTotal = orderItemDiscountOrSurchargeTotal.add(o.getDors() != null ? o.getDors() : BigDecimal.ZERO);
                if (isDualCurrency) {
                    exOrderItemSubtotal = exOrderItemSubtotal.add(o.getExValue() != null ? o.getExValue() : BigDecimal.ZERO);
                    exOrderItemDiscountOrSurchargeTotal = exOrderItemDiscountOrSurchargeTotal.add(o.getExDors() != null ? o.getExDors() : BigDecimal.ZERO);
                }
            }
        }
        // Handle order price
        orderVersionDTO.setSubTotal(orderVersion.getOrderTypeId() == OrderTypeID.CHANGE_ORDER ?
                orderItemSubtotal.add(orderVersion.getMiscCost()) : orderItemSubtotal);
        orderVersionDTO.setOrderItemSubtotal(orderItemSubtotal);
        orderVersionDTO.setDiscountOrSurcharge(orderVersion.getDors() != null ? orderVersion.getDors() : BigDecimal.ZERO);
        orderVersionDTO.setDiscountOrSurchargeTotal(orderItemDiscountOrSurchargeTotal.add(orderVersion.getDors() != null ? orderVersion.getDors() : BigDecimal.ZERO));
        orderVersionDTO.setOrderItemDiscountSurchargeTotal(orderItemDiscountOrSurchargeTotal);
        orderVersionDTO.setTax(orderVersion.getTax());
        orderVersionDTO.setTaxCurrencyId(orderVersion.getTaxCurrencyId());
        orderVersionDTO.setShipping(orderVersion.getShipping());
        orderVersionDTO.setShippingCurrencyId(orderVersion.getShippingCurrencyId());
        orderVersionDTO.setGrandTotal(orderItemSubtotal.add(orderVersion.getTax())
                .add(orderVersion.getMiscCost())
                .add(orderVersion.getShipping()));
        if (isDualCurrency) {
            orderVersionDTO.setExSubTotal(orderVersion.getOrderTypeId() == OrderTypeID.CHANGE_ORDER ?
                    exOrderItemSubtotal.add(orderVersion.getExMiscCost()) : exOrderItemSubtotal);
            orderVersionDTO.setExOrderItemSubtotal(exOrderItemSubtotal);
            orderVersionDTO.setExDiscountOrSurcharge(orderVersion.getExDors());
            orderVersionDTO.setExDiscountOrSurchargeTotal(exOrderItemDiscountOrSurchargeTotal.add(orderVersion.getExDors() != null ? orderVersion.getExDors() : BigDecimal.ZERO));
            orderVersionDTO.setExOrderItemDiscountSurchargeTotal(exOrderItemDiscountOrSurchargeTotal);
            orderVersionDTO.setExTax(orderVersion.getExTax());
            orderVersionDTO.setExTaxCurrencyId(orderVersion.getExTaxCurrencyId());
            orderVersionDTO.setExShipping(orderVersion.getExShipping());
            orderVersionDTO.setExShippingCurrencyId(orderVersion.getExShippingCurrencyId());
            orderVersionDTO.setExGrandTotal(exOrderItemSubtotal.add(orderVersion.getExTax() != null ? orderVersion.getExTax() : BigDecimal.ZERO)
                    .add(orderVersion.getExMiscCost() != null ? orderVersion.getExMiscCost() : BigDecimal.ZERO)
                    .add(orderVersion.getExShipping() != null ? orderVersion.getExShipping() : BigDecimal.ZERO));
        }
        orderVersionDTO.setComments(orderVersion.getComments());

        //Set workgroup preference
        Map<String, String> buyerWorkgroupPreference = preferenceService.findGroupPrefs(orderVersionDTO.getBuyerWorkgroupId());

        orderVersionDTO.setPcAllowBreakouts(preferenceService.check(PreferenceID.PC_ALLOW_BREAKOUTS_OVERRIDE, buyerWorkgroupPreference));
        // Set closeOrderNegotiation
        orderVersionDTO.setCloseOrderNegotiation(preferenceService.check(PreferenceID.WORKGROUP_OPTION_CLOSE_ORDER_NEGOTIATION, buyerWorkgroupPreference));
        orderVersionDTO.setHideOversAndUnders(preferenceService.check(PreferenceID.WORKGROUP_OPTION_HIDE_OVERS_UNDERS, buyerWorkgroupPreference));
        // Set budget enable
        boolean projectBudgetPref = preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROJECT_BUDGET, buyerWorkgroupPreference)
                && viewBudgetPermission.check(null, currentWorkgroupId, currentUserId, projectId) && orderVersionDTO.isUserBuyer();
        orderVersionDTO.setCanViewProjectBudget(projectBudgetPref);

        // Set invoice enable
        orderVersionDTO.setInvoicingEnable(preferenceService.check(PreferenceID.WORKGROUP_OPTION_ORDER_INVOICING, buyerWorkgroupPreference));

        orderVersionDTO.setRequiredApprovalInvoice(preferenceService.check(PreferenceID.PC_INVOICE_REQUIRES_APPROVAL, buyerWorkgroupPreference));

        orderVersionDTO.setAutoAcceptFinalInvoice(preferenceService.check(PreferenceID.PC_INVOICE_AUTO_ACCEPT_FINAL_INVOICE,buyerWorkgroupPreference));

        orderVersionDTO.setInvoiceSentOnClosedOrder(preferenceService.check(PreferenceID.WORKGROUP_OPTION_SEND_FINAL_INVOICE, buyerWorkgroupPreference));

        // Set showPendingApproval
        orderVersionDTO.setShowPendingApproval(preferenceService.check(PreferenceID.WORKGROUP_OPTION_SHOW_PENDING_APPROVALS, buyerWorkgroupPreference));

        // Set show sensitive supplier
        orderVersionDTO.setIsShowSensitive(preferenceService.check(PreferenceID.PC_ORDER_SENT_TO_SECURE_SUPPLIER_ONLY, buyerWorkgroupPreference));

        orderVersionDTO.setTaxLabelString(getTaxLabelString(orderVersionDTO.getBuyerWorkgroupId()));
        orderVersionDTO.setCanManageShipment(manageShipmentPermission.check(null, currentWorkgroupId, currentUserId, projectId));

        RoutingSlip routingSlip = routingSlipRepository.findFirstByObjectIdAndObjectClassIdAndAcWorkgroupIdOrderByModDateDesc(
                orderVersionDTO.getId(), orderVersionDTO.isChangeOrder() || orderVersionDTO.isClosingChangeOrder() ? ObjectClassID.CHANGE_ORDER : ObjectClassID.ORDER, orderVersionDTO.getBuyerWorkgroupId());
        RoutingSlipDTO routingSlipBean = routingSlipMapper.toDTO(routingSlip);
        orderVersionDTO.setRoutingSlip(routingSlipBean);
        // Handle routing approve map
        boolean ignoreChangeOrder = preferenceService.check(PreferenceID.PS_ROUTE_CLOSING_CHANGE_ORDERS_ONLY, buyerWorkgroupPreference);
        orderVersionDTO.setApproveMap(findApproverMap(orderVersionDTO, ignoreChangeOrder));
        orderVersionDTO.setAutoTimeOut(preferenceService.check(PreferenceID.PS_TIMEOUT_PERIOD_ENABLED, buyerWorkgroupPreference));
        orderVersionDTO.setAllAtOnce(preferenceService.check(PreferenceID.PS_ALL_AT_ONCE, buyerWorkgroupPreference));
        orderVersionDTO.setManagerAllAtOnce(preferenceService.check(PreferenceID.PS_M_ALL_AT_ONCE, buyerWorkgroupPreference));
        orderVersionDTO.setAutoMgrTimeOut(preferenceService.check(PreferenceID.PS_M_TIMEOUT_PERIOD_ENABLED, buyerWorkgroupPreference));
        boolean isEnableComplexVAT = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ENABLE_VAT, buyerWorkgroupPreference) && preferenceService.check(PreferenceID.ENABLE_VAT, buyerWorkgroupPreference);
        orderVersionDTO.setIsEnableComplexVAT(isEnableComplexVAT);
        if (isEnableComplexVAT) {
            orderVersionDTO.setVatCode(orderVersion.getVatCode());
            orderVersionDTO.setVatRate(orderVersion.getVatRate());
        }
        //Filter recipient
        if (routingSlipBean != null && (!orderVersionDTO.getShowPendingApproval()) && (!orderVersionDTO.getAllAtOnce()) && (!orderVersionDTO.getManagerAllAtOnce()) && routingSlipBean.getCurrentRecipientId() != null) {
            routingSlipBean.setRoutingRecipients(routingSlipBean.getRoutingRecipients().stream().filter(
                    s -> s.getId().longValue() == routingSlipBean.getCurrentRecipientId()).collect(Collectors.toList()));
        }
        if (routingSlipBean != null) {
            orderVersionDTO.setIsAllRecipientsApproved(routingSlipBean.isAllRecipientsApproved());
        }

        routingService.requiresApproval(orderVersionDTO, buyerWorkgroupPreference);
        routingService.requiresManagerApproval(orderVersionDTO, buyerWorkgroupPreference);
        routingService.requiresRouting(orderVersionDTO);
        routingService.requiresManagerRouting(orderVersionDTO, buyerWorkgroupPreference);
        findCustomFields(orderVersionDTO);
        if (orderVersionDTO.getBuyerProject().getIsPaperFlow() && !orderVersionDTO.isOutsourcingSellOrder(parentProject)) {
            findExternalItems(orderVersionDTO);
        }
        findInvoiceAdjustmentOrder(orderVersionDTO, buyerWorkgroupPreference);
        return orderVersionDTO;
    }

    public void findExternalItems(OrderVersionDTO orderVersionDTO) {
        if (orderVersionDTO.getOrderItemDTOs() != null && orderVersionDTO.getOrderItemDTOs().size() > 0) {
            for (OrderItemDTO orderItemDTO : orderVersionDTO.getOrderItemDTOs()) {
                List<ExternalItem> externalItems = externalItemRepository.findByObjectIdAndObjectClassIdOrderByItemIndex(orderItemDTO.getId(), ObjectClassID.ORDER_ITEM);
                orderItemDTO.setExternalItemDTOs(externalItemMapper.toDTOs(externalItems));
            }
        }
    }

    private void findInvoiceAdjustmentOrder(OrderVersionDTO orderVersionDTO,
                                            Map<String, String> buyerWorkgroupPreference) {
        boolean enableInvoiceAdjustmentOrder = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ENABLE_INVOICE_ADJUSTMENT, buyerWorkgroupPreference);
        if (enableInvoiceAdjustmentOrder) {
            if (orderVersionDTO.getOrderClassificationId() == OrderClassificationID.INVOICE_ADJUSTMENT) {
                long invoiceAdjustmentParentOrderId = orderVersionDTO.getOrder().getInvoiceAdjustmentParentOrderId();
                OrderVersion orderVersion = orderVersionRepository.findFirstByOrderIdAndIsCurrent(invoiceAdjustmentParentOrderId, true);
                orderVersionDTO.setInvoiceAdjustmentParentOrder(orderVersionMapper.toDTO(orderVersion));
            }
            else {
                List<OrderVersion> invoiceAdjustmentOrders = orderVersionRepository.findInvoiceAdjustmentOrderByParentOrderId(orderVersionDTO.getOrderId());
                orderVersionDTO.setInvoiceAdjustmentOrders(orderVersionMapper.toDTOs(invoiceAdjustmentOrders));
            }
        }
    }

    public ProjectDTO findBuyerProject(OrderVersionDTO orderVersionDTO, Long projectId) {

        ProjectDTO buyerProject;
        ProjectDTO parent = orderVersionDTO.getParent();
        if (parent == null) {
            parent = projectService.findProjectById(projectId);
            orderVersionDTO.setParent(parent);
        }
        if (orderVersionDTO.isUserBuyer()) {
            buyerProject = parent;
        } else if (parent.isMaster()) {
            buyerProject = projectService.findProjectByMasterAndWorkgroupId(parent.getId(), orderVersionDTO.getBuyerWorkgroupId());
        } else {
            buyerProject = projectService.findProjectById(parent.getMasterProjectId());
            if (buyerProject.getOwnerWorkgroupId().longValue() != orderVersionDTO.getBuyerWorkgroupId().longValue()) {
                throw new NotFoundException("buyer project not found");
            }
        }
        return buyerProject;
    }

    private List<OrderItemDTO> getOrderItems(Long orderVersionId) {
        return getOrderItems(orderVersionId, false);
    }

    private List<OrderItemDTO> getOrderItems(Long orderVersionId, Boolean isEdit) {
        List<OrderItem> orderItems = orderItemRepository.findByOrderVersionIdOrderByItemIndex(orderVersionId);

        if (orderItems != null && orderItems.size() > 0) {
            List<OrderItemDTO> orderItemDTOs = orderItems.stream().map(orderItemMapper::toDTO).collect(Collectors.toList());
            for (OrderItemDTO o : orderItemDTOs) {
                // Default order items, but if isConvertible is true, then use spec default one
                UofmDTO defaultUofmDTO = uofmMapper.toDTO(uofmRepository.findDefaultBySpecTypeId(o.getSpec().getSpSpecTypeId()));
                if (defaultUofmDTO != null && defaultUofmDTO.getIsConvertible()) {
                    o.setUofm(defaultUofmDTO);
                }
                o.setDefaultUofm(defaultUofmDTO);
                //Check breakouts, check the order's supplier whether he can see the breakout, or the breakout is shared to all
                if (o.getAllowBreakouts()) {
                    List<Breakout> breakouts = breakoutRepository.findByObjectIdAndObjectClassId(o.getId(), ObjectClassID.ORDER_ITEM);
                    if (breakouts != null && breakouts.size() > 0) {
                        if (isEdit) {
                            List<BreakoutDTO> dtos = breakouts.stream().filter(b -> b.getNestingLevel() == (long) 1)
                                    .map(b -> breakoutMapper.toDTO(b, breakouts, 1))
                                    .collect(Collectors.toList());
                            o.setBreakouts(dtos);
                        } else {
                            o.setBreakouts(breakouts.stream().map(breakoutMapper::toDTO).collect(Collectors.toList()));
                        }
                    }
                }
            }
            return orderItemDTOs;
        }
        return null;
    }

    @Transactional(readOnly = true)
    public OrderVersionDTO findSimpleOrderById(Long orderId) {
        OrderVersion orderVersion = orderVersionRepository.findFirstByOrderIdAndIsCurrent(orderId, true);
        if (orderVersion == null)
            return null;

        OrderVersionDTO orderVersionDTO = orderVersionMapper.toDTO(orderVersion);
        boolean isDualCurrency = orderVersionDTO.getRate() != null && orderVersionDTO.getRate().compareTo(BigDecimal.ZERO) > 0 && orderVersionDTO.getExCurrencyId() != null;
        // when Order Status is draft, supplier may is null, avoid NullPointerException
//        if (orderVersion.getSupplierWorkgroupId() == null)
//            orderVersion.setSupplierWorkgroupId((long) -1);
//        if (orderVersion.getSupplierUserId() == null)
//            orderVersion.setSupplierUserId((long) -1);

        BigDecimal orderItemSubtotal = new BigDecimal(0);
        Long orderItemSubtotalCurrencyId = null;
        BigDecimal orderItemDiscountOrSurchargeTotal = new BigDecimal(0);
        BigDecimal exOrderItemSubtotal = new BigDecimal(0);
        BigDecimal exOrderItemDiscountOrSurchargeTotal = new BigDecimal(0);

        // Load order item
        orderVersionDTO.setOrderItemDTOs(getOrderItems(orderVersion.getId()));
        if (orderVersionDTO.getOrderItemDTOs() != null) {
            for (OrderItemDTO o : orderVersionDTO.getOrderItemDTOs()) {
                orderItemSubtotal = orderItemSubtotal.add(o.getValue() != null ? o.getValue() : BigDecimal.ZERO);
                orderItemSubtotalCurrencyId = o.getValueCurrencyId();
                orderItemDiscountOrSurchargeTotal = orderItemDiscountOrSurchargeTotal.add(o.getDors() != null ? o.getDors() : BigDecimal.ZERO);
                if (isDualCurrency) {
                    exOrderItemSubtotal = exOrderItemSubtotal.add(o.getExValue() != null ? o.getExValue() : BigDecimal.ZERO);
                    exOrderItemDiscountOrSurchargeTotal = exOrderItemDiscountOrSurchargeTotal.add(o.getExDors() != null ? o.getExDors() : BigDecimal.ZERO);
                }
            }
        }

        // Handle order price
        orderVersionDTO.setSubTotal(orderVersion.getOrderTypeId() == OrderTypeID.CHANGE_ORDER ?
                orderItemSubtotal.add(orderVersion.getMiscCost()) : orderItemSubtotal);
        orderVersionDTO.setOrderItemSubtotal(orderItemSubtotal);
        orderVersionDTO.setOrderItemSubtotalCurrencyId(orderItemSubtotalCurrencyId);
        orderVersionDTO.setDiscountOrSurchargeTotal(orderItemDiscountOrSurchargeTotal.add(orderVersion.getDors() != null ? orderVersion.getDors() : BigDecimal.ZERO));
        orderVersionDTO.setOrderItemDiscountSurchargeTotal(orderItemDiscountOrSurchargeTotal);
        orderVersionDTO.setTax(orderVersion.getTax());
        orderVersionDTO.setTaxCurrencyId(orderVersion.getTaxCurrencyId());
        orderVersionDTO.setShipping(orderVersion.getShipping());
        orderVersionDTO.setShippingCurrencyId(orderVersion.getShippingCurrencyId());
        orderVersionDTO.setGrandTotal(orderItemSubtotal.add(orderVersion.getTax())
                .add(orderVersion.getMiscCost())
                .add(orderVersion.getShipping()));
        orderVersionDTO.setGrandTotalCurrencyId(orderItemSubtotalCurrencyId);

        if (isDualCurrency) {
            orderVersionDTO.setExSubTotal(orderVersion.getOrderTypeId() == OrderTypeID.CHANGE_ORDER ?
                    exOrderItemSubtotal.add(orderVersion.getExMiscCost()) : exOrderItemSubtotal);
            orderVersionDTO.setExOrderItemSubtotal(exOrderItemSubtotal);
            orderVersionDTO.setExDiscountOrSurchargeTotal(exOrderItemDiscountOrSurchargeTotal.add(orderVersion.getExDors() != null ? orderVersion.getExDors() : BigDecimal.ZERO));
            orderVersionDTO.setExOrderItemDiscountSurchargeTotal(exOrderItemDiscountOrSurchargeTotal);
            orderVersionDTO.setExTax(orderVersion.getExTax());
            orderVersionDTO.setExTaxCurrencyId(orderVersion.getExTaxCurrencyId());
            orderVersionDTO.setExShipping(orderVersion.getExShipping());
            orderVersionDTO.setExShippingCurrencyId(orderVersion.getExShippingCurrencyId());
            orderVersionDTO.setExGrandTotal(exOrderItemSubtotal.add(orderVersion.getExTax() != null ? orderVersion.getExTax() : BigDecimal.ZERO)
                    .add(orderVersion.getExMiscCost() != null ? orderVersion.getExMiscCost() : BigDecimal.ZERO)
                    .add(orderVersion.getExShipping() != null ? orderVersion.getExShipping() : BigDecimal.ZERO));
        }


        OrderState currentOrderState = orderStateRepository.findByOrderIdAndIsCurrent(orderId, true);
        orderVersionDTO.setOrderState(orderStateMapper.toDTO(currentOrderState));
        return orderVersionDTO;
    }

    @Transactional(readOnly = true)
    public OrderVersionDTO findRatingOrderById(Long orderId) {
        OrderVersion orderVersion = orderVersionRepository.findFirstByOrderIdAndIsCurrent(orderId, true);
        if (orderVersion == null)
            return null;

        OrderVersionDTO orderVersionDTO = orderVersionMapper.toDTO(orderVersion);

        // when Order Status is draft, supplier may is null, avoid NullPointerException
//        if (orderVersion.getSupplierWorkgroupId() == null)
//            orderVersion.setSupplierWorkgroupId((long) -1);
//        if (orderVersion.getSupplierUserId() == null)
//            orderVersion.setSupplierUserId((long) -1);

        BigDecimal orderItemSubtotal = new BigDecimal(0);
        BigDecimal orderItemDiscountOrSurchargeTotal = new BigDecimal(0);

        // Load order item
        orderVersionDTO.setOrderItemDTOs(getOrderItems(orderVersion.getId()));
        if (orderVersionDTO.getOrderItemDTOs() != null) {
            for (OrderItemDTO o : orderVersionDTO.getOrderItemDTOs()) {
                orderItemSubtotal = orderItemSubtotal.add(o.getValue() != null ? o.getValue() : BigDecimal.ZERO);
                orderItemDiscountOrSurchargeTotal = orderItemDiscountOrSurchargeTotal.add(o.getDors() != null ? o.getDors() : BigDecimal.ZERO);
            }
        }

        // Handle order price
        orderVersionDTO.setSubTotal(orderVersion.getOrderTypeId() == OrderTypeID.CHANGE_ORDER ?
                orderItemSubtotal.add(orderVersion.getMiscCost()) : orderItemSubtotal);
        orderVersionDTO.setOrderItemSubtotal(orderItemSubtotal);
        orderVersionDTO.setDiscountOrSurchargeTotal(orderItemDiscountOrSurchargeTotal.add(orderVersion.getDors() != null ? orderVersion.getDors() : BigDecimal.ZERO));
        orderVersionDTO.setOrderItemDiscountSurchargeTotal(orderItemDiscountOrSurchargeTotal);
        orderVersionDTO.setTax(orderVersion.getTax());
        orderVersionDTO.setShipping(orderVersion.getShipping());
        orderVersionDTO.setGrandTotal(orderItemSubtotal.add(orderVersion.getTax())
                .add(orderVersion.getMiscCost())
                .add(orderVersion.getShipping()));

        return orderVersionDTO;

    }

    public OrderDetailDTO findOrderInfo(Long orderId, Long projectId, Long currentUserId, Long currentWorkgroupId, String orderType) throws Exception {
        OrderDetailDTO orderDetailDTO = new OrderDetailDTO();
        OrderVersionDTO orderVersionDTO = findOrderVersionById(orderId, projectId, currentUserId, currentWorkgroupId);
        orderDetailDTO.setOrderVersionDTO(orderVersionDTO);
        findOrderButtons(orderId, projectId, currentWorkgroupId, currentUserId, orderDetailDTO, orderType);

        return orderDetailDTO;

    }

    private String getTaxLabelString(Long workgrouopId) {
        String labelStr = i18NUtils.getMessage(StringID.TAX_DEFAULT_LABEL_STR);
        Map<String, String> prefs = preferenceService.findGroupPrefs(workgrouopId, Arrays.asList(PreferenceID.PC_TAX_LABEL_STRING));
        if (prefs != null && (!prefs.isEmpty()) && prefs.containsKey(PreferenceID.PC_TAX_LABEL_STRING) && prefs.get(PreferenceID.PC_TAX_LABEL_STRING) != null) {
            labelStr = prefs.get(PreferenceID.PC_TAX_LABEL_STRING);
        }
        return labelStr;
    }

    private Map findApproverMap(OrderVersionDTO orderVersionDTO, boolean ignoreChangeOrder) {
        Map approverMap = new HashMap();
        String slipName = orderVersionDTO.getRoutingSlip() != null ? orderVersionDTO.getRoutingSlip().getSlipName() : "";
        List<Tracking> trackings = trackingRepository.findByContainable(orderVersionDTO.getParent().getId(),
                ObjectClassID.OBJECT_CLASS_PROJECT, ObjectClassID.OBJECT_CLASS_TRACKING, TrackingTypeID.ROUTING_OBJECT_APPROVED);

        List<Object[]> routings = new ArrayList<>();
        if (trackings != null) {
            for (Tracking trackingWidgetDTO : trackings) {
                String s = trackingWidgetDTO.getI18nData();
                // do some filter like "title=o1 and title=o"
                Map trackingData = new HashMap();
                try {
                    URLUtil.queryStringToMap(s, trackingData);
                    String title = (String) trackingData.get("title");
                    String OBJECT_CLASS = "objectClassName=";
                    if (title != null && title.length() > 0) {
                        if (slipName.equalsIgnoreCase(title)) {
                            int i = s.indexOf(OBJECT_CLASS);
                            String ss = s.substring(i + OBJECT_CLASS.length());
                            int j = ss.indexOf('&');
                            String orderType = URLUtil.decode(ss.substring(0, j));
                            Object[] objs = new Object[2];
                            objs[0] = orderType;
                            objs[1] = trackingWidgetDTO.getEnactingUser() != null ?
                                    trackingWidgetDTO.getEnactingUser().getPerson().getFullName() : "";
                            routings.add(objs);
                        }
                    }
                } catch (Exception e) {
                    log.error("Error in method: OrderService findOrderVersionById, " + e.getMessage());
                }
            }

            for (int i = 0; i < routings.size(); i++) {
                Object[] objs = (Object[]) routings.get(i);
                String orderType = (String) objs[0];
                String userName = (String) objs[1];

                if ("Order".equals(orderType)) {
                    orderType = "Original Order";
                }
                putConcatNames(approverMap, orderType, userName);

            }

            //boolean ignoreChangeOrder = WorkgroupPreferences.enabled(routingGroupId, PreferenceID.PS_ROUTE_CLOSING_CHANGE_ORDERS_ONLY);
            String CHANGE_ORDER = "Change Order";
            String CLOSED_CHANGE_ORDER = "Closed Change Order";
            if (ignoreChangeOrder) {
                // Change all Change Orders to Closed Change Orders
                String names = (String) approverMap.get(CHANGE_ORDER);
                if (names != null) {
                    putConcatNames(approverMap, CLOSED_CHANGE_ORDER, names);
                    approverMap.remove(CHANGE_ORDER);
                }
            } else {
                String names = (String) approverMap.get(CHANGE_ORDER);
                if (names != null) {
                    putConcatNames(approverMap, "     " + CHANGE_ORDER, names);
                    approverMap.remove(CHANGE_ORDER);
                }
            }
        }
        return approverMap;
    }

    private void putConcatNames(Map map, String type, String name) {
        String s = (String) map.get(type);
        if (s == null) {
            s = name;
        } else {
            s += "," + name;
        }
        map.put(type, s);
    }

    private void findCustomFields(OrderVersionDTO orderVersionDTO) {
        if (orderVersionDTO.getBugetTypeFieldId() != null
                && orderVersionDTO.getBugetTypeFieldId().longValue() != -1) {
            CustomField customField = customFieldRepository.findByOwnerWorkgroupIdAndId(orderVersionDTO.getBuyerWorkgroupId(),
                    orderVersionDTO.getBugetTypeFieldId());
            if (customField != null) {
                CustomFieldDTO customFieldDTO = customFieldMapper.toDTO(customField);
                orderVersionDTO.setCustomFieldDTO(customFieldDTO);
            }
        }
    }

    private void findOrderButtons(Long orderId, Long projectId, Long currentWorkgroupId, Long currentUserId,
                                  OrderDetailDTO orderDetailDTO, String orderType) {
        OrderVersionDTO orderVersionDTO = orderDetailDTO.getOrderVersionDTO();

        Boolean canAccept = Boolean.FALSE;
        // Edit draft, delete draft and edit order
        if (editOrderPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)) {
            String editDraft = NooshOneUrlUtil.composeEditDraftOrderLinkToEnterprise(projectId, orderId,
                    orderVersionDTO.getVersion(), orderType);
            String deleteDraft = NooshOneUrlUtil.composeDeleteDraftOrderLinkToEnterprise(projectId, orderId,
                    orderVersionDTO.getVersion(), orderType);
            if ((!orderVersionDTO.isDraft())) {
                String edit = NooshOneUrlUtil.composeEditOrderLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion(), orderType);
                orderDetailDTO.setEditButton(edit);
            }
            if (orderVersionDTO.isDraft()) {
                orderDetailDTO.setEditDraftButton(editDraft);
                orderDetailDTO.setDeleteDraftButton(deleteDraft);
            }
        }

        if (!(orderVersionDTO.getOrderTypeId().longValue() == OrderTypeID.CHANGE_ORDER)) {
            // Accept order
            if ((!(orderVersionDTO.getOrderTypeId().longValue() == OrderTypeID.CHANGE_ORDER)) &&
                    acceptOrderPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId) && (!orderVersionDTO.getRequiresApproval())
                    && (!orderVersionDTO.getIsDisapproved()) && (!orderVersionDTO.isDraft())) {
                String accept = NooshOneUrlUtil.composeAcceptOrderLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion(), orderType);
                canAccept = true;
                orderDetailDTO.setAcceptButton(accept);
            }

            ProjectDTO buyerProject = orderVersionDTO.getBuyerProject();
            if (buyerProject.getIsPaperFlow()) {
            /* Not support paper order for now
            if (orderVersionDTO.isUserSupplier()) {
                //in Direct flow, as long as it contains a paper product
                if (buyerProject.getIsPaperDirect() && aggregatedOrder.isExistsPaperSpec() && !aggregatedOrder.isContainsExternalItems()) {
                    canAccept = false;
                    // Now don't support render spec
//                    if (!acceptButton.isHidden())
//                        template.set("containsPaperSpec", Boolean.TRUE);
                }
                //in Directed flow, it needs to contain paper product and prices
                else if (!buyerProject.getIsPaperDirect() && !aggregatedOrder.isAllPaperSpecsContainingPricing()) {
                    canAccept = false;
                    // Now don't support render spec
//                    if (!acceptButton.isHidden())
//                        template.set("containsPaperSpec", Boolean.TRUE);
                }
            }
            else {
                if (buyerProject.getIsPaperDirect() && aggregatedOrder.isExistsPaperSpec()) {
                    OrderItemBean[] originalOrderItems = aggregatedOrder.getOrderItems();
                    int size = originalOrderItems.length;
                    for (int i = 0; i < size; i++) {
                        ExternalItemBean[] existingItems = ExternalItemBeanHome.findByObject(originalOrderItems[i], true);
                        originalOrderItems[i].setExternalItems(existingItems);
                    }

                    if (!aggregatedOrder.isAllPaperSpecsContainingPricing()) {
                        acceptButton.disable();
                        acceptAndPrintButton.disable();
                        if (!acceptButton.isHidden())
                            template.set("containsPaperSpec", Boolean.TRUE);
                    }
                }
            } */
            }

            // routeForApprovalButton
            // routeForManagerApprovalButton
            if (orderVersionDTO.getRequiresApproval() && (!orderVersionDTO.isDraft()) && (!orderVersionDTO.isRetracted())) {
                String routeForManagerApprovalButton = null;
                if (orderVersionDTO.isUserBuyer()) {
                    if (orderVersionDTO.getRequiresRouting() && (!orderVersionDTO.isCompleted())) {
                        String routeForApprovalButton = NooshOneUrlUtil.composeRouteForApprovalOrderLinkToEnterprise(projectId, orderId,
                                orderVersionDTO.getVersion(), orderType);
                        orderDetailDTO.setRouteForApprovalButton(routeForApprovalButton);
                    } else if (orderVersionDTO.getRequiresManagerRouting()) {
                        routeForManagerApprovalButton = NooshOneUrlUtil.composeRouteForManagerApprovalOrderLinkToEnterprise(projectId, orderId,
                                orderVersionDTO.getVersion(), orderType);
                        orderDetailDTO.setRouteForManagerApprovalButton(routeForManagerApprovalButton);
                    }
                }
            } else if (orderVersionDTO.getRequiresManagerApproval() && (!orderVersionDTO.isDraft())
                    && orderVersionDTO.getRequiresManagerRouting() && (!orderVersionDTO.isRetracted())) {
                String routeForManagerApprovalButton = NooshOneUrlUtil.composeRouteForManagerApprovalOrderLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion(), orderType);
                orderDetailDTO.setRouteForManagerApprovalButton(routeForManagerApprovalButton);
            }

            // submitButton
            if (submitOrderPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)
                    && (!orderVersionDTO.isDraft())) {
                String submitButton = NooshOneUrlUtil.composeSubmitOrderLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion(), orderVersionDTO.getId(), orderType);
                orderDetailDTO.setSubmitButton(submitButton);
            }

            // reject button
            if (rejectOrderPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)
                    && (!orderVersionDTO.isDraft())) {
                String rejectButton = NooshOneUrlUtil.composeRejectOrderDirectlyLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion(), orderVersionDTO.getId(), orderType);
                orderDetailDTO.setRejectButton(rejectButton);
            }

            // retract button
            if (retractOrderPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)
                    && (!orderVersionDTO.isDraft())) {
                String retractButton = NooshOneUrlUtil.composeRetractOrderLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion(), orderVersionDTO.getId(), orderType);
                orderDetailDTO.setRetractButton(retractButton);
            }

            // copy button
            if (copyOrderPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)
                    && (!orderVersionDTO.isDraft()) && (!orderVersionDTO.isPaperOrder())) {
                String copyButton = NooshOneUrlUtil.composeReorderOrderLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion(), orderVersionDTO.getId(), orderType);
                orderDetailDTO.setReorderButton(copyButton);
            }

            // cancel button
            if (cancelOrderPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)
                    && (!cancelOrderPermission.checkSupplierGroup(orderVersionDTO, currentWorkgroupId))) {
                if (!checkAcceptInvoice(orderVersionDTO.getBuyerWorkgroupId(),
                        orderId, projectId, ObjectClassID.OBJECT_CLASS_PROJECT, currentUserId)) {
                    String cancelButton = NooshOneUrlUtil.composeCancelOrderLinkToEnterprise(projectId, orderId,
                            orderVersionDTO.getVersion(), orderVersionDTO.getId(), orderType);
                    orderDetailDTO.setCancelButton(cancelButton);
                }
            }

            // create Change Order Button
            if (createChangeOrderPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)
                    && (!orderVersionDTO.isDraft()) && (!orderVersionDTO.isPaperOrder())) {
                String createChangeOrderButton = NooshOneUrlUtil.composeCreateChangeOrderLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion(), orderVersionDTO.getId(), orderType);
                orderDetailDTO.setCreateChangeOrderButton(createChangeOrderButton);
            }

            // update Button
            if (canUpdateOrderStatus(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)) {
                String updateButton = NooshOneUrlUtil.composeUpdateOrderLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion(), orderVersionDTO.getId(), orderType);
                orderDetailDTO.setUpdateButton(updateButton);
            }

            // edit supplier ref Button
            if (editSupplierRefPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)
                    && (!orderVersionDTO.isDraft())) {
                String backUrl = "/noosh/procurement/ordering/order/viewOrder?orderId=" + orderId + "&objectId="
                        + projectId + "&objectClassId=1000000&renderSpecs=false";
                String editSupplierRefButton = NooshOneUrlUtil.composeUpdateSupplierTrackingLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion(), backUrl, orderType);
                orderDetailDTO.setEditSupplierRefButton(editSupplierRefButton);
            }

            // edit general info Button
            if (editPaymentRefPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)
                    && (!orderVersionDTO.isDraft())) {
                String backUrl = "/noosh/procurement/ordering/order/viewOrder?orderId=" + orderId + "&objectId="
                        + projectId + "&objectClassId=1000000&renderSpecs=false";
                String editInfoButton = NooshOneUrlUtil.composeEditGeneralInfoLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion(), backUrl, orderType);
                orderDetailDTO.setEditInfoButton(editInfoButton);
            }

            // cost Center Allocation Button
            if (costCenterAllocationPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)) {
                String costCenterAllocation = NooshOneUrlUtil.composeCostCenterAllocationLinkToEnterprise(projectId,
                        orderId, orderVersionDTO.getVersion());
                orderDetailDTO.setCostCenterAllocationButton(costCenterAllocation);
            }

            // create edit Shipments Button
            List<Long> jobIds = new ArrayList<>();
            orderVersionDTO.getOrderItemDTOs().stream().forEach(o -> jobIds.add(o.getJobId()));
            List<Shipment> shipments = shipmentRepository.findByPcJobIdIn(jobIds);
            if (editShipmentPermission.check(null, currentWorkgroupId, currentUserId, projectId)
                    && (!orderVersionDTO.isPaperOrder())
                    && viewShipmentPermission.check(null, currentWorkgroupId, currentUserId, projectId)
                    && orderVersionDTO.isAccepted() && (!orderVersionDTO.isPaperOrder())) {
                List<String> shipmentIds = new ArrayList<>();
                shipments.stream().forEach(s -> shipmentIds.add("" + s.getShShipmentId()));
                String backUrl = "/noosh/procurement/ordering/order/viewOrder?orderId=" + orderId + "&objectId="
                        + projectId + "&objectClassId=1000000&renderSpecs=false";
                String editShipment = NooshOneUrlUtil.composeEditShipmentLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion(), backUrl, shipmentIds);
                orderDetailDTO.setEditShipmentButton(editShipment);
            }

            // create supplier rating Button
            if (viewSupplierRatingPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)
                    && (!orderVersionDTO.isDraft())) {
                String supplierRating = NooshOneUrlUtil.composeSupplierRatingLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion());
                orderDetailDTO.setSupplierRatingButton(supplierRating);
            }

            // create supplier sourcing Button
            if (viewSourcingPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)
                    && (!orderVersionDTO.isDraft())) {
                String sourcingButton = NooshOneUrlUtil.composeSourcingLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion());
                orderDetailDTO.setSourcingStrategiesButton(sourcingButton);
            }

            // create closing order Button
            if (completeOrderPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId) && (!orderVersionDTO.isDraft())
                    && (!(hasClosedChangeOrder(orderId, orderVersionDTO.getParent().getOwnerWorkgroupId()) || orderVersionDTO.isCompleted()))) {
                String completeButton = NooshOneUrlUtil.composeCompleteOrderLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion(), orderType);
                orderDetailDTO.setCompleteButton(completeButton);
            }

            // Create approve and disapprove button
            boolean allowResponse = false;

            if (orderVersionDTO.getRoutingSlip() != null) {
                RoutingSlipDTO rsb = orderVersionDTO.getRoutingSlip();
                List<RoutingRecipientDTO> recipients = rsb.getRoutingRecipients();

                if (recipients != null) {
                    for (int i = 0; i < recipients.size(); i++) {

                        /** @todo: ROUTING: Should we check whether it is this user's turn to respond if one after another is enabled */
                        if (currentUserId.longValue() == recipients.get(i).getToUserId() && recipients.get(i).getResponse() == -1) {
                            if (rsb.getIsAllAtOnce() == null || rsb.getIsAllAtOnce().shortValue() == 0) {
                                if (rsb.getCurrentRecipientId().longValue() == recipients.get(i).getId()) {
                                    allowResponse = true;
                                }
                            } else {
                                allowResponse = true;
                            }
                        }
                    }
                    if (rsb.getFirstResponseMode() != null && rsb.getFirstResponseMode().shortValue() == 1
                            && rsb.getPendingCount() < recipients.size())
                        allowResponse = false;
                }
                if (allowResponse) {
                    String backUrl = "/noosh/procurement/ordering/order/viewOrder?orderId=" + orderId + "&objectId="
                            + projectId + "&objectClassId=1000000&renderSpecs=false";
                    String approveButton = NooshOneUrlUtil.composeRoutingResponseLinkToEnterprise(projectId, orderId,
                            ObjectClassID.ORDER, backUrl, (long) 1, orderVersionDTO.getBuyerWorkgroupId(),
                            orderVersionDTO.getId(), rsb.getSlipName());
                    String disapproveButton = NooshOneUrlUtil.composeRoutingResponseLinkToEnterprise(projectId, orderId,
                            ObjectClassID.ORDER, backUrl, (long) 0, orderVersionDTO.getBuyerWorkgroupId(),
                            orderVersionDTO.getId(), rsb.getSlipName());
                        orderDetailDTO.setRoutingApproveButton(approveButton);
                        orderDetailDTO.setRoutingDisapproveButton(disapproveButton);
                }
            }

            // dismiss supplier button
            if (permissionService.checkAll(PermissionID.DISMISS_SUPPLIER, currentWorkgroupId, currentUserId, projectId)) {
                String dismissSupplierButton = null;
                // check if current user is supplier
                if (!orderVersionDTO.isUserSupplier()) {
                    // determine whether there is an estimate related to the order
                    List<Long> estimateItemPriceIds = new ArrayList<>();
                    List<OrderItemDTO> orderItems = orderVersionDTO.getOrderItemDTOs();
                    for (int i = 0; i < orderItems.size(); i++) {
                        if (orderItems.get(i).getEstimateItemPriceId() != null && orderItems.get(i).getEstimateItemPriceId().longValue() > 0) {
                            estimateItemPriceIds.add(orderItems.get(i).getEstimateItemPriceId());
                        }
                    }
                    // if there's no estimate related to this order, hide the button and exit
                    if (estimateItemPriceIds.size() > 0) {
                        // find the RFE the the estimate. If none can be found, hide the button and exit
                        List<EstimateItemPrice> itemPrices = estimateItemPriceRepository.findByIdIn(estimateItemPriceIds);
                        if (itemPrices != null && itemPrices.size() > 0) {
                            // check whether there are still active suppliers for this RFE. If not, hide the button and exit
                            long rfeId = itemPrices.get(0).getEstimateItem().getRfeItem().getRfeId();

                            dismissSupplierButton = NooshOneUrlUtil.composedismissSupplierButtonLinkToEnterprise(projectId, rfeId);

                            if (!permissionService.checkAll(PermissionID.VIEW_RFE, currentWorkgroupId, currentUserId, projectId)) { // the current user may not have access to RFE
                                dismissSupplierButton = null;
                            }

                            List<RfeSupplierDTO> dismissableGroups = rfeService.findDismissableSupplier(rfeId, projectId);
                            if (dismissableGroups == null || dismissableGroups.size() == 0) {
                                dismissSupplierButton = null;
                            }

                        }

                    }

                }
                orderDetailDTO.setDismissButton(dismissSupplierButton);
            }
        }

        // Print Vendor PO
        if (orderVersionDTO.getBuyerProject().isMaster() && (!orderVersionDTO.isDraft())) {
            orderDetailDTO.setPrintVendorReportButton(NooshOneUrlUtil.composePrintVendorReportLinkToEnterprise(
                    orderVersionDTO.getParent().getId(), orderVersionDTO.getParent().getMasterProjectId() == null ?
                    orderVersionDTO.getParent().getId() : orderVersionDTO.getParent().getMasterProjectId(),
                    orderId, orderVersionDTO.getParent().getClientWorkgroupId()));
        }
        orderDetailDTO.setCanAccept(canAccept);
        orderDetailDTO.setShowApprovalLink(NooshOneUrlUtil.composeShowPendingApprovalLinkToEnterprise(projectId, orderId, false));
        orderDetailDTO.setV1000IReportLink(NooshOneUrlUtil.composeV1000IReportLinkToEnterprise(projectId, orderId,
                orderVersionDTO.getParent().getClientWorkgroupId()));
    }

    private boolean canUpdateOrderStatus(OrderVersionDTO order, Long currentWorkgroupId, Long currentUserId, Long projectId) {
        if (!order.isAccepted()) return false;
        if (acceptOrderPermission.check(order, currentWorkgroupId, currentUserId, projectId)
                || acceptNotYetShippedOrderPermission.check(order, currentWorkgroupId, currentUserId, projectId)
                || shipOrderPermission.check(order, currentWorkgroupId, currentUserId, projectId)
                || partiallyShipOrderPermission.check(order, currentWorkgroupId, currentUserId, projectId)
                || deliverOrderPermission.check(order, currentWorkgroupId, currentUserId, projectId))
            return true;
        else
            return false;
    }

    public boolean hasPendingChangeOrders(Long orderId) {
        List<Order> changeOrders = orderRepository.findByParentOrderIdOrderByIdAsc(orderId);
        if (changeOrders != null) {
            for (Order o : changeOrders) {
                OrderVersion orderVersion = orderVersionRepository.findFirstByOrderIdAndIsCurrent(o.getId(), true);
                OrderVersionDTO orderVersionDTO = orderVersionMapper.toDTO(orderVersion);

                orderVersionDTO.setOrderState(orderStateMapper.toDTO(o.getOrderStateSet().stream().filter(
                        s -> s.getIsCurrent() != null && s.getIsCurrent()).findAny().get()));
                if (orderVersionDTO.isPending()) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean hasClosedChangeOrder(Long orderId, Long parentWorkgroupId) {
        boolean hasCCO = false;
        List<Order> changeOrders = orderRepository.findByParentOrderIdOrderByIdAsc(orderId);
        if (changeOrders.size() > 0) {
            for (int i = 0; !hasCCO && i < changeOrders.size(); i++) {
                OrderVersion orderVersion = orderVersionRepository.findFirstByOrderIdAndIsCurrent(changeOrders.get(i).getId(), true);
                OrderVersionDTO orderVersionDTO = orderVersionMapper.toDTO(orderVersion);

                orderVersionDTO.setOrderState(orderStateMapper.toDTO(changeOrders.get(i).getOrderStateSet().stream().filter(
                        s -> s.getIsCurrent() != null && s.getIsCurrent()).findAny().get()));
                if (orderVersionDTO.isClosingChangeOrder() && (!orderVersionDTO.isRejected())
                        && (!orderVersionDTO.isRetracted()) && (!orderVersionDTO.isReplaced())) {
                    //buyer could have one draft closing order, so does supplier
                    if (orderVersionDTO.isDraft() && orderVersionDTO.isCreatedByTheUserOfParentGroup(parentWorkgroupId)) {
                        hasCCO = true;
                    }
                }
            }
        }

        return hasCCO;
    }

    /**
     * check if invoice can be accepted
     *
     * @param buyerWorkgroupId buyerWorkgroupId
     * @param orderId          orderId
     * @param parentId         parentId
     * @param parentClassId    parentClassId
     * @param currentUserId    currentUserId
     * @return bool
     */
    @Transactional(readOnly = true)
    public boolean checkAcceptInvoice(Long buyerWorkgroupId, Long orderId, Long parentId, Long parentClassId, Long currentUserId) {

        boolean isEnable = preferenceService.check(PreferenceID.WORKGROUP_OPTION_DISABLE_CANCEL_ORDER_BUTTON, buyerWorkgroupId);
        if (!isEnable) {
            isEnable = preferenceService.getValueForUser(PreferenceID.WORKGROUP_OPTION_DISABLE_CANCEL_ORDER_BUTTON,
                    currentUserId, "0").equalsIgnoreCase("1");
        }

        if (!isEnable) {
            return false;
        }

        List<Invoice> invoices = invoiceRepository.findInvoiceByOrderAndParentId(orderId, parentId, parentClassId);
        if (invoices != null && invoices.size() > 0) {
            if (invoices.stream().filter(i -> i.isAccepted() || i.isPending()).findAny().isPresent()) {
                return true;
            }
        }
        return false;
    }

    public List<PaymentMethodDTO> findAllPaymentMethod() {
        List<PaymentMethodDTO> paymentMethodDTOs = new ArrayList<PaymentMethodDTO>();
        List<PaymentMethod> paymentMethods = paymentMethodRepository.findAll();
        return paymentMethods.stream().filter(p -> p.getId() == PaymentMethodID.PO || p.getId() == PaymentMethodID.CREDIT_CARD ||
                p.getId() == PaymentMethodID.TRANSFER || p.getId() == PaymentMethodID.CASH ||
                p.getId() == PaymentMethodID.IN_CONTRACT).map(paymentMethodMapper::toDTO).collect(Collectors.toList());
    }

    public List<WorkgroupReasonDTO> findWorkgroupReason(Long currentWorkgroupId) {
        List<WorkgroupReason> workgroupReasons = workgroupReasonRepository.findByWorkgroupIdAndIsActive(currentWorkgroupId, true);
        if (workgroupReasons != null && workgroupReasons.size() > 0) {
            return workgroupReasonMapper.toDTOs(workgroupReasons.stream().filter(p -> p.getPcReason().getTypeId() == null).collect(Collectors.toList()));
        }
        return null;
    }

    //NOTE: the order type for buyer/supplier is different judging by this method
    public boolean isSellOrder(ProjectOrderWidgetDTO projectOrderWidgetDTO, ProjectDTO parent) {
        long supplierWorkgroupId = projectOrderWidgetDTO.getSupplierWorkgroupId().longValue();
        long projectOwnerWorkgroupId = parent.getOwnerWorkgroupId().longValue();
        if (parent.isClientNotOnNoosh()) {
            return projectOrderWidgetDTO.getBuClientId() != null && (supplierWorkgroupId == projectOwnerWorkgroupId);
        } else {
            return supplierWorkgroupId == projectOwnerWorkgroupId;
        }
    }

    @Transactional(readOnly = true)
    public OrderDeskoidVO getOrderDeskoidVO(Long projectId, String type, List<Long> cogFilter, Long currentUserId, Long currentWorkgroupId) {
        OrderDeskoidVO vo = new OrderDeskoidVO();

        boolean canViewOrder = permissionService.checkAll(PermissionID.VIEW_ORDER, currentWorkgroupId, currentUserId, projectId);
        vo.setCanViewOrder(canViewOrder);
        ProjectDTO projectDTO = projectService.findProjectById(projectId);

        List<ProjectOrderWidgetDTO> orderList = getProjectOrderListWithBuyCheck(projectDTO, type, cogFilter, currentWorkgroupId, currentUserId);
        customFieldService.setCustomAttributes(orderList);
        vo.setList(orderList);

        vo.setTotal(orderMyBatisMapper.countTotalProjectOrderListWithBuyCheck(projectId, projectDTO.isClientNotOnNoosh(), type));


        ProjectVO projectVO = new ProjectVO();
        projectVO.setIsBuyerProject(projectDTO.isBuyerProject());
        projectVO.setIsClientProject(projectDTO.isClientProject());
        projectVO.setIsSupplierProject(projectDTO.isSupplierProject());
        projectVO.setIsClientNotOnNoosh(projectDTO.isClientNotOnNoosh());
        vo.setParent(projectVO);

        Map<String, String> wgPrefs = preferenceService.findGroupPrefs(currentWorkgroupId,
                Arrays.asList(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY, PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY_HIDE_BASE_CURRENCY));
        vo.setIsDualCurrency(preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY, wgPrefs));
        vo.setHideBaseCurrency(preferenceService.check(PreferenceID.WORKGROUP_OPTION_MULTIPLE_CURRENCY_HIDE_BASE_CURRENCY, wgPrefs));

        // Create Quick Order Link
        if(!projectDTO.isClientProject() && !projectDTO.isSupplierProject() && !ORDER_SELL.equals(type)) {
            vo.setCreateQuickOrderExternalLink(NooshOneUrlUtil.composeCreateQuickOrderToEnterprise(projectId));
        }
        
        return vo;
    }

    public boolean hasAcceptedChangeOrder(Long orderId) {
        List<Order> changeOrders = orderRepository.findAllOrdersByParentOrderId(orderId, ACCEPTED_ORDER_STATE_IDS);
        return changeOrders != null && changeOrders.size() > 1;
    }

    private List<ProjectOrderWidgetDTO> getProjectOrderListWithBuyCheck(ProjectDTO projectDTO,
                                                                       String type,
                                                                       List<Long> cogFilter,
                                                                       Long currentWorkgroupId, Long currentUserId) {
        Long projectId = projectDTO.getId();
        List<ProjectOrderWidgetDTO> orderWidgetDTOList = orderMyBatisMapper
                .getProjectOrderListWithBuyCheck(projectId, type, projectDTO.isClientNotOnNoosh(), cogFilter);

        // Check draft change order, excluding draft from other side
        orderWidgetDTOList = orderWidgetDTOList.stream()
                .filter(changeOrder -> !changeOrder.isDraft()
                        || (changeOrder.isDraft() && changeOrderPassesFilter(changeOrder, currentWorkgroupId)))
                .collect(Collectors.toList());

        orderWidgetDTOList.stream().forEach(order -> setProjectOrderAdditionalData(order, projectId, currentWorkgroupId, currentUserId));

        orderWidgetDTOList.forEach(orderWidgetDTO -> {
            if (orderWidgetDTO.getSupplierWorkgroupId() != null) {
                boolean isRealTimeQuery = orderWidgetDTO.getStateId() == ObjectStateID.ORDER_DRAFT ||
                        orderWidgetDTO.getStateId() == ObjectStateID.ORDER_PENDING_SUBMISSION;
                orderWidgetDTO.setSupplierFlag(supplierWorkgroupService.getSupplierFlagVO(isRealTimeQuery,
                        orderWidgetDTO.getParentOrderId(),
                        ObjectClassID.ORDER,
                        orderWidgetDTO.getBuyerWorkgroupId(),
                        orderWidgetDTO.getSupplierWorkgroupId()));
                orderWidgetDTO.setSupplierScore(ratingService.findSupplierScore(orderWidgetDTO.getBuyerWorkgroupId(),
                        orderWidgetDTO.getSupplierWorkgroupId()));
            }
        });

        // change order
        // first, Sort by isClosing, then sort by orderId
        List<ProjectOrderWidgetDTO> changeOrders = orderWidgetDTOList.stream()
                .filter(o -> o.getOrderTypeId() == OrderTypeID.CHANGE_ORDER)
                .sorted(Comparator.comparing(ProjectOrderWidgetDTO::getIsClosing)
                        .thenComparing(ProjectOrderWidgetDTO::getOrderId))
                .collect(Collectors.toList());

        List<ProjectOrderWidgetDTO> originalOrders = orderWidgetDTOList.stream()
                .filter(o -> o.getOrderTypeId() != OrderTypeID.CHANGE_ORDER)
                .collect(Collectors.toList());

        // set change order into original order
        Map<Long, List<ProjectOrderWidgetDTO>> originalToChangeOrdersMap = new HashMap<>(changeOrders.size());
        changeOrders.forEach(changeOrder -> {
            List<ProjectOrderWidgetDTO> changeOrderList = originalToChangeOrdersMap.get(changeOrder.getParentOrderId());
            if (changeOrderList != null) {
                // append
                changeOrderList.add(changeOrder);
            } else {
                // new list and append
                changeOrderList = new ArrayList<>();
                changeOrderList.add(changeOrder);
                originalToChangeOrdersMap.put(changeOrder.getParentOrderId(), changeOrderList);
            }

        });

        originalOrders.forEach(originalOrder -> {
            originalOrder.setChangeOrderList(originalToChangeOrdersMap.get(originalOrder.getParentOrderId()));
        });

        return originalOrders;
    }

    private boolean changeOrderPassesFilter(ProjectOrderWidgetDTO changeOrder, Long currentWorkgroupId) {
        boolean isIncludingDraft = true;
        if (changeOrder.getStateCreateUserId() != null) {
            AccountUser creator = accountUserRepository.findById(changeOrder.getStateCreateUserId()).orElse(null);
            if (creator != null && creator.getWorkgroupId().longValue() != currentWorkgroupId) {
                isIncludingDraft = false;
            }
        }
        return isIncludingDraft;
    }

    private boolean isShowPendingApproverLink(ProjectOrderWidgetDTO orderWidgetDTO, boolean isChangeOrder, long buyerWorkgroupId) {
        boolean isShowPendingApproverLink = false;

        Map<String, String> workgroupPreference = preferenceService.findGroupPrefs(buyerWorkgroupId);
        boolean showPendingApproval = preferenceService.check(PreferenceID.WORKGROUP_OPTION_SHOW_PENDING_APPROVALS, workgroupPreference);

        if (!isChangeOrder && orderWidgetDTO.isPendingSubmission() && showPendingApproval) {
            isShowPendingApproverLink = true;

        } else if (isChangeOrder && showPendingApproval) {
            long objectClassId = isChangeOrder || orderWidgetDTO.getIsClosing() ? ObjectClassID.CHANGE_ORDER : ObjectClassID.ORDER;
            RoutingSlip routingSlip = routingSlipRepository.findFirstByObjectIdAndObjectClassIdAndAcWorkgroupIdOrderByModDateDesc(
                    orderWidgetDTO.getOrderVersionId(), objectClassId, buyerWorkgroupId);
            RoutingSlipDTO routingSlipBean = routingSlipMapper.toDTO(routingSlip);
            if (orderWidgetDTO.isPendingApproval(routingSlipBean) && (!orderWidgetDTO.isRetracted()) && (!orderWidgetDTO.isReplaced())) {
                isShowPendingApproverLink = true;
            }
        }
        return isShowPendingApproverLink;
    }

    /**
     * if there has offline client, then use offline client name as Buyer Workgroup Name
     *
     * @param orderWidgetDTO
     * @param projectDTO
     * @param supplierWorkgroupId
     * @param buyerWorkgroupId
     * @param buClientId
     */
    private void setBuyerAndSupplierWorkgroupName(ProjectOrderWidgetDTO orderWidgetDTO, ProjectDTO projectDTO, long supplierWorkgroupId, long buyerWorkgroupId, long buClientId) {
        if (projectDTO.isClientNotOnNoosh()
                && supplierWorkgroupId == buyerWorkgroupId
                && buClientId > 0) {

            ClientWorkgroup clientWorkgroup = clientWorkgroupRepository.findById(buClientId).orElse(null);
            if (clientWorkgroup != null) {
                orderWidgetDTO.setBuyerWorkgroupName(clientWorkgroup.getName());
            } else {
                orderWidgetDTO.setBuyerWorkgroupName(projectDTO.getClientAccount());
            }

        } else {
            Workgroup buyerWorkgroup = workgroupRepository.findById(buyerWorkgroupId).get();
            orderWidgetDTO.setBuyerWorkgroupName(buyerWorkgroup.getName());
        }

        if (supplierWorkgroupId > 0) {
            Workgroup supplierWorkgroup = workgroupRepository.findById(supplierWorkgroupId).get();
            orderWidgetDTO.setSupplierWorkgroupName(supplierWorkgroup.getName());
            orderWidgetDTO.setSupplierWorkgroupId(supplierWorkgroupId);
        }
    }

    private void setProjectOrderAdditionalData(ProjectOrderWidgetDTO orderWidgetDTO, Long projectId,
                                               Long currentWorkgroupId, Long currentUserId) {
        ProjectDTO projectDTO = projectService.findProjectById(projectId);

        boolean isChangeOrder = orderWidgetDTO.getOrderTypeId() == OrderTypeEnum.ChangeOrder.getOrderTypeId();

        long supplierWorkgroupId = -1;
        if (orderWidgetDTO.getSupplierWorkgroupId() != null) {
            supplierWorkgroupId = orderWidgetDTO.getSupplierWorkgroupId();
        }

        long buyerWorkgroupId = -1;
        if (orderWidgetDTO.getBuyerWorkgroupId() != null) {
            buyerWorkgroupId = orderWidgetDTO.getBuyerWorkgroupId();
        }

        boolean isBuyerOrder = true;
        if (supplierWorkgroupId == projectDTO.getOwnerWorkgroupId()) {
            isBuyerOrder = false;
        }

        long orderId = orderWidgetDTO.getOrderId();
        if (isShowPendingApproverLink(orderWidgetDTO, isChangeOrder, buyerWorkgroupId)) {
            orderWidgetDTO.setShowPendingApproverLink(NooshOneUrlUtil.composeShowPendingApprovalLinkToEnterprise(
                    projectDTO.getId(), orderId, isChangeOrder));
        }

        boolean isDualCurrency = orderWidgetDTO.getRate() != null && orderWidgetDTO.getRate().compareTo(BigDecimal.ZERO) > 0 && orderWidgetDTO.getExCurrencyId() != null;
        List<OrderItemDTO> orderItems = orderItemMyBatisMapper.findByOrderVersionId(orderWidgetDTO.getOrderVersionId());
        Long valueCurrencyId = null;
        if (orderItems != null && orderItems.size() > 0) {
            valueCurrencyId = orderItems.get(0).getValueCurrencyId();
        }

        // Get change order price
        if (isChangeOrder) {
            // Get all accepted orders
            List<Order> allOrders = orderRepository.findAllOrdersByParentOrderId(orderWidgetDTO.getParentOrderId(),
                    ACCEPT_COMPLETED_ORDER_STATE_IDS);

            // Sum all accepted order's price, including original and all changed order, excluding itself
            Double originalPrice = 0.0;
            Double exOriginalPrice = 0.0;
            if (allOrders != null && allOrders.size() > 0) {
                for (Order order : allOrders) {
                    if (order.getId() < orderId) {
                        List<OrderItemDTO> orderItemDTOS = orderItemMyBatisMapper.findByOrderVersionId(order.getOrderVersionId());
                        originalPrice += (orderItemDTOS.stream()
                                .map(OrderItemDTO::getValue)
                                .mapToDouble(BigDecimal::doubleValue)
                                .sum());
                        OrderVersion orderVersion = orderVersionRepository.findFirstByOrderIdAndIsCurrent(order.getId(), true);
                        originalPrice += orderVersion.getShipping().add(orderVersion.getTax()).doubleValue();

                        if (isDualCurrency) {
                            exOriginalPrice += (orderItemDTOS.stream()
                                    .filter(item -> item.getExValue() != null)
                                    .map(OrderItemDTO::getExValue)
                                    .mapToDouble(BigDecimal::doubleValue)
                                    .sum());
                            BigDecimal exShipping = orderVersion.getExShipping() != null ? orderVersion.getExShipping() : BigDecimal.ZERO;
                            BigDecimal exTax = orderVersion.getExTax() != null ? orderVersion.getExTax() : BigDecimal.ZERO;
                            exOriginalPrice += exShipping.add(exTax).doubleValue();
                        }
                    }
                }
            }

            // Set order price and change price
            orderWidgetDTO.setPrice(originalPrice);
            orderWidgetDTO.setPriceCurrencyId(valueCurrencyId);
            Double changePrice = 0d;
            Double exChangePrice = 0d;
            Double discountOrSurchargeTotal = orderWidgetDTO.getDiscountOrSurcharge() != null ? orderWidgetDTO.getDiscountOrSurcharge() : 0d;
            Double exDiscountOrSurchargeTotal = orderWidgetDTO.getExDiscountOrSurcharge() != null ? orderWidgetDTO.getExDiscountOrSurcharge() : 0d;
            for (OrderItemDTO item : orderItems) {
                if (item.getValue() != null) {
                    changePrice += item.getValue().doubleValue();
                }
                if (item.getDors() != null) {
                    discountOrSurchargeTotal += item.getDors().doubleValue();
                }
                if (isDualCurrency) {
                    if (item.getExValue() != null) {
                       exChangePrice += item.getExValue().doubleValue();
                    }
                    if (item.getExDors() != null) {
                        exDiscountOrSurchargeTotal += item.getExDors().doubleValue();
                    }
                }
            }
            changePrice = changePrice + orderWidgetDTO.getTax() + orderWidgetDTO.getShipping();

            orderWidgetDTO.setChangePrice(changePrice);
            orderWidgetDTO.setChangePriceCurrencyId(valueCurrencyId);
            if (orderWidgetDTO.getIsClosing() && discountOrSurchargeTotal > 0) {
                orderWidgetDTO.setDiscountOrSurcharge(discountOrSurchargeTotal);
                orderWidgetDTO.setDiscountOrSurchargeCurrencyId(valueCurrencyId);
            }
            if (isDualCurrency) {
                orderWidgetDTO.setExPrice(exOriginalPrice);
                orderWidgetDTO.setExPriceCurrencyId(orderWidgetDTO.getExCurrencyId());
                Double exTax = orderWidgetDTO.getExTax() != null ? orderWidgetDTO.getExTax() : 0d;
                Double exShipping = orderWidgetDTO.getExShipping() != null ? orderWidgetDTO.getExShipping() : 0d;
                exChangePrice = exChangePrice + exTax + exShipping;
                orderWidgetDTO.setExChangePrice(exChangePrice);
                orderWidgetDTO.setExChangePriceCurrencyId(orderWidgetDTO.getExCurrencyId());
                if (orderWidgetDTO.getIsClosing() && exDiscountOrSurchargeTotal > 0) {
                    orderWidgetDTO.setExDiscountOrSurcharge(exDiscountOrSurchargeTotal);
                    orderWidgetDTO.setExDiscountOrSurchargeCurrencyId(orderWidgetDTO.getExCurrencyId());
                }
            }

            // Get parent order type
            OrderVersion parentOrder = orderVersionRepository.findFirstByOrderIdAndIsCurrent(orderWidgetDTO.getParentOrderId(), true);
            orderWidgetDTO.setParentOrderType(OrderTypeEnum.getOrderType(parentOrder.getOrderTypeId()).getOrderType());

            // Check order type
            if (parentOrder.getSupplierWorkgroupId().longValue() == projectDTO.getOwnerWorkgroupId().longValue()) {
                isBuyerOrder = false;
            }
            orderWidgetDTO.setParentBuyerWorkgroupId(parentOrder.getBuyerWorkgroupId());
            orderWidgetDTO.setParentSupplierWorkgroupId(parentOrder.getSupplierWorkgroupId());

        } else {
            //Set order price
            orderWidgetDTO.setPrice(orderItems
                    .stream()
                    .filter(item -> item.getValue() != null)
                    .map(OrderItemDTO::getValue)
                    .mapToDouble(BigDecimal::doubleValue)
                    .sum()
                    + orderWidgetDTO.getTax() + orderWidgetDTO.getShipping());
            orderWidgetDTO.setPriceCurrencyId(valueCurrencyId);

            if (isDualCurrency) {
                Double exTax = orderWidgetDTO.getExTax() != null ? orderWidgetDTO.getExTax() : 0d;
                Double exShipping = orderWidgetDTO.getExShipping() != null ? orderWidgetDTO.getExShipping() : 0d;
                orderWidgetDTO.setExPrice(orderItems
                        .stream()
                        .filter(item -> item.getExValue() != null)
                        .map(OrderItemDTO::getExValue)
                        .mapToDouble(BigDecimal::doubleValue)
                        .sum() + exTax + exShipping);
                orderWidgetDTO.setExPriceCurrencyId(orderWidgetDTO.getExCurrencyId());
            }
            // Set rate supplier link
            OrderState currentOrderState = orderStateRepository.findByOrderIdAndIsCurrent(orderId, true);
            Map<String, PropertyAttribute> propertyAttributeMap = preferenceService.getOwnerAttributesByDefaultValueForWorkgroup(buyerWorkgroupId, "1");
            Long triggerStateId = Long.parseLong(preferenceService.getValueForWorkgroup("SR_RATING_TRIGGER", buyerWorkgroupId));

            if (propertyAttributeMap.containsKey(PreferenceID.WORKGROUP_OPTION_SUPPLIER_RATING)
                    && propertyAttributeMap.containsKey("SR_ACTIVE")
                    && ((triggerStateId != (long) -1 && (orderWidgetDTO.isAccepted()
                    || currentOrderState.getObjectState().getId() == ObjectStateID.ORDER_CANCELLED))
                    || (triggerStateId == (long) -1))) {
                String href = EnterpriseLinkDestination.SUPPLIER_RATING + "?orderId=" + orderId
                        + "&objectId=" + projectId + "&objectClassId=" + ObjectClassID.OBJECT_CLASS_PROJECT;
                orderWidgetDTO.setSupplierRatingLink(NooshOneUrlUtil.composeLinkToEnterprise(href));
            }

            OrderVersionDTO orderVersionDTO = findSimpleOrderById(orderId);
            orderVersionDTO.setParent(projectDTO);

            // create supplier sourcing Button
            if (viewSourcingPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)
                    && (!orderVersionDTO.isDraft())) {
                String sourcingButton = NooshOneUrlUtil.composeSourcingLinkToEnterprise(projectId, orderId,
                        orderVersionDTO.getVersion());
                orderWidgetDTO.setSourcingStrategiesLink(sourcingButton);
            }

            orderWidgetDTO.setEditSourcingStrategyExternalLink(NooshOneUrlUtil.composeEditSourcingLinkToEnterprise(projectId, orderId));

            // Handle total saving amount
            List<BenchmarkItem> benchmarkItems = benchmarkItemRepository.findByOrderId(orderId);
            if (benchmarkItems != null) {
                double totalSaving = benchmarkItems.stream().filter(b -> b.getSavingAmt() != null)
                        .mapToDouble(b -> b.getSavingAmt().doubleValue()).sum();
                orderWidgetDTO.setTotalSaving(totalSaving);
            }

            // cancel button
            if (cancelOrderPermission.check(orderVersionDTO, currentWorkgroupId, currentUserId, projectId)
                    && (!cancelOrderPermission.checkSupplierGroup(orderVersionDTO, currentWorkgroupId))) {
                if (!checkAcceptInvoice(orderVersionDTO.getBuyerWorkgroupId(),
                        orderId, projectId, ObjectClassID.OBJECT_CLASS_PROJECT, currentUserId)) {
                    String cancelButton = NooshOneUrlUtil.composeCancelOrderLinkToEnterprise(projectId, orderId,
                            orderVersionDTO.getVersion(), orderVersionDTO.getId(), null);
                    orderWidgetDTO.setCancelOrderLink(cancelButton);
                }
            }
        }

        HashMap<String, String> params = new HashMap<>();
        params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
        params.put("objectId", "" + projectId);

        if (isChangeOrder) {

            if (orderWidgetDTO.isDraft()) {
                orderWidgetDTO.setExternalLink(NooshOneUrlUtil.composeEditDraftChangeOrderLinkToEnterprise(projectId,
                        orderWidgetDTO.getParentOrderId(), orderWidgetDTO.getVersionNumber(), orderWidgetDTO.getOrderId()));
            } else {
                params.put("changeOrderId", "" + orderId);
                orderWidgetDTO.setExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.VIEW_CHANGE_ORDER, params));
            }


        } else {

            if (orderWidgetDTO.isDraft()) {
                orderWidgetDTO.setExternalLink(NooshOneUrlUtil.composeEditDraftOrderLinkToEnterprise(projectId, orderWidgetDTO.getOrderId(),
                        orderWidgetDTO.getVersionNumber(), null));
            } else {
                params.put("orderId", "" + orderId);
                orderWidgetDTO.setExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.VIEW_ORDER, params));
            }

        }

        params.remove("changeOrderId");

        params.put("orderId", isChangeOrder ? "" + orderWidgetDTO.getParentOrderId() : "" + orderId);
        params.put("aggregate", "true");
        //Set order with changes link
        orderWidgetDTO.setOrderWithChangesLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.VIEW_ORDER, params));

        params.remove("aggregate");
        params.put("renderSpecs", "true");
        orderWidgetDTO.setOrderDetailsWithSpecLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.VIEW_ORDER, params));

        // Set is buyer or sell order
        orderWidgetDTO.setIsSellOrder(!isBuyerOrder);

        // Set order state
        orderWidgetDTO.setState(i18NUtils.getObjectStateMessage(orderWidgetDTO.getStateId()));

        long buClientId = -1;
        if (orderWidgetDTO.getBuClientId() != null) {
            buClientId = orderWidgetDTO.getBuClientId();
        }

        setBuyerAndSupplierWorkgroupName(orderWidgetDTO, projectDTO, supplierWorkgroupId, buyerWorkgroupId, buClientId);

        // if show supplier workgroup
        long currentUserWorkGroupId = JwtUtil.getWorkgroupId();
        if (isChangeOrder) {
            orderWidgetDTO.setShowSupplierWorkgroup(orderWidgetDTO.getParentBuyerWorkgroupId() == currentUserWorkGroupId);
        } else {
            orderWidgetDTO.setShowSupplierWorkgroup(buyerWorkgroupId == currentUserWorkGroupId);
        }

        if (buClientId > 0 && projectDTO.isBrokerOutsourcerProject()) {
            orderWidgetDTO.setShowSupplierWorkgroup(false);
        }

        // set order type
        boolean isClosingOrder = orderWidgetDTO.getIsClosing();
        if (isClosingOrder && isChangeOrder) {
            orderWidgetDTO.setOrderType("Closing Change");
        } else {
            if (isChangeOrder) {
                orderWidgetDTO.setOrderType("change");
            } else {
                if (orderWidgetDTO.isUserBuyer(projectDTO.getOwnerWorkgroupId())) {
                    orderWidgetDTO.setOrderType("buy");
                } else {
                    orderWidgetDTO.setOrderType("sell");
                }
            }
        }
    }

    private List<Breakout> findChildBreakouts(Long objectId, Long objectClassId, Long parentId, List<Breakout> breakoutList) {
        if (breakoutList == null) {
            breakoutList = new ArrayList<Breakout>();
        }
        List<Breakout> newChildList = breakoutRepository.findByParent(objectId, objectClassId, parentId);
        if (newChildList != null && newChildList.size() > 0) {
            for (Breakout breakout : newChildList) {
                breakoutList.add(breakout);
                findChildBreakouts(objectId, objectClassId, breakout.getBreakoutTypeId(), breakoutList);
            }
        }
        return breakoutList;
    }

    private List<Breakout> findBreakoutsByObjectId(Long objectId, Long objectClassId) {
        List<Breakout> finalList = new ArrayList<Breakout>();
        List<Breakout> parents = breakoutRepository.findByObjectIdAndObjectClassIdAndNestingLevel(objectId, objectClassId, (long)1);
        if (parents != null && parents.size() > 0) {
            for (Breakout breakout : parents) {
                finalList.add(breakout);
                findChildBreakouts(objectId, objectClassId, breakout.getBreakoutTypeId(), finalList);
            }
        }
        return finalList;
    }

}
