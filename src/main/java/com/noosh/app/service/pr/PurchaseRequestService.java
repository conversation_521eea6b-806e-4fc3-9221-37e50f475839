package com.noosh.app.service.pr;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.noosh.app.commons.constant.EnterpriseLinkDestination;
import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.pr.PurchaseRequestDTO;
import com.noosh.app.commons.dto.pr.PurchaseRequestItemDTO;
import com.noosh.app.commons.dto.pr.PurchaseRequestRevisionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.pr.PurchaseRequestFilter;
import com.noosh.app.commons.vo.pr.PurchaseRequestFilterVO;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.repository.mybatis.pr.PurchaseRequestMyBatisMapper;
import com.noosh.app.repository.security.ObjectStateRepository;
import com.noosh.app.service.ProjectService;
import com.noosh.app.service.permission.PermissionService;
import com.noosh.app.service.permission.pr.DeletePurchaseRequestPermission;
import com.noosh.app.service.permission.pr.EditPurchaseRequestPermission;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.util.NooshOneUrlUtil;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 4/2/21
 */
@Service
public class PurchaseRequestService {
    @Inject
    private PurchaseRequestMyBatisMapper purchaseRequestMyBatisMapper;
    @Inject
    private EditPurchaseRequestPermission editPurchaseRequestPermission;
    @Inject
    private DeletePurchaseRequestPermission deletePurchaseRequestPermission;
    @Inject
    private ObjectStateRepository objectStateRepository;
    @Inject
    private PreferenceService preferenceService;
    @Inject
    private ProjectService projectService;
    @Inject
    private PermissionService permissionService;

    public List<PurchaseRequestDTO> findByProjectId(Long workgroupId, Long userId, Long projectId, PageVO page) {
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        pageInfo.setUnsafeOrderBy(page.getOrderBy());
        List<PurchaseRequestDTO> prs = purchaseRequestMyBatisMapper.findByProjectId(projectId, false);
        page.setTotal(pageInfo.getTotal());
        if (prs != null) {
            for (PurchaseRequestDTO dto : prs) {
                if (dto.getStateId() != null) {
                    ObjectState objectState = objectStateRepository.findById(dto.getStateId()).orElse(null);
                    if (objectState != null) {
                        dto.setStatusStrId(objectState.getDescriptionStrId() != null ? objectState.getDescriptionStrId().toString() : null);
                    }
                }
                if (deletePurchaseRequestPermission.check(dto, workgroupId, userId, projectId)) {
                    dto.setCanDelete(true);
                    dto.setDeleteExternalLink(NooshOneUrlUtil.getPurchaseRequestDeleteLink(projectId, dto.getId()));
                }
                if (editPurchaseRequestPermission.check(dto, workgroupId, userId, projectId)) {
                    dto.setCanEdit(true);
                    dto.setEditExternalLink(NooshOneUrlUtil.getPurchaseRequestEditLink(projectId, dto.getId()));
                }
                dto.setDetailExternalLink(NooshOneUrlUtil.getPurchaseRequestDetailLink(projectId, dto.getId()));
                dto.setGrandTotal(calculateGrandTotal(dto.getItems(), dto.getTax(), dto.getShipping()));

                if (dto.getItems() != null) {
                    String back = NooshOneUrlUtil.getPurchaseRequestListLink(projectId);
                    for (PurchaseRequestItemDTO itemDTO : dto.getItems()) {
                        itemDTO.setDetailExternalLink(NooshOneUrlUtil.composeViewSpecLinkForOrderItemToEnterprise(
                                itemDTO.getSpecNodeId(), itemDTO.getSpecId(), projectId, back));
                        itemDTO.setUnitPrice(getPricePerUnits(itemDTO, 1));
                    }
                }

                if (dto.getRevisions() != null) {
                    for (PurchaseRequestRevisionDTO revision : dto.getRevisions()) {
                        if (revision.getStateId() != null) {
                            ObjectState objectState = objectStateRepository.findById(revision.getStateId()).orElse(null);
                            if (objectState != null) {
                                revision.setStatusStrId(objectState.getDescriptionStrId() != null ? objectState.getDescriptionStrId().toString() : null);
                            }
                        }
                        revision.setDetailExternalLink(NooshOneUrlUtil.getPurchaseRequestDetailLink(projectId, revision.getId()));
                        revision.setGrandTotal(calculateGrandTotal(revision.getItems(), revision.getTax(), revision.getShipping()));
                    }
                }
            }
        }

        return prs;
    }

    private BigDecimal calculateGrandTotal(List<PurchaseRequestItemDTO> itemDTOs, BigDecimal tax, BigDecimal shipping) {
        if (itemDTOs == null || itemDTOs.size() == 0) return null;
        BigDecimal total = BigDecimal.ZERO;
        for (PurchaseRequestItemDTO itemDTO : itemDTOs) {
            total = total.add(itemDTO.getValue() != null ? itemDTO.getValue() : BigDecimal.ZERO);
        }
        return total.add(tax != null ? tax : BigDecimal.ZERO).add(shipping != null ? shipping : BigDecimal.ZERO);
    }

    public BigDecimal getPricePerUnits(PurchaseRequestItemDTO itemDTO, long perNumUnits)
    {
        if (itemDTO.getQuantity() == null || itemDTO.getQuantity().compareTo(BigDecimal.ZERO) == 0
                || itemDTO.getValue() == null || itemDTO.getValue().compareTo(BigDecimal.ZERO) == 0)
            return null;
        BigDecimal bigDecimal = itemDTO.getValue().divide(itemDTO.getQuantity(), 10, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(perNumUnits));
        bigDecimal = bigDecimal.setScale(10, BigDecimal.ROUND_HALF_UP);
        return bigDecimal;
    }

    public void updatePageSize(Long workgroupId, Long userId, Integer pageSize) throws Exception {
        Map<String, String> prefs = new HashMap<>();
        prefs.put(PurchaseRequestFilter.PR_LIST_FILTER_PREF_PREFIX + PurchaseRequestFilter.CONTROLNAME_PAGE_SIZE, String.valueOf(pageSize));
        preferenceService.saveUserPreference(workgroupId, userId, prefs);
    }

    public PurchaseRequestFilterVO getPrLinkAndPageSize(Long workgroupId, Long userId, Long projectId, String locale) {
        PurchaseRequestFilterVO linkVO = new PurchaseRequestFilterVO();
        ProjectDTO projectDTO = projectService.findProjectById(projectId);
        if (projectDTO == null) throw new NotFoundException("Project not found");
        HashMap<String, String> params = new HashMap<>();
        params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
        params.put("objectId", "" + projectId);
        String externalLink = NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.VIEW_PROJECT_HOME, params);
        linkVO.setProjectExternalLink(externalLink);
        linkVO.setProjectName(projectDTO.getProjectTitle());

        Map<String, String> prefs = preferenceService.findUserPrefs(workgroupId, userId);
        String pageSizeStr = preferenceService.getString(
                PurchaseRequestFilter.PR_LIST_FILTER_PREF_PREFIX
                        + PurchaseRequestFilter.CONTROLNAME_PAGE_SIZE, prefs,
                String.valueOf(10));
        linkVO.setPageSize(Integer.valueOf(pageSizeStr));
        if ( permissionService.checkAll(PermissionID.CREATE_PURCHASE_REQUEST, workgroupId, userId, projectId)
                && preferenceService.check(PreferenceID.WORKGROUP_OPTION_PURCHASE_REQUEST, prefs) && (!projectDTO.isClientProject())) {
            linkVO.setCreatePrExternalLink(NooshOneUrlUtil.composeLinkToEnterprise("/noosh/procurement/pr/create", params));
        }
        return linkVO;
    }
}
