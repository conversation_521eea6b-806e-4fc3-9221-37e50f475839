package com.noosh.app.service.permission.invoice;

import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.invoice.InvoiceDTO;
import com.noosh.app.service.InvoiceService;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.Map;

@Service
public class AcceptInvoicePermission extends Permission {

    @Inject
    private PermissionService permissionService;
    @Inject
    private InvoiceService invoiceService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        if (!permissionService.checkAll(PermissionID.ACCEPT_INVOICE, workgroupId, userId, projectId)) {
            return false;
        }
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.RETRACT_INVOICE, workgroupId, userId, projectId);
    }

    public boolean check(Object object, Long workgroupId, Long userId, long projectId, Map<String, Boolean> permMap) {
        if (!permissionService.check(PermissionID.ACCEPT_INVOICE, projectId, permMap)) {
            return false;
        }
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.check(PermissionID.RETRACT_INVOICE, projectId, permMap);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        if (object instanceof InvoiceDTO) {
            InvoiceDTO invoice = (InvoiceDTO) object;

            if ( invoice.isUserBuyer() && !invoiceService.requiresApproval(invoice) &&
                    invoice.getStateId() == ObjectStateID.INVOICE_PENDING_ACCEPTANCE)
                return true;

            return false;
        }

        return false;
    }

}
