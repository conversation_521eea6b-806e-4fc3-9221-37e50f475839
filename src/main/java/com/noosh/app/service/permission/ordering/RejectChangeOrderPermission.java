package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.entity.collaboration.Psf;
import com.noosh.app.repository.collaboration.PsfRepository;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * @Author: neals
 * @Date: 08/05/2016
 */
@Service
public class RejectChangeOrderPermission extends Permission {

    @Inject
    private PermissionService permissionService;
    @Inject
    private PsfRepository psfRepository;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        // no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.REJECT_CHANGE_ORDER, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO changeOrder = (OrderVersionDTO) object;
        if (!changeOrder.isPending() && !changeOrder.isPendingSubmission()) {
            return false;
        }
        Psf psf = psfRepository.findById(changeOrder.getParent().getPsfId()).orElse(null);
        boolean isSupplierDirect = (psf != null && psf.getIsSupplierDirect());
        // can't reject generated orders
        if (changeOrder.getIsGenerated() && !isSupplierDirect) {
            return false;
        }

        // recipients should be able to reject
        if (changeOrder.isUserBuyer()
                && changeOrder.getInitByWorkgroupId().longValue() != changeOrder.getBuyerWorkgroupId().longValue()) {
            return true;
        }
        if (changeOrder.isUserSupplier()
                && changeOrder.getInitByWorkgroupId().longValue() != changeOrder.getSupplierWorkgroupId().longValue()) {
            return true;
        }
        return false;
    }
}
