package com.noosh.app.service.permission.invoice;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.invoice.InvoiceListDTO;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.Map;

@Service
public class EditInvoicePermission extends Permission {

    @Inject
    private PermissionService permissionService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.EDIT_INVOICE, workgroupId, userId, projectId);
    }

    public boolean check(Object object, Long workgroupId, Long userId, long projectId, Map<String, Boolean> permissionMap) {
        if (object == null) {
            if (permissionMap != null) {
                return permissionService.check(PermissionID.EDIT_INVOICE, projectId, permissionMap);
            } else {
                return permissionService.checkAll(PermissionID.EDIT_INVOICE, workgroupId, userId, projectId);
            }
        }

        if (!checkState(object, workgroupId, userId, projectId)) return false;

        if (permissionMap != null) {
            return permissionService.check(PermissionID.EDIT_INVOICE, projectId, permissionMap);
        } else {
            return permissionService.checkAll(PermissionID.EDIT_INVOICE, workgroupId, userId, projectId);
        }
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        if (object instanceof InvoiceListDTO) {
            InvoiceListDTO invoice = (InvoiceListDTO) object;

            if (!(invoice.getSupplierWorkgroupId().longValue() == workgroupId)) {
                return false;
            }

            if (invoice.isDraft()) {
                return true;
            }

            if (invoice.isPending()) {
                return true;
            }

            return false;

        }

        return false;
    }
}
