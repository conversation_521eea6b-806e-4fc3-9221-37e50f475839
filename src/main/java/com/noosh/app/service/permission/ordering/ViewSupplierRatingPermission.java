package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.entity.property.PropertyAttribute;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.preference.PreferenceService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.Map;

/**
 * User: leilaz
 * Date: 11/26/17
 */
@Service
public class ViewSupplierRatingPermission extends Permission {
    @Inject
    private PermissionService permissionService;
    @Inject
    private PreferenceService preferenceService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        // no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.VIEW_SUPPLIER_RATING, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        if (object instanceof OrderVersionDTO) {
            OrderVersionDTO order = (OrderVersionDTO) object;
            ProjectDTO parentOwner = order.getParent();
            Map<String, String> prefs = preferenceService.findGroupPrefs(order.getBuyerWorkgroupId());
            // if supplier rating not enabled or sr not active, return false
            if (!(prefs.containsKey("WORKGROUP_OPTION_SUPPLIER_RATING")
                    && prefs.get("WORKGROUP_OPTION_SUPPLIER_RATING").equalsIgnoreCase("1")) ||
                    !(prefs.containsKey("SR_ACTIVE")
                            && prefs.get("SR_ACTIVE").equalsIgnoreCase("1"))) {
                return false;
            }

            // For NKB144053 - Ability to partially complete Supplier Rating, If Order Created ("-1) is selected, the supplierRatingButton will show
            if (prefs.containsKey("SR_RATING_TRIGGER") && prefs.get("SR_RATING_TRIGGER").equalsIgnoreCase("-1")) {
                if (order.isUserBuyer()) {
                    return true;
                } else {
                    return (prefs.containsKey("SR_RATING_PUBLIC") && prefs.get("SR_RATING_PUBLIC").equalsIgnoreCase("1"));
                }
            } else {
                // if order is not yet accepted, supplier rating should not be available
                if (!order.isAccepted() && order.getOrderState().getObjectStateId() != ObjectStateID.ORDER_CANCELLED) {
                    return false;
                }
                // buyers can always see supplier rating, supplier can only see supplier rating
                // if the rating was marked as public by the buyer
                if (order.isUserBuyer()) {
                    return true;
                } else {
                    return (prefs.containsKey("SR_RATING_PUBLIC") && prefs.get("SR_RATING_PUBLIC").equalsIgnoreCase("1"));
                }
            }
        }
        return true;
    }
}
