package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.shipment.ShipmentDTO;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 11/21/17
 */
@Service
public class UpdateShipmentReceivePermission extends Permission {
    @Inject
    private PermissionService permissionService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        // no reason to continue if statemachine returns false
        return checkState(object, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        boolean bCheck = permissionService.checkAll(PermissionID.UPDATE_SHIPMENT_RECEIVED, workgroupId, userId, projectId);
        if (bCheck && object != null) {
            ShipmentDTO myShipment = (ShipmentDTO) object;
            long ownerGroupId = myShipment.getParent().getOwnerWorkgroupId() ;
            return myShipment.getJob().getJobStatuses().stream().filter(s -> s.getOwnerAcWorkgroupId() == ownerGroupId &&
                    (s.getOcObjectStateId() == ObjectStateID.JOB_ORDER_ACCEPTED ||
                            s.getOcObjectStateId() == ObjectStateID.JOB_COMPLETED)).findFirst().isPresent();

        }
        return bCheck;
    }
}
