package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.ReAcceptanceStatusID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * @Author: neals
 * @Date: 08/05/2016
 */
@Service
public class AcceptChangeOrderPermission extends Permission {

    @Inject
    private PermissionService permissionService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        // no reason to continue if state machine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.ACCEPT_CHANGE_ORDER, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO changeOrder = (OrderVersionDTO) object;
        if (changeOrder.getOrderState().getObjectStateId() == ObjectStateID.ORDER_COMPLETED)
            return false;

        if (changeOrder.getCreateDate().compareTo(changeOrder.getOrderState().getCreateDate()) > 0 && changeOrder.getReAcceptanceStatusId() != ReAcceptanceStatusID.BYPASS_RE_ACCEPTANCE)
            return false;


        if ((changeOrder.isUserBuyer() && changeOrder.isPendingBuyerAcceptance()) || (changeOrder.isUserSupplier() && changeOrder.isPendingSupplierAcceptance())) {
            return true;
        }

        if (changeOrder.getParent().isOutsourcerProject() && changeOrder.isUserSupplier() && changeOrder.isPendingBuyerAcceptance())
            return true;

        return false;
    }
}
