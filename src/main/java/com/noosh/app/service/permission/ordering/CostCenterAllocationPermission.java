package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.entity.property.PropertyAttribute;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.preference.PreferenceService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.Map;

/**
 * User: leilaz
 * Date: 11/15/17
 */
@Service
public class CostCenterAllocationPermission extends Permission {
    @Inject
    private PermissionService permissionService;
    @Inject
    private PreferenceService preferenceService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        if(((OrderVersionDTO) object).isDraft()){
            return false;
        }
        // no reason to continue if state machine returns false
        if (!checkState(object, workgroupId, userId, projectId)) {
            return false;
        }
        return permissionService.checkAll(PermissionID.VIEW_ORDER_COST_CENTER, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO) object;
        // if cc not enabled for the BUYER, return false (regardless of whether the current user is buyer or supplier
        Map<String, String> buyerPrefs = preferenceService.findGroupPrefs(order.getBuyerWorkgroupId());
        String allowSupplier = null;
        String costCenterEnabled = null;
        if (buyerPrefs.containsKey("ALLOW_COSTCENTER_SUP")) {
            allowSupplier = buyerPrefs.get("ALLOW_COSTCENTER_SUP");
        }

        if (buyerPrefs.containsKey("WORKGROUP_OPTION_COSTCENTER")) {
            costCenterEnabled = buyerPrefs.get("WORKGROUP_OPTION_COSTCENTER");
        }

        if (costCenterEnabled == null || costCenterEnabled.equals("0")) {
            return false;
        }

        // if current user is supplier, return false if buyer does NOT allow suppliers to view cost center info
        if (order.isUserSupplier() && !order.isUserBuyer()) {
            if (allowSupplier == null || allowSupplier.equals("0")) {
                return false;
            }
        }
        return true;
    }
}
