package com.noosh.app.service.permission.invoice;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.invoice.InvoiceListDTO;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.Map;

@Service
public class DeleteInvoicePermission extends Permission {

    @Inject
    private PermissionService permissionService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.DELETE_INVOICE, workgroupId, userId, projectId);
    }

    public boolean check(Object object, Long workgroupId, Long userId, long projectId, Map<String, Boolean> permMap) {
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.check(PermissionID.DELETE_INVOICE, projectId, permMap);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        if (object instanceof InvoiceListDTO) {
            InvoiceListDTO invoice = (InvoiceListDTO) object;
            if (invoice.getIsFinal()) {
                return false;
            }

            if (invoice.getId() > 0
                    && invoice.isDraft()
                    && invoice.getSupplierWorkgroupId().longValue() == workgroupId)
                return true;

            return false;

        }

        return false;
    }
}
