package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 1/8/18
 */
@Service
public class RetractChangeOrderPermission extends Permission {
    @Inject
    private PermissionService permissionService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        // no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.RETRACT_CHANGE_ORDER, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO changeOrder = (OrderVersionDTO)object;
        if (!(changeOrder.isPending() || changeOrder.isPendingSubmission())) {
            return false;
        } /* else  if (changeOrder.getIsGenerated()) {
            return false;
        } */

        // initiators should be able to retract
        if (changeOrder.isUserBuyer()
                && changeOrder.getInitByWorkgroupId() != null
                && changeOrder.getInitByWorkgroupId().longValue() == changeOrder.getBuyerWorkgroupId()) {
            return true;
        }
        if (changeOrder.isUserSupplier()
                && changeOrder.getInitByWorkgroupId() != null
                && changeOrder.getInitByWorkgroupId().longValue() == changeOrder.getSupplierWorkgroupId()) {
            return true;
        }
        return false;
    }
}
