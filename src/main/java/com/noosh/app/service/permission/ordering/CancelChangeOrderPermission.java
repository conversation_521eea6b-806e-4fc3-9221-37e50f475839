package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.order.ChangeOrderVersionDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.entity.collaboration.Psf;
import com.noosh.app.commons.entity.order.OrderVersion;
import com.noosh.app.repository.collaboration.PsfRepository;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 1/9/18
 */
@Service
public class CancelChangeOrderPermission extends Permission {
    @Inject
    private PermissionService permissionService;
    @Inject
    private PsfRepository psfRepository;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        // no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;

        return permissionService.checkAll(PermissionID.CANCEL_CHANGE_ORDER, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        ChangeOrderVersionDTO order = (ChangeOrderVersionDTO) object;
        OrderVersionDTO changeOrder = order.getChangeOrder();
        OrderVersionDTO parentOrder = order.getParentOrder();
        Psf psf = psfRepository.findById(changeOrder.getParent().getPsfId()).orElse(null);
        boolean isSupplierDirect = (psf != null && psf.getIsSupplierDirect());
        if (parentOrder.getOrderState().getObjectStateId() == ObjectStateID.ORDER_COMPLETED) {
            return false;
        } else if (changeOrder.getIsGenerated() && !isSupplierDirect) {
            return false;
        }
        //then check the state of the change order
        if (changeOrder.isAccepted()) {
            return true;
        }

        return false;
    }

//    public boolean checkChangeOrderCancel(OrderVersion changeOrder, OrderVersionDTO parentOrder, Long workgroupId, Long userId, long projectId) {
//        if (changeOrder.getOrder().getOrderStateSet() == null || changeOrder.getOrder().getOrderStateSet().size() == 0) return false;
//        long orderStateId = changeOrder.getOrder().getOrderStateSet().stream().filter(os -> os.getIsCurrent()).findFirst().get().getObjectStateId();
//        if (orderStateId == ObjectStateID.ORDER_ACCEPTED
//                || orderStateId == ObjectStateID.ORDER_PARTIALLY_SHIPPED
//                || orderStateId == ObjectStateID.ORDER_SHIPPED
//                || orderStateId == ObjectStateID.ORDER_DELIVERED
//                || orderStateId == ObjectStateID.ORDER_ACCEPTED_NOT_YET_SHIPPED
//                || orderStateId == ObjectStateID.ORDER_COMPLETED
//                || orderStateId == ObjectStateID.ORDER_FINALIZED) {
//
//            return permissionService.checkAll(PermissionID.CANCEL_CHANGE_ORDER, workgroupId, userId, projectId);
//
//        }
//        return false;
//    }
}
