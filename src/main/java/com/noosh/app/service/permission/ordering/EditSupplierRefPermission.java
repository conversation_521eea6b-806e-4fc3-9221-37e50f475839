package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 11/14/17
 */
@Service
public class EditSupplierRefPermission extends Permission {
    @Inject
    private PermissionService permissionService;
    @Inject
    private ProjectService projectService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        // no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.VIEW_ORDER, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO)object;

        if (order.getParent() == null) {
            order.setParent(projectService.findProjectById(projectId));
        }

        if (order.isUserSupplier()) {
            return true;
        } else {
            return false;
        }
    }
}
