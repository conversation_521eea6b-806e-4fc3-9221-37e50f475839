package com.noosh.app.service.permission.invoice;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.entity.preference.Preference;
import com.noosh.app.repository.jpa.preference.PreferenceRepository;
import com.noosh.app.repository.mybatis.invoice.InvoiceMyBatisMapper;
import com.noosh.app.repository.mybatis.order.OrderMyBatisMapper;
import com.noosh.app.service.AggregatedOrderService;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.security.PermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.Map;

/**
 * User: <PERSON>
 * Date: 4/17/2018
 */
@Service
public class CreateInvoicePermission extends Permission {

    @Inject
    private PermissionService permissionService;
    @Inject
    private PreferenceService preferenceService;
    @Inject
    private PreferenceRepository preferenceRepository;
    @Autowired
    private OrderMyBatisMapper orderMyBatisMapper;
    @Autowired
    private InvoiceMyBatisMapper invoiceMyBatisMapper;
    @Autowired
    private AggregatedOrderService aggregatedOrderService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.CREATE_INVOICE, workgroupId, userId, projectId);
    }

    public boolean check(Object object, Long workgroupId, Long userId, long projectId, Map<String, Boolean> permMap) {
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.check(PermissionID.CREATE_INVOICE, projectId, permMap);
    }

    public boolean check(OrderVersionDTO order, Long workgroupId, Long userId, long projectId) {
        boolean hasPerm = permissionService.checkAll(PermissionID.CREATE_INVOICE, workgroupId, userId, projectId);
        long groupId = order.getBuyerWorkgroupId();
        Map<String, String> buyerGroupPrefs = preferenceService.findGroupPrefs(groupId);
        boolean disallowCreateInvoices = preferenceService.check(PreferenceID.WORKGROUP_OPTION_DISALLOW_CREATE_INVOICES,
                buyerGroupPrefs);
        if (disallowCreateInvoices && (order.isCompleted() || order.isCancelled())) {
            return false;
        }

        // if invoice not enable,
        // return don't do anything
        boolean invoiceEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ORDER_INVOICING, buyerGroupPrefs);
        if (!invoiceEnabled) {
            return false;
        }

        if (aggregatedOrderService.hasPendingClosingChangeOrder(order)) {
            return false;
        }

        boolean orderStateAllow = order.isAccepted() || order.isCancelled();
        if (hasPerm &&
                permissionService.checkAll(PermissionID.VIEW_ORDER, workgroupId, userId, projectId) &&
                orderStateAllow == true &&
                order.getSupplierWorkgroupId().longValue() == workgroupId &&
                invoiceMyBatisMapper.getDraftAndPendingInvoiceCountForOrderId(order.getOrderId()) == 0) {
            return true;
        }

        return false;
    }

    public boolean check(OrderVersionDTO order, Long workgroupId, Long userId, long projectId, Map<String, Boolean>  permissionMap) {
        boolean hasPerm;
        if (permissionMap != null) {
            hasPerm = permissionService.check(PermissionID.CREATE_INVOICE, projectId, permissionMap);
        } else {
            hasPerm = permissionService.checkAll(PermissionID.CREATE_INVOICE, workgroupId, userId, projectId);
        }
        long groupId = order.getBuyerWorkgroupId();
        Map<String, String> buyerGroupPrefs = preferenceService.findGroupPrefs(groupId);
        boolean disallowCreateInvoices = preferenceService.check(PreferenceID.WORKGROUP_OPTION_DISALLOW_CREATE_INVOICES,
                buyerGroupPrefs);
        if (disallowCreateInvoices && (order.isCompleted() || order.isCancelled())) {
            return false;
        }

        // if invoice not enable,
        // return don't do anything
        boolean invoiceEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ORDER_INVOICING, buyerGroupPrefs);
        if (!invoiceEnabled) {
            return false;
        }

        if (aggregatedOrderService.hasPendingClosingChangeOrder(order)) {
            return false;
        }

        boolean orderStateAllow = order.isAccepted() || order.isCancelled();

        boolean viewOrderPerm;
        if (permissionMap != null) {
            viewOrderPerm = permissionService.check(PermissionID.VIEW_ORDER, projectId, permissionMap);
        } else {
            viewOrderPerm = permissionService.checkAll(PermissionID.VIEW_ORDER, workgroupId, userId, projectId);
        }

        if (hasPerm && viewOrderPerm && orderStateAllow == true &&
                order.getSupplierWorkgroupId().longValue() == workgroupId &&
                invoiceMyBatisMapper.getDraftAndPendingInvoiceCountForOrderId(order.getOrderId()) == 0) {
            return true;
        }

        return false;
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        if (object instanceof ProjectDTO) {
            ProjectDTO project = (ProjectDTO) object;
            if (project.isSupplierProject())
                return true;

            if (project.isBrokerOutsourcerProject() && project.isClientOnNoosh()) {
                Preference clientPrefs = preferenceRepository.findByObjectClassIdAndObjectId(ObjectClassID.WORKGROUP, project.getClientWorkgroupId());
                if (!clientPrefs.check(PreferenceID.WORKGROUP_OPTION_ORDER_INVOICING))
                    return false;
            }

            Long soldOrders = orderMyBatisMapper.findSoldOrdersCount(projectId, workgroupId, project.isClientNotOnNoosh());
            boolean hashSoldOrders = (soldOrders > 0);
            if (hashSoldOrders) {
                return true;
            }

        }

        return false;
    }
}
