package com.noosh.app.service.permission.invoice;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.dto.invoice.InvoiceDTO;
import com.noosh.app.commons.dto.invoice.InvoiceListDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.entity.preference.Preference;
import com.noosh.app.repository.jpa.preference.PreferenceRepository;
import com.noosh.app.repository.mybatis.invoice.InvoiceMyBatisMapper;
import com.noosh.app.repository.mybatis.order.OrderMyBatisMapper;
import com.noosh.app.service.AggregatedOrderService;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.security.PermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.Map;

/**
 * User: Neal Shan
 * Date: 4/17/2018
 */
@Service
public class SendInvoicePermission extends Permission {

    @Inject
    private PermissionService permissionService;
    @Inject
    private PreferenceService preferenceService;
    @Inject
    private PreferenceRepository preferenceRepository;
    @Autowired
    private OrderMyBatisMapper orderMyBatisMapper;
    @Autowired
    private InvoiceMyBatisMapper invoiceMyBatisMapper;
    @Autowired
    private AggregatedOrderService aggregatedOrderService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.SEND_INVOICE, workgroupId, userId, projectId);
    }

    public boolean check(Object object, Long workgroupId, Long userId, long projectId, Map<String, Boolean> permissionMap) {

        if (object == null) {
            if (permissionMap != null) {
                return permissionService.check(PermissionID.SEND_INVOICE, projectId, permissionMap);
            } else {
                return permissionService.checkAll(PermissionID.SEND_INVOICE, workgroupId, userId, projectId);
            }
        }

        if (!permissionService.check(PermissionID.VIEW_ORDER, projectId, permissionMap)) {
            return false;
        }

        if (!checkState(object, workgroupId, userId, projectId)) return false;

        if (permissionMap != null) {
            return permissionService.check(PermissionID.SEND_INVOICE, projectId, permissionMap);
        } else {
            return permissionService.checkAll(PermissionID.SEND_INVOICE, workgroupId, userId, projectId);
        }
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        if (object instanceof InvoiceDTO) {
            InvoiceDTO invoice = (InvoiceDTO) object;
            if (invoice.isUserSupplier() && invoice.getStateId() == ObjectStateID.INVOICE_DRAFT)
                return true;
        }

        return false;
    }
}
