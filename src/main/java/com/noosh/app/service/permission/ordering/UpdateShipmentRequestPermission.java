package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.shipment.ShipmentDTO;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 11/21/17
 */
@Service
public class UpdateShipmentRequestPermission extends Permission {
    @Inject
    private PermissionService permissionService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        return checkState(object, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        boolean bCheck = permissionService.checkAll(PermissionID.UPDATE_SHIPMENT_REQUESTED, workgroupId, userId, projectId);
        if (bCheck && object != null) {
            ShipmentDTO shipment = (ShipmentDTO) object;
            if (shipment.getJob().getOwnerWorkgroupId() == shipment.getParent().getOwnerWorkgroupId() )
                bCheck = true;
        }

        return bCheck;
    }
}
