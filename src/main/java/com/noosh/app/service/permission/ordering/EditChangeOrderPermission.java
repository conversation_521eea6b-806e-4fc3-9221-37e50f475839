package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 1/9/18
 */
@Service
public class EditChangeOrderPermission extends Permission {
    @Inject
    private PermissionService permissionService;

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {
        // no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.EDIT_CHANGE_ORDER, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO)object;
        ProjectDTO parent = order.getParent();
        long stateId = order.getOrderState().getObjectStateId();
        if (stateId== ObjectStateID.ORDER_RETRACTED) return false;
        if (stateId==ObjectStateID.ORDER_REJECTED) return false;
        if (stateId==ObjectStateID.ORDER_ACCEPTED) return false;
        if (stateId==ObjectStateID.ORDER_CANCELLED) return false;
        if (stateId==ObjectStateID.ORDER_COMPLETED) return false;
        if (stateId==ObjectStateID.ORDER_REPLACED) return false;

        return true;
    }
}
