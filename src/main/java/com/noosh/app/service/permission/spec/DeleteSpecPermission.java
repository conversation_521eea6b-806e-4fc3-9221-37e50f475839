package com.noosh.app.service.permission.spec;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.entity.SyContainable;
import com.noosh.app.commons.entity.security.TeamObject;
import com.noosh.app.commons.entity.spec.Spec;
import com.noosh.app.commons.entity.spec.SpecNode;
import com.noosh.app.repository.security.TeamObjectRepository;
import com.noosh.app.service.CollaborationService;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 3/31/17
 */
@Service
public class DeleteSpecPermission extends Permission {
    @Inject
    private PermissionService permissionService;
    @Inject
    private CollaborationService collaborationService;
    @Inject
    private TeamObjectRepository teamObjectRepository;

    @Override
    public boolean check(Object object, long workgroupId, long userId, long projectId) {// no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId))
            return false;
        return permissionService.checkAll(PermissionID.DELETE_SPEC, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, long workgroupId, long userId, long projectId) {
        // no deletion allowed if spec is immutable
        SpecNode specNode = (SpecNode) object;
        Spec spec = specNode.getSpec();
        if (spec != null && spec.getIsImmutable()) {
            return false;
        }

        // no deletion allowed if spec is immutable
        if (spec != null && spec.getSpecReference() != null && spec.getSpecReference().getPreferredSpSpecId() != null
                && spec.getSpecReference().getPreferredSpSpecId() == spec.getId().longValue()) {
            return false;
        }

        TeamObject teamObject = teamObjectRepository.findByObjectIdAndObjectClassId(projectId, ObjectClassID.OBJECT_CLASS_PROJECT);

        // no deletion allowed if spec has been shared with this workgroup
        // don't bother to check sharing if spec-ref is owned by current parent
        // MH: not sure how spec can ever be null
        if ( spec != null ) {
            SyContainable cont = collaborationService.findByObjectIdAndParentObjectId(spec.getId(), projectId, ObjectClassID.OBJECT_CLASS_SPEC, ObjectClassID.OBJECT_CLASS_PROJECT);
            String attr = cont.getParentObjectAttr();
            if (attr != null && attr.startsWith(CollaborationService.TEAM_SHARE_ATTR) && attr.endsWith("/" + teamObject.getTeamId())) {
                return false;
            }
        }

        return true;  //To change body of implemented methods use File | Settings | File Templates.
    }
}
