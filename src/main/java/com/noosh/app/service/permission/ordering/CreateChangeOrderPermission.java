package com.noosh.app.service.permission.ordering;

import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.entity.order.Order;
import com.noosh.app.mapper.OrderStateMapper;
import com.noosh.app.repository.jpa.order.OrderRepository;
import com.noosh.app.service.permission.Permission;
import com.noosh.app.service.security.PermissionService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.List;

/**
 * User: leilaz
 * Date: 11/13/17
 */
@Service
public class CreateChangeOrderPermission extends Permission {
    @Inject
    private PermissionService permissionService;
    @Inject
    private OrderRepository orderRepository;
    @Inject
    private OrderStateMapper orderStateMapper;

    public boolean check(long projectId, Long userId, Long workgroupId) {
        return permissionService.checkAll(PermissionID.CREATE_CHANGE_ORDER, workgroupId, userId, projectId);
    }

    @Override
    public boolean check(Object object, Long workgroupId, Long userId, long projectId) {// no reason to continue if statemachine returns false
        if (!checkState(object, workgroupId, userId, projectId)) return false;
        return permissionService.checkAll(PermissionID.CREATE_CHANGE_ORDER, workgroupId, userId, projectId);
    }

    @Override
    public boolean checkState(Object object, Long workgroupId, Long userId, long projectId) {
        OrderVersionDTO order = (OrderVersionDTO) object;
        long stateId = order.getOrderState().getObjectStateId();
        if (!order.isAccepted() || stateId == ObjectStateID.ORDER_COMPLETED) {
            return false;
        }
        if (hasPendingClosingChangeOrder(order.getOrderId()))
            return false;

        return true;
    }

    private boolean hasPendingClosingChangeOrder(Long orderId) {
        List<Order> changeOrders = orderRepository.findByParentOrderIdOrderByIdAsc(orderId);
        if (changeOrders != null) {
            for (Order o : changeOrders) {
                OrderVersionDTO orderVersionDTO = new OrderVersionDTO();
                orderVersionDTO.setOrderState(orderStateMapper.toDTO(o.getOrderStateSet().stream().filter(
                        s -> s.getIsCurrent() != null && s.getIsCurrent()).findAny().get()));
                if (o.getIsClosing() != null && o.getIsClosing() && orderVersionDTO.isPending()) {
                    return true;
                }
            }
        }
        return false;
    }
}
