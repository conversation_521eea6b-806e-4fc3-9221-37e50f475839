package com.noosh.app.service.permission;


import com.noosh.app.feign.AccountResourceFeignClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * @Author: neals
 * @Date: 12/29/2015
 */
@Service
@Transactional
public class PermissionService {

    @Autowired
    private AccountResourceFeignClient accountResourceFeignClient;

    public Boolean checkAll(Long permissionId, Long workgroupId, Long userId, Long projectId) {
        return accountResourceFeignClient.checkPermission(permissionId, workgroupId, userId, projectId);
    }

    public Boolean checkWorkgroupLevelCannotPrivilege(Long permissionId, Long workgroupId, Long userId) {
        return accountResourceFeignClient.checkWorkgroupLevelCannotPrivilege(permissionId, workgroupId, userId);
    }

    public Map<String, Boolean> getPermissionMap(List<Long> permissionIds, Long workgroupId, Long userId, List<Long> projectIds) {
        return accountResourceFeignClient.getPermissionMap(permissionIds, workgroupId, userId, projectIds);
    }

    /***
     * when there isn't a project, pass -1 as the projectId
     * @param permissionId
     * @param workgroupId
     * @param userId
     * @param projectId
     * @param objectId
     * @return
     */
    public Boolean checkWithObject(Long permissionId, Long workgroupId, Long userId, Long projectId, Long objectId) {
        return accountResourceFeignClient.checkWithObject(permissionId, workgroupId, userId, projectId, objectId);
    }
}
