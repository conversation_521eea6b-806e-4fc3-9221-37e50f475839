package com.noosh.app.service.report;

import com.noosh.app.commons.constant.Constants;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.constant.WorkgroupTypeID;
import com.noosh.app.commons.dto.report.ReportCategoryDTO;
import com.noosh.app.commons.dto.report.ReportListDTO;
import com.noosh.app.commons.entity.workgroup.Workgroup;
import com.noosh.app.commons.vo.report.ReportFilterVO;
import com.noosh.app.repository.jpa.workgroup.WorkgroupRepository;
import com.noosh.app.service.permission.PermissionService;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.util.ReportUtil;
import com.noosh.app.service.util.TemplatesResource;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 9/15/2021
 */
@Service
@Transactional
public class ReportCheckPermissionService {

    public static final String DEFAULT_PORTAL = "default";

    @Autowired
    private PermissionService permissionService;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private WorkgroupRepository workgroupRepository;
    @Autowired
    private SpringTemplateEngine templateEngine;

    private List<Long> permissionIds = new ArrayList<>();
    private List<String> preferenceIds = new ArrayList<>();
    private Map<String, Boolean> permissionMap;
    private Map<String, String> userPrefs;


    @PostConstruct
    public void init() {
        // permissions
        permissionIds.add(PermissionID.RUN_ALL_ACTIVITY_REPORT);
        permissionIds.add(PermissionID.RUN_LIMITED_ACTIVITY_REPORT);
        permissionIds.add(PermissionID.CREATE_REPORT_SPEC);
        permissionIds.add(PermissionID.RUN_OUTSOURCE_PROFIT_REPORT);
        permissionIds.add(PermissionID.RUN_ESTIMATE_ANALYSIS_REPORT);
        permissionIds.add(PermissionID.RUN_ESTIMATE_WINLOSS_REPORT);
        permissionIds.add(PermissionID.RUN_ORDER_ACTIVITY_REPORT);
        permissionIds.add(PermissionID.RUN_COST_CENTER_REPORT);
        permissionIds.add(PermissionID.RUN_ORDER_STATUS_REPORT);
        permissionIds.add(PermissionID.RUN_SUPPLIER_RATING_REPORT);
        permissionIds.add(PermissionID.MANAGE_RESOURCES);
        permissionIds.add(PermissionID.RUN_WHERE_USED_REPORT);
        permissionIds.add(PermissionID.RUN_DIRECT_MAIL_MATRIX_REPORT);
        permissionIds.add(PermissionID.RUN_PROJECT_BUDGET_REPORT);
        permissionIds.add(PermissionID.RUN_PROJECT_STATUS_REPORT);
        permissionIds.add(PermissionID.RUN_PROJECT_PARTICIPATION_REPORT);
        // preferences
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_ACCOUNT_ACTIVITY_BUYING_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_ACCOUNT_ACTIVITY_SELLING_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_ENABLE_REPORTWRITER);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_PROCUREMENT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_ORDER_INVOICING);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_SOURCING_STRATEGY_REPORTS);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_INVENTORY);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_PROPOSAL_WRITER);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_OUTSOURCE_PROFIT_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_BID_ANALYSIS_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_ESTIMATE_ANALYSIS_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_ESTIMATE_ANALYSIS_BID_COUNT_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_JOB_ANALYSIS_BID_COUNT_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_SELL_PRICE_ANALYSIS_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_SELL_PRICE_ANALYSIS_BID_COUNT_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_ESTIMATE_WINLOSS_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_RFE_WINLOSS_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_ORDER_ACTIVITY_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_ORDER_ACTIVITY_BY_BUYER_SUPPLIER_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_COSTCENTER);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_COST_CENTER_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_ORDER_STATUS_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_ENABLE_REPORTWRITER);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_UNCUT_SHIPMENT_ORDER_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_SUPPLIER_RATING_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_TIMECARD);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_RESOURCE_VIEW_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_WHERE_USED_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_PRODUCTION_MATRIX);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_PROJECT_BUDGET);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_PROJECT_BUDGET_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_PROJECT_STATUS_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_PROJECT_PARTICIPATION_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_PROJECT_MISC_COST_REPORT);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_WORK_IN_PROGRESS);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_WORKBOOK_REPORTS);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_DASHBOARD_REPORTS);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_OUTSOURCING);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_BROKERING);
        preferenceIds.add(PreferenceID.WORKGROUP_OPTION_SUPPLIER_RATING);
    }

    public List<ReportCategoryDTO> checkPermission(Long userId, Long workgroupId, ReportFilterVO filterVO, String portal) throws Exception {

        permissionMap = permissionService.getPermissionMap(permissionIds, workgroupId, userId, null);
        userPrefs = preferenceService.findGroupPrefs(workgroupId, preferenceIds);
        List<ReportCategoryDTO> reportCategoryDTOList;
        SAXReader reader = new SAXReader();
        // to be compliant, completely disable DOCTYPE declaration:
        reader.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
        if (filterVO != null) {
            reportCategoryDTOList = this.readReportXML(portal, filterVO, workgroupId, false, reader);
        } else {
            reportCategoryDTOList = this.getReportCategoryFromXML(portal, workgroupId, false, reader);
        }
        // custom report list
        List<ReportCategoryDTO> customReportList = new ArrayList<>();
        if (!DEFAULT_PORTAL.equals(portal)) {
            Workgroup workgroup = workgroupRepository.findById(workgroupId).orElse(new Workgroup());
            boolean isSupplierWorkgroup = workgroup.getWorkGroupTypeId() == WorkgroupTypeID.SUPPLIER;
            if (!isSupplierWorkgroup) {
                if (filterVO != null) {
                    customReportList = this.readReportXML(portal, filterVO, workgroupId, true, reader);
                } else {
                    customReportList = this.getReportCategoryFromXML(portal, workgroupId, true, reader);
                }
            }
        }

        reportCategoryDTOList.addAll(customReportList);

        return reportCategoryDTOList;
    }

    public List<ReportCategoryDTO> readReportXML(String portal, ReportFilterVO filterVO, Long workgroupId, boolean isCustomReport, SAXReader reader) throws Exception {

        String localeCode = "en_US";
        Locale locale =  Locale.forLanguageTag(localeCode.replaceAll("_", "-"));
        Context context = new Context(locale, null);
        boolean isOutSourcingOrBrokering = preferenceService.check("WORKGROUP_OPTION_OUTSOURCING", userPrefs) || preferenceService.check("WORKGROUP_OPTION_BROKERING", userPrefs);
        context.setVariable("isOutSourcingOrBrokering", isOutSourcingOrBrokering);
        context.setVariable("portalName", portal);
        context.setVariable("workgroupId", workgroupId);
        String xmlTemplate = isCustomReport ? "/custom-report-list.xml" : "/report-list.xml";
        String pathPortal = isCustomReport ? portal  : "default";
        String tempReportPath = TemplatesResource.getReportFilesResource(pathPortal, xmlTemplate);
        InputStream inputStream = null;
        // report category
        List<ReportCategoryDTO> categoryDTOList = new ArrayList<>();
        if (tempReportPath != null) {
            String xmlContent = templateEngine.process(pathPortal + "/" + xmlTemplate, context);
            inputStream = new ByteArrayInputStream(xmlContent.getBytes(StandardCharsets.UTF_8));
            if (inputStream == null) {
                return categoryDTOList;
            }
            Document document = reader.read(inputStream);
            Element rootElement = document.getRootElement();
            List<Element> reportCategorys = rootElement.elements();
            for (Element category : reportCategorys) {
                ReportCategoryDTO categoryDTO = new ReportCategoryDTO();

                // category filter
                if (filterVO.getCategoryValue(String.valueOf(Constants.NOT_INITIALIZED))
                        && filterVO.getCategoryValue(category.attributes().get(0).getValue())) {
                    continue;
                }

                // category name
                categoryDTO.setCategoryName(category.attributes().get(0).getValue());
                // report
                List<ReportListDTO> reportVOList = new ArrayList<>();
                List<Element> reports = category.elements();
                for (Element report : reports) {
                    ReportListDTO dto = new ReportListDTO();
                    Map<String, String> reportMap = new HashMap<>();
                    List<Attribute> attributes = report.attributes();
                    for (Attribute attribute : attributes) {
                        reportMap.put(attribute.getName(), attribute.getValue());
                    }

                    dto.setTitle(reportMap.get(ReportUtil.TITLE));
                    dto.setDescription(reportMap.get(ReportUtil.DESCRIPTION));
                    dto.setType(reportMap.get(ReportUtil.TYPE));
                    dto.setUrl(reportMap.get(ReportUtil.URL));
                    dto.setAction(reportMap.get(ReportUtil.ACTION));
                    dto.setIcon(reportMap.get(ReportUtil.ICON));
                    dto.setNrd(reportMap.get(ReportUtil.NRD));
                    dto.setArticleId(reportMap.get(ReportUtil.ARTICLEID));
                    dto.setIsShow(checkPrefAndPerm(reportMap.get(ReportUtil.CHECK)));
                    reportVOList.add(dto);
                }
                categoryDTO.setReports(reportVOList);
                categoryDTOList.add(categoryDTO);
            }
        }
        return categoryDTOList;
    }

    /**
     * Get category options
     * @param portal
     * @param isCustomReport
     * @return
     * @throws Exception
     */
    public List<ReportCategoryDTO> getReportCategoryFromXML(String portal,  Long workgroupId, boolean isCustomReport, SAXReader reader) throws Exception {

        String localeCode = "en_US";
        Locale locale =  Locale.forLanguageTag(localeCode.replaceAll("_", "-"));
        Context context = new Context(locale, null);
        boolean isOutSourcingOrBrokering = preferenceService.check("WORKGROUP_OPTION_OUTSOURCING", userPrefs) || preferenceService.check("WORKGROUP_OPTION_BROKERING", userPrefs);
        context.setVariable("isOutSourcingOrBrokering", isOutSourcingOrBrokering);
        context.setVariable("portalName", portal);
        context.setVariable("workgroupId", workgroupId);
        String xmlTemplate = isCustomReport ? "/custom-report-list.xml" : "/report-list.xml";
        String pathPortal = isCustomReport ? portal  : "default";
        String tempReportPath = TemplatesResource.getReportFilesResource(pathPortal, xmlTemplate);
        InputStream inputStream = null;
        // report category
        List<ReportCategoryDTO> categoryDTOList = new ArrayList<>();
        if (tempReportPath != null) {
            String xmlContent = templateEngine.process(pathPortal + "/" + xmlTemplate, context);
            inputStream = new ByteArrayInputStream(xmlContent.getBytes(StandardCharsets.UTF_8));
            if (inputStream == null) {
                return categoryDTOList;
            }
            Document document = reader.read(inputStream);
            Element rootElement = document.getRootElement();
            List<Element> reportCategorys = rootElement.elements();
            for (Element category : reportCategorys) {
                ReportCategoryDTO categoryDTO = new ReportCategoryDTO();
                // category name
                categoryDTO.setCategoryName(category.attributes().get(0).getValue());
                // report
                List<ReportListDTO> reportVOList = new ArrayList<>();
                List<Element> reports = category.elements();
                for (Element report : reports) {
                    ReportListDTO dto = new ReportListDTO();
                    Map<String, String> reportMap = new HashMap<>();
                    List<Attribute> attributes = report.attributes();
                    for (Attribute attribute : attributes) {
                        reportMap.put(attribute.getName(), attribute.getValue());
                    }

                    dto.setTitle(reportMap.get(ReportUtil.TITLE));
                    dto.setDescription(reportMap.get(ReportUtil.DESCRIPTION));
                    dto.setType(reportMap.get(ReportUtil.TYPE));
                    dto.setUrl(reportMap.get(ReportUtil.URL));
                    dto.setAction(reportMap.get(ReportUtil.ACTION));
                    dto.setIcon(reportMap.get(ReportUtil.ICON));
                    dto.setNrd(reportMap.get(ReportUtil.NRD));
                    dto.setArticleId(reportMap.get(ReportUtil.ARTICLEID));
                    dto.setIsShow(checkPrefAndPerm(reportMap.get(ReportUtil.CHECK)));
                    reportVOList.add(dto);
                }
                categoryDTO.setReports(reportVOList);
                categoryDTOList.add(categoryDTO);
            }
        }
        return categoryDTOList;
    }

    private boolean checkPrefAndPerm(String check) throws Exception {
        if (check == null) {
            return true;
        }
        String[] strs = check.split(" ");
        for (String str : strs) {
            if (str.contains("pref:")) {
                String pref = str.substring(5);
                if (!preferenceService.check(pref, userPrefs)) {
                    return false;
                }
            } else if (str.contains("perm:")) {
                String perm = str.substring(5);
                Field field = PermissionID.class.getField(perm);
                field.setAccessible(true);
                String permValue = field.get(null).toString();
                if (permissionMap.get(permValue)) {
                    return true;
                } else {
                    return false;
                }
            }
        }
        return true;
    }

}
