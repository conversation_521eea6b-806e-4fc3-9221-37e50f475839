package com.noosh.app.service.report;

import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.accounts.UserProfileDTO;
import com.noosh.app.commons.dto.report.DatamartDTO;
import com.noosh.app.commons.dto.report.ReportCategoryDTO;
import com.noosh.app.commons.dto.report.ReportDTO;
import com.noosh.app.commons.dto.report.ReportListDTO;
import com.noosh.app.commons.entity.security.AccountUser;
import com.noosh.app.commons.entity.report.ReportFavorite;
import com.noosh.app.commons.entity.team.TeamMember;
import com.noosh.app.commons.entity.team.TeamObject;
import com.noosh.app.commons.entity.workgroup.Workgroup;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.commons.vo.report.*;
import com.noosh.app.config.portal.service.PortalService;
import com.noosh.app.repository.jpa.security.AccountUserRepository;
import com.noosh.app.repository.jpa.report.ReportFavoriteRepository;
import com.noosh.app.repository.jpa.team.TeamMemberRepository;
import com.noosh.app.repository.jpa.team.TeamObjectRepository;
import com.noosh.app.repository.jpa.workgroup.WorkgroupRepository;
import com.noosh.app.repository.mybatis.report.ReportMyBatisMapper;
import com.noosh.app.service.permission.PermissionService;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 9/13/2021
 */
@Service
@Transactional
public class ReportListService {

    public static final String PROCESS_DATAMART = "DM";
    public static final String REPORT_LIST_FILTER_PREF_PREFIX = "REACT_REPORT_LIST_FILTER_";

    public final static String CONTROLNAME_CATEGORYS = "categorys";
    public final static String CONTROLNAME_CREATORS = "creators";
    public final static String CONTROLNAME_IS_REAL_TIME = "isRealTime";
    public final static String CONTROLNAME_IS_DATA_MART = "isDataMart";
    public final static String CONTROLNAME_IS_SCHEDULE = "isSchedule";
    public final static String CONTROLNAME_PAGE_SIZE = "pageSize";
    public final static String CONTROLNAME_SORT = "sort";
    public final static String CONTROLNAME_SORT_ORDER = "order";
    public static final String DEFAULT_PORTAL = "default";

    @Autowired
    private ReportFavoriteRepository reportFavoriteRepository;
    @Autowired
    private ReportMyBatisMapper reportMybatisMapper;
    @Autowired
    private TeamObjectRepository teamObjectRepository;
    @Autowired
    private TeamMemberRepository teamMemberRepository;
    @Autowired
    private ReportCheckPermissionService reportCheckPermissionService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private AccountUserRepository accountUserRepository;
    @Autowired
    private WorkgroupRepository workgroupRepository;
    @Autowired
    private PortalService portalService;

    private boolean canCreateReport;
    private boolean hasReportWriter;
    private static final List<String> PREF_IDS = new ArrayList<>(16);

    @PostConstruct
    public void init() {
        PREF_IDS.add(REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_CATEGORYS);
        PREF_IDS.add(REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_CREATORS);
        PREF_IDS.add(REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_IS_REAL_TIME);
        PREF_IDS.add(REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_IS_DATA_MART);
        PREF_IDS.add(REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_IS_SCHEDULE);
        PREF_IDS.add(REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_PAGE_SIZE);
        PREF_IDS.add(REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_SORT);
        PREF_IDS.add(REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_SORT_ORDER);
    }

    public ReportFilterOptionVO options(Long currentUserId, Long currentWorkgroupId) throws Exception {
        Workgroup workgroup = workgroupRepository.findById(currentWorkgroupId).orElse(new Workgroup());
        String portal = workgroup.getPortal();
        if(portal == null){
            portal = DEFAULT_PORTAL;
        }
        // Get all standard and custom reports
        List<ReportCategoryDTO> reportCategoryList = reportCheckPermissionService.checkPermission(currentUserId, currentWorkgroupId, null, portal);
        List<String> categoryNames = new ArrayList<>();
        reportCategoryList.forEach(reportCategoryDTO -> {
            if (!categoryNames.contains(reportCategoryDTO.getCategoryName())) {
                categoryNames.add(reportCategoryDTO.getCategoryName());
            }
        });

        // Category
        List<DropdownVO<String>> categorys = new ArrayList<>();
        categorys.add(new DropdownVO<>(String.valueOf(Constants.NOT_INITIALIZED), null));
        categoryNames.forEach(categoryName -> {
            categorys.add(new DropdownVO<>(categoryName, categoryName));
        });

        // Creator
        List<DropdownVO<String>> creators = new ArrayList<>();
        TeamObject teamObject = teamObjectRepository.findByObjectIdAndObjectClassId(currentWorkgroupId, ObjectClassID.WORKGROUP);
        TeamMember teamMember = teamMemberRepository.findByTeamIdAndUserIdAndIsCurrent(teamObject.getTeamId(), currentUserId, true);
        // Get all saved reports
        List<ReportDTO> savedReports = reportMybatisMapper.findCreators(currentUserId, currentWorkgroupId, teamMember.getTeamId(), teamMember.getRoleId());
        creators.add(new DropdownVO<>(String.valueOf(Constants.NOT_INITIALIZED), null));
        savedReports.forEach(savedReport -> {
            creators.add(new DropdownVO<>(String.valueOf(savedReport.getOwnerUserId()), savedReport.getOwnerUser()));
        });

        // Report Type
        List<DropdownVO<String>> reportTypes = new ArrayList<>();
        reportTypes.add(new DropdownVO<>(String.valueOf(Constants.NOT_INITIALIZED), null));
        reportTypes.add(new DropdownVO<>(ReportUtil.TAB_FAVORITE, ReportUtil.TAB_FAVORITE));
        reportTypes.add(new DropdownVO<>(ReportUtil.TAB_SAVED, ReportUtil.TAB_SAVED));
        reportTypes.add(new DropdownVO<>(ReportUtil.TAB_STANDARD, ReportUtil.TAB_STANDARD));

        ReportFilterOptionVO optionVO = new ReportFilterOptionVO();
        optionVO.setCategorys(categorys);
        optionVO.setCreators(creators);
        optionVO.setReportTypes(reportTypes);

        return optionVO;
    }

    public ReportListFilterVO getFilter(long workgroupId, long userId) {

        Map<String, String> prefs = preferenceService.findUserPrefs(workgroupId, userId, PREF_IDS);
        ReportListFilterVO filter = new ReportListFilterVO();

        // categorys
        String categorysStr = preferenceService.getString(
                REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_CATEGORYS, prefs, String.valueOf(Constants.NOT_INITIALIZED));
        if (!Util.isNullOrEmpty(categorysStr)) {
            String[] categorysPref = Util.split(categorysStr, ",");
            filter.setCategorys(Arrays.asList(categorysPref));
        }

        // creators
        String creatorsStr = preferenceService.getString(
                REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_CREATORS, prefs, String.valueOf(Constants.NOT_INITIALIZED));
        if (!Util.isNullOrEmpty(creatorsStr)) {
            String[] creatorsPref = Util.split(creatorsStr, ",");
            filter.setCreators(Arrays.asList(creatorsPref));
        }

        // is Real Time
        String isRealTime = preferenceService.getString(
                REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_IS_REAL_TIME, prefs,
                String.valueOf(0));
        filter.setIsRealTime(Boolean.valueOf(isRealTime));

        // is Data Mart
        String isDataMart = preferenceService.getString(
                REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_IS_DATA_MART, prefs,
                String.valueOf(0));
        filter.setIsDataMart(Boolean.valueOf(isDataMart));

        // is Schedule
        String isSchedule = preferenceService.getString(
                REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_IS_SCHEDULE, prefs,
                String.valueOf(0));
        filter.setIsSchedule(Boolean.valueOf(isSchedule));
        // page size
        String pageSizeStr = preferenceService.getString(
                REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_PAGE_SIZE, prefs,
                String.valueOf(10));
        filter.setPageSize(Integer.valueOf(pageSizeStr));

        return filter;
    }

    public void updateFilter(List<String> categorys, List<String> creators, boolean isRealTime,
                             boolean isDataMart, boolean isSchedule, Long currentUserId, Long currentWorkgroupId, PageVO page) {
        Map<String, String> prefs = new HashMap<>();

        // category
        if(categorys != null) {
            prefs.put(
                    REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_CATEGORYS,
                    Util.arrayToString(categorys.toArray(), ",", false));
        }

        // creator
        if (creators != null) {
            prefs.put(
                    REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_CREATORS,
                    Util.arrayToString(creators.toArray(), ",", false));
        }

        // is real time
        prefs.put(
                REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_IS_REAL_TIME,
                String.valueOf(isRealTime));

        // is data mart
        prefs.put(
                REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_IS_DATA_MART,
                String.valueOf(isDataMart));

        // is schedule
        prefs.put(
                REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_IS_SCHEDULE,
                String.valueOf(isSchedule));

        // page size
        prefs.put(
                REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_PAGE_SIZE, String.valueOf(page.getSize()));

        prefs.put(REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_SORT, page.getSort());
        prefs.put(REPORT_LIST_FILTER_PREF_PREFIX + CONTROLNAME_SORT_ORDER, page.getOrder());

        preferenceService.saveUserPreference(currentWorkgroupId, currentUserId, prefs);
    }

    public String getAiViewMoreExternalUrl(Long currentWorkgroupId, String search) {
        boolean enableReactReportList = preferenceService.check(PreferenceID.WHITELIST_REACT_REPORT_LIST, currentWorkgroupId);
        HashMap<String, String> params = new HashMap<>();
        if (enableReactReportList) {
            params.put("reportType", "all");
            params.put("searchString", search);
            return NooshOneUrlUtil.composeLinkToReact("/report/list", params);
        } else {
            params.put("tab", "saved");
            return NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.REPORT_LIST, params);
        }

    }

    public List<ReportListVO> list(ReportFilterVO filterVO, Long currentUserId, Long currentWorkgroupId, PageVO page) throws Exception {
        List<ReportListVO> reportListVOList = new ArrayList<>();

        canCreateReport = permissionService.checkAll(PermissionID.CREATE_REPORT_SPEC, currentWorkgroupId, currentUserId, -1L);
        hasReportWriter = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ENABLE_REPORTWRITER, currentWorkgroupId);

        TeamObject teamObject = teamObjectRepository.findByObjectIdAndObjectClassId(currentWorkgroupId, ObjectClassID.WORKGROUP);
        TeamMember teamMember = teamMemberRepository.findByTeamIdAndUserIdAndIsCurrent(teamObject.getTeamId(), currentUserId, true);
        Workgroup workgroup = workgroupRepository.findById(currentWorkgroupId).orElse(null);
        String portal = workgroup.getPortal();
        if(portal == null){
            portal = DEFAULT_PORTAL;
        }
        // Get all standard and custom reports
        List<ReportCategoryDTO> reportCategoryList = reportCheckPermissionService.checkPermission(currentUserId, currentWorkgroupId, filterVO, portal);

        // Get all saved reports
        Map<String, ArrayList<ReportDTO>> savedReportMap = findSavedReports(currentUserId, currentWorkgroupId, teamMember.getTeamId(), teamMember.getRoleId());
        checkPermission(reportCategoryList, savedReportMap);

        // Get all scheduled reports
        Map<Long, ReportDTO> scheduledReportMap = findScheduledReports(currentUserId, currentWorkgroupId, teamMember.getTeamId(), teamMember.getRoleId());
        AccountUser accountUser = accountUserRepository.findById(currentUserId).orElse(null);
        String personEmail = accountUser.getPerson().getDefaultEmail() != null ? accountUser.getPerson().getDefaultEmail().getEmail() : null;

        // Get all favorite reports
        Map<String, ReportFavorite> favoriteReportMap = findFavoriteReports(currentUserId);

        // custom fields
        Map<String, String> customFields = portalService.getAttributes(portal);

        boolean filterFavorite = filterVO.getReportTypeValue(ReportUtil.TAB_FAVORITE) && !filterVO.getReportTypeValue(String.valueOf(Constants.NOT_INITIALIZED))
                                    && !filterVO.getReportTypeValue(ReportUtil.TAB_STANDARD) && !filterVO.getReportTypeValue(ReportUtil.TAB_SAVED);
        boolean filterStandard = (filterVO.getReportTypeValue(String.valueOf(Constants.NOT_INITIALIZED)) || filterVO.getReportTypeValue(ReportUtil.TAB_STANDARD))
                                    && filterVO.getCreatorValue(String.valueOf(Constants.NOT_INITIALIZED));
        boolean filterSaved = (filterVO.getReportTypeValue(String.valueOf(Constants.NOT_INITIALIZED)) || filterVO.getReportTypeValue(ReportUtil.TAB_SAVED));

        boolean filterFavoriteAndOthers = (filterVO.getReportTypeValue(ReportUtil.TAB_FAVORITE) && filterVO.getReportTypeValue(ReportUtil.TAB_SAVED))
                || (filterVO.getReportTypeValue(ReportUtil.TAB_FAVORITE) && filterVO.getReportTypeValue(ReportUtil.TAB_STANDARD));

        for (ReportCategoryDTO reportCategoryDTO : reportCategoryList) {
            String categoryName = reportCategoryDTO.getCategoryName();
            // report category filter
            if (filterVO.getCategoryMap().get(String.valueOf(Constants.NOT_INITIALIZED)) == null
                && filterVO.getCategoryMap().get(categoryName) == null) {
                continue;
            }
            for (ReportListDTO reportListDTO : reportCategoryDTO.getReports()) {
                boolean hasFavorite = false;
                boolean hasStandard = false;
                boolean hasSaved = false;
                String favoriteString;

                if (reportListDTO.getUrl() != null) {
                    favoriteString = reportListDTO.getUrl();
                } else {
                    favoriteString = reportListDTO.getNrd();

                    if(savedReportMap.get(reportListDTO.getNrd()) != null) {
                        for (ReportDTO savedReport : savedReportMap.get(reportListDTO.getNrd())) {
                            String savedFavoriteString = String.valueOf(savedReport.getReportId());
                            if (favoriteReportMap.get(savedFavoriteString) != null) {
                                hasFavorite = true;
                            }
                        }
                    }
                }
                // favorite report
                if (favoriteReportMap.get(favoriteString) != null) {
                    hasFavorite = true;
                }
                if ((hasFavorite && filterFavorite) || (hasFavorite && filterFavoriteAndOthers)) {
                    getFavoriteReportVO(reportListDTO, savedReportMap, favoriteReportMap, scheduledReportMap, personEmail, filterVO, reportListVOList, categoryName, customFields);
                }

                // standard report
                if (ReportUtil.TYPE_STANDARD.equals(reportListDTO.getType())) {
                    hasStandard = true;
                }
                boolean isDuplicate = reportListVOList.stream().anyMatch(reportListVO -> reportListVO.getReportName().equals(reportListDTO.getTitle()) && categoryName.equals(reportListVO.getReportCategory()));
                if (hasStandard && filterStandard && !isDuplicate) {
                    reportListVOList.add(getStandardReportVO(reportListDTO, hasFavorite, categoryName, customFields));
                }

                // saved report
                if (savedReportMap.get(reportListDTO.getNrd()) != null && savedReportMap.get(reportListDTO.getNrd()).size() > 0) {
                    hasSaved = true;
                }
                if (hasSaved && filterSaved) {
                    getSavedReportVO(reportListDTO, savedReportMap, favoriteReportMap, scheduledReportMap, personEmail, filterVO, categoryName, customFields, reportListVOList);
                }
            }
        }

        // report name filter
        if (filterVO.getSearch() != null) {
            reportListVOList = reportListVOList.stream()
                    .filter(reportListVO ->reportListVO.getReportName().toUpperCase().contains(filterVO.getSearch().toUpperCase()))
                    .collect(Collectors.toList());
        }
        // filter by realtime or datamart
        if (filterVO.isRealTime() || filterVO.isDataMart() || filterVO.isSchedule()) {
            if (filterVO.isRealTime()) {
                reportListVOList = reportListVOList.stream()
                        .filter(reportListVO ->reportListVO.getIsRealTime())
                        .collect(Collectors.toList());
            }
            if (filterVO.isDataMart()) {
                reportListVOList = reportListVOList.stream()
                        .filter(reportListVO ->reportListVO.getIsDataMart())
                        .collect(Collectors.toList());
            }

            if (filterVO.isSchedule()) {
                reportListVOList = reportListVOList.stream()
                        .filter(reportListVO ->reportListVO.getIsScheduleReport())
                        .collect(Collectors.toList());
            }
        }
        // Pagination
        page.setTotal(reportListVOList.size());
        List list = ReportUtil.pageHelper(reportListVOList, page);
        return list;
    }

    private Map<String, ArrayList<ReportDTO>> findSavedReports(Long currentUserId, Long currentWorkgroupId, Long teamId, Long roleId) {

        // Get all saved reports
        List<ReportDTO> savedReports = reportMybatisMapper.findSavedReports(currentUserId, currentWorkgroupId, teamId, roleId);
        Map<String, ArrayList<ReportDTO>> savedReportMap = new HashMap<>();
        for (ReportDTO savedReport : savedReports) {
            ArrayList<ReportDTO> reportList = savedReportMap.get(savedReport.getOriginalNrd());
            if (reportList == null) {
                reportList = new ArrayList<>();
                reportList.add(savedReport);
            }
            if (!reportList.contains(savedReport)) {
                reportList.add(savedReport);
            }
            savedReportMap.put(savedReport.getOriginalNrd(), reportList);
        }
        return savedReportMap;
    }

    private Map<Long, ReportDTO> findScheduledReports(Long currentUserId, Long currentWorkgroupId, Long teamId, Long roleId) {

        // Get all scheduled reports
        List<ReportDTO> scheduledReports = reportMybatisMapper.findScheduledReports(currentUserId, currentWorkgroupId, teamId, roleId);
        Map<Long, ReportDTO> scheduledReportMap = new HashMap<>();
        for (ReportDTO scheduledReport : scheduledReports) {
            scheduledReportMap.put(scheduledReport.getReportId(), scheduledReport);
        }
        return scheduledReportMap;
    }

    private Map<String, ReportFavorite> findFavoriteReports(Long currentUserId) {

        // Get all favorite reports
        List<ReportFavorite> reportFavoriteList = reportFavoriteRepository.findAllByAccountUserId(currentUserId);
        Map<String, ReportFavorite> favoritesMap = new HashMap();

        for (ReportFavorite reportFavorite : reportFavoriteList) {
            if (reportFavorite.getReportId() == null) {
                favoritesMap.put(reportFavorite.getOriginalNrd(), reportFavorite);
            } else {
                favoritesMap.put(String.valueOf(reportFavorite.getReportId()), reportFavorite);
            }
        }

        return favoritesMap;
    }

    private void checkPermission(List<ReportCategoryDTO> reportCategoryList, Map<String, ArrayList<ReportDTO>> savedReportMap) {
        reportCategoryList.forEach(reportCategoryDTO -> {
            List<ReportListDTO> reports = reportCategoryDTO.getReports();
            List<ReportListDTO> afterReports = new ArrayList<>();
            for (ReportListDTO report : reports) {
                if (!report.getIsShow()) {
                    if (savedReportMap.get(report.getNrd()) == null) {
                        // remove report
                        continue;
                    } else {
                        // disable report (will show saved report)
                        report.setAction(null);
                    }
                }
                afterReports.add(report);
            }
            reportCategoryDTO.setReports(afterReports);
        });

    }

    public void getFavoriteReportVO(ReportListDTO reportListDTO,
                                    Map<String, ArrayList<ReportDTO>> savedReportMap,
                                    Map<String, ReportFavorite> favoriteReportMap,
                                    Map<Long, ReportDTO> scheduledReportMap,
                                    String personEmail,
                                    ReportFilterVO filterVO,
                                    List<ReportListVO> reportListVOList,
                                    String categoryName,
                                    Map<String, String> customFields) {
        if (savedReportMap.get(reportListDTO.getNrd()) != null) {
            for (ReportDTO savedReport : savedReportMap.get(reportListDTO.getNrd())) {
                if(!filterVO.getCreatorValue(String.valueOf(Constants.NOT_INITIALIZED)) && !filterVO.getCreatorValue(String.valueOf(savedReport.getOwnerUserId()))){
                    continue;
                }
                if(favoriteReportMap.get(reportListDTO.getNrd()) != null) {
                    ReportListVO vo = getReportListVO(reportListDTO, categoryName, customFields);
                    reportListVOList.add(vo);
                }
                String savedFavoriteString = String.valueOf(savedReport.getReportId());
                if (favoriteReportMap.get(savedFavoriteString) != null) {
                    ReportListVO vo = new ReportListVO();
                    vo.setSavedReportId(savedReport.getReportId());
                    vo.setReportCategory(categoryName);
                    vo.setReportName(savedReport.getReportTitle());
                    vo.setDescription(savedReport.getDescription());
                    vo.setIsSavedReport(true);
                    vo.setIsFavoriteReport(true);
                    if (reportListDTO.getIcon().equals(ReportUtil.ICON_DATAMART)) {
                        vo.setIsDataMart(true);
                    } else if(reportListDTO.getIcon().equals(ReportUtil.ICON_REALTIME)) {
                        vo.setIsRealTime(true);
                    } else {
                        // custom icon
                        customFields.put(ReportUtil.ICON, reportListDTO.getIcon());
                        vo.setCustomFields(customFields);
                    }
                    // run link
                    HashMap<String, String> params = new HashMap<>();
                    params.put("reportId", String.valueOf(savedReport.getReportId()));
                    vo.setRunExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.REPORT_RUN, params));
                    vo.setReportCreator(savedReport.getOwnerUser());
                    vo.setReportCreatorDTO(this.getReportCreatorDTO(savedReport));
                    if (scheduledReportMap.get(savedReport.getReportId()) != null) {
                        ScheduleReportVO scheduleReportVO = getScheduleReportVO(scheduledReportMap.get(savedReport.getReportId()), personEmail);
                        vo.setScheduleReportVO(scheduleReportVO);
                        vo.setIsScheduleReport(true);
                    }
                    vo.setLastUpdateDate(savedReport.getLastUpdatedDate());
                    reportListVOList.add(vo);
                }
            }
        } else {
            if(filterVO.getCreatorValue(String.valueOf(Constants.NOT_INITIALIZED))){
                ReportListVO vo = getReportListVO(reportListDTO, categoryName, customFields);
                reportListVOList.add(vo);
            }
        }
    }

    private ReportListVO getReportListVO(ReportListDTO reportListDTO, String categoryName, Map<String, String> customFields) {
        ReportListVO vo = new ReportListVO();
        vo.setReportNrd(reportListDTO.getNrd());
        vo.setReportCategory(categoryName);
        vo.setReportName(reportListDTO.getTitle());
        vo.setDescription(reportListDTO.getDescription());
        vo.setIsFavoriteReport(true);
        vo.setIsStandardReport(ReportUtil.TYPE_STANDARD.equals(reportListDTO.getType()));
        if (reportListDTO.getIcon().equals(ReportUtil.ICON_DATAMART)) {
            vo.setIsDataMart(true);
        } else if(reportListDTO.getIcon().equals(ReportUtil.ICON_REALTIME)) {
            vo.setIsRealTime(true);
        }  else {
            // custom icon
            customFields.put(ReportUtil.ICON, reportListDTO.getIcon());
            vo.setCustomFields(customFields);
        }
        if (reportListDTO.getUrl() != null) {
            vo.setReportNrd(reportListDTO.getUrl());
            String reportURL = ReportUtil.REPORT_URL_PREFIX + reportListDTO.getUrl();
            vo.setRunExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(reportURL));
        } else {
            HashMap<String, String> params = new HashMap<>();
            params.put("report", reportListDTO.getNrd());
            if (reportListDTO.getAction() != null && reportListDTO.getAction().contains(ReportUtil.ACTION_RUN)) {
                vo.setRunExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.REPORT_RUN, params));
            }
        }
        return vo;
    }

    public ReportListVO getStandardReportVO(ReportListDTO reportListDTO, boolean hasFavorite, String categoryName, Map<String, String> customFields) {
        ReportListVO vo = new ReportListVO();
        vo.setReportCategory(categoryName);
        vo.setReportName(reportListDTO.getTitle());
        vo.setDescription(reportListDTO.getDescription());
        vo.setIsStandardReport(true);
        if (reportListDTO.getIcon().equals(ReportUtil.ICON_DATAMART)) {
            vo.setIsDataMart(true);
        } else if(reportListDTO.getIcon().equals(ReportUtil.ICON_REALTIME)) {
            vo.setIsRealTime(true);
        }  else {
            // custom icon
            customFields.put(ReportUtil.ICON, reportListDTO.getIcon());
            vo.setCustomFields(customFields);
        }
        if (reportListDTO.getUrl() != null) {
            String reportURL = ReportUtil.REPORT_URL_PREFIX + reportListDTO.getUrl();
            vo.setReportNrd(reportListDTO.getUrl());
            vo.setRunExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(reportURL));
        } else {
            vo.setReportNrd(reportListDTO.getNrd());

            HashMap<String, String> params = new HashMap<>();
            params.put("report", reportListDTO.getNrd());
            if (reportListDTO.getAction() != null && reportListDTO.getAction().contains(ReportUtil.ACTION_RUN)) {
                vo.setRunExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.REPORT_RUN, params));
            }
            if (canCreateReport && hasReportWriter) {
                if (reportListDTO.getAction() != null && reportListDTO.getAction().contains(ReportUtil.ACTION_EDIT)) {
                    vo.setCustomizeExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.REPORT_EDIT, params));
                } else if(reportListDTO.getAction() != null && reportListDTO.getAction().contains(ReportUtil.ACTION_SCHEDULE)) {
                    params.put("schedOnly", "1");
                    vo.setScheduleExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.REPORT_EDIT, params));
                }
            }
        }
        if (reportListDTO.getAction() != null && reportListDTO.getAction().contains(ReportUtil.ACTION_RUN)) {
            if(hasFavorite) {
                vo.setIsFavoriteReport(true);
            }
        }
        return vo;
    }

    public List<ReportListVO> getSavedReportVO(ReportListDTO reportListDTO,
                                               Map<String, ArrayList<ReportDTO>> savedReportMap,
                                               Map<String, ReportFavorite> favoriteReportMap,
                                               Map<Long, ReportDTO> scheduledReportMap,
                                               String personEmail,
                                               ReportFilterVO filterVO,
                                               String categoryName,
                                               Map<String, String> customFields,
                                               List<ReportListVO> reportListVOList) {
        for (ReportDTO savedReport : savedReportMap.get(reportListDTO.getNrd())) {
            if(reportListVOList.stream().anyMatch(reportListVO -> reportListVO.getSavedReportId() == savedReport.getReportId())) {
                continue;
            }
            ReportListVO vo = new ReportListVO();
            vo.setReportCategory(categoryName);
            if (!filterVO.getCreatorValue(String.valueOf(Constants.NOT_INITIALIZED))
                    && !filterVO.getCreatorValue(String.valueOf(savedReport.getOwnerUserId()))) {
                continue;
            }
            vo.setReportCreator(savedReport.getOwnerUser());
            vo.setReportCreatorDTO(this.getReportCreatorDTO(savedReport));

            String savedFavoriteString = String.valueOf(savedReport.getReportId());
            if(favoriteReportMap.get(savedFavoriteString) != null) {
                vo.setIsFavoriteReport(true);
            }
            vo.setIsSavedReport(true);
            vo.setSavedReportId(savedReport.getReportId());
            vo.setReportName(savedReport.getReportTitle());

            if (reportListDTO.getIcon().equals(ReportUtil.ICON_DATAMART)) {
                vo.setIsDataMart(true);
            } else if(reportListDTO.getIcon().equals(ReportUtil.ICON_REALTIME)) {
                vo.setIsRealTime(true);
            } else {
                // custom icon
                customFields.put(ReportUtil.ICON, reportListDTO.getIcon());
                vo.setCustomFields(customFields);
            }

            // run link
            HashMap<String, String> params = new HashMap<>();
            params.put("reportId", String.valueOf(savedReport.getReportId()));
            vo.setRunExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.REPORT_RUN, params));

            if (canCreateReport && hasReportWriter) {
                params.clear();
                params.put("copyReportId", String.valueOf(savedReport.getReportId()));
                params.put("copyTitle", "Copy of " + savedReport.getReportTitle());
                // copy link
                if(reportListDTO.getAction() != null && reportListDTO.getAction().contains(ReportUtil.ACTION_SCHEDULE)) {
                    params.put("schedOnly", "1");
                    vo.setCopyExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.REPORT_EDIT, params));
                } else {
                    vo.setCopyExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.REPORT_EDIT, params));
                }

                if (savedReport.getOwnerUserId() == JwtUtil.getUserId().longValue()) {
                    params.clear();
                    if (reportListDTO.getAction() != null && reportListDTO.getAction().contains(ReportUtil.ACTION_EDIT)) {
                        // edit link
                        params.put("reportId", String.valueOf(savedReport.getReportId()));
                        params.put("listTab", "saved");
                        vo.setEditExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.REPORT_EDIT, params));
                    } else if(reportListDTO.getAction() != null && reportListDTO.getAction().contains(ReportUtil.ACTION_SCHEDULE)) {
                        // schedule link
                        params.put("reportId", String.valueOf(savedReport.getReportId()));
                        params.put("schedOnly", "1");
                        vo.setScheduleExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.REPORT_EDIT, params));
                    }
                    params.clear();
                    params.put("reportId", String.valueOf(savedReport.getReportId()));
                    params.put("tab", "saved");
                    params.put("acName", "ACTION_DELETE");
                    // detele link
                    vo.setDeleteExternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.REPORT_LIST, params));
                }
            }

            if (scheduledReportMap.get(savedReport.getReportId()) != null) {
                ScheduleReportVO scheduleReportVO = getScheduleReportVO(scheduledReportMap.get(savedReport.getReportId()), personEmail);
                vo.setIsScheduleReport(true);
                vo.setScheduleReportVO(scheduleReportVO);
            }

            vo.setDescription(savedReport.getDescription());
            vo.setLastUpdateDate(savedReport.getLastUpdatedDate());
            reportListVOList.add(vo);
        }
        return reportListVOList;
    }

    private UserProfileDTO getReportCreatorDTO(ReportDTO savedReport) {
        UserProfileDTO userProfileDTO = new UserProfileDTO();
        userProfileDTO.setFullName(savedReport.getOwnerUser());
        userProfileDTO.setFirstName(savedReport.getOwnerUserFirstName());
        userProfileDTO.setLastName(savedReport.getOwnerUserLastName());
        if (savedReport.getOwnerUserProfileImage() != null) {
            userProfileDTO.setProfileImage(NooshOneUrlUtil.composeLinkToS3(savedReport.getOwnerUserProfileImage()));
        }
        return userProfileDTO;
    }

    private ScheduleReportVO getScheduleReportVO(ReportDTO report, String personEmail) {
        ScheduleReportVO scheduleReportVO = new ScheduleReportVO();
        scheduleReportVO.setLastRunTime(report.getLastScheduledDate());
        scheduleReportVO.setNextRunTime(report.getNextScheduledDate());
        List<String> recipientList = new ArrayList<>();
        recipientList.add(personEmail);
        if (report.getRecipients() != null) {
            String[] recipients = report.getRecipients().split(",");
            for (String recipient : recipients) {
                recipientList.add(recipient);
            }
        }
        scheduleReportVO.setRecipients(recipientList);
        return scheduleReportVO;
    }

    /**
     * update favorite report
     * @param favoriteMap key is report nrd or report id
     * @param userId
     * @param workgroupId
     */
    public void updateFavorite(Map<String, Long> favoriteMap, Long userId, Long workgroupId) {
        Map<String, ReportFavorite> alreadySelected = findFavoriteReports(userId);
        TeamObject teamObject = teamObjectRepository.findByObjectIdAndObjectClassId(workgroupId, ObjectClassID.WORKGROUP);
        TeamMember teamMember = teamMemberRepository.findByTeamIdAndUserIdAndIsCurrent(teamObject.getTeamId(), userId, true);
        for (Iterator iter = favoriteMap.keySet().iterator(); iter.hasNext(); ) {
            String key = iter.next().toString();
            Long value = favoriteMap.get(key);
            if (value == 1) {
                    //Check to see if the report is already selected as a favorite
                if (alreadySelected.containsKey(key)) {
                    continue;
                }
                ReportFavorite rfb = new ReportFavorite();
                rfb.setAccountUserId(userId);
                // Check to see if the report identifier is a number or not (report id or not)
                if (key.toLowerCase().equals(key.toUpperCase())) {
                    ReportDTO report = reportMybatisMapper.findReport(userId, workgroupId, teamMember.getTeamId(), teamMember.getRoleId(), Long.valueOf(key));
                    rfb.setReportId(report.getReportId());
                    rfb.setOriginalNrd(report.getOriginalNrd());
                } else {
                    rfb.setOriginalNrd(key);
                }
                rfb.setCreateUserId(userId);
                reportFavoriteRepository.save(rfb);
            } else {
                if (!alreadySelected.containsKey(key)) {
                    continue;
                }
                ReportFavorite reportFavorite = alreadySelected.get(key);
                reportFavoriteRepository.delete(reportFavorite);
            }
        }

    }

    /**
     * get report options
     * @param filterVO
     * @param currentUserId
     * @param currentWorkgroupId
     * @return
     * @throws Exception
     */
    public ReportOptionListVO reportOptions(ReportFilterVO filterVO, Long currentUserId, Long currentWorkgroupId) throws Exception {
        ReportOptionListVO optionListVO = new ReportOptionListVO();
        optionListVO.setPrefixEnternalLink(NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.REPORT_EDIT));
        canCreateReport = permissionService.checkAll(PermissionID.CREATE_REPORT_SPEC, currentWorkgroupId, currentUserId, -1L);
        hasReportWriter = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ENABLE_REPORTWRITER, currentWorkgroupId);
        TeamObject teamObject = teamObjectRepository.findByObjectIdAndObjectClassId(currentWorkgroupId, ObjectClassID.WORKGROUP);
        TeamMember teamMember = teamMemberRepository.findByTeamIdAndUserIdAndIsCurrent(teamObject.getTeamId(), currentUserId, true);
        // Get all standard and custom reports
        Workgroup workgroup = workgroupRepository.findById(currentWorkgroupId).orElse(null);
        // Get datamart refresh dates
        DatamartDTO dto;
        if (workgroup.getCustomReportDS() != null) {
            // customized
            dto = new DatamartDTO();
        } else {
            dto = reportMybatisMapper.lastAndNextRefreshStartTime(PROCESS_DATAMART);
        }
        optionListVO.setThisRefreshStartTime(dto.getThisRefreshStartTime());
        optionListVO.setThisRefreshEndTime(dto.getThisRefreshEndTime());
        optionListVO.setLastRefreshStartTime(dto.getLastRefreshStartTime());
        optionListVO.setLastRefreshEndTime(dto.getLastRefreshEndTime());
        optionListVO.setEstThisRefreshEndTime(dto.getEstThisRefreshEndTime());

        String portal = workgroup.getPortal();
        if(portal == null){
            portal = DEFAULT_PORTAL;
        }
        List<ReportCategoryDTO> reportCategoryList = reportCheckPermissionService.checkPermission(currentUserId, currentWorkgroupId, null, portal);

        // Get all saved reports
        Map<String, ArrayList<ReportDTO>> savedReportMap = findSavedReports(currentUserId, currentWorkgroupId, teamMember.getTeamId(), teamMember.getRoleId());
        checkPermission(reportCategoryList, savedReportMap);

        boolean hasDataExtractionReports = false;
        boolean hasSpecialReports = false;
        List<ReportOptionVO> createReportList = new ArrayList<>();
        List<ReportOptionVO> aggregateReportList = new ArrayList<>();
        for (ReportCategoryDTO reportCategoryDTO : reportCategoryList) {
            for (ReportListDTO reportListDTO : reportCategoryDTO.getReports()) {
                if (reportListDTO.getNrd() != null) {
                    String title = reportCategoryDTO.getCategoryName() + " - " + reportListDTO.getTitle();
                    if (ReportUtil.TYPE_DATA_EXTRACTION.equals(reportListDTO.getType())) {
                        hasDataExtractionReports = true;
                        createReportList.add(getReportOptionVO(title, reportListDTO));
                    } else if(ReportUtil.TYPE_SPECIAL.equals(reportListDTO.getType())) {
                        hasSpecialReports = true;
                        aggregateReportList.add(getReportOptionVO(title, reportListDTO));
                    }
                }
            }
        }

        if (hasReportWriter && canCreateReport) {
            if (hasDataExtractionReports) {
                optionListVO.setCreateReports(createReportList);
            }

            if((filterVO.getReportTypeValue(String.valueOf(Constants.NOT_INITIALIZED)) ||
                    filterVO.getReportTypeValue(ReportUtil.TAB_SAVED)) && hasSpecialReports) {
                optionListVO.setAggregateReports(aggregateReportList);
            }
        }

        //custom fields
        Map<String, String> customFields = portalService.getAttributes(portal);
        optionListVO.setCustomFields(customFields);
        return optionListVO;
    }

    private ReportOptionVO getReportOptionVO(String title, ReportListDTO reportListDTO) {
        ReportOptionVO reportOptionVO = new ReportOptionVO();
        reportOptionVO.setTitle(title);
        reportOptionVO.setValue(reportListDTO.getNrd());
        reportOptionVO.setDescription(reportListDTO.getDescription());
        return reportOptionVO;
    }
}
