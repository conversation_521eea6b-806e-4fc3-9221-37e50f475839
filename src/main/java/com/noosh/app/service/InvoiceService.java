package com.noosh.app.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.invoice.InvoiceDTO;
import com.noosh.app.commons.dto.invoice.InvoiceListDTO;
import com.noosh.app.commons.dto.order.AggregatedOrderVersionDTO;
import com.noosh.app.commons.dto.order.OrderVersionDTO;
import com.noosh.app.commons.dto.order.ProjectOrderWidgetDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.entity.invoice.InvoiceItem;
import com.noosh.app.commons.entity.project.Project;
import com.noosh.app.commons.entity.security.Workgroup;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.invoice.*;
import com.noosh.app.mapper.invoice.InvoiceMapper;
import com.noosh.app.repository.jpa.invoice.InvoiceItemRepository;
import com.noosh.app.repository.jpa.invoice.InvoiceRepository;
import com.noosh.app.repository.jpa.order.OrderItemRepository;
import com.noosh.app.repository.jpa.project.ProjectRepository;
import com.noosh.app.repository.jpa.security.WorkgroupRepository;
import com.noosh.app.repository.mybatis.invoice.InvoiceMyBatisMapper;
import com.noosh.app.repository.mybatis.order.OrderMyBatisMapper;
import com.noosh.app.service.account.SupplierWorkgroupService;
import com.noosh.app.service.permission.invoice.CreateInvoicePermission;
import com.noosh.app.service.permission.invoice.DeleteInvoicePermission;
import com.noosh.app.service.permission.invoice.EditInvoicePermission;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.rating.RatingService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.util.I18NUtils;
import com.noosh.app.service.util.NooshOneUrlUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.repository.jpa.security.ObjectStateRepository;

import jakarta.inject.Inject;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Shan
 * @since 0.0.1
 */
@Service
public class InvoiceService {

    @Autowired
    private OrderItemRepository orderItemRepository;
    @Autowired
    private I18NUtils i18NUtils;
    @Autowired
    private ProjectRepository projectRepository;
    @Autowired
    private SupplierWorkgroupService supplierWorkgroupService;
    @Autowired
    private WorkgroupRepository workgroupRepository;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private OrderMyBatisMapper orderMyBatisMapper;
    @Autowired
    private EditInvoicePermission editInvoicePermission;
    @Autowired
    private CreateInvoicePermission createInvoicePermission;
    @Autowired
    private PreferenceService preferenceService;
    @Autowired
    private InvoiceMyBatisMapper invoiceMyBatisMapper;
    @Autowired
    private ObjectStateRepository objectStateRepository;
    @Autowired
    private AggregatedOrderService aggregatedOrderService;
    @Autowired
    private InvoiceItemRepository invoiceItemRepository;
    @Autowired
    private DeleteInvoicePermission deleteInvoicePermission;
    @Autowired
    private PermissionService permissionService;
    @Inject
    private InvoiceRepository invoiceRepository;
    @Inject
    private InvoiceMapper invoiceMapper;
    @Inject
    private RatingService ratingService;
    @Inject
    private OrderService orderService;

    public final static String ORDER_BUY = "Buy";
    public final static String ORDER_SELL = "Sell";
    
    /**
     * get invoice list with pagination
     *
     * @param workgroupId workgroupId
     * @param userId      userId
     * @param projectId   projectId
     * @param tabId       tabId
     * @param page        page
     * @return invoice list
     */
    public InvoiceListVO getInvoiceList(Long workgroupId, Long userId, Long projectId, Integer tabId, PageVO page) {

        InvoiceListVO vo = new InvoiceListVO();
        vo.setTabId(tabId);

        ProjectDTO parentProject = projectService.findProjectById(projectId);
        vo.setProjectName(parentProject.getTitle());
        vo.setProjectExternalLink(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(projectId));

        Boolean canViewInvoice = permissionService.checkAll(PermissionID.VIEW_INVOICE, workgroupId, userId, projectId);

        Long soldOrders = orderMyBatisMapper.findSoldOrdersCount(projectId, workgroupId, parentProject.isClientNotOnNoosh());
        boolean hashSoldOrders = (soldOrders > 0);
        boolean canCreateInvoice = createInvoicePermission.check(parentProject, workgroupId, userId, projectId);

        if (tabId == null) {
            tabId = -1;
        }

        if (hashSoldOrders && canCreateInvoice) {
            vo.setCreateInvoiceExternalLink(NooshOneUrlUtil.composeGotoCreateInvoiceLinkToEnterprise(projectId));
            if (parentProject.isOutsourcerProject() && tabId == 0) {
                vo.setCreateInvoiceExternalLink(null);
            }
        }

        vo.setIsOutsourcer(parentProject.isBrokerOutsourcerProject());
        vo.setIsBuyer(parentProject.isBuyerProject());

        boolean showBuyAndSellTabs = false;
        if (parentProject.isClientOnNoosh() || parentProject.isOutsourcerProject()) {

            ProjectDTO masterProject = parentProject;
            if (!parentProject.isMaster()) {
                masterProject = projectService.findProjectById(parentProject.getMasterProjectId());
            }

            Map<String, String> ownerGroupPrefs = preferenceService.findGroupPrefs(masterProject.getOwnerWorkgroupId());
            boolean invoiceEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ORDER_INVOICING, ownerGroupPrefs);

            Map<String, String> clientPrefs = parentProject.isClientOnNoosh()
                    ? preferenceService.findGroupPrefs(masterProject.getClientWorkgroupId())
                    : null;
            boolean clientInvoiceEnabled = parentProject.isClientOnNoosh()
                    ? preferenceService.check(PreferenceID.WORKGROUP_OPTION_ORDER_INVOICING, clientPrefs)
                    : preferenceService.check(PreferenceID.WORKGROUP_OPTION_ORDER_INVOICING, ownerGroupPrefs);

            showBuyAndSellTabs = invoiceEnabled && clientInvoiceEnabled;
        }
        vo.setShowTabs(showBuyAndSellTabs);

        List<InvoiceListDTO> invoiceListDTOS = null;
        if (canViewInvoice) {
            Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
            if (showBuyAndSellTabs && tabId == 0) {
                // buy
                vo.setIsIncludeBuyerSide(true);
                vo.setIsIncludeSupplierSide(false);
                invoiceListDTOS = invoiceMyBatisMapper.findInvoiceList(projectId, workgroupId,
                        true, false, parentProject.isClientNotOnNoosh());

            } else if (showBuyAndSellTabs && tabId == 1) {
                // sell
                vo.setIsIncludeBuyerSide(false);
                vo.setIsIncludeSupplierSide(true);
                invoiceListDTOS = invoiceMyBatisMapper.findInvoiceList(projectId, workgroupId,
                        false, true, parentProject.isClientNotOnNoosh());

            } else {
                // all
                vo.setIsIncludeBuyerSide(true);
                vo.setIsIncludeSupplierSide(true);
                invoiceListDTOS = invoiceMyBatisMapper.findInvoiceList(projectId, workgroupId,
                        true, true, parentProject.isClientNotOnNoosh());
            }
            page.setTotal(pageInfo.getTotal());
        }

        if (invoiceListDTOS != null) {
            Map<String, InvoiceListOrderVO> orderMap = new HashMap<>();
            // build order - invoice tree
            List<InvoiceListOrderVO> orders = new ArrayList<>();
            Map<String, Boolean> permissionMap = getPermissionMap(workgroupId, userId, projectId);
            invoiceListDTOS.forEach(dto -> {
                AggregatedOrderVersionDTO aggregatedOrderVersionDTO = aggregatedOrderService.findSimpleAggregatedOrderDTO(
                        dto.getOrderId(), workgroupId, userId);

                InvoiceListOrderVO invoiceListOrderVO = orderMap.get(dto.getOrderReference());
                if (invoiceListOrderVO == null) {
                    invoiceListOrderVO = new InvoiceListOrderVO();

                    // Order $!order.reference
                    invoiceListOrderVO.setId(dto.getOrderId());

                    // oder name title (reference)
                    invoiceListOrderVO.setOrderName(dto.getOrderTitle() == null ?
                            dto.getOrderReference() :
                            dto.getOrderTitle() + " (" + dto.getOrderReference() + ")"
                    );
                    // ORDER URL if it's change order, then show aggregated order
                    OrderVersionDTO originalOrder = aggregatedOrderVersionDTO.getOriginalOrder();
                    Boolean isDualCurrency = originalOrder.getRate() != null && originalOrder.getRate().compareTo(BigDecimal.ZERO) > 0 && originalOrder.getExCurrencyId() != null;
                    if (orderService.hasAcceptedChangeOrder(aggregatedOrderVersionDTO.getOriginalOrder().getOrderId())) {
                        invoiceListOrderVO.setIsAggregateOrder(true);
                        invoiceListOrderVO.setViewOrderExternalUrl(NooshOneUrlUtil.composeOrderWithChangesLinkToEnterprise(projectId, dto.getOrderId()));
                    } else {
                        invoiceListOrderVO.setIsAggregateOrder(false);
                        invoiceListOrderVO.setViewOrderExternalUrl(NooshOneUrlUtil.composeViewOrderLinkToEnterprise(projectId, dto.getOrderId()));
                    }

                    if (dto.getOrderObjectStateId() != null) {
                        invoiceListOrderVO.setStatus(i18NUtils.getObjectStateMessage(dto.getOrderObjectStateId()));
                        ObjectState objectState = objectStateRepository.findById(dto.getOrderObjectStateId()).orElse(null);
                        invoiceListOrderVO.setStatusStrId(objectState != null ? objectState.getDescriptionStrId().toString() : null);
                    }

                    invoiceListOrderVO.setAcceptDate(dto.getOrderAcceptDate());
                    invoiceListOrderVO.setIsDualCurrency(isDualCurrency);
                    invoiceListOrderVO.setOrderAmount(aggregatedOrderVersionDTO.getAggregatedOrder().getGrandTotal());
                    if (isDualCurrency) {
                        invoiceListOrderVO.setExOrderAmount(aggregatedOrderVersionDTO.getAggregatedOrder().getExGrandTotal());
                        invoiceListOrderVO.setExOrderAmountCurrencyId(originalOrder.getExCurrencyId());
                    }

                    // buyer, supplier workgroup name
                    invoiceListOrderVO.setIsBuyOrder(dto.getOrderBuyerWorkgroupId() != null &&
                            dto.getOrderBuyerWorkgroupId().longValue() == workgroupId);
                    invoiceListOrderVO.setBuyerWorkgroupName(dto.getOrderBuyerWorkgroupName());
                    invoiceListOrderVO.setSupplierWorkgroupName(dto.getOrderSupplierWorkgroupName());

                    // check whether current order is invoice adjustment order
                    invoiceListOrderVO.setIsInvoiceAdjustmentOrder(dto.getOrderClassificationId() == OrderClassificationID.INVOICE_ADJUSTMENT);

                    // invoices
                    List<InvoiceListInvoiceVO> invoices = new ArrayList<>();
                    invoices.add(getInvocie(projectId, workgroupId, userId, canCreateInvoice, dto,
                            aggregatedOrderVersionDTO.getOriginalOrder(), permissionMap));

                    invoiceListOrderVO.setInvoices(invoices);
                    orders.add(invoiceListOrderVO);

                    orderMap.put(dto.getOrderReference(), invoiceListOrderVO);
                } else {
                    // other invoices
                    invoiceListOrderVO.getInvoices().add(getInvocie(projectId, workgroupId, userId,
                            canCreateInvoice, dto, aggregatedOrderVersionDTO.getOriginalOrder(), permissionMap));
                }

            });


            // Calculate aggregatedInvoiceAmount = accepted invoice amount, remainingBalance = orderAmount - invoiceAmount
            orders.forEach(orderVO -> {
                BigDecimal aggregatedInvoiceAmount = new BigDecimal("0");
                BigDecimal exAggregatedInvoiceAmount = new BigDecimal("0");
                for (InvoiceListInvoiceVO invoiceListInvoiceVO : orderVO.getInvoices()) {
                    if (invoiceListInvoiceVO.isAccepted()) {
                        aggregatedInvoiceAmount = aggregatedInvoiceAmount.add(invoiceListInvoiceVO.getInvoiceAmount());
                        if (orderVO.getIsDualCurrency() && invoiceListInvoiceVO.getExInvoiceAmount() != null) {
                            exAggregatedInvoiceAmount = exAggregatedInvoiceAmount.add(invoiceListInvoiceVO.getExInvoiceAmount());
                        }
                    }
                }
                orderVO.setInvoiceAmount(aggregatedInvoiceAmount);
                orderVO.setRemainingBalance(orderVO.getOrderAmount().subtract(aggregatedInvoiceAmount));
                if (orderVO.getIsDualCurrency()) {
                    orderVO.setExInvoiceAmount(exAggregatedInvoiceAmount);
                    orderVO.setExInvoiceAmountCurrencyId(orderVO.getExOrderAmountCurrencyId());
                    orderVO.setExRemainingBalance(orderVO.getExOrderAmount().subtract(exAggregatedInvoiceAmount));
                    orderVO.setExRemainingBalanceCurrencyId(orderVO.getExOrderAmountCurrencyId());
                }
            });

            vo.setOrders(orders);
        } else {
            vo.setOrders(new ArrayList<>());
        }


        return vo;
    }

    /**
     * get invoice info for invoice list
     *
     * @param projectId        projectId
     * @param workgroupId      workgroupId
     * @param userId           userId
     * @param canCreateInvoice canCreateInvoice
     * @param dto              dto
     * @param originalOrder    originalOrder
     * @return invoice info
     */
    private InvoiceListInvoiceVO getInvocie(Long projectId, Long workgroupId, Long userId,
                                            boolean canCreateInvoice, InvoiceListDTO dto,
                                            OrderVersionDTO originalOrder, Map<String, Boolean> permissionMap) {
        InvoiceListInvoiceVO invoiceListInvoiceVO = new InvoiceListInvoiceVO();
        invoiceListInvoiceVO.setId(dto.getId());
        invoiceListInvoiceVO.setStateId(dto.getStateId());
        invoiceListInvoiceVO.setInvoiceNumber(dto.getOwnerReference());
        invoiceListInvoiceVO.setIsFinal(dto.getIsFinal());
        invoiceListInvoiceVO.setViewInvoiceExternalLink(NooshOneUrlUtil.composeViewInvoiceLinkToEnterprise(projectId, dto.getId()));

        if (dto.getStateId() != null) {
            String objectStateMessage = i18NUtils.getObjectStateMessage(dto.getStateId());
            if (dto.isPending() && dto.getIsApproved()) {
                objectStateMessage += " " + "(Approved)";
            }
            invoiceListInvoiceVO.setStatus(objectStateMessage);
            invoiceListInvoiceVO.setIsApproved(dto.getIsApproved());
            invoiceListInvoiceVO.setIsPending(dto.isPending());
            if (dto.getStateId() != null) {
                ObjectState objectState = objectStateRepository.findById(dto.getStateId()).orElse(null);
                invoiceListInvoiceVO.setStatusStrId(objectState != null ? objectState.getDescriptionStrId().toString() : null);
            }


            invoiceListInvoiceVO.setIsAccepted(dto.isAccepted());

        }

        if (dto.getDueDate() != null) {
            invoiceListInvoiceVO.setDueDate(dto.getDueDate());
        }

        if (dto.getAcceptedDate() != null) {
            invoiceListInvoiceVO.setAcceptedDate(dto.getAcceptedDate());
        }

        invoiceListInvoiceVO.setNonBillable(dto.getIsNonBillable());
        List<InvoiceItem> items = invoiceItemRepository.findByInvoiceId(dto.getId());
        invoiceListInvoiceVO.setInvoiceAmount(getGrandTotal(dto.getIsFinal(), dto.getTax(), dto.getShipping(),
                dto.getMiscCost(), dto.getDiscountOrSurcharge(), items));
        Boolean isDualCurrency = originalOrder.getRate() != null && originalOrder.getRate().compareTo(BigDecimal.ZERO) > 0 && originalOrder.getExCurrencyId() != null;
        if (isDualCurrency) {
            invoiceListInvoiceVO.setExInvoiceAmount(getExGrandTotal(dto.getIsFinal(), dto.getExTax(), dto.getExShipping(),
                    dto.getExMiscCost(), dto.getExDiscountOrSurcharge(), items));
            invoiceListInvoiceVO.setExInvoiceAmountCurrencyId(originalOrder.getExCurrencyId());
        }

        // print
        invoiceListInvoiceVO.setPrintExternalLink(NooshOneUrlUtil.composePrintInvoiceLinkToEnterprise(projectId, dto.getId()));

        if (isUserSupplier(dto.getSupplierWorkgroupId(), workgroupId)) {

            // copy
            if (canCreateInvoice && createInvoicePermission.check(originalOrder, workgroupId, userId, projectId, permissionMap)) {
                invoiceListInvoiceVO.setCopyExternalLink(NooshOneUrlUtil.composeCopyInvoiceLinkToEnterprise(projectId, dto.getId()));
            }

            // edit
            if (editInvoicePermission.check(dto, workgroupId, userId, projectId, permissionMap)) {
                invoiceListInvoiceVO.setEditExternalLink(NooshOneUrlUtil.composeEditInvoiceLinkToEnterprise(projectId, dto.getId()));
            }

            // delete
            if (deleteInvoicePermission.check(dto, workgroupId, userId, projectId, permissionMap)) {
                invoiceListInvoiceVO.setDeleteExternalLink(NooshOneUrlUtil.composeDeleteInvoiceLinkToEnterprise(projectId, dto.getId()));
            }
        }

        return invoiceListInvoiceVO;
    }

    public boolean requiresApproval(InvoiceDTO invoice) {
        if (!invoice.isPending() || invoice.getApproved())
            return false;
        Map<String, String> prefs = preferenceService.findGroupPrefs(invoice.getBuyerWorkgroupId());
        if (preferenceService.check(PreferenceID.PC_INVOICE_REQUIRES_APPROVAL, prefs)) {
            return true;
        }
        return false;
    }

    /**
     * check if the user is supplier
     *
     * @param supplierWorkgroupId supplierWorkgroupId
     * @param ownerGroupId        ownerGroupId
     * @return bool
     */
    private boolean isUserSupplier(long supplierWorkgroupId, long ownerGroupId) {
        return (supplierWorkgroupId == ownerGroupId);
    }


    /**
     * get grand total for invoice
     *
     * @param isFinal             is Final invoice
     * @param tax                 tax
     * @param shipping            shipping
     * @param miscTotal           miscTotal
     * @param discountOrSurcharge discountOrSurcharge
     * @param items invoice items
     * @return grand total
     */
    public BigDecimal getGrandTotal(Boolean isFinal, BigDecimal tax, BigDecimal shipping,
                                    BigDecimal miscTotal, BigDecimal discountOrSurcharge, List<InvoiceItem> items) {

        BigDecimal total = new BigDecimal("0");

        if (tax != null) {
            total = total.add(tax);
        }

        if (shipping != null) {
            total = total.add(shipping);
        }

        if (miscTotal != null) {
            total = total.add(miscTotal);
        }

        BigDecimal invoiceItemTotal = getInvoiceItemTotal(isFinal, items);
        if (invoiceItemTotal != null) {
            total = total.add(invoiceItemTotal);
        }

        if (discountOrSurcharge != null) {
            total = total.add(discountOrSurcharge);
        }
        return total;
    }

    public BigDecimal getExGrandTotal(Boolean isFinal, BigDecimal exTax, BigDecimal exShipping,
                                      BigDecimal exMiscTotal, BigDecimal exDiscountOrSurcharge, List<InvoiceItem> items) {

        BigDecimal total = new BigDecimal("0");

        if (exTax != null) {
            total = total.add(exTax);
        }

        if (exShipping != null) {
            total = total.add(exShipping);
        }

        if (exMiscTotal != null) {
            total = total.add(exMiscTotal);
        }

        BigDecimal exInvoiceItemTotal = getExInvoiceItemTotal(isFinal, items);
        if (exInvoiceItemTotal != null) {
            total = total.add(exInvoiceItemTotal);
        }

        if (exDiscountOrSurcharge != null) {
            total = total.add(exDiscountOrSurcharge);
        }
        return total;
    }

    /**
     * get Invoice Item Total
     *
     * @param isFinal   is Final invoice
     * @param items invoice items
     * @return Invoice Item Total
     */
    public BigDecimal getInvoiceItemTotal(Boolean isFinal, List<InvoiceItem> items) {
        BigDecimal total = new BigDecimal("0");
        if (items != null && items.size() > 0) {
            for (int i = 0; i < items.size(); i++) {
                InvoiceItem item = items.get(i);
                total = total.add(item.getAmount());
                if (isFinal) {
                    if (item.getDiscountOrSurcharge() != null) {
                        total = total.add(item.getDiscountOrSurcharge());
                    }
                }
            }
        }
        return total;
    }

    public BigDecimal getExInvoiceItemTotal(Boolean isFinal, List<InvoiceItem> items) {
        BigDecimal exTotal = new BigDecimal("0");
        if (items != null && items.size() > 0) {
            for (int i = 0; i < items.size(); i++) {
                InvoiceItem item = items.get(i);
                exTotal = exTotal.add(item.getExAmount());
                if (isFinal) {
                    if (item.getExDiscountOrSurcharge() != null) {
                        exTotal = exTotal.add(item.getExDiscountOrSurcharge());
                    }
                }
            }
        }
        return exTotal;
    }

    @Transactional(readOnly = true)
    public InvoiceDTO findFinalInvoiceTemplate(Long parentOrderId) {
        return invoiceMapper.toDTO(invoiceRepository.findFinalInvoiceTemplate(parentOrderId));
    }

    /**
     * Invoice Deskoid
     * @param projectId
     * @param filter Show All, Hide Buy Side (do not display any buy side order or invoices),
     *               Hide Sell Side (do not display any sell side order or invoices).
     *               By default – Show All
     * @return
     */
    public InvoiceDeskoidVO getInvoiceDeskoid(Long projectId, String filter, Long workgroupId, Long userId) {
        int tabId = -1;
        if (ORDER_BUY.equals(filter)) {
            // Hide Buy Side
            tabId = 1;
        } else if (ORDER_SELL.equals(filter)) {
            // Hide Sell Side
            tabId = 0;
        }

        Boolean canViewInvoice = permissionService.checkAll(PermissionID.VIEW_INVOICE, workgroupId, userId, projectId);
        ProjectDTO projectDTO = projectService.findProjectById(projectId);
        Workgroup workgroup = workgroupRepository.findById(workgroupId).get();
        long workgroupTypeId = workgroup.getWorkGroupTypeId();

        InvoiceDeskoidVO vo = new InvoiceDeskoidVO();
        vo.setCanViewInvoice(canViewInvoice);

        Long soldOrders = orderMyBatisMapper.findSoldOrdersCount(projectId, workgroupId, projectDTO.isClientNotOnNoosh());
        boolean hashSoldOrders = (soldOrders > 0);
        boolean canCreateInvoice = createInvoicePermission.check(projectDTO, workgroupId, userId, projectId);

        if (hashSoldOrders && canCreateInvoice) {
            vo.setCreateInvoiceExternalLink(NooshOneUrlUtil.composeGotoCreateInvoiceLinkToEnterprise(projectId));
            if (projectDTO.isOutsourcerProject() && tabId == 0) {
                vo.setCreateInvoiceExternalLink(null);
            }
        }

        List<InvoiceListDTO> invoiceListDTOS = null;
        if (canViewInvoice) {
            if (tabId == 0) {
                // buy
                invoiceListDTOS = invoiceMyBatisMapper.findInvoiceList(projectId, workgroupId,
                        true, false, projectDTO.isClientNotOnNoosh());

            } else if (tabId == 1) {
                // sell
                invoiceListDTOS = invoiceMyBatisMapper.findInvoiceList(projectId, workgroupId,
                        false, true, projectDTO.isClientNotOnNoosh());

            } else {
                // all
                invoiceListDTOS = invoiceMyBatisMapper.findInvoiceList(projectId, workgroupId,
                        true, true, projectDTO.isClientNotOnNoosh());
            }
            vo.setTotal((long) invoiceListDTOS.size());
        }

        if (invoiceListDTOS != null) {
            Map<String, InvoiceDeskoidOrderVO> orderMap = new HashMap<>();
            // build order - invoice tree
            List<InvoiceDeskoidOrderVO> orders = new ArrayList<>();
            invoiceListDTOS.forEach(originalOrderWidgetDTO -> {
                AggregatedOrderVersionDTO aggregatedOrderVersionDTO = aggregatedOrderService.findSimpleAggregatedOrderDTO(
                        originalOrderWidgetDTO.getOrderId(), workgroupId, userId);

                InvoiceDeskoidOrderVO invoiceDeskoidOrderVO = orderMap.get(originalOrderWidgetDTO.getOrderReference());
                if (invoiceDeskoidOrderVO == null) {
                    invoiceDeskoidOrderVO = new InvoiceDeskoidOrderVO();

                    invoiceDeskoidOrderVO.setOrderId(originalOrderWidgetDTO.getOrderId());
                    invoiceDeskoidOrderVO.setOrderName(getOrderName(originalOrderWidgetDTO));
                    invoiceDeskoidOrderVO.setOrderType(getOrderType(projectDTO, originalOrderWidgetDTO));
                    invoiceDeskoidOrderVO.setStatus(i18NUtils.getObjectStateMessage(originalOrderWidgetDTO.getOrderObjectStateId()));
                    invoiceDeskoidOrderVO.setStatusId(originalOrderWidgetDTO.getOrderObjectStateId());
                    // Get Client or Supplier workgroup name
                    getClientOrSupplierWorkgroupName(originalOrderWidgetDTO, workgroupTypeId, invoiceDeskoidOrderVO);

                    boolean isDualCurrency = originalOrderWidgetDTO.getRate() != null && originalOrderWidgetDTO.getRate().compareTo(BigDecimal.ZERO) > 0 && originalOrderWidgetDTO.getExCurrencyId() != null;
                    invoiceDeskoidOrderVO.setIsDualCurrency(isDualCurrency);
                    double total = 0;
                    double exTotal = 0;
                    if (originalOrderWidgetDTO.getOrderObjectStateId() == ObjectStateID.ORDER_ACCEPTED
                            || originalOrderWidgetDTO.getOrderObjectStateId() ==  ObjectStateID.ORDER_COMPLETED ) {
                        OrderVersionDTO aggregatedOrder = aggregatedOrderVersionDTO.getAggregatedOrder();
                        total = aggregatedOrder.getGrandTotal().doubleValue();
                        if (isDualCurrency) {
                            exTotal = aggregatedOrder.getExGrandTotal().doubleValue();
                        }
                    }

                    // Total $ amount for order
                    invoiceDeskoidOrderVO.setValue(total);
                    if (isDualCurrency) {
                        invoiceDeskoidOrderVO.setExValue(exTotal);
                        invoiceDeskoidOrderVO.setExValueCurrencyId(originalOrderWidgetDTO.getExCurrencyId());
                    }

                    if (orderService.hasAcceptedChangeOrder(aggregatedOrderVersionDTO.getOriginalOrder().getOrderId())) {
                        invoiceDeskoidOrderVO.setOrderExternalLink(NooshOneUrlUtil.composeOrderWithChangesLinkToEnterprise(projectId, originalOrderWidgetDTO.getOrderId()));
                    } else {
                        invoiceDeskoidOrderVO.setOrderExternalLink(NooshOneUrlUtil.composeViewOrderLinkToEnterprise(projectId, originalOrderWidgetDTO.getOrderId()));
                    }
                    
                    // invoices
                    List<InvoiceDeskoidInvoiceVO> invoices = new ArrayList<>();
                    invoices.add(getOrderInvoice(projectDTO, originalOrderWidgetDTO));

                    invoiceDeskoidOrderVO.setInvoices(invoices);
                    orders.add(invoiceDeskoidOrderVO);

                    orderMap.put(originalOrderWidgetDTO.getOrderReference(), invoiceDeskoidOrderVO);
                } else {
                    // other invoices
                    invoiceDeskoidOrderVO.getInvoices().add(getOrderInvoice(projectDTO, originalOrderWidgetDTO));
                }

            });


            // Calculate aggregatedInvoiceAmount = accepted invoice amount, remainingBalance = orderAmount - invoiceAmount
            orders.forEach(orderVO -> {
                BigDecimal aggregatedInvoiceAmount = new BigDecimal("0");
                BigDecimal exAggregatedInvoiceAmount = new BigDecimal("0");
                for (InvoiceDeskoidInvoiceVO invoiceListInvoiceVO : orderVO.getInvoices()) {
                    if (invoiceListInvoiceVO.getIsAccepted()) {
                        aggregatedInvoiceAmount = aggregatedInvoiceAmount.add(invoiceListInvoiceVO.getValue());
                        if (invoiceListInvoiceVO.getIsDualCurrency() && invoiceListInvoiceVO.getExValue() != null) {
                            exAggregatedInvoiceAmount = exAggregatedInvoiceAmount.add(invoiceListInvoiceVO.getExValue());
                        }
                    }
                }
                orderVO.setTotalInvoicedAmount(aggregatedInvoiceAmount);
                if (orderVO.getIsDualCurrency()) {
                    orderVO.setExTotalInvoicedAmount(exAggregatedInvoiceAmount);
                    orderVO.setExTotalInvoicedAmountCurrencyId(orderVO.getExValueCurrencyId());
                }
            });

            vo.setInvoiceOrders(orders);
        } else {
            vo.setInvoiceOrders(new ArrayList<>());
        }

        return vo;
    }

    // Order Name
    private String getOrderName(InvoiceListDTO originalOrderWidgetDTO) {
        if(originalOrderWidgetDTO.getOrderTitle() != null) {
            return originalOrderWidgetDTO.getOrderTitle()+"("+originalOrderWidgetDTO.getOrderReference()+")";
        } else {
            return String.valueOf(originalOrderWidgetDTO.getOrderReference());
        }
    }

    // Order type
    private String getOrderType(ProjectDTO projectDTO, InvoiceListDTO orderWidgetDTO) {
        if(isOutsourcingSellOrder(projectDTO, orderWidgetDTO) || isBrokerSellOrder(projectDTO, orderWidgetDTO)) {
            return ORDER_SELL;
        } else {
            return ORDER_BUY;
        }
    }

    private boolean isOutsourcingSellOrder(ProjectDTO projectDTO, InvoiceListDTO orderWidgetDTO) {
        if(projectDTO.isClientNotOnNoosh()
                && orderWidgetDTO.getOrderBuyerWorkgroupId().longValue() == orderWidgetDTO.getSupplierWorkgroupId().longValue()
                && orderWidgetDTO.getBuClientId() != null && orderWidgetDTO.getBuClientId() > 0) {
            return true;
        }
        if((orderWidgetDTO.getOrderBuyerWorkgroupId().longValue() == projectDTO.getOwnerWorkgroupId().longValue())
                && projectDTO.isClientProject()
                && getOwnerParent(projectDTO).isOutsourcerProject()) {
            return true;
        }
        if((orderWidgetDTO.getSupplierWorkgroupId().longValue() == projectDTO.getOwnerWorkgroupId().longValue())
                && projectDTO.isOutsourcerProject()) {
            return true;
        }
        return false;
    }

    private boolean isBrokerSellOrder(ProjectDTO projectDTO, InvoiceListDTO orderWidgetDTO) {
        if(projectDTO.isClientNotOnNoosh()
                && orderWidgetDTO.getOrderBuyerWorkgroupId().longValue() == orderWidgetDTO.getSupplierWorkgroupId()
                && orderWidgetDTO.getBuClientId() > 0) {
            return true;
        }
        if((orderWidgetDTO.getOrderBuyerWorkgroupId().longValue() == projectDTO.getOwnerWorkgroupId())
                && projectDTO.isClientProject()
                && getOwnerParent(projectDTO).isBrokerOutsourcerProject()) {
            return true;
        }
        if((orderWidgetDTO.getSupplierWorkgroupId().longValue() == projectDTO.getOwnerWorkgroupId())
                && projectDTO.isBrokerOutsourcerProject()) {
            return true;
        }
        return false;
    }

    private ProjectDTO getOwnerParent(ProjectDTO projectDTO) {
        if (!projectDTO.isMaster()) {
            Project project = projectRepository.findById(projectDTO.getMasterProjectId()).orElse(null);
            return new ProjectDTO(project);
        }
        return projectDTO;
    }

    // Cient or Supplier workgroup name
    private void getClientOrSupplierWorkgroupName(InvoiceListDTO orderWidgetDTO,
                                                  Long workgroupTypeId,
                                                  InvoiceDeskoidOrderVO invoiceDeskoidOrderVO) {
        /* Buyer: Only when it's a sell order
         * workgroup
         * User
         */
        if((ORDER_SELL.equals(invoiceDeskoidOrderVO.getOrderType())
                && workgroupTypeId == WorkgroupTypeID.BROKER)
                || workgroupTypeId == WorkgroupTypeID.SUPPLIER) {
            Workgroup workgroup = workgroupRepository.findById(orderWidgetDTO.getOrderBuyerWorkgroupId()).orElse(null);
            invoiceDeskoidOrderVO.setClient(workgroup.getName());
        } else {
            /* Supplier:
             * Workgroup
             * Contact name
             */
            Workgroup workgroup = workgroupRepository.findById(orderWidgetDTO.getSupplierWorkgroupId()).orElse(null);
            invoiceDeskoidOrderVO.setSupplier(workgroup.getName());

            invoiceDeskoidOrderVO.setSupplierFlag(supplierWorkgroupService.getSupplierFlagVO(false,
                    orderWidgetDTO.getParentOrderId(),
                    ObjectClassID.ORDER,
                    orderWidgetDTO.getOrderBuyerWorkgroupId(),
                    orderWidgetDTO.getSupplierWorkgroupId()));
            invoiceDeskoidOrderVO.setSupplierScore(ratingService.findSupplierScore(orderWidgetDTO.getOrderBuyerWorkgroupId(),
                    orderWidgetDTO.getSupplierWorkgroupId()));
        }
    }

    public boolean showAggregated(ProjectOrderWidgetDTO originalOrderWidgetDTO) {
        List<ProjectOrderWidgetDTO> changeOrders = originalOrderWidgetDTO.getChangeOrderList();
        if (changeOrders == null || changeOrders.size()==0) return false;

        // check if accepted
        if (isAccepted(originalOrderWidgetDTO.getStateId())) {
            for (ProjectOrderWidgetDTO changeOrder : changeOrders) {
                if (isAccepted(changeOrder.getStateId()))
                    return true;
            }
        }

        if (originalOrderWidgetDTO.getStateId()==ObjectStateID.ORDER_CANCELLED) {
            for (ProjectOrderWidgetDTO changeOrder : changeOrders) {
                if (changeOrder.getStateId() == ObjectStateID.ORDER_CANCELLED)
                    return true;
            }
        }
        return false;
    }

    public boolean isAccepted(Long stateId) {
        if (stateId == null) return false;
        return (stateId == ObjectStateID.ORDER_ACCEPTED
                || stateId == ObjectStateID.ORDER_PARTIALLY_SHIPPED
                || stateId == ObjectStateID.ORDER_SHIPPED
                || stateId == ObjectStateID.ORDER_DELIVERED
                || stateId == ObjectStateID.ORDER_ACCEPTED_NOT_YET_SHIPPED
                || stateId == ObjectStateID.ORDER_COMPLETED
                || stateId == ObjectStateID.ORDER_FINALIZED);
    }

    private InvoiceDeskoidInvoiceVO getOrderInvoice(ProjectDTO projectDTO, InvoiceListDTO invoice) {

        InvoiceDeskoidInvoiceVO invoiceVO = new InvoiceDeskoidInvoiceVO();
        invoiceVO.setInvoiceId(invoice.getId());
        invoiceVO.setIsFinal(invoice.getIsFinal());
        invoiceVO.setStatus(i18NUtils.getObjectStateMessage(invoice.getStateId()));
        invoiceVO.setStatusId(invoice.getStateId());
        // Any invoice in pending status, display in grey
        if (invoice.getStateId() != null
                && invoice.getStateId().longValue() == ObjectStateID.INVOICE_PENDING_ACCEPTANCE) {
            invoiceVO.setIsGreyValue(true);
        }
        invoiceVO.setDueDate(invoice.getDueDate());
        invoiceVO.setLastUpdatedDate(invoice.getModDate());

        List<InvoiceItem> items = invoiceItemRepository.findByInvoiceId(invoice.getId());
        invoiceVO.setValue(getGrandTotal(invoice.getIsFinal(), invoice.getTax(), invoice.getShipping(),
                invoice.getMiscCost(), invoice.getDiscountOrSurcharge(), items));
        Boolean isDualCurrency = invoice.getRate() != null && invoice.getRate().compareTo(BigDecimal.ZERO) > 0 && invoice.getExCurrencyId() != null;
        invoiceVO.setIsDualCurrency(isDualCurrency);
        if (isDualCurrency) {
            invoiceVO.setExValue(getExGrandTotal(invoice.getIsFinal(), invoice.getExTax(), invoice.getExShipping(),
                    invoice.getExMiscCost(), invoice.getExDiscountOrSurcharge(), items));
            invoiceVO.setExValueCurrencyId(invoice.getExCurrencyId());
        }
        invoiceVO.setInvoiceExternalLink(NooshOneUrlUtil.composeViewInvoiceLinkToEnterprise(projectDTO.getId(), invoice.getId()));
        invoiceVO.setIsAccepted(invoice.isAccepted());

        return invoiceVO;
    }

    private Map<String, Boolean> getPermissionMap(Long workgroupId, Long userId, Long projectId) {
        List<Long> permissionIds = new ArrayList<>();
        permissionIds.add(PermissionID.CREATE_INVOICE);
        permissionIds.add(PermissionID.EDIT_INVOICE);
        permissionIds.add(PermissionID.DELETE_INVOICE);
        permissionIds.add(PermissionID.VIEW_INVOICE);
        permissionIds.add(PermissionID.VIEW_ORDER);

        List<Long> projectIds = new ArrayList<>();
        projectIds.add(projectId);

        return permissionService.getPermissionMap(permissionIds, workgroupId, userId, projectIds);
    }


}
