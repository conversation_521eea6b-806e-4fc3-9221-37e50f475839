package com.noosh.app.service.messages;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.noosh.app.commons.dto.messages.MessageDTO;
import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.commons.vo.messages.MessageVO;
import com.noosh.app.mapper.messages.MessageMapper;
import com.noosh.app.repository.jpa.security.ObjectStateRepository;
import com.noosh.app.repository.mybatis.messages.MessageMyBatisMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * author: Yang
 * Date: 11/27/2022
 */
@Service
public class BroadcastMessageService {
    @Autowired
    private ObjectStateRepository objectStateRepository;
    @Autowired
    private MessageMyBatisMapper messageMyBatisMapper;
    @Autowired
    private MessageMapper messageMapper;

    private static final Map<Long,Long> objectStateMap = new HashMap();

    @PostConstruct
    public void initObjectStateMap() {
        List<ObjectState> objectStates = objectStateRepository.findAll();
        for (ObjectState objectState: objectStates) {
            objectStateMap.put(objectState.getId(), objectState.getDescriptionStrId());
        }
    }

    public List<MessageVO> getBroadcastMessageList(long workgroupId, PageVO page) {
        Page pageInfo = PageHelper.startPage(page.getNum(), page.getSize());
        pageInfo.setUnsafeOrderBy(page.getOrderBy());
        List<MessageDTO> messageDTOS = messageMyBatisMapper.findBroadcastMessagesByWorkgroupId(workgroupId);
        page.setTotal(pageInfo.getTotal());
        return messageDTOS.stream().map(messageDTO -> messageMapper.toMessageVO(messageDTO, objectStateMap)).collect(Collectors.toList());
    }
}
