package com.noosh.app.service.rfe;

import com.noosh.app.commons.dto.rfe.RfeSupplierDTO;
import com.noosh.app.commons.entity.rfe.RfeSupplier;
import com.noosh.app.mapper.rfe.RfeSupplierMapper;
import com.noosh.app.repository.rfe.RfeSupplierRepository;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.List;
import java.util.stream.Collectors;

/**
 * User: <PERSON>
 * Date: 5/6/2018
 */
@Service
public class RfeSupplierService {

    @Inject
    private RfeSupplierRepository rfeSupplierRepository;
    @Inject
    private RfeSupplierMapper rfeSupplierMapper;

    List<RfeSupplierDTO> findRfeSuppliers(long rfeId){
        List<RfeSupplier> rfeSuppliers = rfeSupplierRepository.findByRfeId(rfeId);
        return rfeSuppliers.stream().map(rfeSupplierMapper::toRfeSupplierDTO).collect(Collectors.toList());
    }
}
