package com.noosh.app.service.rfe;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.account.AccountUserDTO;
import com.noosh.app.commons.dto.apijob.builder.ScheduledApiDtoBuilder;
import com.noosh.app.commons.dto.estimate.EstimateDTO;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import com.noosh.app.commons.dto.rfe.RfeDTO;
import com.noosh.app.commons.dto.rfe.RfeSupplierDTO;
import com.noosh.app.commons.entity.account.AccountUser;
import com.noosh.app.commons.entity.account.SupplierWorkgroup;
import com.noosh.app.commons.entity.bid.Bid;
import com.noosh.app.commons.entity.estimate.Estimate;
import com.noosh.app.commons.entity.estimate.EstimateItem;
import com.noosh.app.commons.entity.estimate.EstimateItemPrice;
import com.noosh.app.commons.entity.rfe.Rfe;
import com.noosh.app.commons.entity.rfe.RfeItem;
import com.noosh.app.commons.entity.rfe.RfeSupplier;
import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.commons.entity.security.Workgroup;
import com.noosh.app.commons.entity.task.Task;
import com.noosh.app.commons.entity.tracking.Tracking;
import com.noosh.app.commons.vo.ServerResponse;
import com.noosh.app.commons.vo.account.SupplierFlagRequestVO;
import com.noosh.app.commons.vo.account.SupplierFlagVO;
import com.noosh.app.commons.vo.property.PropertyAttributeVO;
import com.noosh.app.commons.vo.rfe.RfeBidVO;
import com.noosh.app.commons.vo.rfe.RfeInlineEditingVO;
import com.noosh.app.commons.vo.rfe.RfeSupplierVO;
import com.noosh.app.commons.vo.rfe.RfeVO;
import com.noosh.app.exception.NoPermissionException;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.exception.UnexpectedException;
import com.noosh.app.feign.OrderResourceFeignClient;
import com.noosh.app.mapper.rfe.RfeMapper;
import com.noosh.app.mapper.rfe.RfeSupplierMapper;
import com.noosh.app.repository.jpa.account.AccountUserRepository;
import com.noosh.app.repository.jpa.account.SupplierWorkgroupRepository;
import com.noosh.app.repository.jpa.bid.BidRepository;
import com.noosh.app.repository.jpa.estimate.EstimateItemPriceRepository;
import com.noosh.app.repository.jpa.estimate.EstimateItemRepository;
import com.noosh.app.repository.jpa.estimate.EstimateRepository;
import com.noosh.app.repository.jpa.rfe.RfeItemRepository;
import com.noosh.app.repository.jpa.rfe.RfeRepository;
import com.noosh.app.repository.jpa.rfe.RfeSupplierRepository;
import com.noosh.app.repository.jpa.security.ObjectStateRepository;
import com.noosh.app.repository.jpa.security.WorkgroupRepository;
import com.noosh.app.repository.jpa.task.TaskRepository;
import com.noosh.app.repository.jpa.tracking.TrackingRepository;
import com.noosh.app.repository.mybatis.account.AccountUserMyBatisMapper;
import com.noosh.app.repository.mybatis.estimate.EstimateMyBatisMapper;
import com.noosh.app.repository.mybatis.rfe.RfeMyBatisMapper;
import com.noosh.app.repository.mybatis.rfe.RfeSupplierMyBatisMapper;
import com.noosh.app.service.account.SupplierFlagService;
import com.noosh.app.service.apijob.RabbitMQProducerService;
import com.noosh.app.service.apijob.ScheduledApiService;
import com.noosh.app.service.apijob.WebClientService;
import com.noosh.app.service.permission.rfe.DismissSupplierPermission;
import com.noosh.app.service.permission.rfe.ReviseRfePermission;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.property.PropertyService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.util.DateUtil;
import com.noosh.app.service.util.NooshOneUrlUtil;
import com.noosh.app.service.util.URLUtil;
import jakarta.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @auther mario
 * @date 11/18/2019
 */
@Service
public class RfeService {

    private final Logger log = LoggerFactory.getLogger(RfeService.class);
    public final static long VALIDATE_ERROR_NONE = 0;

    @Inject
    private PermissionService permissionService;
    @Inject
    private PreferenceService preferenceService;
    @Inject
    private SupplierFlagService supplierFlagService;
    @Inject
    private RfeRepository rfeRepository;
    @Inject
    private RfeItemRepository rfeItemRepository;
    @Inject
    private BidRepository bidRepository;
    @Inject
    private RfeSupplierRepository rfeSupplierRepository;
    @Inject
    private AccountUserRepository accountUserRepository;
    @Inject
    private EstimateRepository estimateRepository;
    @Inject
    private WorkgroupRepository workgroupRepository;
    @Inject
    private TrackingRepository trackingRepository;
    @Inject
    private RfeMyBatisMapper rfeMyBatisMapper;
    @Inject
    private RfeSupplierMyBatisMapper rfeSupplierMyBatisMapper;
    @Inject
    private SupplierWorkgroupRepository supplierWorkgroupRepository;
    @Inject
    private AccountUserMyBatisMapper accountUserMyBatisMapper;
    @Inject
    private EstimateMyBatisMapper estimateMyBatisMapper;
    @Inject
    private TaskRepository taskRepository;
    @Inject
    private ObjectStateRepository objectStateRepository;
    @Inject
    private RfeMapper rfeMapper;
    @Inject
    private ProjectService projectService;
    @Inject
    private ReviseRfePermission reviseRfePermission;
    @Inject
    private DismissSupplierPermission dismissSupplierPermission;
    @Inject
    private PropertyService propertyService;
    @Inject
    private ScheduledApiService scheduledApiService;
    @Inject
    private RabbitMQProducerService rabbitMQProducerService;
    @Inject
    private WebClientService webClientService;
    @Inject
    private RfeSupplierMapper rfeSupplierMapper;
    @Inject
    private EstimateItemRepository estimateItemRepository;
    @Inject
    private EstimateItemPriceRepository estimateItemPriceRepository;

    @Autowired
    private Environment environment;
    @Value("${enterprise.internalDomain}")
    private String nooshApiEnterpriseHost;
    @Value("${enterprise.port}")
    private String nooshApiEnterprisePort;
    @Inject
    private OrderResourceFeignClient orderResourceFeignClient;

    private final Map<String, String> errorMap = new HashMap<>();

    @PostConstruct
    public void init() {
        errorMap.put(String.valueOf(StringID.CONSTRAINT_SELECT_ESTIMATOR), "At least one estimator must be selected");
        errorMap.put(String.valueOf(StringID.INVALID_SUPPLIERS), "Invalid supplier(s).");
        errorMap.put(String.valueOf(StringID.RFE_CURRENCY_CONSTRAINT),
                "You cannot currently send an @{RFE} to a supplier who uses a different currency. Please select a different supplier.");
        errorMap.put(String.valueOf(StringID.UNAPPROVED_SUPPLIER_CONSTRAINT),
                "The supplier is not authorized/approved.  Please select different supplier or add the supplier to your Approved Supplier List and proceed.");
        errorMap.put("Can't be prior to today's date", "Can't be prior to today's date");
        errorMap.put("Can't be later than Order Completion date", "Can't be later than Order Completion date");
        errorMap.put(String.valueOf(StringID.RFE_STARTDATE_INVALID_CONSTRAINT), "Invalid Start Date.  Start Date must not be sooner than today's date");
        errorMap.put(String.valueOf(StringID.RFE_BIDENDDATE_INVALID_CONSTRAINT), "Invalid Bid End Date.  Bid End Date must not be sooner than today's date");
        errorMap.put(String.valueOf(StringID.RFE_STARTDATE_AFTER_BIDENDDATE_INVALID_CONSTRAINT), "Invalid dates.  Bid Start Date must be before the Bid End Date.");
        errorMap.put(String.valueOf(StringID.RFE_BIDENDDATE_BEFORE_BIDSTARTDATE_INVALID_CONTRAINT), "Invalid dates.  Bid End Date must be after the Bid Start Date.");
    }

    public RfeDTO findById(Long rfeId) {
        Rfe rfe = rfeRepository.findById(rfeId).orElse(null);
        if (rfe == null) {
            throw new NotFoundException("rfe not found");
        }
        RfeDTO rfeDTO = rfeMapper.toDTO(rfe);
        return rfeDTO;
    }

    // 1. RFE Detail
    public RfeVO getRfeDetail(RfeDTO rfe,
                             Long projectId, Long userId, Long workgroupId, String localeCode,
                             Map<String, String> groupPrefs, Map<String, Boolean> permissionMap) {
        List<RfeSupplierDTO> rfeSupplierDTOS = rfe.getRfeSuppliers();

        RfeVO rfeVO = new RfeVO();
        rfeVO.setRfeId(rfe.getId());
        rfeVO.setIsOpenBid(rfe.getIsOpenBidType());
        rfeVO.setIsReverseAuction(rfe.getIsReverseAuctionBidType());
        rfeVO.setTitle(rfe.getTitle());
        rfeVO.setReferenceNumber(rfe.getOwnerReference());
        rfeVO.setNooshId(rfe.getReference());
        rfeVO.setCustomPropertyId(rfe.getCustomPropertyId());
        rfeVO.setCustomAttributes(propertyService.loadCustomAttributes(rfe.getCustomPropertyId()));
        //NO-7191, set rfe owner workgroup id
        rfeVO.setOwnerWorkgroupId(rfe.getOwnerWorkgroupId());
        if (!rfe.getParent().isSupplierProject()) {
            if (rfe.getStateId() != null) {
                ObjectState objectState = objectStateRepository.findById(rfe.getStateId()).orElse(null);
                if (objectState != null && objectState.getDescriptionStrId() != null) {
                    rfeVO.setStatusStrId(objectState.getDescriptionStrId().toString());
                }
            }
            rfeVO.setStatusFormat(getStatusFormat(rfe));
        } else {
            RfeSupplierDTO rfeSupplierDTO = rfeSupplierDTOS.stream()
                    .filter(r -> r.getGroupId().longValue() == rfe.getParent().getOwnerWorkgroupId())
                    .findFirst()
                    .orElse(new RfeSupplierDTO());

            String statusStrId = "";
            if (rfeSupplierDTO.getStateId() != null) {
                ObjectState objectState = objectStateRepository.findById(rfeSupplierDTO.getStateId()).orElse(null);
                statusStrId = objectState != null && objectState.getDescriptionStrId() != null ?
                        objectState.getDescriptionStrId().toString() : null;
            }
            if (rfeSupplierDTO.isPendingSupplierAcceptance()) {
                rfeVO.setIsPendingSupplierAcceptance(rfeSupplierDTO.isPendingSupplierAcceptance());
                rfeVO.setStatusStrId(statusStrId);
            } else {
                rfeVO.setStatusStrId(statusStrId);
            }

            rfeVO.setStatusFormat(getStatusFormat(rfeSupplierDTOS.stream()
                    .filter(r -> r.getGroupId().longValue() == rfe.getParent().getOwnerWorkgroupId())
                    .findFirst()
                    .orElse(new RfeSupplierDTO())));
        }
        rfeVO.setEstimateDue(rfe.getDueDate());

        boolean autoCloseEnabled = preferenceService.check(PreferenceID.BU_WORKGROUP_AUTO_CLOSE_RFE_PREF, groupPrefs);
        boolean dismissSuppliersEnabled = preferenceService.check(PreferenceID.BU_WORKGROUP_RFE_DISMISS_SUPPLIER_PREF, groupPrefs);
        PropertyAttributeVO disPlayVsfPropertyAttribute = propertyService.findPropertyAttribute(rfe.getOwnerWorkgroupId(), ObjectClassID.WORKGROUP,
                PropertyTypeID.PROPERTY_TYPE_OXF, "DISPLAY_VSF_bool");
        // Auto close @{RFE} upon due date
        if (autoCloseEnabled || rfe.getIsAutoClose()) {
            if (!rfe.getSealedBidding() && !rfe.getIsOpenBidType()) {
                rfeVO.setAutoCloseEnabled(true);
                if (rfe.getIsAutoClose()) {
                    rfeVO.setIsAutoClose(true);
                } else {
                    rfeVO.setIsAutoClose(false);
                }
            }
        }

        rfeVO.setIsAutoPricing(rfe.getIsAutoPricing());
        rfeVO.setDisplayVsf(disPlayVsfPropertyAttribute != null
                && disPlayVsfPropertyAttribute.getNumberValue() != null
                && disPlayVsfPropertyAttribute.getNumberValue().compareTo(BigDecimal.ONE) == 0);
        boolean pricingEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_CONTRACT_PRICING, groupPrefs);
        boolean autoPricingEnabled = preferenceService.check(PreferenceID.WORKGROUP_OPTION_GENERATE_ESTIMATE_FROM_PRICING, groupPrefs);
        if (pricingEnabled && autoPricingEnabled && !rfe.getIsOpenBidType()) {
            rfeVO.setAutoPricingEnabled(true);
        }

        // Also dismiss suppliers with no estimates submitted
        if (dismissSuppliersEnabled || rfe.getIsDismissSupplier()) {
            if (!rfe.getSealedBidding() && !rfe.getIsOpenBidType()) {
                rfeVO.setDismissSuppliersEnabled(true);
                if (rfe.getIsDismissSupplier()) {
                    rfeVO.setIsDismissSuppliers(true);
                } else {
                    rfeVO.setIsDismissSuppliers(false);
                }
            }
        }

        boolean canEditEstimateDue;
        if (rfe.getIsOpenBidType()) {
            canEditEstimateDue = permissionService.check(PermissionID.CREATE_OPEN_BID_RFE, projectId, permissionMap);
        } else {
            canEditEstimateDue = permissionService.check(PermissionID.OPEN_RFE, projectId, permissionMap);
        }
        rfeVO.setCanEditEstimateDue(canEditEstimateDue);
        rfeVO.setOrderCompletionDate(rfe.getProposedCompletionDate());
        rfeVO.setSubmittedDate(rfe.getSubmitDate());
        rfeVO.setBidStartDate(rfe.getBidStartDate());
        rfeVO.setBidEndDate(rfe.getBidEndDate());
        rfeVO.setOrigBidEndDate(rfe.getOrigBidEndDate());
        rfeVO.setDescription(rfe.getDescription());
        rfeVO.setComments(rfe.getComments());

        rfeVO.setItemizedTaxAndShippingEnabled(preferenceService.check(PreferenceID.WORKGROUP_OPTION_ITEMIZED_TAX_SHIPPING, groupPrefs));
        rfeVO.setIsItemizedTaxAndShipping(rfe.getItemizedTaxAndShipping());
        if ((preferenceService.check(PreferenceID.WORKGROUP_OPTION_SEALED_BID, groupPrefs)
                || rfe.getSealedBidding()) && !rfe.getIsOpenBidType() && !rfe.getIsReverseAuctionBidType()) {
            rfeVO.setSealedBidEnabled(true);
        }
        if (rfe.getSealedBidding() != null) {
            rfeVO.setIsSealedBidding(rfe.getSealedBidding());
        }
        rfeVO.setAllOrNoneEnabled(preferenceService.check(PreferenceID.WORKGROUP_OPTION_REQUEST_FOR_ALL_OR_NONE, groupPrefs));
        if (rfe.getRequestAllOrNone() != null) {
            rfeVO.setIsRequestAllOrNone(rfe.getRequestAllOrNone());
        }
        if (rfe.getIsOpenBidType() != null) {
            rfeVO.setBlindBiddingEnabled(rfe.getIsOpenBidType());
        }
        if (rfe.getBlindBidding() != null) {
            rfeVO.setIsBlindBidding(rfe.getBlindBidding());
        }
        rfeVO.setIsSensitive(rfe.getIsSensitive());

        boolean isSupplierWorkgroup = false;
        Optional<Workgroup> workgroupOptional = workgroupRepository.findById(workgroupId);
        if (workgroupOptional.isPresent()) {
            isSupplierWorkgroup = workgroupOptional.get().getWorkGroupTypeId() == WorkgroupTypeID.SUPPLIER;
        }
        boolean displayAliasName = isSupplierWorkgroup && preferenceService.check(PreferenceID.WORKGROUP_OPTION_ENABLE_DISPLAY_ALIAS_NAME, groupPrefs);
        if (rfe.getOwnerUserId() != null) {
            AccountUser user = accountUserRepository.findById(rfe.getOwnerUserId()).orElse(null);
            String requestorName = user.getPerson().getFullName();
            if (displayAliasName && null != user.getPerson().getAliasName() && !"".equals(user.getPerson().getAliasName())) {
                requestorName = user.getPerson().getAliasName();
            }
            rfeVO.setRequestorName(requestorName);
            rfeVO.setRequestorWorkgroup(user.getWorkgroup().getName());
            rfeVO.setRequestorExternalLink(displayAliasName ? "" :
                    NooshOneUrlUtil.composeTeamMemberLinkToEnterprise(user.getPerson().getId(), projectId, rfe.getId(), rfe.getParent().isSupplierProject()));
        }
        // This @{RFE} has been ordered. This @{RFE} can not be recalled or revised.
        int orderCount = reviseRfePermission.getOrderCount(rfe, projectId, groupPrefs);
        if (orderCount == 0) {
            rfeVO.setCanOrdered(true);
        }

        if (rfe.getParent().isSupplierProject()) {
            if (!rfeSupplierDTOS.isEmpty()) {
                rfeSupplierDTOS.stream().forEach(rfeSupplierDTO -> {
                    if (rfeVO.getEstimators() == null
                            && (userId.equals(rfeSupplierDTO.getUserId()) || workgroupId.equals(rfeSupplierDTO.getGroupId()))) {
                        rfeVO.setEstimators(rfeSupplierDTO.getWorkgroupName());
                        if (!rfeSupplierDTO.isActive()) {
                            rfeVO.setReason(rfeSupplierDTO.getStateChangeComment());
                        }
                    }
                });
            }
        } else {
            List<EstimateDTO> awardedEstimates = estimateMyBatisMapper.getAwardedEstimatesByRfeId(rfe.getId(), projectId);
            int dismissableCounter = 0;
            // set supplier flag
            Map<String, SupplierFlagVO> supplierFlagMap = new HashMap<>();
            List<SupplierFlagRequestVO> supplierFlagRequestVOs = new ArrayList<>(rfeSupplierDTOS.size());
            rfeSupplierDTOS.forEach(rfeSupplierDTO -> {
                SupplierFlagRequestVO supplierFlagRequestVO = new SupplierFlagRequestVO(rfe.isDraft(),
                        rfeSupplierDTO.getId(),
                        ObjectClassID.RFE_SUPPLIER,
                        rfe.getOwnerWorkgroupId(),
                        rfeSupplierDTO.getGroupId());
                supplierFlagRequestVOs.add(supplierFlagRequestVO);
            });
            supplierFlagMap = supplierFlagService.getSupplierFlagVOs(supplierFlagRequestVOs);

            List<RfeSupplierVO> rfeSuppliers = new ArrayList<>();
            for (RfeSupplierDTO rfeSupplierDTO : rfeSupplierDTOS) {

                RfeSupplierVO rfeSupplierVO = rfeSupplierMapper.toVO(rfeSupplierDTO);

                String supplierFlagKey = supplierFlagService.getSupplierFlagKey(rfe.isDraft(),
                        rfeSupplierDTO.getId(), ObjectClassID.RFE_SUPPLIER, rfe.getOwnerWorkgroupId(),
                        rfeSupplierDTO.getGroupId());
                rfeSupplierVO.setSupplierFlag(supplierFlagMap.get(supplierFlagKey));
                rfeSupplierVO.setSupplierScore(orderResourceFeignClient.findSupplierScore(rfe.getOwnerWorkgroupId(), rfeSupplierDTO.getGroupId()));

                //check if the supplier has any estimate that has been awarded order
                boolean bAwarded = false;
                for (int estInd = 0; !bAwarded && estInd < awardedEstimates.size(); estInd++) {
                    if (awardedEstimates.get(estInd).getOwnerWorkgroupId().longValue() == rfeSupplierDTO.getGroupId().longValue()) {
                        bAwarded = true;
                    }
                }
                if (bAwarded == false && (rfeSupplierDTO.isActive() || rfeSupplierDTO.isPendingSupplierAcceptance())) {
                    dismissableCounter++;
                }
                rfeSupplierVO.setTeamMemberExternalLink(NooshOneUrlUtil.composeTeamMemberLinkToEnterprise(rfeSupplierDTO.getPersonId(), projectId, rfe.getId(), rfe.getParent().isSupplierProject()));

                rfeSuppliers.add(rfeSupplierVO);
            }
            if (!dismissSupplierPermission.check(rfe, projectId, permissionMap)
                    || dismissableCounter <= 0) {
                rfeVO.setDisableDismissButton(true);
            }
            if (rfe.getIsReverseAuctionBidType() && (rfe.isInBidding() || rfe.isInAwarding())) {
                rfeVO.setDisableDismissButton(true);
            }
            // Outsourcer
            rfeVO.setRfeSuppliers(rfeSuppliers);
        }

        return rfeVO;
    }

    // 1-1 get status color
    public String getStatusFormat(RfeDTO rfe) {
        String format;
        if (rfe.isDraft() || rfe.isInBidding()) {
            format = "status_draft";
        } else if (rfe.isOpen() || rfe.isInAwarding()) {
            format = "status_sent";
        } else if (rfe.isRecalled()
                || rfe.getStateId() == ObjectStateID.RFE_DELETED
                || rfe.getStateId() == ObjectStateID.RFE_CANCELLED) {
            format = "status_rejected";
        } else {
            format = "status_normal";
        }

        return format;
    }

    // get supplier status color
    public String getStatusFormat(RfeSupplierDTO rfeSupplierDTO) {
        String format;
        if (rfeSupplierDTO.getStateId() == ObjectStateID.RFE_SUPPLIER_DRAFT
                || rfeSupplierDTO.getStateId() == ObjectStateID.RFE_SUPPLIER_RFE_SENT
                || rfeSupplierDTO.getStateId() == ObjectStateID.RFE_SUPPLIER_IN_AUCTION
                || rfeSupplierDTO.getStateId() == ObjectStateID.RFE_SUPPLIER_SEND_PENDING) {
            format = "status_draft";
        } else if (rfeSupplierDTO.getStateId() == ObjectStateID.RFE_SUPPLIER_AUTO_PRICING
                || rfeSupplierDTO.getStateId() == ObjectStateID.RFE_SUPPLIER_AWARD_AUCTION
                || rfeSupplierDTO.getStateId() == ObjectStateID.RFE_SUPPLIER_PARTIALLY_ACCEPTED
                || rfeSupplierDTO.getStateId() == ObjectStateID.RFE_SUPPLIER_ESTIMATING) {
            format = "status_sent";
        } else if (rfeSupplierDTO.getStateId() == ObjectStateID.RFE_SUPPLIER_SEND_FAILED
                || rfeSupplierDTO.getStateId() == ObjectStateID.RFE_SUPPLIER_REJECTED
                || rfeSupplierDTO.getStateId() == ObjectStateID.RFE_SUPPLIER_RECALLED
                || rfeSupplierDTO.getStateId() == ObjectStateID.RFE_SUPPLIER_DISMISSED) {
            format = "status_rejected";
        } else {
            format = "status_normal";
        }

        return format;
    }

    // 3-3
    public BigDecimal calculateNextLowestBid(Rfe rfe) {
        //calculate the next lowest bid and set onto the form
        BigDecimal auctionPrice = rfe.getAuctionPrice();
        BigDecimal intialPrice = rfe.getInitialPrice();

        if (auctionPrice == null && intialPrice == null) {
            return null;   //there is no lowest possible bid, can bid anything
        }

        if (auctionPrice == null && intialPrice != null) {
            return intialPrice;
        }

        BigDecimal lowestPossibleBid = new BigDecimal(String.valueOf(auctionPrice));

        if (rfe.getPricingRuleId().longValue() == PricingRuleID.PRICING_RULE_DECREMENT_PERCENT
                && rfe.getDecrementPricePercent() != null && rfe.getDecrementPricePercent().doubleValue() > 0) {
            double decrementPercent = rfe.getDecrementPricePercent();
            double percentValue = auctionPrice.doubleValue() * decrementPercent / 100;
            BigDecimal percentValueBigDecimal = new BigDecimal(String.valueOf(percentValue));
            lowestPossibleBid = lowestPossibleBid.subtract(percentValueBigDecimal);
        } else if (rfe.getPricingRuleId().longValue() == PricingRuleID.PRICING_RULE_DECREMENT_VALUE
                && rfe.getDecrementPrice() != null && rfe.getDecrementPrice().doubleValue() > 0) {
            double decrementValue = rfe.getDecrementPrice().doubleValue();
            BigDecimal decrementValueBigDecimal = new BigDecimal(String.valueOf(decrementValue));
            lowestPossibleBid = lowestPossibleBid.subtract(decrementValueBigDecimal);
        } else {
            BigDecimal decrementValueBigDecimal = new BigDecimal(String.valueOf("0.01"));
            lowestPossibleBid = lowestPossibleBid.subtract(decrementValueBigDecimal);
        }
        BigDecimal lowestPossibleBidBigDecimal = lowestPossibleBid.setScale(2, BigDecimal.ROUND_HALF_UP);
        return lowestPossibleBidBigDecimal;
    }

    // 4-1 Status Format
    public String getStatusFormat(EstimateDTO estimateDTO) {
        String format;
        if (estimateDTO.isDraft()) {
            format = "status_draft";
        } else if (estimateDTO.isSent()) {
            format = "status_sent";
        } else if (estimateDTO.isRejected() || estimateDTO.isRetracted() || estimateDTO.isInvalidated()) {
            format = "status_rejected";
        } else {
            format = "status_normal";
        }

        return format;
    }

    /*
     ** estimate due date has been updated
     ** notify suppliers
     */
    public ServerResponse updateDates(RfeInlineEditingVO rfeInlineEditingVO, Long projectId,
                                      Long rfeId, Long userId, Long workgroupId, String customPortalName) {
        Optional<Rfe> optionalRfe = rfeRepository.findById(rfeId);
        if (optionalRfe.isPresent()) {
            Rfe rfe = optionalRfe.get();

            //check for privilege
            boolean permission;
            if (rfe.getIsOpenBidType() || rfe.getIsReverseAuctionBidType()) {
                permission = permissionService.checkAll(PermissionID.CREATE_OPEN_BID_RFE, workgroupId,
                        userId, projectId);
            } else {
                permission = permissionService.checkAll(PermissionID.OPEN_RFE, workgroupId,
                        userId, projectId);
            }

            if (!permission) {
                throw new NoPermissionException(String.valueOf(StringID.NO_PERMISSION_TO_REVISE_RFE_DATES));
            }

            if (rfeInlineEditingVO.getEstimateDueDate() != null) {
                //validate start and due date
                String errorMsg = validateDates(rfe, rfeInlineEditingVO.getEstimateDueDate(), null, null);
                if (errorMsg != null) {
                    return ServerResponse.error(String.valueOf(errorMsg), errorMap.get(String.valueOf(errorMsg)), null);
                }

                rfe.setDueDate(rfeInlineEditingVO.getEstimateDueDate());
                rfeRepository.saveAndFlush(rfe);
                // notify suppliers
                notifySuppliers(userId, projectId, rfeId, DateUtil.getDate(rfeInlineEditingVO.getEstimateDueDate()), null, null);
            }

            if (rfeInlineEditingVO.getBidStartDate() != null || rfeInlineEditingVO.getBidEndDate() != null) {
                if (rfe.getIsLimitedExtension() && rfe.getMaxExtensions() <= rfe.getNumberOfExtensions()) {
                    ServerResponse.error(String.valueOf(StringID.RFE_CONSTRAINT_EXCEEDING_MAX_EXTENSIONS),
                            "You cannot revise the RFE bidding dates because this RFE has reached the maximum number of extensions being allowed.", null);
                }

                String errorMsg = validateDates(rfe, null, rfeInlineEditingVO.getBidStartDate(), rfeInlineEditingVO.getBidEndDate());
                if (errorMsg != null) {
                    return ServerResponse.error(errorMsg);
                }

                if (rfeInlineEditingVO.getBidStartDate() != null && rfe.getAuctionPrice() == null) {
                    rfe.setBidStartDate(rfeInlineEditingVO.getBidStartDate());
                }
                if (rfeInlineEditingVO.getBidEndDate() != null && LocalDateTime.now().isBefore(rfe.getBidEndDate())) {
                    rfe.setBidEndDate(rfeInlineEditingVO.getBidEndDate());
                }
                rfeRepository.saveAndFlush(rfe);
                // notify suppliers
                notifySuppliers(userId, projectId, rfeId, null,
                        rfeInlineEditingVO.getBidStartDate() != null ? DateUtil.getDate(rfeInlineEditingVO.getBidStartDate()) : null,
                        rfeInlineEditingVO.getBidEndDate() != null ? DateUtil.getDate(rfeInlineEditingVO.getBidEndDate()) : null);

                RfeBidVO rfeBidVO = new RfeBidVO();
                if (rfe.isInBidding() || rfe.isInAwarding()) {
                    rfeBidVO.setTimeLeftStr("Auction begins");
                    rfeBidVO.setTimeLeftDate(rfe.getBidStartDate());
                } else if (!rfe.isDraft()) {
                    rfeBidVO.setTimeLeftStr("Auction Ended");
                    rfeBidVO.setTimeLeftDate(rfe.getBidEndDate());
                }
                return ServerResponse.success(rfeBidVO);
            }
        } else {
            throw new NotFoundException("Rfe not found");
        }
        return ServerResponse.success();
    }

    /**
     * This will check the dates when updating dates for sent rfe
     * The reason why I broke out the two date validation methods is because the original validateDates() method
     * was only passing in the rfeBean, with the dates overwritten.  This presented a problem if I needed the previous date
     * to compare the new date to.  Also the previous method, allowed updating of the due date to be before the start date
     *
     * @param rfe             original rfe with original dates
     * @param newBidStartDate new bid start date, can be null, if nothing is entered on the form
     * @param newBidEndDate   new bid end date, can be null, if nothing is entered on the form
     * @param estimateDueDate new estimate due date, can be null, if nothing is entered on the form
     * @return
     */
    private String validateDates(Rfe rfe, LocalDateTime estimateDueDate, LocalDateTime newBidStartDate, LocalDateTime newBidEndDate) {
        if (estimateDueDate != null) {
            LocalDateTime completionDate = rfe.getProposedCompletionDate();
            Map<String, String> groupPrefs = preferenceService.findGroupPrefs(rfe.getOwnerWorkgroupId());
            boolean isDatePrefsDisabled = !preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROCUREMENT_DATE_PREFERENCES, groupPrefs, false);
            boolean isDueDateRequired = isDatePrefsDisabled || preferenceService.check(PreferenceID.BU_RFE_DUE_DATE_REQUIRED, groupPrefs, true);
            if (isDueDateRequired && estimateDueDate != null) {
                if (estimateDueDate.isBefore(LocalDateTime.now())) {
                    return "Can't be prior to today's date";
                }
                if (completionDate != null && estimateDueDate.isAfter(completionDate)) {
                    return "Can't be later than Order Completion date";
                }
            }
        } else if (newBidStartDate != null || newBidEndDate != null) {
            if (rfe.getIsReverseAuctionBidType()) {
                LocalDateTime previousStartDate = rfe.getBidStartDate();
                LocalDateTime previousEndDate = rfe.getBidEndDate();

                if (newBidStartDate != null && newBidStartDate.isBefore(LocalDateTime.now())) {
                    return String.valueOf(StringID.RFE_STARTDATE_INVALID_CONSTRAINT);
                } else if (newBidEndDate != null && newBidEndDate.isBefore(LocalDateTime.now())) {
                    return String.valueOf(StringID.RFE_BIDENDDATE_INVALID_CONSTRAINT);
                }

                if (newBidStartDate != null) {
                    if ((newBidEndDate != null && newBidStartDate.isAfter(newBidEndDate)) || (newBidEndDate == null && newBidStartDate.isAfter(previousEndDate))) {
                        return String.valueOf(StringID.RFE_STARTDATE_AFTER_BIDENDDATE_INVALID_CONSTRAINT);  //Invalid dates.  Bid Start Date must be before the Bid End Date.
                    }
                }

                if (newBidEndDate != null) {
                    if ((newBidStartDate != null && newBidEndDate.isBefore(newBidStartDate)) || (newBidStartDate == null && newBidEndDate.isBefore(previousStartDate))) {
                        return String.valueOf(StringID.RFE_BIDENDDATE_BEFORE_BIDSTARTDATE_INVALID_CONTRAINT); //Invalid dates.  Bid End Date must be after the Bid Start Date.
                    }
                }
            }
        }
        return null;
    }

    private void notifySuppliers(long userId, Long projectId, Long rfeId, Date newDueDate, Date newBidStartDate, Date newBidEndDate) {
        // send notification by scheduled api
        Map dateRevisedMap = new HashMap();
        dateRevisedMap.put("userId", userId);
        dateRevisedMap.put("projectId", projectId);
        dateRevisedMap.put("rfeId", rfeId);
        if (newDueDate != null) {
            dateRevisedMap.put("newDueDate", newDueDate.getTime());
        }
        if (newBidStartDate != null) {
            dateRevisedMap.put("newBidStartDate", newBidStartDate.getTime());
        }
        if (newBidStartDate != null) {
            dateRevisedMap.put("newBidEndDate", newBidEndDate.getTime());
        }

        // http://localhost:7777
        String host = nooshApiEnterpriseHost;
        if (!("80").equals(nooshApiEnterprisePort)) {
            host = nooshApiEnterpriseHost + ":" + nooshApiEnterprisePort;
        }

        ObjectMapper objectMapper = new ObjectMapper();
        try {
            String jsonBody = objectMapper.writeValueAsString(dateRevisedMap);
            if (environment.getProperty("apijob.scheduledjobs.enabled", boolean.class, false)) {
                scheduledApiService.createScheduledApiWithJsonBody(
                        "http://",
                        host,
                        "/noosh/internal/api/rfeDateRevisedNotification",
                        HttpMethod.POST,
                        null,
                        jsonBody,
                        null,
                        (int) userId,
                        new Date());
            } else if (environment.getProperty("apijob.messageconsumer.enabled", boolean.class, false)) {
                rabbitMQProducerService.convertAndSendScheduledApiDto(ScheduledApiDtoBuilder.builder()
                        .protocol("http://")
                        .host(host)
                        .path("/noosh/internal/api/rfeDateRevisedNotification")
                        .method(HttpMethod.POST.name())
                        .body(jsonBody)
                        .createUserId((int) userId)
                        .createDate(new Date())
                        .build());
            } else {
                webClientService.createWebApiWithJsonBody(
                        "http://",
                        host,
                        "/noosh/internal/api/rfeDateRevisedNotification",
                        HttpMethod.POST,
                        null,
                        jsonBody
                );
            }
        } catch (JsonProcessingException e) {
            log.error(e.getMessage());
        }
    }

    /**
     * TODO: It is complex and requires the development of common components
     * Invite Suppliers
     */
    public ServerResponse inviteSuppliers(RfeInlineEditingVO rfeInlineEditingVO, Long projectId,
                                          Long rfeId, Long userId, Long workgroupId) {

        Optional<Rfe> rfeOptional = rfeRepository.findById(rfeId);
        if (rfeOptional.isPresent()) {
            Rfe rfe = rfeOptional.get();
            RfeDTO rfeDTO = rfeMapper.toDTO(rfe);
            ProjectDTO projectDTO = projectService.findProjectById(projectId);
            rfeDTO.setParent(projectDTO);

            if (rfe.getIsReverseAuctionBidType()) {
                if (!rfe.isInBidding()) {
                    return ServerResponse.error(String.valueOf(StringID.INVITE_SUPPLIERS_CONSTRAINTS),
                            "Invite Suppliers is not allow. Invite Suppliers is only allowed when the Rfe is open for estimating and reverse auction bidding", null);
                }
            } else if (!rfe.isOpen()) {
                return ServerResponse.error(String.valueOf(StringID.INVITE_SUPPLIERS_CONSTRAINTS),
                        "Invite Suppliers is not allow. Invite Suppliers is only allowed when the Rfe is open for estimating and reverse auction bidding", null);
            }
            //validate to make sure the RFE has already sent to the supplier
            if (rfeInlineEditingVO.getUserIds() != null && !rfeInlineEditingVO.getUserIds().isEmpty()) {
                List<AccountUser> accountUsers = accountUserRepository.findAllById(rfeInlineEditingVO.getUserIds());
                List<RfeSupplier> rfeSuppliers = rfeSupplierRepository.findByRfeId(rfeId);
                List<String> duplicatedSuppliers = new ArrayList();
                for (AccountUser accountUser : accountUsers) {
                    for (RfeSupplier rfeSupplier : rfeSuppliers) {
                        if (accountUser.getId().longValue() == rfeSupplier.getUserId() && rfe.getStateId() != ObjectStateID.RFE_SUPPLIER_DISMISSED) {
                            duplicatedSuppliers.add(accountUser.getPerson().getFullName());
                        }
                        if (rfe.getStateId() != ObjectStateID.ACCOUNT_USER_ACTIVATED) {
                            Map params = new HashMap();
                            params.put("name", accountUser.getPerson().getFullName());
                            return ServerResponse.error(String.valueOf(StringID.INACTIVE_SUPPLIER),
                                    "The supplier ${name} is not active.  Please select a different supplier.", null);
                        }
                    }
                }
                if (duplicatedSuppliers.size() > 0) {
                    //format the names
                    StringBuffer names = new StringBuffer("<UL>");
                    for (int i = 0; i < duplicatedSuppliers.size(); i++) {
                        names.append("<LI>" + duplicatedSuppliers + "</LI>");
                    }
                    names.append("</UL>");

                    //report error back to user
                    Map map = new HashMap();
                    map.put("names", names.toString());
                    return ServerResponse.error(String.valueOf(StringID.RFE_SUPPLIER_ALREADY_EXISTED_CONSTRAINT),
                            "This RFE has already been sent to ${names}Please re-select your suppliers.", null);
                }

                //set up for validation
                long errorId = validateSuppliers(accountUsers, rfeDTO);
                if (errorId != VALIDATE_ERROR_NONE) {
                    return ServerResponse.error(String.valueOf(errorId), errorMap.get(String.valueOf(errorId)), null);
                }

                //revert back to the existing map & post event
                boolean isAutoPricing = rfeDTO.getIsAutoPricing();
            } else {
                return ServerResponse.error(String.valueOf(StringID.CONSTRAINT_SELECT_ESTIMATOR), "At least one estimator must be selected", null);
            }
        } else {
            throw new UnexpectedException("RFE CONTROLLER:  inviteSuppliers() can't find rfeBean for requestId=" + rfeId);
        }

        return ServerResponse.success();
    }

    private long validateSuppliers(List<AccountUser> accountUsers, RfeDTO rfeDTO) {
        if (accountUsers.size() <= 0) {
            return StringID.CONSTRAINT_SELECT_ESTIMATOR;
        }

        for (AccountUser accountUser : accountUsers) {
            if (accountUser.getObjectStateId() != ObjectStateID.ACCOUNT_USER_ACTIVATED) {
                return StringID.INVALID_SUPPLIERS;
            }
        }

        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(rfeDTO.getOwnerWorkgroupId());
        boolean approveSupplierOnly = preferenceService.check(PreferenceID.BU_RFE_UNREGIS_SUP, groupPrefs);

        Workgroup ownerGroup = workgroupRepository.findById(rfeDTO.getOwnerWorkgroupId()).orElse(null);
        long myGroupCurrency = ownerGroup.getDefaultCurrencyId();
        for (AccountUser accountUser : accountUsers) {

            // supplier can not use different currency as the rfe owner
            if (myGroupCurrency != accountUser.getWorkgroup().getDefaultCurrencyId()) {
                return StringID.RFE_CURRENCY_CONSTRAINT;
            }

            // approved supplier list
            if (approveSupplierOnly) {
                SupplierWorkgroup groupSupplier;
                long clientWorkgroupId = rfeDTO.getParent().getClientWorkgroupId();
                if (clientWorkgroupId > 0) {
                    groupSupplier = supplierWorkgroupRepository.findByOwnerWorkgroupIdAndSupplierWorkgroupIdAndClientWorkgroupId(rfeDTO.getOwnerWorkgroupId(), accountUser.getWorkgroup().getId(), clientWorkgroupId);
                } else {
                    groupSupplier = supplierWorkgroupRepository.findByOwnerWorkgroupIdAndSupplierWorkgroupIdAndClientWorkgroupIdIsNull(rfeDTO.getOwnerWorkgroupId(), accountUser.getWorkgroup().getId());
                }
                if (groupSupplier == null || !groupSupplier.getIsApproved()) {
                    return StringID.UNAPPROVED_SUPPLIER_CONSTRAINT;
                }
            }
        }
        return VALIDATE_ERROR_NONE;
    }

    /**
     * Dismiss Suppliers
     */
    @Transactional
    public ServerResponse dismissSuppliers(RfeInlineEditingVO rfeInlineEditingVO, Long projectId,
                                           Long rfeId, long userId, Long workgroupId, String localeCode) throws Exception {
        Optional<Rfe> optionalRfe = rfeRepository.findById(rfeId);
        if (optionalRfe.isPresent()) {
            Rfe rfe = optionalRfe.get();

            if ((rfe.isOpen() || rfe.isInBidding() || rfe.isClosed())
                    && rfeInlineEditingVO.getSupplierWorkgroupIds() != null) {

                /*========================================================
                 ** change RFE team membership to be dismissed
                 ** cancel the createEstimate and sendEstimate tasks
                 ** send dismiss notification
                 *========================================================*/
                AccountUserDTO sender = accountUserMyBatisMapper.findById(userId);

                long rfeOwnerGroupId = rfe.getOwnerWorkgroupId();
                List<RfeSupplierDTO> rfeSupplierDTOS = rfeSupplierMyBatisMapper.findRfeSuppliersByRfeId(rfeId);
                Set<Long> activeSupplierWorkgroupIds = new HashSet<>();
                List dismissSupplierWorkgroupIds = new ArrayList();
                rfeSupplierDTOS.stream().forEach(rfeSupplierDTO -> {
                    if (rfeSupplierDTO.isActive() || rfeSupplierDTO.isPendingSupplierAcceptance()) {
                        long memberGroupId = rfeSupplierDTO.getGroupId();
                        if (isInSuppliersList(memberGroupId, rfeInlineEditingVO.getSupplierWorkgroupIds())) {
                            // --- Rfe team membership:  change role
                            RfeSupplier rfeSupplier = rfeSupplierRepository.findById(rfeSupplierDTO.getId()).orElse(null);
                            rfeSupplier.setStateId(ObjectStateID.RFE_SUPPLIER_DISMISSED);
                            rfeSupplier.setStateChangeComment(rfeInlineEditingVO.getDismissComments());
                            rfeSupplier.setModUserId(userId);
                            rfeSupplier.setModDate(LocalDateTime.now());
                            rfeSupplierRepository.saveAndFlush(rfeSupplier);

                            dismissSupplierWorkgroupIds.add(memberGroupId);

                            // --- Supplier tasks:  delete the createEstimate task for the supplier
                            List<Task> taskList = taskRepository.findByTaskByObjectAndClassId(rfe.getId());
                            if (!taskList.isEmpty()) {
                                for (Task task : taskList) {
                                    if (TaskTypeID.CREATE_ESTIMATE != task.getTaTaskTypeId()) {
                                        continue;
                                    }
                                    if (ObjectStateID.TASK_CREATED == task.getOcObjectStateId()) {
                                        task.setOcObjectStateId(ObjectStateID.TASK_CANCELLED);
                                        task.setAssignedToUserId(rfeSupplierDTO.getUserId());
                                        taskRepository.saveAndFlush(task);
                                    }
                                }
                            }

                        }
                    }

                    if (ObjectStateID.RFE_SUPPLIER_ESTIMATING == rfeSupplierDTO.getStateId()) {
                        activeSupplierWorkgroupIds.add(rfeSupplierDTO.getGroupId());
                    }
                });

                // send notification by scheduled api
                Map dismissedSupplierMap = new HashMap();
                dismissedSupplierMap.put("userId", userId);
                dismissedSupplierMap.put("projectId", projectId);
                dismissedSupplierMap.put("rfeId", rfeId);
                dismissedSupplierMap.put("dismissSupplierWorkgroupIds", dismissSupplierWorkgroupIds);
                dismissedSupplierMap.put("dismissComment", rfeInlineEditingVO.getDismissComments());

                // http://localhost:7777
                String host = nooshApiEnterpriseHost;
                if (!("80").equals(nooshApiEnterprisePort)) {
                    host = nooshApiEnterpriseHost + ":" + nooshApiEnterprisePort;
                }

                ObjectMapper objectMapper = new ObjectMapper();
                try {
                    String jsonBody = objectMapper.writeValueAsString(dismissedSupplierMap);
                    if (environment.getProperty("apijob.scheduledjobs.enabled", boolean.class, false)) {
                        scheduledApiService.createScheduledApiWithJsonBody(
                                "http://",
                                host,
                                "/noosh/internal/api/rfeDismissSupplierNotification",
                                HttpMethod.POST,
                                null,
                                jsonBody,
                                null,
                                (int) userId,
                                new Date());
                    } else if (environment.getProperty("apijob.messageconsumer.enabled", boolean.class, false)) {
                        rabbitMQProducerService.convertAndSendScheduledApiDto(ScheduledApiDtoBuilder.builder()
                                .protocol("http://")
                                .host(host)
                                .path("/noosh/internal/api/rfeDismissSupplierNotification")
                                .method(HttpMethod.POST.name())
                                .body(jsonBody)
                                .createUserId((int) userId)
                                .createDate(new Date())
                                .build());
                    } else {
                        webClientService.createWebApiWithJsonBody(
                                "http://",
                                host,
                                "/noosh/internal/api/rfeDismissSupplierNotification",
                                HttpMethod.POST,
                                null,
                                jsonBody
                        );
                    }
                } catch (JsonProcessingException e) {
                    log.error(e.getMessage());
                }

                /*===============================================================================
                 ** for each estimate submitted by the dismissed supplier, add to-be-rejected list
                 **===============================================================================*/
                List<EstimateDTO> estimateDTOList = new ArrayList<>();
                if (permissionService.checkAll(PermissionID.VIEW_ESTIMATE, workgroupId, userId, projectId)) {
                    estimateDTOList = estimateMyBatisMapper.getEstimatesByRfeId(rfeId, projectId);
                }
                for (long supplierGroupId : rfeInlineEditingVO.getSupplierWorkgroupIds()) {
                    for (EstimateDTO estimateDTO : estimateDTOList) {
                        if (estimateDTO.getOwnerWorkgroupId() == supplierGroupId) {
                            //estimate sent
                            if (estimateDTO.getStateId() == ObjectStateID.ESTIMATE_SENT) {
                                Estimate estimate = new Estimate();
                                estimate.setId(estimateDTO.getId());
                                estimate.setStateId(ObjectStateID.ESTIMATE_REJECTED);
                                estimate.setOwnerWorkgroupId(supplierGroupId);
                                estimate.setCreateUserId(userId);
                                estimateRepository.saveAndFlush(estimate);
                            }
                            //estimate draft
                            else if (estimateDTO.getStateId() == ObjectStateID.ESTIMATE_DRAFT) {
                                // --- Cancel the review estimate task
                                List<Task> taskList = taskRepository.findByTaskByObjectAndClassId(estimateDTO.getId());
                                if (!taskList.isEmpty()) {
                                    for (Task task : taskList) {
                                        if (TaskTypeID.SEND_ESTIMATE != task.getTaTaskTypeId()) {
                                            continue;
                                        }
                                        if (ObjectStateID.TASK_CREATED == task.getOcObjectStateId()) {
                                            task.setOcObjectStateId(ObjectStateID.TASK_CANCELLED);
                                            taskRepository.saveAndFlush(task);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // --- Tracking per workgroup
                    Workgroup workgroup = workgroupRepository.findById(supplierGroupId).orElse(null);
                    ProjectDTO projectDTO = projectService.findProjectById(projectId);
                    Tracking tracking = new Tracking();
                    tracking.setTrTrackingTypeId(TrackingTypeID.DISMISS_SUPPLIER);
                    tracking.setObjectClassId(ObjectClassID.RFE);
                    tracking.setObjectId(rfeId);
                    tracking.setCreateUserId(userId);
                    tracking.setModUserId(userId);
                    tracking.setRecordedDate(LocalDateTime.now());
                    tracking.setEnactingUserId(userId);
                    Map map = new HashMap();
                    map.put("requestId", rfeId.toString());
                    map.put("supplierGroup", workgroup.getName());
                    map.put("projectName", projectDTO.getTitle());
                    String i18NData = "";
                    i18NData = URLUtil.mapToQueryString(map);
                    tracking.setI18nData(i18NData);
                    trackingRepository.save(tracking);
                }

                // -- unseal the rfe if all active suppliers has submit an estimates
                if (rfe.getIsSealed() &&
                        !preferenceService.check(PreferenceID.WORKGROUP_OPTION_SEALED_BID_DATE_ONLY, rfe.getOwnerWorkgroupId())) {
                    // if there is no active suppliers, unseal the rfe
                    // if all active supplier has submit estimate, unseal the rfe
                    int activeCount = activeSupplierWorkgroupIds != null ? activeSupplierWorkgroupIds.size() : 0;
                    boolean hasUnsubmittedSupplier = false;
                    for (Long supplierWorkgroupId : activeSupplierWorkgroupIds) {
                        int estCount = 0;
                        for (EstimateDTO estimateDTO : estimateDTOList) {
                            if (supplierWorkgroupId != null && supplierWorkgroupId.longValue() == estimateDTO.getOwnerWorkgroupId()) {
                                estCount++;
                            }
                        }
                        if (estCount == 0) {
                            hasUnsubmittedSupplier = true;
                            break;
                        }
                    }
                    if (!hasUnsubmittedSupplier) {
                        rfe.setIsSealed(false);
                        if (rfe.getStateId() != ObjectStateID.RFE_CLOSED) {
                            if (rfe.getStateId() != ObjectStateID.RFE_SENT && rfe.getStateId() != ObjectStateID.RFE_IN_AUCTION) {
                                throw new NoPermissionException(String.valueOf(StringID.CANNOT_CLOSE_RFE_STATE_CONSTRAINT));
                            }
                            rfe.setStateId(ObjectStateID.RFE_CLOSED);
                            rfeRepository.save(rfe);
                        }
                    }
                }
            }
        } else {
            throw new NotFoundException("Rfe not found");
        }
        return ServerResponse.success();
    }

    private boolean isInSuppliersList(long id, List<Long> supplierWorkgroupIds) {
        boolean found = false;
        for (int i = 0; !found && i < supplierWorkgroupIds.size(); i++) {
            long supplierId = supplierWorkgroupIds.get(i).longValue();
            if (supplierId == id) {
                found = true;
            }
        }
        return found;
    }

    public ServerResponse updateEndTimeIncrement(RfeInlineEditingVO rfeInlineEditingVO,
                                                 Long projectId, Long rfeId, Long userId, Long workgroupId) {
        if (!permissionService.checkAll(PermissionID.EDIT_RFE, workgroupId, userId, projectId)) {
            throw new NoPermissionException(String.valueOf(StringID.NO_PERMISSION_EDIT_RFE_INFO));
        }

        Optional<Rfe> optionalRfe = rfeRepository.findById(rfeId);
        if (optionalRfe.isPresent()) {
            Rfe rfe = optionalRfe.get();

            Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId);
            if (preferenceService.check(PreferenceID.BU_WORKGROUP_EXTEND_BID_TIME_FROM_LAST_SUPPLIER_BID_REVERSE_AUCTION_PREF, groupPrefs)
                    && rfe.getIsAutoExtend()) {
                rfe.setExtendFromLastBid(true);
            } else {
                rfe.setExtendFromLastBid(false);
            }

            if (rfe.getAutoExtendMinutes().intValue() < 0) {
                rfe.setAutoExtendMinutes(0L);
            }
            if (rfe.getMaxExtensions().intValue() < 0) {
                rfe.setMaxExtensions(0L);
            }

            rfe.setTimeIncrement(rfeInlineEditingVO.getTimeIncrement());
            if (rfe.getIsOpenBidType()
                    && rfeInlineEditingVO.getAutoExtendMinutes() != null) {
                rfe.setAutoExtendMinutes(rfeInlineEditingVO.getAutoExtendMinutes());
            }
            if (rfe.getIsReverseAuctionBidType() &&
                    rfeInlineEditingVO.isManualSelectWinningBidder() != null) {
                rfe.setManualSelectWinningBidder(rfeInlineEditingVO.isManualSelectWinningBidder().intValue() == 1
                        ? Boolean.TRUE : Boolean.FALSE);
            }
            rfeRepository.saveAndFlush(rfe);
        } else {
            throw new NotFoundException("Rfe not found");
        }

        return ServerResponse.success();
    }

    public ServerResponse submitBid(RfeInlineEditingVO rfeInlineEditingVO,
                                    Long projectId, Long rfeId, Long userId, Long workgroupId) {
        // find RFE suppliers
        List<RfeSupplierDTO> rfeSupplierDTOS = rfeSupplierMyBatisMapper.findRfeSuppliersByRfeId(rfeId);
        if (rfeSupplierDTOS.isEmpty()) {
            return ServerResponse.error(String.valueOf(StringID.INVALID_BID_PERMISSION), "You are not able to submit a bid.", null);
        }

        Rfe rfe = rfeRepository.findById(rfeId).orElse(null);

        if (rfe != null) {

            if (rfe.getAllowHigherBid() != null && !rfe.getAllowHigherBid()) {
                if (rfeInlineEditingVO.getBidAmount().doubleValue() <= 0 || !validateBid(rfeInlineEditingVO.getBidAmount().doubleValue(), rfe)) {
                    return ServerResponse.error(String.valueOf(StringID.INVALID_BID_AMOUNT), "Your bid does not meet the minimum bid required.", null);
                }
            }
            LocalDateTime submitDate = LocalDateTime.now();
            if (rfe.getBidEndDate() != null && submitDate.isAfter(rfe.getBidEndDate())) {
                return ServerResponse.error(String.valueOf(StringID.INVALID_BID_SUBMIT_DATE), "You submitted your bid after the auction has expired.", null);
            }

            //add bid
            Bid bid = new Bid();
            bid.setBidAmount(rfeInlineEditingVO.getBidAmount());
            bid.setBidAmountCurrencyId(CurrencyID.USD);
            bid.setRfeId(rfeId);
            bid.setSupplierUserId(userId);
            bid.setCreateUserId(userId);
            bid.setSubmitDate(submitDate);
            rfeSupplierDTOS.stream().forEach(rfeSupplierDTO -> {
                if (userId.longValue() == rfeSupplierDTO.getUserId().longValue()) {
                    String bidderName = rfeSupplierDTO.getFullName().concat("of")
                            .concat(rfeSupplierDTO.getWorkgroupName());
                    bid.setBidderName(bidderName);
                }
            });
            bid.setOwnerWorkgroupId(workgroupId);
            // save bid
            bidRepository.saveAndFlush(bid);
        } else {
            throw new NotFoundException("Rfe not found");
        }
        return ServerResponse.success();
    }

    private boolean validateBid(double bidAmount, Rfe rfe) {
        Double lowest = calculateNextLowestBid(rfe).doubleValue();
        if (lowest != null) {
            return bidAmount <= lowest.doubleValue();
        }
        return true;
    }

    /**
     * get Average Estimates Prices
     * @param rfeId, specId
     * @return average estimates price
     */
    public BigDecimal getAverageEstimatesPrice(Long rfeId, Long specId, Long optionIndex) {
        BigDecimal average = BigDecimal.ZERO;
        RfeItem rfeItem = rfeItemRepository.findByRfeIdAndSpecId(rfeId, specId);
        List<EstimateItem> estimateItems = estimateItemRepository.findByRfeItemIdAndEstStateId(rfeItem.getId(), ObjectStateID.ESTIMATE_SENT);
        if (estimateItems != null && estimateItems.size() > 0) {
            BigDecimal sum = BigDecimal.ZERO;
            List<Long> validEstItemIds = estimateItems.stream().map(EstimateItem::getId).collect(Collectors.toList());
            List<EstimateItemPrice> estimateItemPrices = estimateItemPriceRepository.findByEstimateItemIdInAndItemOption_OptionIndex(validEstItemIds, optionIndex);
            if (estimateItemPrices != null && estimateItemPrices.size() > 0) {
                for (EstimateItemPrice estimateItemPrice : estimateItemPrices) {
                    if (estimateItemPrice != null) {
                        if (optionIndex.equals(estimateItemPrice.getItemOption().getOptionIndex())) {
                            sum = sum.add(estimateItemPrice.getPrice());
                        }
                    }
                }
                average = sum.divide(BigDecimal.valueOf(estimateItemPrices.size()), 5, RoundingMode.HALF_UP);
            }
        }
        return average;
    }

}
