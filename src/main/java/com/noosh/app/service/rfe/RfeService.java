package com.noosh.app.service.rfe;

import com.noosh.app.commons.dto.estimate.EstimateDTO;
import com.noosh.app.commons.dto.rfe.RfeSupplierDTO;
import com.noosh.app.repository.mybatis.estimate.EstimateMyBatisMapper;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * User: <PERSON>
 * Date: 5/6/2018
 */
@Service
public class RfeService {

    @Inject
    private RfeSupplierService rfeSupplierService;
    @Inject
    private EstimateMyBatisMapper estimateMyBatisMapper;

    public List<RfeSupplierDTO> findDismissableSupplier(long rfeId, long projectId) {

        List<RfeSupplierDTO> dismissedSuppliers = new ArrayList<>();

        List<EstimateDTO> awardedEstimates = estimateMyBatisMapper.findAwardedEstimates(rfeId, projectId);
        List<RfeSupplierDTO> members = rfeSupplierService.findRfeSuppliers(rfeId);
        for (RfeSupplierDTO member : members) {
            if (member.isActive() || member.isPendingSupplierAcceptance()) {
                long groupId = member.getWorkgroupId().longValue();
                Optional<EstimateDTO> estimateDTO = awardedEstimates.stream().filter(awardedEstimate -> awardedEstimate.getOwnerWorkgroupId() == groupId).findFirst();
                if (!estimateDTO.isPresent()) {
                    dismissedSuppliers.add(member);
                }
            }
        }
        return dismissedSuppliers;
    }


}
