package com.noosh.app.service.customfield;

import com.noosh.app.commons.constant.PropertyTypeID;
import com.noosh.app.commons.dto.customfield.UserFieldDTO;
import com.noosh.app.commons.dto.order.ProjectOrderWidgetDTO;
import com.noosh.app.commons.entity.property.PropertyParam;
import com.noosh.app.commons.vo.invoice.InvoiceDetailVO;
import com.noosh.app.commons.vo.order.OrderGeneralInfoVO;
import com.noosh.app.feign.WorkgroupOpenFeignClient;
import com.noosh.app.repository.jpa.property.PropertyParamRepository;
import com.noosh.app.repository.mybatis.customfield.CustomFieldMyBatisMapper;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 9/23/20
 */
@Service
public class CustomFieldService {
    @Inject
    private CustomFieldMyBatisMapper customFieldMyBatisMapper;
    @Inject
    private WorkgroupOpenFeignClient workgroupOpenFeignClient;
    @Inject
    private PropertyParamRepository propertyParamRepository;

    public List<UserFieldDTO> getFieldsByWorkgroupId(Long workgroupId, Long objectClassId, boolean excludeInvisibleSupplierField) {
        return customFieldMyBatisMapper.getAllUserFieldsByWorkgroupId(workgroupId, objectClassId, excludeInvisibleSupplierField);
    }

    public void setCustomAttributes(OrderGeneralInfoVO orderGeneralInfoVO) {
        List<Long> propertyIds = new ArrayList<>();
        if (hasPropertyId(orderGeneralInfoVO.getCustomPropertyId())) {
            propertyIds.add(orderGeneralInfoVO.getCustomPropertyId());
        }
        if (orderGeneralInfoVO.getOrderItems() != null) {
            orderGeneralInfoVO.getOrderItems().forEach(orderItemVO -> {
                if (hasPropertyId(orderItemVO.getCustomPropertyId())) {
                    propertyIds.add(orderItemVO.getCustomPropertyId());
                }
            });
        }
        if (!propertyIds.isEmpty()) {
            Map<Long, Map<String, Object>> customAttributesMap = workgroupOpenFeignClient.findCustomAttribute(propertyIds);
            if (hasPropertyId(orderGeneralInfoVO.getCustomPropertyId())) {
                orderGeneralInfoVO.setCustomAttributes(customAttributesMap.get(orderGeneralInfoVO.getCustomPropertyId()));
            }
            if (orderGeneralInfoVO.getOrderItems() != null) {
                orderGeneralInfoVO.getOrderItems().forEach(orderItemVO -> {
                    if (hasPropertyId(orderItemVO.getCustomPropertyId())) {
                        orderItemVO.setCustomAttributes(customAttributesMap.get(orderItemVO.getCustomPropertyId()));
                    }
                });
            }
        }
    }

    public void setCustomAttributes(List<ProjectOrderWidgetDTO> orderList) {
        List<Long> propertyIds = new ArrayList<>();
        for (ProjectOrderWidgetDTO orderWidgetDTO : orderList) {
            if (hasPropertyId(orderWidgetDTO.getCustomPropertyId())) {
                propertyIds.add(orderWidgetDTO.getCustomPropertyId());
            }
        }

        if (!propertyIds.isEmpty()) {
            Map<Long, Map<String, Object>> customAttributesMap = workgroupOpenFeignClient.findCustomAttribute(propertyIds);
            for (ProjectOrderWidgetDTO orderWidgetDTO : orderList) {
                if (hasPropertyId(orderWidgetDTO.getCustomPropertyId())) {
                    orderWidgetDTO.setCustomAttributes(customAttributesMap.get(orderWidgetDTO.getCustomPropertyId()));
                }
            }
        }
    }

    public List<UserFieldDTO> getFieldsByWorkgroupIdForUHG(Long workgroupId, Long objectClassId, boolean excludeInvisibleSupplierField) {
        List<UserFieldDTO> userFieldDTOList = customFieldMyBatisMapper.getAllUserFieldsByWorkgroupId(workgroupId, objectClassId, excludeInvisibleSupplierField);
        //For UHG, there's additional custom field 'CCA_QTY_num' needs to be retrieved for Cost Center Creation
        PropertyParam ccaQtyParam = propertyParamRepository.findByParamNameAndPrPropertyTypeId("CCA_QTY_num", PropertyTypeID.PROPERTY_TYPE_OXF);
        if(ccaQtyParam != null){
            UserFieldDTO ccaQtyDTO = new UserFieldDTO();
            ccaQtyDTO.setParamId(ccaQtyParam.getPrPropertyParamId());
            ccaQtyDTO.setParamName(ccaQtyParam.getParamName());
            ccaQtyDTO.setLabel("Qty Allocation");
            userFieldDTOList.add(ccaQtyDTO);
        }
        return userFieldDTOList;
    }

    private boolean hasPropertyId(Long propertyId) {
        return propertyId != null && propertyId > 0;
    }
}
