package com.noosh.app.service.terms;

import com.noosh.app.commons.constant.PreferenceID;
import com.noosh.app.commons.constant.TermsTypeID;
import com.noosh.app.commons.entity.security.Workgroup;
import com.noosh.app.commons.entity.terms.AcTerms;
import com.noosh.app.commons.vo.order.TermsVO;
import com.noosh.app.repository.jpa.security.WorkgroupRepository;
import com.noosh.app.repository.terms.AcTermsRepository;
import com.noosh.app.repository.terms.TermsTypeRepository;
import com.noosh.app.service.preference.PreferenceService;
import org.springframework.stereotype.Service;

import jakarta.inject.Inject;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
public class TermsService {
    @Inject
    AcTermsRepository termsRepository;
    @Inject
    private WorkgroupRepository workgroupRepository;
    @Inject
    private TermsTypeRepository termsTypeRepository;
    @Inject
    private PreferenceService preferenceService;

    public TermsVO findCurrentTermsAndConditions(Workgroup workgroup, Long termsTypeId) {
        TermsVO termsVO = new TermsVO();
        if (workgroup != null) {
            String workgroupName = workgroup.getName();
            Long workgroupId = workgroup.getId();
            termsVO.setOwnerWorkgroupName(workgroupName);
            List<AcTerms> acTermsList = termsRepository.findByWorkgroupIdAndTermsTypeIdOrderByVersionNumberDesc(workgroupId, termsTypeId);
            String text = workgroupName + " has not entered any Terms & Conditions.";
            if (acTermsList != null && !acTermsList.isEmpty()) {
                AcTerms acTerms = acTermsList.get(0);
                if (acTerms != null && acTerms.getText() != null) {
                    text = acTerms.getText();
                }
            }
            termsVO.setText(text);
            termsVO.setIsDisplay(isTermsAndConditionsEnabled(workgroupId, termsTypeId));
        }
        return termsVO;
    }

    public TermsVO findTermAndCondition(Long termsId, Long termsTypeId, Long ownerWorkgroupId) {
        TermsVO termsVO = new TermsVO();
        if (ownerWorkgroupId != null) {
            Workgroup ownerWorkgroup = workgroupRepository.findById(ownerWorkgroupId).orElse(null);
            if (ownerWorkgroup != null) {
                String ownerWorkgroupName = ownerWorkgroup.getName();
                termsVO.setOwnerWorkgroupName(ownerWorkgroupName);

                if (termsId != null) {
                    termsRepository.findById(termsId).ifPresent(value -> termsVO.setText(value.getText()));
                } else {
                    termsVO.setText(ownerWorkgroupName + " has not entered any Terms & Conditions.");
                }
                termsTypeRepository.findById(termsTypeId)
                        .ifPresent(termsType -> termsVO.setTermTypeDescriptionStrId(termsType.getDescriptionStrId() != null ?
                                termsType.getDescriptionStrId().toString() : null));
                termsVO.setIsDisplay(isTermsAndConditionsEnabled(ownerWorkgroupId, termsTypeId));
            }
        }
        return termsVO;
    }

    private boolean isTermsAndConditionsEnabled(Long workgroupId, Long termsTypeId) {
        Map<String, String> groupPrefs = preferenceService.findGroupPrefs(workgroupId, Arrays.asList(PreferenceID.WORKGROUP_OPTION_ORDER_TC, PreferenceID.WORKGROUP_OPTION_PURCHASE_TC, PreferenceID.WORKGROUP_OPTION_RFE_TC, PreferenceID.WORKGROUP_OPTION_ESTIMATE_TC, PreferenceID.WORKGROUP_OPTION_INVOICE_TC, PreferenceID.WORKGROUP_OPTION_PROPOSAL_TC));
        if (termsTypeId == TermsTypeID.SUPPLIER_SELL) {
            return preferenceService.check(PreferenceID.WORKGROUP_OPTION_ORDER_TC, groupPrefs);
        } else if (termsTypeId == TermsTypeID.BUYER_PURCHASE) {
            return preferenceService.check(PreferenceID.WORKGROUP_OPTION_PURCHASE_TC, groupPrefs);
        } else if (termsTypeId == TermsTypeID.BUYER_RFE) {
            return preferenceService.check(PreferenceID.WORKGROUP_OPTION_RFE_TC, groupPrefs);
        } else if (termsTypeId == TermsTypeID.SUPPLIER_ESTIMATE) {
            return preferenceService.check(PreferenceID.WORKGROUP_OPTION_ESTIMATE_TC, groupPrefs);
        } else if (termsTypeId == TermsTypeID.INVOICE) {
            return preferenceService.check(PreferenceID.WORKGROUP_OPTION_INVOICE_TC, groupPrefs);
        } else if (termsTypeId == TermsTypeID.PROPOSAL) {
            return preferenceService.check(PreferenceID.WORKGROUP_OPTION_PROPOSAL_TC, groupPrefs);
        }
        return false;
    }
}
