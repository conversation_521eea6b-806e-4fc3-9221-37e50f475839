package com.noosh.app.service.comparator;

import com.noosh.app.commons.dto.order.OrderVersionDTO;
import java.time.LocalDateTime;

import java.util.Comparator;

public class AcceptDateComparator implements Comparator {

    public int compare(Object obj1, Object obj2) {
        LocalDateTime date1 = ((OrderVersionDTO)obj1).getAcceptDate();
        LocalDateTime date2 = ((OrderVersionDTO)obj2).getAcceptDate();
       
        if(date2==null){
            return 0;
        }else{
            return (date2.compareTo(date1));
        }
    }
}