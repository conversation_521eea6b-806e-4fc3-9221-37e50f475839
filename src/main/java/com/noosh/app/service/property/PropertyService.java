package com.noosh.app.service.property;

import com.noosh.app.commons.constant.DataTypeID;
import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.PropertyTypeID;
import com.noosh.app.commons.entity.property.Property;
import com.noosh.app.commons.entity.property.PropertyAttribute;
import com.noosh.app.commons.entity.property.PropertyParam;
import com.noosh.app.commons.vo.property.PropertyAttributeVO;
import com.noosh.app.exception.UnexpectedException;
import com.noosh.app.repository.jpa.property.PropertyAttributeRepository;
import com.noosh.app.repository.jpa.property.PropertyParamRepository;
import com.noosh.app.repository.jpa.property.PropertyRepository;
import com.noosh.app.service.preference.ParamNameUtil;
import java.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;


@Service
@Transactional
public class PropertyService {
    private final Logger log = LoggerFactory.getLogger(PropertyService.class);

    @Autowired
    private PropertyRepository propertyRepository;

    @Autowired
    private PropertyAttributeRepository propertyAttributeRepository;

    @Autowired
    private PropertyParamRepository propertyParamRepository;

    public Map<String, String> getMyDeskoidFilter(Long userId, Map<String, String> deskoidMap) {
        //First load user property
        Property userProperty = propertyRepository.findByObjectIdAndObjectClassIdAndPrPropertyTypeId(userId,
                ObjectClassID.ACCOUNT_USER, PropertyTypeID.PROPERTY_TYPE_PREFERENCE);
        Map<String, String> deskoidValueMap = new HashMap();

        for (Map.Entry<String, String> entry : deskoidMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            if (userProperty != null) {
                //Find Current Layout if has then do update, otherwise insert it
                Optional<PropertyAttribute> layoutAttributeO = userProperty.getPropertyAttributeSet().stream().filter(attribute ->
                        attribute.getPropertyParam().getParamName().equalsIgnoreCase(value)).findFirst();
                if (layoutAttributeO.isPresent()) {
                    PropertyAttribute layoutAttribute = layoutAttributeO.get();
                    deskoidValueMap.put(key, layoutAttribute.getStringValue());
                } else {
                    deskoidValueMap.put(key, null);
                }
            } else {
                deskoidValueMap.put(key, null);
            }
        }
        return deskoidValueMap;
    }


    //This is user level
    public void saveMyDeskoidLayout(String updated, String preferenceId, Long userId) throws Exception {
        saveMyDeskoidLayout(updated, preferenceId, userId, ObjectClassID.ACCOUNT_USER, userId);
    }

    // TODO: rename this to saveUserPreference
    public void saveMyDeskoidLayout(String updated, String preferenceId, Long objectId, Long objectClassId, Long userId) throws Exception {
        //First load user property
        Property userProperty = propertyRepository.findByObjectIdAndObjectClassIdAndPrPropertyTypeId(objectId,
                objectClassId, PropertyTypeID.PROPERTY_TYPE_PREFERENCE);
        if (userProperty != null) {
            //Find Current Layout if has then do update, otherwise insert it
            Optional<PropertyAttribute> layoutAttributeO = userProperty.getPropertyAttributeSet().stream().filter(attribute ->
                    attribute.getPropertyParam().getParamName().equalsIgnoreCase(preferenceId)).findFirst();
            if (layoutAttributeO.isPresent()) {
                PropertyAttribute layoutAttribute = layoutAttributeO.get();
                layoutAttribute.setStringValue(updated);
                propertyAttributeRepository.save(layoutAttribute);
            } else {
                insertPropertyAttForPreference(userProperty, updated, preferenceId, userId);
            }
        } else {
            // Insert user property
            Property newProperty = new Property();
            newProperty.setObjectClassId(objectClassId);
            newProperty.setObjectId(objectId);
            newProperty.setPropertyName("PROPS");
            newProperty.setPrPropertyTypeId(PropertyTypeID.PROPERTY_TYPE_PREFERENCE);
            newProperty.setCreateUserId(userId);
            propertyRepository.saveAndFlush(newProperty);
            insertPropertyAttForPreference(newProperty, updated, preferenceId, userId);
        }
    }


    public void insertPropertyAttForPreference(Property property, String stringValue, String paramName, Long userId) throws Exception {
        PropertyParam propertyParam = propertyParamRepository.findByParamNameAndPrPropertyTypeId(paramName,
                PropertyTypeID.PROPERTY_TYPE_PREFERENCE);

        if (propertyParam == null) {
            //Insert user preference property Param
            PropertyParam newPropertyParam = new PropertyParam();
            newPropertyParam.setParamName(paramName);
            newPropertyParam.setPrPropertyTypeId(PropertyTypeID.PROPERTY_TYPE_PREFERENCE);
            newPropertyParam.setIsReportField((short) 0);
            newPropertyParam.setPrDataTypeId(DataTypeID.STRING);
            newPropertyParam.setCreateUserId(userId);
            propertyParam = propertyParamRepository.saveAndFlush(newPropertyParam);
        }

        PropertyAttribute newPro = new PropertyAttribute();
        newPro.setPrPropertyId(property.getId());
        newPro.setPrPropertyParamId(propertyParam.getPrPropertyParamId());
        newPro.setStringValue(stringValue);
        newPro.setCreateUserId(userId);
        propertyAttributeRepository.save(newPro);
    }

    /**
     * Find User property attribute
     *
     * @param objectId
     * @param objectClassId
     * @param propertyTypeId
     * @param preferenceId
     * @return
     */
    public PropertyAttributeVO findPropertyAttribute(Long objectId, Long objectClassId, Long propertyTypeId, final String preferenceId) {
        //First load user property
        Property userProperty = propertyRepository.findByObjectIdAndObjectClassIdAndPrPropertyTypeId(objectId,
                objectClassId, propertyTypeId);
        if (userProperty != null) {
            //Find Current PropertyAttribute
            Optional<PropertyAttribute> propertyAttributeOptional = userProperty.getPropertyAttributeSet().stream().filter(attribute ->
                    attribute.getPropertyParam().getParamName().equalsIgnoreCase(ParamNameUtil.getPreference(preferenceId))).findFirst();
            if (propertyAttributeOptional.isPresent()) {
                PropertyAttribute propertyAttribute = propertyAttributeOptional.get();
                PropertyAttributeVO propertyAttributeVO = new PropertyAttributeVO();
                propertyAttributeVO.setPrPropertyAttributeId(propertyAttribute.getId());
                propertyAttributeVO.setDateValue(propertyAttribute.getDateValue());
                propertyAttributeVO.setNumberValue(propertyAttribute.getNumberValue());
                propertyAttributeVO.setStringValue(propertyAttribute.getStringValue());
                return propertyAttributeVO;
            }
        }
        return null;
    }

    /**
     * Update User property attribute
     *
     * @param objectId
     * @param objectClassId
     * @param propertyTypeId
     * @param preferenceId
     * @param propertyAttributeVO
     * @param userId              create or mod user Id
     */
    public void updatePropertyAttribute(Long objectId, Long objectClassId, Long propertyTypeId,
                                        String preferenceId, PropertyAttributeVO propertyAttributeVO,
                                        Long userId) {

        Property userProperty = propertyRepository.findByObjectIdAndObjectClassIdAndPrPropertyTypeId(objectId,
                objectClassId, propertyTypeId);
        PropertyParam propertyParam = propertyParamRepository.findByParamNameAndPrPropertyTypeId(ParamNameUtil.getPreference(preferenceId), propertyTypeId);
        if (propertyParam == null) {
            //Insert user property Param
            PropertyParam newPropertyParam = new PropertyParam();
            newPropertyParam.setParamName(ParamNameUtil.getPreference(preferenceId));
            newPropertyParam.setPrPropertyTypeId(propertyTypeId);
            newPropertyParam.setIsReportField((short) 0);
            newPropertyParam.setPrDataTypeId(dataTypeIdOf(propertyAttributeVO.getStringValue()));
            newPropertyParam.setCreateUserId(userId);
            propertyParamRepository.saveAndFlush(newPropertyParam);
        }
        if (userProperty != null) {
            //Find Current PropertyAttribute
            Optional<PropertyAttribute> propertyAttributeOptional = userProperty.getPropertyAttributeSet().stream().filter(attribute ->
                    attribute.getPropertyParam().getParamName().equalsIgnoreCase(ParamNameUtil.getPreference(preferenceId))).findFirst();
            // update property attribute
            if (propertyAttributeOptional.isPresent()) {
                PropertyAttribute propertyAttribute = propertyAttributeOptional.get();
                propertyAttribute.setStringValue(propertyAttributeVO.getStringValue());
                propertyAttribute.setModUserId(userId);
                propertyAttribute.setModDate(LocalDateTime.now());
                propertyAttributeRepository.save(propertyAttribute);
            } else if (!propertyAttributeOptional.isPresent()) {
                // Insert user property attribute
                this.insertpropertyAttribute(userProperty.getId(),
                        propertyTypeId, preferenceId, propertyAttributeVO, userId);
            }
        } else {
            // Insert user property
            Property newProperty = new Property();
            newProperty.setObjectClassId(objectClassId);
            newProperty.setObjectId(objectId);
            newProperty.setPropertyName("PROPS");
            newProperty.setPrPropertyTypeId(propertyTypeId);
            newProperty.setCreateUserId(userId);
            propertyRepository.saveAndFlush(newProperty);
            // Insert user property attribute
            this.insertpropertyAttribute(newProperty.getId(),
                    propertyTypeId, preferenceId, propertyAttributeVO, userId);
        }
    }

    /**
     * Insert user property attribute
     *
     * @param propertyId
     * @param propertyTypeId
     * @param preferenceId
     * @param propertyAttributeVO
     */
    private void insertpropertyAttribute(Long propertyId, Long propertyTypeId,
                                         String preferenceId, PropertyAttributeVO propertyAttributeVO, Long userId) {
        //Find property param
        PropertyParam propertyParam = propertyParamRepository.findByParamNameAndPrPropertyTypeId(
                ParamNameUtil.getPreference(preferenceId), propertyTypeId);
        PropertyAttribute propertyAttribute = new PropertyAttribute();
        propertyAttribute.setPrPropertyId(propertyId);
        propertyAttribute.setPrPropertyParamId(propertyParam.getPrPropertyParamId());
        propertyAttribute.setStringValue(propertyAttributeVO.getStringValue());
        propertyAttribute.setDateValue(propertyAttributeVO.getDateValue());
        propertyAttribute.setNumberValue(propertyAttributeVO.getNumberValue());
        propertyAttribute.setCreateUserId(userId);
        propertyAttributeRepository.save(propertyAttribute);
    }

    /**
     * Derives the data type ID as defined in pr_data_type of a generic object
     * used as the value in a pr_property_attribute record.
     *
     * @param value Input object to check.
     * @return The data type ID of the given object.
     * @throws UnexpectedException If value is null or an unsupported type.
     * @see com.noosh.app.commons.constant.DataTypeID
     */
    public static final long dataTypeIdOf(Object value) {
        long dataTypeId;
        if (value instanceof Long) {
            dataTypeId = DataTypeID.LONG;
        } else if (value instanceof Double) {
            dataTypeId = DataTypeID.DOUBLE;
        } else if (value instanceof Boolean) {
            dataTypeId = DataTypeID.BOOLEAN;
        } else if (value instanceof Date) {
            dataTypeId = DataTypeID.DATE;
        } else if (value instanceof String) {
            dataTypeId = DataTypeID.STRING;
        } else { // null or unsupported object
            throw new UnexpectedException("Unsupported object: " + ((value != null) ? value.getClass().getName() : value));
        }
        return dataTypeId;
    }

}

