package com.noosh.app.service.message;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.noosh.app.collaboration.nfc.bean.AnonymousBean;
import com.noosh.app.collaboration.nfc.bean.Bean;
import com.noosh.app.collaboration.nfc.security.Permission;
import com.noosh.app.collaboration.nfc.security.TeamBean;
import com.noosh.app.collaboration.nfc.security.User;
import com.noosh.app.collaboration.nts.CollaborationEventService;
import com.noosh.app.collaboration.nts.ItemAccessControl;
import com.noosh.app.commons.constant.*;
import com.noosh.app.commons.dto.apijob.builder.ScheduledApiDtoBuilder;
import com.noosh.app.commons.dto.message.MessageCountDTO;
import com.noosh.app.commons.dto.message.MessageDTO;
import com.noosh.app.commons.dto.permission.PermittedEntities;
import com.noosh.app.commons.dto.project.ProjectDTO;
import com.noosh.app.commons.dto.role.RoleDTO;
import com.noosh.app.commons.dto.team.TeamDTO;
import com.noosh.app.commons.dto.team.TeamMemberDTO;
import com.noosh.app.commons.entity.account.AccountUser;
import com.noosh.app.commons.entity.message.Message;
import com.noosh.app.commons.entity.message.MessageStatus;
import com.noosh.app.commons.entity.security.ObjectAccessControl;
import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.commons.vo.message.*;
import com.noosh.app.commons.vo.team.TeamMemberVO;
import com.noosh.app.commons.vo.team.TeamVO;
import com.noosh.app.exception.BadRequestException;
import com.noosh.app.exception.NotFoundException;
import com.noosh.app.exception.UnexpectedException;
import com.noosh.app.repository.account.AccountUserRepository;
import com.noosh.app.repository.message.MessageRepository;
import com.noosh.app.repository.message.MessageStatusRepository;
import com.noosh.app.repository.mybatis.MessageMyBatisMapper;
import com.noosh.app.repository.mybatis.role.RoleMyBatisMapper;
import com.noosh.app.repository.mybatis.security.ObjectAccessControlMyBatisMapper;
import com.noosh.app.repository.mybatis.team.TeamMyBatisMapper;
import com.noosh.app.repository.security.ObjectStateRepository;
import com.noosh.app.service.MessageService;
import com.noosh.app.service.preference.PreferenceService;
import com.noosh.app.service.project.ProjectService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.util.NooshOneUrlUtil;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.inject.Inject;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @auther mario
 * @date 8/19/2020
 */
@Service
@Transactional(readOnly = true)
public class MessageDetailService {

    private final Logger log = LoggerFactory.getLogger(MessageDetailService.class);

    @Inject
    private ProjectService projectService;
    @Inject
    private MessageService messageService;
    @Inject
    private PreferenceService preferenceService;
    @Inject
    private MessageMyBatisMapper messageMyBatisMapper;
    @Inject
    private TeamMyBatisMapper teamMyBatisMapper;
    @Inject
    private RoleMyBatisMapper roleMyBatisMapper;
    @Inject
    private ObjectAccessControlMyBatisMapper objectAccessControlMyBatisMapper;
    @Inject
    private MessageRepository messageRepository;
    @Inject
    private MessageStatusRepository messageStatusRepository;
    @Inject
    private AccountUserRepository accountUserRepository;
    @Inject
    private ObjectStateRepository objectStateRepository;
    @Inject
    private CollaborationEventService collaborationEventService;
    @Inject
    private PermissionService permissionService;

    @Transactional
    public String sendMessage(Long userId, Long projectId, MessageCreateVO createVO) {
        validateMessageCreateVO(projectId, createVO);

        ProjectDTO project = projectService.findProjectById(projectId);

        Message message = new Message();
        message.setSubject(createVO.getSubject());
        if (createVO.getContent() != null && createVO.getContent().getBytes().length > 4000) {
            throw new UnexpectedException("Content length cannot exceed 4000 characters");
        }
        message.setContent(createVO.getContent());
        message.setPriorityStrId(createVO.getPriorityId());
        message.setAuthorUserId(userId);
        message.setCreateUserId(userId);
        message.setPostedDate(LocalDateTime.now());
        message = messageRepository.saveAndFlush(message);

        Message rootMessage;
        if (createVO.getMessageId() == null) {
            // create message
            message.setRootSubject(createVO.getSubject());
            message.setRootMessageId(message.getId());
        } else {
            rootMessage = messageRepository.findById(createVO.getMessageId()).orElse(null);
            if (rootMessage == null) {
                throw new NotFoundException("messageId not found!");
            }
            // reply message
            message.setReplyMessageId(createVO.getMessageId());
            message.setRootMessageId(rootMessage.getRootMessageId());
            message.setRootSubject(rootMessage.getRootSubject());
        }

        messageRepository.saveAndFlush(message);
        // message creator by default has a "read" status on the message
        MessageStatus messageStatus = new MessageStatus();
        messageStatus.setObjectStateId(ObjectStateID.MESSAGE_READ);
        messageStatus.setMessageId(message.getId());
        messageStatus.setReceiverUserId(userId);
        messageStatus.setCreateUserId(userId);
        messageStatusRepository.saveAndFlush(messageStatus);

        ItemAccessControl control = new ItemAccessControl();
        control.setAttributes(createVO, new Permission(PermissionID.VIEW_MESSAGE));
        control.addGroup(project.getOwnerWorkgroupId());
        control.addPermission(new User(message.getAuthorUserId()), new Permission(PermissionID.VIEW_MESSAGE));

        Bean parent = new AnonymousBean(project.getId(), ObjectClassID.OBJECT_CLASS_PROJECT);
        Bean messageBean = new AnonymousBean(message.getId(), ObjectClassID.MESSAGE);
        collaborationEventService.processPostItemEvent(userId, parent, messageBean, control);
        messageService.notifyRecipients(userId, message.getId(), projectId);

        return NooshOneUrlUtil.composeMessageListLinkToEnterprise(projectId);
    }

    private void validateMessageCreateVO(Long projectId, MessageCreateVO createVO) {
        Set<String> sendToUsers = new HashSet<>();
        List<TeamVO> teamVOList = createVO.getSendTo();
        Map<String, String> sentToMap = new HashMap<>();
        for (TeamVO teamVO : teamVOList) {
            List<TeamMemberVO> teamMemberVOList = teamVO.getTeamMembers();
            for (TeamMemberVO teamMemberVO : teamMemberVOList) {
                final String memberKey = teamVO.getTeamId() + "_" + teamMemberVO.getUserId();
                sendToUsers.add(memberKey);
                sentToMap.put(memberKey, teamMemberVO.getFirstName() + " " + teamMemberVO.getLastName());
            }
        }

        List<TeamMemberDTO> teamMemberDTOS = teamMyBatisMapper.findAllTeamMembers(projectId);
        Set<String> allowedUsers = new HashSet<>();
        teamMemberDTOS.forEach(teamMemberDTO -> {
            allowedUsers.add(teamMemberDTO.getTeamId() + "_" + teamMemberDTO.getUserId());
        });

        if (!allowedUsers.containsAll(sendToUsers)) {
            List<String> invalidUsers = new ArrayList<>();
            for (String memberKey : sentToMap.keySet()) {
                if (!allowedUsers.contains(memberKey)) {
                    invalidUsers.add(sentToMap.get(memberKey));
                }
            }
            String names = String.join(", ", invalidUsers);
            Map<String, String> params = new HashMap<>();
            params.put("invalidUserNames", names);
            throw new BadRequestException("SEND_MESSAGE_INVALID_SENT_TO_USER", params);
        }

    }

    public MessageDetailVO detailMessage(Long userId, Long workgroupId, Long projectId, Long messageId) throws Exception {
        ProjectDTO projectDTO = projectService.findProjectById(projectId);
        boolean displayAliasName = false;
        if (projectDTO.isSupplierProject()) {
            ProjectDTO buyerProjectDTO = projectService.findProjectById(projectDTO.getMasterProjectId());
            displayAliasName = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ENABLE_DISPLAY_ALIAS_NAME, buyerProjectDTO.getOwnerWorkgroupId());
        }

        MessageDetailVO detailVO = new MessageDetailVO();
        detailVO.setProjectName(projectDTO.getProjectTitle());
        detailVO.setProjectExternalLink(NooshOneUrlUtil.composeGotoProjectHomeLinkToEnterprise(projectId));
        detailVO.setMessageListLink(NooshOneUrlUtil.composeMessageListLinkToEnterprise(projectId));
        if (messageId == null) {
            return detailVO;
        }
        Message message = messageRepository.findById(messageId).orElse(null);
        if (message == null) {
            throw new NotFoundException("messageId not found");
        }

        boolean allowWgPermissionOverride = permissionService.checkAll(PermissionID.VIEW_MESSAGE, workgroupId, userId, projectId);
        getPrevAndNextButtons(userId, workgroupId, projectId, message.getRootMessageId(), detailVO, allowWgPermissionOverride);

        List<Long> projectIds = teamMyBatisMapper.findProjectIds(projectId);
        List<TeamDTO> allowedTeams = teamMyBatisMapper.findCollaborationTeamMembers(projectId, projectIds);

        List<MessageDTO> messages = messageMyBatisMapper.findMessagesByRootMessageId(userId, workgroupId, projectId, message.getRootMessageId(), allowWgPermissionOverride);

        List<MessageInfoVO> infoVOList = new ArrayList<>();
        for (MessageDTO messageDTO : messages) {
            MessageInfoVO infoVO = new MessageInfoVO();
            boolean displayAliasNameForCurrent = displayAliasName && messageDTO.getCreatorWorkgroupId().longValue() != workgroupId;
            infoVO.setMessageId(messageDTO.getId());
            infoVO.setFromUserId(messageDTO.getUserId());
            infoVO.setFromName(displayAliasNameForCurrent ? messageDTO.getAliasFullName() : messageDTO.getFullName());
            infoVO.setFromWorkgroup(messageDTO.getWorkgroupName());
            String back = EnterpriseLinkDestination.VIEW_MESSAGE_DETAIL + "?objectClassId=" + ObjectClassID.OBJECT_CLASS_PROJECT + "&objectId=" + projectId + "&messId=" + messageDTO.getId();
            infoVO.setFromExternalLink(displayAliasNameForCurrent ? "" : NooshOneUrlUtil.composeViewPersonalInfoLinkWithBackToEnterprise(messageDTO.getPersonId(), back));
            infoVO.setSendOn(messageDTO.getPostedDateTime());
//            infoVO.setToName(getRecipientList(messageDTO.getId(), allowedTeams, locale, displayAliasName, workgroupId));
            infoVO.setToNameStr(getRecipientListStr(messageDTO.getId(), allowedTeams, displayAliasName, workgroupId));

            infoVO.setPriorityId(messageDTO.getPriorityStringId());
            infoVO.setSubject(messageDTO.getSubject());
            Long statusId = messageDTO.getStateId() != null ? messageDTO.getStateId() : ObjectStateID.MESSAGE_UNREAD;
            infoVO.setStatusId(statusId);
            ObjectState objectState = objectStateRepository.findById(statusId).orElse(null);
            infoVO.setStatusStrId(objectState != null && objectState.getDescriptionStrId() != null
                    ? objectState.getDescriptionStrId().toString() : null);
            infoVO.setContent(messageDTO.getContent());

            PermittedEntities recipients = getPermittedEntities(messageId);
            List<Long> userIds = recipients.getUserIds();
            List<Long> teamIds = recipients.getTeamIds();
            //populate list of users
            HashSet allowedUids = new HashSet();
            for (TeamDTO team : allowedTeams) {
                List<TeamMemberDTO> members = team.getMembers();
                for (TeamMemberDTO member : members) {
                    allowedUids.add(member.getUserId());
                    if (messageDTO.getUserId().longValue() == member.getUserId()) {
                        infoVO.setFromTeamId(team.getTeamId());
                    }
                }
            }

            HashSet uidSet = new HashSet();
            for (Long recipUserId : userIds)
            {
                //ensure current user can view this User as a team member
                if (allowedUids.contains(recipUserId))
                    uidSet.add(recipUserId);
            }
            userIds = new ArrayList<>(uidSet);
            List<TeamVO> teams = new ArrayList<>();
            for (TeamDTO teamDTO : allowedTeams) {
                TeamVO teamVO = new TeamVO();
                List<TeamMemberVO> members = new ArrayList<>();
                for (Long teamId : teamIds) {
                    if (teamId.longValue() == teamDTO.getTeamId()) {
                        teamVO.setIsAllTeamMembersChecked(true);
                        for (TeamMemberDTO teamMemberDTO : teamDTO.getMembers()) {
                            TeamMemberVO memberVO = new TeamMemberVO();
                            displayAliasNameForCurrent = displayAliasName &&
                                    teamMemberDTO.getWorkgroupId().longValue() != workgroupId && teamMemberDTO.hasAliasName();
                            memberVO.setUserId(teamMemberDTO.getUserId());
                            memberVO.setFirstName(displayAliasNameForCurrent ? teamMemberDTO.getAliasName() : teamMemberDTO.getFirstName());
                            memberVO.setLastName(displayAliasNameForCurrent ? "" : teamMemberDTO.getLastName());
                            memberVO.setEmail(teamMemberDTO.getEmailAddress());
                            memberVO.setWorkgroup(teamMemberDTO.getWorkgroupName());
                            memberVO.setCompanyName(teamMemberDTO.getCompanyName());
                            members.add(memberVO);
                        }
                    }
                }
                if(members.isEmpty()) {
                    for (TeamMemberDTO teamMemberDTO : teamDTO.getMembers()) {
                        for (Long recipientUserId : userIds) {
                            if (recipientUserId.longValue() == teamMemberDTO.getUserId()) {
                                TeamMemberVO memberVO = new TeamMemberVO();
                                displayAliasNameForCurrent = displayAliasName &&
                                        teamMemberDTO.getWorkgroupId().longValue() != workgroupId && teamMemberDTO.hasAliasName();
                                memberVO.setUserId(teamMemberDTO.getUserId());
                                memberVO.setFirstName(displayAliasNameForCurrent ? teamMemberDTO.getAliasName() : teamMemberDTO.getFirstName());
                                memberVO.setLastName(displayAliasNameForCurrent ? "" : teamMemberDTO.getLastName());
                                memberVO.setEmail(teamMemberDTO.getEmailAddress());
                                memberVO.setWorkgroup(teamMemberDTO.getWorkgroupName());
                                memberVO.setCompanyName(teamMemberDTO.getCompanyName());
                                members.add(memberVO);
                            }
                        }
                    }
                }
                if(members.size() > 0) {
                    teamVO.setTeamId(teamDTO.getTeamId());
                    teamVO.setTeamName(teamDTO.getWorkgroupName());
                    teamVO.setIsSupplier(teamDTO.getWorkgroupTypeId() == WorkgroupTypeID.SUPPLIER);
                    teamVO.setIsClient(isClientWorkgroup(teamDTO.getWorkgroupId()));
                    teamVO.setTeamMembers(members);
                    teams.add(teamVO);
                }
            }
            infoVO.setSendTo(teams);
            infoVOList.add(infoVO);
        }

        detailVO.setMessages(infoVOList);
        return detailVO;
    }

    private void getPrevAndNextButtons(Long userId, Long workgroupId,
                                       Long projectId, Long currMsgId,
                                       MessageDetailVO detailVO,
                                       boolean allowWgPermissionOverride) {
        MessageListFilter messageListFilter = messageService.getMessageListFilter(workgroupId, userId);
        Long statusFilter = ObjectStateID.MESSAGE_UNREAD;
        if (messageListFilter.getIsShowAll()) {
            statusFilter = -1L;
        } else if (messageListFilter.getIsShowUnread()) {
            statusFilter = 2000100L;
        } else if (messageListFilter.getIsShowFollowUp()) {
            statusFilter = 2000101L;
        } else if (messageListFilter.getIsShowRead()) {
            statusFilter = 2000102L;
        } else if (messageListFilter.getIsShowArchived()) {
            statusFilter = 2000103L;
        }
        List<MessageCountDTO> rootMessages = messageMyBatisMapper.getRootMessageByProjectId(userId, workgroupId, projectId, statusFilter, allowWgPermissionOverride);

        int i=0;
        for (; i<rootMessages.size();i++)
        {
            if (currMsgId.longValue()  == rootMessages.get(i).getRootMessageId())
                break;
        }
        if (i > 0) {
            detailVO.setPrevMessageLink(NooshOneUrlUtil.getMessageLink(projectId, rootMessages.get(i-1).getRootMessageId(), userId));
            detailVO.setPrevMessageId(rootMessages.get(i-1).getRootMessageId());
        }

        if (i < rootMessages.size() -1) {
            detailVO.setNextMessageLink(NooshOneUrlUtil.getMessageLink(projectId, rootMessages.get(i+1).getRootMessageId(), userId));
            detailVO.setNextMessageId(rootMessages.get(i+1).getRootMessageId());
        }
    }

    private String getRecipientListStr(Long messageId, List<TeamDTO> allowedTeams, boolean displayAliasName, long workgroupId) {

        PermittedEntities recipients = getPermittedEntities(messageId);

        StringBuffer retVal = new StringBuffer();

        Map<Long, TeamDTO> teamsMap = new HashMap();
        for (TeamDTO team : allowedTeams)
            teamsMap.put(team.getTeamId(), team);

        //populate list of teams:
        List<Long> teamIds = recipients.getTeamIds();
        String teamStr = "{" + String.valueOf(StringID.DESKOID_TITLE_TEAM) + "}";
        for (Long teamId : teamIds) {
            TeamDTO team = teamsMap.get(teamId);
            if (team == null)
                continue;
            retVal.append(teamStr);
            retVal.append(" ");
            retVal.append(team.getWorkgroupName());
            retVal.append(", ");
        }

        //populate list of roles:
        List<Long> roleIds = recipients.getRoleIds();
        if (roleIds.isEmpty()) {
            roleIds.add(-1L);
        }
        String allStr = "{" + String.valueOf(StringID.ALL) + "}";
        List<RoleDTO> roles = roleMyBatisMapper.findRolesByRoleId(roleIds);
        for (RoleDTO role : roles) {
            retVal.append(allStr);
            retVal.append(" ");
            retVal.append(role.getDescription() );
            retVal.append(", ");
        }

        //populate list of users
        HashSet allowedUids = new HashSet();
        for (TeamDTO team : allowedTeams) {
            List<TeamMemberDTO> members = team.getMembers();
            for (TeamMemberDTO member : members)
                allowedUids.add(member.getUserId());
        }

        List<Long> userIds = recipients.getUserIds();
        HashSet uidSet = new HashSet();
        for (Long userId : userIds)
        {
            //ensure current user can view this User as a team member
            if (allowedUids.contains(userId))
                uidSet.add(userId);
        }
        retVal.append(getRecipientUsers(uidSet, displayAliasName, workgroupId));

        if (retVal.length() >= 2)
            return retVal.substring(0,retVal.length()-2);
        else
            return null;
    }

//    private String getRecipientList(Long messageId, List<TeamDTO> allowedTeams, String locale, boolean displayAliasName, long workgroupId) {
//
//        PermittedEntities recipients = getPermittedEntities(messageId);
//
//        StringBuffer retVal = new StringBuffer();
//
//        Map<Long, TeamDTO> teamsMap = new HashMap();
//        for (TeamDTO team : allowedTeams)
//            teamsMap.put(team.getTeamId(), team);
//
//        //populate list of teams:
//        List<Long> teamIds = recipients.getTeamIds();
//        String teamStr = i18NUtils.getMessage(StringID.DESKOID_TITLE_TEAM, locale);
//        for (Long teamId : teamIds) {
//            TeamDTO team = teamsMap.get(teamId);
//            if (team == null)
//                continue;
//            retVal.append(teamStr);
//            retVal.append(" ");
//            retVal.append(team.getWorkgroupName());
//            retVal.append(", ");
//        }
//
//        //populate list of roles:
//        List<Long> roleIds = recipients.getRoleIds();
//        if (roleIds.isEmpty()) {
//            roleIds.add(-1L);
//        }
//        String allStr = i18NUtils.getMessage(StringID.ALL, locale);
//        List<RoleDTO> roles = roleMyBatisMapper.findRolesByRoleId(roleIds);
//        for (RoleDTO role : roles) {
//            retVal.append(allStr);
//            retVal.append(" ");
//            retVal.append(role.getDescription() );
//            retVal.append(", ");
//        }
//
//        //populate list of users
//        HashSet allowedUids = new HashSet();
//        for (TeamDTO team : allowedTeams) {
//            List<TeamMemberDTO> members = team.getMembers();
//            for (TeamMemberDTO member : members)
//                allowedUids.add(member.getUserId());
//        }
//
//        List<Long> userIds = recipients.getUserIds();
//        HashSet uidSet = new HashSet();
//        for (Long userId : userIds)
//        {
//            //ensure current user can view this User as a team member
//            if (allowedUids.contains(userId))
//                uidSet.add(userId);
//        }
//        retVal.append(getRecipientUsers(uidSet, displayAliasName, workgroupId));
//
//        if (retVal.length() >= 2)
//            return retVal.substring(0,retVal.length()-2);
//        else
//            return null;
//    }

    public PermittedEntities getPermittedEntities(Long messageId) {
        // query to get entities that can perform given action on given object instance
        List<ObjectAccessControl> objectAccessControls = objectAccessControlMyBatisMapper.findPermittedEntities(PermissionID.VIEW_MESSAGE, messageId);

        PermittedEntities permittedEntities = new PermittedEntities();
        for (ObjectAccessControl objectAccessControl : objectAccessControls) {
            boolean teamAccess = objectAccessControl.getTeamAccess() != null
                    && objectAccessControl.getTeamAccess() == 1 ? true : false;
            if (teamAccess) {
                Long teamId = objectAccessControl.getTmTeamId();
                if (teamId !=null && teamId > 0)
                    permittedEntities.addTeamId(teamId);
            } else {
                Long workgroupId = objectAccessControl.getAcWorkgroupId();
                if (workgroupId !=null && workgroupId > 0)
                    permittedEntities.addWorkgroupId(workgroupId);
                Long roleId = objectAccessControl.getReRoleId();
                if (roleId !=null && roleId > 0)
                    permittedEntities.addRoleId(roleId);
                Long userId = objectAccessControl.getUserId();
                if (userId !=null && userId > 0)
                    permittedEntities.addUserId(userId);
            }
        }

        return permittedEntities;
    }

    private String getRecipientUsers(Set uidSet, boolean displayAliasName, long workgroupId) {
        StringBuffer retVal = new StringBuffer();
        List<Long> uids = new ArrayList<>();
        Iterator iterator = uidSet.iterator();
        while(iterator.hasNext())
            uids.add((Long)iterator.next());

        List<AccountUser> users = accountUserRepository.findAllById(uids);
        for(AccountUser user : users) {
            boolean showAliasName = displayAliasName && user.getWorkgroupId() != workgroupId
                    && StringUtils.isNotEmpty(user.getPerson().getAliasName());
            retVal.append(showAliasName ? user.getPerson().getAliasName() : user.getPerson().getFullName());
            retVal.append(", ");
        }
        return retVal.toString();
    }

    public boolean isOutSourcingWorkgroup(long workgroupId) {
        boolean outSourcingOptVal = preferenceService.check(PreferenceID.WORKGROUP_OPTION_OUTSOURCING, workgroupId);
        boolean brokerOptVal = preferenceService.check(PreferenceID.WORKGROUP_OPTION_BROKERING, workgroupId);
        boolean outSourcingOpt = outSourcingOptVal || brokerOptVal;
        return outSourcingOpt;
    }

    public boolean isClientWorkgroup(long workgroupId) {
        boolean clientOptVal = preferenceService.check(PreferenceID.WORKGROUP_OPTION_CLIENT, workgroupId);
        return clientOptVal;
    }

    /**
     * Update one message.
     * Currently only for Status and Priority
     */
    @Transactional
    public void updateMessage(Long userId, Long messageId, MessageUpdateVO updateVO) {
        Message message = messageRepository.findById(messageId).orElse(null);
        if (message == null) {
            throw new NotFoundException("messageId not found");
        }

        // update the priority
        if (updateVO.getMessagePriorityId() != null) {
            message.setPriorityStrId(updateVO.getMessagePriorityId());
            messageRepository.saveAndFlush(message);
        }

        if (updateVO.getMessageStatusId() != null) {
            MessageStatus status = messageStatusRepository.findByMessageIdAndReceiverUserId(messageId, userId);
            if(status != null) {
                status.setObjectStateId(updateVO.getMessageStatusId());
                messageStatusRepository.saveAndFlush(status);
            } else {
                //Create one statue
                MessageStatus messageStatus = new MessageStatus();
                // Message Read Status
                messageStatus.setObjectStateId(updateVO.getMessageStatusId());
                messageStatus.setMessageId(message.getId());
                messageStatus.setCreateUserId(userId);
                messageStatus.setCreateDate(LocalDateTime.now());
                messageStatus.setReceiverUserId(userId);
                // Save message status
                messageStatusRepository.save(messageStatus);
            }
        }
    }


    public List<TeamVO> getTeamMembers(Long projectId) {
        ProjectDTO projectDTO = projectService.findProjectById(projectId);
        boolean displayAliasName = false;
        if (projectDTO.isSupplierProject()) {
            ProjectDTO buyerProjectDTO = projectService.findProjectById(projectDTO.getMasterProjectId());
            displayAliasName = preferenceService.check(PreferenceID.WORKGROUP_OPTION_ENABLE_DISPLAY_ALIAS_NAME,
                    buyerProjectDTO.getOwnerWorkgroupId());
        }
        List<Long> projectIds = teamMyBatisMapper.findProjectIds(projectId);
        List<TeamDTO> allowedTeams = teamMyBatisMapper.findCollaborationTeamMembers(projectId, projectIds);
        List<TeamVO> teams = new ArrayList<>();
        for (TeamDTO teamDTO : allowedTeams) {
            TeamVO teamVO = new TeamVO();
            teamVO.setTeamId(teamDTO.getTeamId());
            teamVO.setTeamName(teamDTO.getWorkgroupName());
            teamVO.setIsSupplier(teamDTO.getWorkgroupTypeId() == WorkgroupTypeID.SUPPLIER);
            teamVO.setIsClient(isClientWorkgroup(teamDTO.getWorkgroupId()));
            List<TeamMemberVO> members = new ArrayList<>();
            for(TeamMemberDTO teamMemberDTO : teamDTO.getMembers()) {
                TeamMemberVO memberVO = new TeamMemberVO();
                boolean displayAliasNameForCurrent = displayAliasName && teamMemberDTO.hasAliasName()
                        && projectDTO.getOwnerWorkgroupId().longValue() != teamMemberDTO.getWorkgroupId();
                memberVO.setUserId(teamMemberDTO.getUserId());
                memberVO.setFirstName(displayAliasNameForCurrent ? teamMemberDTO.getAliasName() : teamMemberDTO.getFirstName());
                memberVO.setLastName(displayAliasNameForCurrent ? "" : teamMemberDTO.getLastName());
                memberVO.setEmail(teamMemberDTO.getEmailAddress());
                memberVO.setWorkgroup(teamMemberDTO.getWorkgroupName());
                memberVO.setCompanyName(teamMemberDTO.getCompanyName());
                members.add(memberVO);
            }
            teamVO.setTeamMembers(members);
            teams.add(teamVO);
        }
        return teams;
    }
}
