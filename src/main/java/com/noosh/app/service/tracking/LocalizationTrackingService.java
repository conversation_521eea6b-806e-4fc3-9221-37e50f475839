package com.noosh.app.service.tracking;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.noosh.app.commons.constant.SourceTypeID;
import com.noosh.app.commons.dto.tracking.WGTrackingDTO;
import com.noosh.app.commons.entity.tracking.WGTracking;
import com.noosh.app.commons.vo.PageVO;
import com.noosh.app.feign.FileResourceFeignClient;
import com.noosh.app.mapper.tracking.WGTrackingMapper;
import com.noosh.app.repository.jpa.tracking.WGTrackingRepository;
import com.noosh.app.repository.mybatis.tracking.TrackingMyBatisMapper;
import com.noosh.app.service.util.I18NUtils;
import com.noosh.app.service.util.TrackingDescUtil;
import com.noosh.app.service.util.URLUtil;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * User: leilaz
 * Date: 3/6/24
 */
@Service
public class LocalizationTrackingService {
    private final Logger log = LoggerFactory.getLogger(LocalizationTrackingService.class);

    @Autowired
    private WGTrackingRepository wgTrackingRepository;
    @Autowired
    private TrackingMyBatisMapper trackingMyBatisMapper;
    @Autowired
    private WGTrackingMapper wgTrackingMapper;
    @Autowired
    private TrackingDescUtil trackingDescUtil;
    @Autowired
    private I18NUtils i18NUtils;
    @Autowired
    private FileResourceFeignClient fileResourceFeignClient;

    private WGTracking packageTrackingDto(WGTrackingDTO trackingDTO) {
        WGTracking tracking = new WGTracking();
        tracking.setTrackingTypeId(trackingDTO.getTrackingTypeId());
        tracking.setCreateDate(LocalDateTime.now());
        tracking.setModDate(LocalDateTime.now());
        tracking.setCreateUserId(trackingDTO.getCreateUserId());
        tracking.setModUserId(trackingDTO.getCreateUserId());

        String i18NData = "";
        try {
            i18NData = URLUtil.mapToQueryString(trackingDTO.getI18nDataMap());
        } catch (Exception e) {
            log.error("Errors from creating i18NData in tracking service", e);
        }
        tracking.setI18nData(i18NData);
        tracking.setAcSourceTypeId(SourceTypeID.WORKGROUP_RESOURCE);
        tracking.setObjectClassId(trackingDTO.getObjectClassId());
        tracking.setObjectId(trackingDTO.getObjectId());
        tracking.setComments(trackingDTO.getComments());
        return tracking;
    }

    @Transactional(readOnly = false)
    public WGTrackingDTO insertTracking(WGTrackingDTO trackingDTO) {
        WGTracking tracking = packageTrackingDto(trackingDTO);
        return wgTrackingMapper.toDto(wgTrackingRepository.save(tracking));
    }

    public WGTrackingDTO buildTrackingDTO(Long trackingTypeId, Long userId, Long objectId, Long objectClassId,
                                          Map i18nDataMap, Long createUserId, String comments) {
        WGTrackingDTO trackingDTO = new WGTrackingDTO();
        trackingDTO.setTrackingTypeId(trackingTypeId);
        trackingDTO.setCreateUserId(userId);
        trackingDTO.setComments(comments);
        trackingDTO.setObjectClassId(objectClassId);
        trackingDTO.setObjectId(objectId);
        trackingDTO.setI18nDataMap(i18nDataMap);
        trackingDTO.setCreateUserId(createUserId);
        return trackingDTO;
    }

    public WGTrackingDTO buildAndSaveTracking(Long trackingTypeId, Long userId, Long objectId, Long objectClassId,
                                              Map i18nDataMap, Long enactingUserId, String comments) {
        WGTrackingDTO newTrackingDto = buildTrackingDTO(trackingTypeId, userId, objectId, objectClassId, i18nDataMap, enactingUserId, comments);
        return insertTracking(newTrackingDto);
    }

    public List<WGTrackingDTO> findByWorkgroupId(Long workgroupId, List<Long> typeIds, PageVO pageVO, String locale,
                                                 LocalDateTime createDateFrom, LocalDateTime createDateTo, String searchStr) throws Exception {
        String datetimeFrom = null, datetimeTo = null;

        if (createDateFrom != null) {
            datetimeFrom = createDateFrom.format(DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss"));
        }

        if (createDateTo != null) {
            datetimeTo = createDateTo.format(DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss"));
        }
        Page pageInfo = null;
        searchStr = searchStr.trim();
        if (!StringUtils.hasText(searchStr)) {
            pageInfo = PageHelper.startPage(pageVO.getNum(), pageVO.getSize());
        }

        List<WGTrackingDTO> trackingDTOList = trackingMyBatisMapper.getTrackingByWorkgroup(
                workgroupId, datetimeFrom, datetimeTo, typeIds, pageVO.getOrder());
        if (pageInfo != null) {
            pageVO.setTotal(pageInfo.getTotal());
        }

        List<WGTrackingDTO> newTrackingList = new ArrayList<WGTrackingDTO>();
        if (trackingDTOList != null) {
            for (WGTrackingDTO wgTrackingDTO : trackingDTOList) {
                Map map = new HashMap();
                try {
                    map = URLUtil.queryStringToMap(wgTrackingDTO.getI18nData(), map);
                } catch (Exception e) {
                    throw new Exception("Error retrieving i18nData from tracking object");
                }
                wgTrackingDTO.setResourceName(map.containsKey("resourceName") ? map.get("resourceName").toString() : null);
                wgTrackingDTO.setResourceValue(map.containsKey("resourceValue") ? map.get("resourceValue").toString() : null);
                wgTrackingDTO.setOldResourceValue(map.containsKey("oldResourceValue") ? map.get("oldResourceValue").toString() : null);
                wgTrackingDTO.setOldResourceName(map.containsKey("oldResourceName") ? map.get("oldResourceName").toString() : null);
                wgTrackingDTO.setNewResourceValue(map.containsKey("newResourceValue") ? map.get("newResourceValue").toString() : null);
                wgTrackingDTO.setNewResourceName(map.containsKey("newResourceName") ? map.get("newResourceName").toString() : null);
                wgTrackingDTO.setLanguage(map.containsKey("language") ? map.get("language").toString() : null);
                wgTrackingDTO.setPortal(map.containsKey("portal") ? map.get("portal").toString() : null);
                wgTrackingDTO.setDuplicatedCount(map.containsKey("duplicatedCount") ? map.get("duplicatedCount").toString() : null);
                wgTrackingDTO.setNewResourceCount(map.containsKey("newResourceCount") ? map.get("newResourceCount").toString() : null);
                if (map.containsKey("resourceDownloadPath")) {
                    String responseData = fileResourceFeignClient.getAmazonLinkByKey((String)map.get("resourceDownloadPath"));
                    if (StringUtils.hasLength(responseData)) {
                        JSONObject jsonObject = new JSONObject(responseData);
                        if (jsonObject.has("data")) {
                            String downloadLink = jsonObject.getString("data");
                            wgTrackingDTO.setResourceDownloadPath(downloadLink);
                            if (map.containsKey("resourceFileRowCount") && map.get("resourceFileRowCount") != null) {
                                wgTrackingDTO.setResourceFileRowCount((String)map.get("resourceFileRowCount"));
                            }
                        }
                    }
                }
                wgTrackingDTO.setTrackingDesc(trackingDescUtil.getTrackingDescription(wgTrackingDTO.getI18nData(),
                        wgTrackingDTO.getDescStrId().toString(), Locale.forLanguageTag(locale.replaceAll("_", "-"))));
                if (wgTrackingDTO.getCustomNameStr() != null && wgTrackingDTO.getNameStrId() == null) {
                    wgTrackingDTO.setTrackingName(wgTrackingDTO.getCustomNameStr());
                } else if (wgTrackingDTO.getNameStrId() != null){
                    wgTrackingDTO.setTrackingName(i18NUtils.getMessage(wgTrackingDTO.getNameStrId(),
                            Locale.forLanguageTag(locale.replaceAll("_", "-"))));
                }
                if (StringUtils.hasText(searchStr)) {
                    if (wgTrackingDTO.getTrackingDesc().toUpperCase().contains(searchStr.toUpperCase())
                            || wgTrackingDTO.getCreator().toUpperCase().contains(searchStr.toUpperCase())
                            || (wgTrackingDTO.getPortal() != null && wgTrackingDTO.getPortal().toUpperCase().contains(searchStr.toUpperCase()))
                            || (wgTrackingDTO.getLanguage() != null && wgTrackingDTO.getLanguage().toUpperCase().contains(searchStr.toUpperCase()))) {
                        newTrackingList.add(wgTrackingDTO);
                    }
                } else {
                    newTrackingList.add(wgTrackingDTO);
                }
            }
        }
        if (pageInfo == null) {
            int fromIndex = pageVO.getNum();
            int toIndex = pageVO.getSize();
            if (fromIndex == 1) {
                fromIndex = 0;
                toIndex = toIndex > newTrackingList.size() ? newTrackingList.size() : toIndex;
            } else if (fromIndex > 1) {
                fromIndex = ((fromIndex - 1) * pageVO.getSize()) > newTrackingList.size() ? newTrackingList.size() : ((fromIndex - 1) * pageVO.getSize());
                toIndex = (fromIndex + pageVO.getSize()) > newTrackingList.size() ? newTrackingList.size() : (fromIndex + pageVO.getSize());
            }
            pageVO.setTotal(newTrackingList.size());
            return newTrackingList.subList(fromIndex, toIndex);
        } else {
            return newTrackingList;
        }
    }
}
