package com.noosh.app.repository.terms;

import com.noosh.app.commons.entity.terms.AcTerms;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * User: leilaz
 * Date: 10/12/17
 */

public interface AcTermsRepository extends JpaRepository<AcTerms, Long> {
    List<AcTerms> findByWorkgroupIdAndTermsTypeIdOrderByVersionNumberDesc(Long workgroupId, Long termsTypeId);
}
