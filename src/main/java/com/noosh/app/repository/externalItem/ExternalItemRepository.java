package com.noosh.app.repository.externalItem;

import com.noosh.app.commons.entity.externalItem.ExternalItem;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * User: leilaz
 * Date: 3/31/22
 */

public interface ExternalItemRepository extends JpaRepository<ExternalItem, Long> {
    public List<ExternalItem> findByObjectIdAndObjectClassIdOrderByItemIndex(Long objectId, Long objectClassId);
}
