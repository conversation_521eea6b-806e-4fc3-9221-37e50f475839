package com.noosh.app.repository.rating;

import com.noosh.app.commons.entity.rating.SrRating;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 8/19/16
 */

public interface SrRatingRepository extends JpaRepository<SrRating, Long> {
    @Query("select sr from SrRating sr, SrRatingItem sri, SrQuestion sq \n" +
            "where sr.id = sri.ratingId and sri.questionId = sq.id \n" +
            "and sr.objectId = ?1 and sr.objectClassId = ?2 \n" +
            "and sr.ratedByWorkGroupId =?3")
    List<SrRating> findByObjectIdsAndByWorkGroupId(long id, long objectClassId, long workGroupId);

    @Query("select sr from SrRating sr \n" +
            "where sr.objectId = ?1 and sr.objectClassId = ?2 \n" +
            "and sr.ratedByWorkGroupId =?3 order by sr.id")
    List<SrRating> findByObjectIdAndByWorkGroupId(long id, long objectClassId, long workGroupId);

    @Query("select sr from SrRating sr \n" +
            "where sr.objectId = ?1 and sr.objectClassId = ?2 \n" +
            "and sr.ratedByWorkGroupId =?3 and sr.rateForWorkGroupId =?4 order by sr.id")
    SrRating findByObjectIdAndByWorkGroupIdAndForWorkGroupId(long id, long objectClassId, long byWorkGroupId, long forWorkGroupId);

    SrRating findByObjectIdAndObjectClassId(long id, long objectClassId);
}
