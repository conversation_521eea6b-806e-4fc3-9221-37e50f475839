package com.noosh.app.repository.rating;

import com.noosh.app.commons.entity.rating.SrRatingAverage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 2/8/19
 */

public interface SrRatingAverageRepository extends JpaRepository<SrRatingAverage, Long> {
    @Query("select SR from SrRatingAverage SR where SR.ratedByWorkGroupId = ?1 and SR.rateForWorkgroupId = ?2 and SR.objectClassId in ?3")
    List<SrRatingAverage> findByWorkgroupIdAndObjectId(Long byWorkgroupId, Long forWorkgroupId, List<Long> objectIds);

    @Query("select SR from SrRatingAverage SR where SR.ratedByWorkGroupId = ?1 and SR.rateForWorkgroupId = ?2 and SR.objectClassId = ?3")
    SrRatingAverage findSupplierWorkgroupRating(Long byWorkgroupId, Long forWorkgroupId, Long objectId);

    @Query("select SR from SrRatingAverage SR where SR.ratedByWorkGroupId = ?1 and SR.rateForWorkgroupId in ?2 and SR.objectClassId = ?3")
    List<SrRatingAverage> findSupplierWorkgroupRatings(Long byWorkgroupId, List<Long> forWorkgroupId, Long objectId);
}
