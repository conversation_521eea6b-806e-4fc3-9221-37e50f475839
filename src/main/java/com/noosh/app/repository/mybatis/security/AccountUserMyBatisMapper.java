package com.noosh.app.repository.mybatis.security;

import java.util.List;

import com.noosh.app.commons.dto.security.AccountUserDTO;
import org.apache.ibatis.annotations.Param;

public interface AccountUserMyBatisMapper {

    List<AccountUserDTO> findWithAllData(@Param("workgroupId") Long workgroupId, @Param("userStatusId") Long userStatusId);
    AccountUserDTO findByPersonIdAndWorkgroupId(@Param("personId") Long personId, @Param("workgroupId") Long workgroupId);

    AccountUserDTO findWithPersonAndWorkgroupByUserId(@Param("userId") Long userId);

}