package com.noosh.app.repository.mybatis;

import java.util.List;

import com.noosh.app.commons.dto.security.CurrencyDTO;
import org.apache.ibatis.annotations.Param;

public interface CurrencyMyBatisMapper {

     CurrencyDTO findCurrency(@Param("id") Long id);

     List<CurrencyDTO> findAllCurrencies();

     List<CurrencyDTO> findDefaultVendorDualCurrencies(@Param("ownerGroupId") Long ownerGroupId);

     List<CurrencyDTO> findDefaultClientDualCurrencies(@Param("ownerGroupId") Long ownerGroupId);

}