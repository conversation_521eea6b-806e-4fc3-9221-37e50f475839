package com.noosh.app.repository.mybatis.security;


/**
 * <AUTHOR>
 * @since 0.0.1
 */
public interface AccessMyBatisMapper {

    /**
     * Rules:
     * 1. is a active team member
     * 2. has VIEW_WORKGROUP_PROJECT permission
     *
     * @param workgroupId current workgroup id
     * @param userId      current user id
     * @param projectId   project id
     * @return ture if user can access
     */
    <PERSON>olean hasAccessToProject(Long workgroupId, Long userId, Long projectId);

    /**
     * Rules:
     * is a active workgroup member
     *
     * @param workgroupId current workgroup id
     * @param userId current user id
     * @return ture
     */
    Boolean hasAccessToWorkgroup(Long workgroupId, Long userId);

}
