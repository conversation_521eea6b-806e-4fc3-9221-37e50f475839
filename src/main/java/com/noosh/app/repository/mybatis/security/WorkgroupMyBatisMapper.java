package com.noosh.app.repository.mybatis.security;

import com.noosh.app.commons.dto.security.WorkgroupDTO;
import com.noosh.app.commons.vo.supplier.SupplierFilterVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 5/24/2022
 */
public interface WorkgroupMyBatisMapper {
    WorkgroupDTO findWorkgroupWithAllData(@Param("workgroupId") Long workgroupId);

    WorkgroupDTO findWorkgroup(@Param("workgroupId") Long workgroupId);
    
    WorkgroupDTO findWorkgroupByGuid(@Param("guid") String guid);

    List<WorkgroupDTO> findWithFirstNameAndLastNameAndDefaultEmail(@Param("workgroupId") Long workgroupId,
                                                                          @Param("isInternal") boolean isInternal,
                                                                          @Param("workgroupName") String workgroupName,
                                                                          @Param("firstName") String firstName,
                                                                          @Param("lastName") String lastName,
                                                                          @Param("defaultEmail") String defaultEmail,
                                                                          @Param("currencyId") long currencyId);
    List<WorkgroupDTO> findWorkgroupsBySupplierFilter(@Param("filter") SupplierFilterVO supplierFilter,
                                                      @Param("isInternal") boolean isInternal,
                                                      @Param("partnerWgId") Long partnerWgId,
                                                      @Param("workgroupId") Long workgroupId);
}
