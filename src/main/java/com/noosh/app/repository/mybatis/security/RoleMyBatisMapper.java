package com.noosh.app.repository.mybatis.security;

import com.noosh.app.commons.dto.security.RoleDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RoleMyBatisMapper {
    
    RoleDTO findWorkgroupRoleByUserId(@Param("userId") Long userId);
    
    List<RoleDTO> findRolesByWorkgroup(@Param("workgroupId") Long workgroupId,
                                       @Param("roleClassId") Long roleClassId);

}