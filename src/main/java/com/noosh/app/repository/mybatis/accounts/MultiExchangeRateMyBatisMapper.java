package com.noosh.app.repository.mybatis.accounts;

import java.util.List;

import com.noosh.app.commons.dto.accounts.MultiExchangeRateDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 7/17/2022
 */
public interface MultiExchangeRateMyBatisMapper {
    List<MultiExchangeRateDTO> findAllMultiExchangeRates(@Param("workgroupId") Long workgroupId,
                                                         @Param("targetWorkgroupType") String targetWorkgroupType,
                                                         @Param("supportSellSideMultiCurrency") Boolean supportSellSideMultiCurrency);


    MultiExchangeRateDTO findMultiExchangeRate(@Param("workgroupId") Long workgroupId,
                                               @Param("buWorkgroupId") Long buWorkgroupId,
                                               @Param("currencyId") Long currencyId,
                                               @Param("targetWorkgroupType") String targetWorkgroupType);

    MultiExchangeRateDTO findSupplierDualCurrencyByTargetGroupId(@Param("workgroupId") Long workgroupId,
                                               @Param("targetGroupId") Long targetGroupId);

    MultiExchangeRateDTO findClientDualCurrencyByTargetGroupId(@Param("workgroupId") Long workgroupId,
                                                               @Param("buWorkgroupId") Long buWorkgroupId,
                                                               @Param("targetGroupId") Long targetGroupId);

    List<MultiExchangeRateDTO> findAllMultiExchangeRatesWithoutOrder(@Param("workgroupId") Long workgroupId,
                                                                     @Param("targetWorkgroupType") String targetWorkgroupType,
                                                                     @Param("supportSellSideMultiCurrency") Boolean supportSellSideMultiCurrency,
                                                                     @Param("activeDateFrom") String activeDateFrom, @Param("activeDateTo") String activeDateTo);

    MultiExchangeRateDTO getMultiExchangeRateById(@Param("exchangeRateId") Long exchangeRateId);
}
