package com.noosh.app.repository.mybatis.report;

import com.noosh.app.commons.dto.report.DatamartDTO;
import com.noosh.app.commons.dto.report.ReportDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 9/15/2021
 */
public interface ReportMyBatisMapper {

    List<ReportDTO> findSavedReports(@Param("userId") Long userId, @Param("workgroupId") Long workgroupId, @Param("teamId") Long teamId, @Param("roleId") Long roleId);
    List<ReportDTO> findScheduledReports(@Param("userId") Long userId, @Param("workgroupId") Long workgroupId, @Param("teamId") Long teamId, @Param("roleId") Long roleId);
    ReportDTO findReport(@Param("userId") Long userId, @Param("workgroupId") Long workgroupId,
                         @Param("teamId") Long teamId, @Param("roleId") Long roleId, @Param("reportId") Long reportId);
    List<ReportDTO> findCreators(@Param("userId") Long userId, @Param("workgroupId") Long workgroupId, @Param("teamId") Long teamId, @Param("roleId") Long roleId);
    DatamartDTO lastAndNextRefreshStartTime(@Param("processName") String processName);
}
