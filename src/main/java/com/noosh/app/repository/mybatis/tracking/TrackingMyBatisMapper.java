package com.noosh.app.repository.mybatis.tracking;

import com.noosh.app.commons.dto.tracking.WGTrackingDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * User: leilaz
 * Date: 3/11/24
 */
public interface TrackingMyBatisMapper {
    public List<WGTrackingDTO> getTrackingByWorkgroup(@Param("workgroupId") Long workgroupId, @Param("startDate") String startDate, @Param("endDate") String endDate,
                                                      @Param("typeIds") List<Long> typeIds, @Param("order") String order);
}
