package com.noosh.app.repository.mybatis.collaboration;

import com.noosh.app.commons.dto.collaboration.ProjectStatusDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * User: leilaz
 * Date: 7/27/22
 */
public interface ProjectStatusMyBatisMapper {
    List<ProjectStatusDTO> findProjectStatuss();

    public Long getTotalProjectStatusInUsed(@Param("projectStatusId") Long projectStatusId);

    public void updateProjectStatus(@Param("newStatusId") Long newStatusId, @Param("oldStatusId") Long oldStatusId);
}
