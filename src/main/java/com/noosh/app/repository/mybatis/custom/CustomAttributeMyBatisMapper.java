package com.noosh.app.repository.mybatis.custom;

import java.util.List;

import com.noosh.app.commons.dto.custom.CustomAttributeDTO;
import org.apache.ibatis.annotations.Param;

public interface CustomAttributeMyBatisMapper {
	List<CustomAttributeDTO> findCustomAttributes(@Param("propertyIds") List<Long> propertyIds,
	                                              @Param("paramNames") List<String> paramNames);
}
