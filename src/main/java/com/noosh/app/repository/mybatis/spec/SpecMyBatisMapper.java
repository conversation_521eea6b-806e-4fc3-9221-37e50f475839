package com.noosh.app.repository.mybatis.spec;

import com.noosh.app.commons.dto.spec.SpecDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SpecMyBatisMapper {
    List<SpecDTO> findSpecsBySpecRefId(@Param("parentObjectId") Long parentObjectId, @Param("specRefId") Long specRefId);
    Integer findTotalSpecReferenceCountByProjectIds(@Param("projectIds") List<Long> projectIds);
}
