package com.noosh.app.repository.mybatis.accounts;

import com.noosh.app.commons.dto.accounts.RatingQuestionnaireDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RatingQuestionnaireMyBatisMapper {

    List<RatingQuestionnaireDTO> findByWorkGroupId(@Param("WorkGroupId") long workGroupId);
    RatingQuestionnaireDTO findWithQuestions(@Param("questionnaireId") long questionnaireId);
}