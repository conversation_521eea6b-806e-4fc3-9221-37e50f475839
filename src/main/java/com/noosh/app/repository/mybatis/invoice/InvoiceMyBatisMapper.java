package com.noosh.app.repository.mybatis.invoice;

import com.noosh.app.commons.dto.breakout.BreakoutDTO;
import com.noosh.app.commons.dto.breakout.BreakoutTypeDTO;
import com.noosh.app.commons.dto.invoice.InvoiceDTO;
import com.noosh.app.commons.dto.invoice.InvoiceItemDTO;
import com.noosh.app.commons.dto.invoice.InvoiceListDTO;
import com.noosh.app.commons.dto.invoice.RequestDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 0.0.1
 */
public interface InvoiceMyBatisMapper {

    List<InvoiceListDTO> findInvoiceList(@Param("projectId") Long projectId,
                                         @Param("ownerGroupId") Long ownerWorkgroupId,
                                         @Param("includeBuySide") Boolean includeBuySide,
                                         @Param("includeSellSide") Boolean includeSellSide,
                                         @Param("isClientNotOnNoosh") Boolean isClientNotOnNoosh);

    /**
     * find the count for all draft and pending invoices for an order
     * @param orderId
     * @return
     */
    Long getDraftAndPendingInvoiceCountForOrderId(@Param("orderId") Long orderId);

    InvoiceDTO findInvoiceDetail(@Param("projectId") Long projectId,
                                 @Param("invoiceId") Long invoiceId);

    List<InvoiceDTO> findInvoiceForOrder(@Param("projectId") Long projectId,
                                         @Param("orderId") Long orderId);

    List<InvoiceItemDTO> findInvoiceItem(@Param("invoiceId") Long invoiceId, @Param("workgroupId") Long workgroupId);

    List<BreakoutDTO> findBreakoutByInvoiceItemIds(@Param("invoiceItemIds") List<Long> invoiceItemIds);

    List<BreakoutTypeDTO> findAllWithRootBreakoutTypeIdAndSpecTypeIdAndSupplierGroupId(@Param("breakoutTypeId") Long breakoutTypeId,
                                                                                       @Param("specTypeId") Long specTypeId,
                                                                                       @Param("supplierWorkgroupId") Long supplierWorkgroupId);

    List<RequestDTO> findShipmentRecordsByInvoiceItemIdAndJobId(@Param("invoiceItemId") Long invoiceItemId,
                                                                @Param("jobId") Long jobId);
    List<RequestDTO> findShipmentRecordsByJobIdAndRequestIds(@Param("jobId") Long jobId,
                                                             @Param("requestIds") List<Long> requestIds);
}
