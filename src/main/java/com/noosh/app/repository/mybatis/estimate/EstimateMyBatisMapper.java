package com.noosh.app.repository.mybatis.estimate;

import com.noosh.app.commons.dto.estimate.EstimateDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 11/12/15
 * Time: 6:32 PM
 */
@Component
public interface EstimateMyBatisMapper {
    List<EstimateDTO> findAwardedEstimates(@Param("rfeId") Long rfeId, @Param("projectId") Long projectId);
}
