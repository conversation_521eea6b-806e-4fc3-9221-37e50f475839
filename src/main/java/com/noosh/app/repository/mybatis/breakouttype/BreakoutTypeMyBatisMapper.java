package com.noosh.app.repository.mybatis.breakouttype;

import com.noosh.app.commons.dto.breakout.BreakoutDTO;
import com.noosh.app.commons.dto.breakout.BreakoutTypeDTO;
import com.noosh.app.commons.dto.invoice.InvoiceDTO;
import com.noosh.app.commons.dto.invoice.InvoiceItemDTO;
import com.noosh.app.commons.dto.invoice.InvoiceListDTO;
import com.noosh.app.commons.dto.invoice.RequestDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * BreakoutTypeMyBatisMapper
 *
 */
public interface BreakoutTypeMyBatisMapper {
    List<BreakoutTypeDTO> findByWorkgroupAndSpecTypeId(@Param("workgroupId") Long workgroupId, @Param("specTypeId") Long specTypeId);

    List<BreakoutTypeDTO> findAllWithRootBreakoutTypeIdAndSpecTypeIdAndSupplierGroupId(@Param("breakoutTypeId") Long breakoutTypeId,
                                                                                       @Param("specTypeId") Long specTypeId,
                                                                                       @Param("supplierWorkgroupId") Long supplierWorkgroupId);

    List<BreakoutTypeDTO> findAllByRootBreakoutTypeId(@Param("rootBreakoutTypeId") Long rootBreakoutTypeId);
}
