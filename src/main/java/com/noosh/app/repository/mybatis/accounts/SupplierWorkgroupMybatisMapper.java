package com.noosh.app.repository.mybatis.accounts;

import com.noosh.app.commons.entity.account.SupplierWorkgroup;
import com.noosh.app.repository.util.MybatisExtendedLanguageDriver;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * @Author: neals
 * @Date: 08/04/2016
 */
public interface SupplierWorkgroupMybatisMapper {

    String FIND_SUPPLIER_WORGROUP = " SELECT BU.* FROM BU_SUPPLIER_WORKGROUP BU " +
            " WHERE BU.OWNER_AC_WORKGROUP_ID = #{ownerWorkgroupId} AND BU.SUPPLIER_AC_WORKGROUP_ID = #{supplierWorkgroupId} " +
            " <choose> " +
            "   <when test=\"clientWorkgroupId == -2\"> " +           // == NO_CLIENT
            "       AND BU.CLIENT_AC_WORKGROUP_ID is null " +
            "   </when>" +
            "   <when test=\"clientWorkgroupId != -1\"> " +           // != IGNORE_CLIENT
            "       AND BU.CLIENT_AC_WORKGROUP_ID = #{clientWorkgroupId} " +
            "   </when>" +
            "   <otherwise> </otherwise> " +
            " </choose> ";

    @Results(value = {
            @Result(property = "id", column = "BU_SUPPLIER_WORKGROUP_ID"),
            @Result(property = "ownerWorkgroupId", column = "OWNER_AC_WORKGROUP_ID"),
            @Result(property = "supplierWorkgroupId", column = "SUPPLIER_AC_WORKGROUP_ID"),
            @Result(property = "isApproved", column = "IS_APPROVED"),
            @Result(property = "clientWorkgroupId", column = "CLIENT_AC_WORKGROUP_ID"),
            @Result(property = "alias", column = "ALIAS"),
            @Result(property = "acceptQuote", column = "ACCEPT_QUOTE"),
            @Result(property = "acceptChangeOrder", column = "ACCEPT_CHANGE_ORDER"),
            @Result(property = "supplierCode", column = "SUPPLIER_CODE"),
            @Result(property = "defaultSupplierUserId", column = "DEFAULT_SUPPLIER_USER_ID"),
            @Result(property = "customPropertyId", column = "CUSTOM_PR_PROPERTY_ID")
    })
    @Select(FIND_SUPPLIER_WORGROUP)
    @Lang(MybatisExtendedLanguageDriver.class)
    List<SupplierWorkgroup> find(@Param("ownerWorkgroupId") Long ownerWorkgroupId,
                                 @Param("supplierWorkgroupId") Long supplierWorkgroupId,
                                 @Param("clientWorkgroupId") Long clientWorkgroupId);
}
