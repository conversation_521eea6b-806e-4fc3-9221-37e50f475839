package com.noosh.app.repository.mybatis.accounts;

import java.util.List;

import com.noosh.app.commons.dto.accounts.WorkgroupAttributeDTO;
import org.apache.ibatis.annotations.Param;

public interface WorkgroupAttributeMyBatisMapper {

    List<WorkgroupAttributeDTO> findWorkgroupAttributes(@Param("workgroupId") Long workgroupId,
                                                        @Param("typeId") Long typeId,
                                                        @Param("orderBy") String orderBy,
                                                        @Param("activeOnly") Boolean activeOnly);

    List<WorkgroupAttributeDTO> findByType(@Param("typeId") Long typeId,
                                           @Param("orderBy") String orderBy);

}