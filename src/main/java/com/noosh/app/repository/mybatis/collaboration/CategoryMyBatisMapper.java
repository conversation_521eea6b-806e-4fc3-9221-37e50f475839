package com.noosh.app.repository.mybatis.collaboration;

import java.util.List;

import com.noosh.app.commons.dto.collaboration.CategoryDTO;
import org.apache.ibatis.annotations.Param;


public interface CategoryMyBatisMapper {

    List<CategoryDTO> findProjectBudgetCategory(@Param("userId") Long userId, @Param("workgroupId") Long workgroupId);

    List<CategoryDTO> getCategoryList(@Param("workgroupId") Long workgroupId, @Param("userId") Long userId);

}