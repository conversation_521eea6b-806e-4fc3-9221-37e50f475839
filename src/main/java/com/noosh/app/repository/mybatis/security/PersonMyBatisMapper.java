package com.noosh.app.repository.mybatis.security;

import com.noosh.app.commons.dto.security.PersonDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PersonMyBatisMapper {

    /**
     * This is a very "specialized" finder. Most likely, it is only needed in the
     * account management section.  It finds all persons that are part of a
     * company/hierarchy, and are NOT currently assigned to the workgroup
     * that is specified as a parameter.  It also excludes inactive members.
     * Includes: [PersonBean, ...]
     *
     * {all person from the company} MINUS {all person from the given workgroup}
     */
    List<PersonDTO> findAllPersonsInCompanyNotInWorkgroup(@Param("workgroupId") Long workgroupId,
                                                          @Param("companyId") Long companyId);

    List<PersonDTO> findByEmailAddress(@Param("email") String email);
}