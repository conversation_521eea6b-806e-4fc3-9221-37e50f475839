package com.noosh.app.repository.mybatis.order;

import com.noosh.app.commons.dto.invoice.InvoicePendingMyDeskDTO;
import com.noosh.app.commons.dto.order.OrderInvoiceMyDeskDTO;
import com.noosh.app.commons.dto.order.OrderPendingMyDeskDTO;
import com.noosh.app.commons.dto.order.OrderStatusMyDeskDTO;
import com.noosh.app.commons.dto.order.OrderSupplierMyDeskDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * User: leilaz
 * Date: 3/25/24
 */
public interface OrderMyDeskMyBatisMapper {
    public List<OrderStatusMyDeskDTO> findOrderWithStatus(@Param("workgroupId") Long workgroupId, @Param("userId") Long userId,
                                                          @Param("isMyProject") boolean isMyProject);

    public List<OrderSupplierMyDeskDTO> findOrderWithSupplier(@Param("workgroupId") Long workgroupId,
                                                              @Param("userId") Long userId,
                                                              @Param("isMyProject") boolean isMyProject,
                                                              @Param("dateRange") Long dateRange,
                                                              @Param("dateType") String dateType);

    public List<OrderInvoiceMyDeskDTO> findOrderWithInvoice(@Param("workgroupId") Long workgroupId,
                                                              @Param("userId") Long userId,
                                                              @Param("isMyProject") boolean isMyProject,
                                                              @Param("isBuy") boolean isBuy,
                                                              @Param("isSell") boolean isSell,
                                                              @Param("dateRange") Long dateRange);

    public List<OrderPendingMyDeskDTO> findOrderPending(@Param("workgroupId") Long workgroupId, @Param("userId") Long userId,
                                                        @Param("isMyProject") boolean isMyProject, @Param("isOverdue") boolean isOverdue,
                                                        @Param("isDue") boolean isDue, @Param("isPending") boolean isPending,
                                                        @Param("startDate") String startDate, @Param("endDate") String endDate);

    public List<InvoicePendingMyDeskDTO> findInvoicePending(@Param("workgroupId") Long workgroupId, @Param("userId") Long userId,
                                                        @Param("isMyProject") boolean isMyProject, @Param("isOverdue") boolean isOverdue,
                                                        @Param("isDue") boolean isDue, @Param("isPending") boolean isPending,
                                                        @Param("startDate") String startDate, @Param("endDate") String endDate);

    public List<OrderPendingMyDeskDTO> findChangeOrderPending(@Param("workgroupId") Long workgroupId,@Param("userId") Long userId,
                                                              @Param("isMyProject") boolean isMyProject, @Param("isOverdue") boolean isOverdue,
                                                              @Param("isDue") boolean isDue, @Param("isPending") boolean isPending,
                                                              @Param("startDate") String startDate, @Param("endDate") String endDate);

}
