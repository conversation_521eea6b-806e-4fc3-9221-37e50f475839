package com.noosh.app.repository.mybatis.order;

import com.noosh.app.commons.dto.order.OrderItemDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 0.0.1
 */
public interface OrderItemMyBatisMapper {

    List<OrderItemDTO> findByOrderVersionId(@Param("orderVersionId") final Long orderVersionId);

    List<OrderItemDTO> findByJobId(@Param("jobId") final Long jobId);
}
