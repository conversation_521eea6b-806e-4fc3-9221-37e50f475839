package com.noosh.app.repository.mybatis.costcenter;

import com.noosh.app.commons.dto.costcenter.CostCenterDTO;
import com.noosh.app.commons.entity.costcenter.CostCenter;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CostCenterMyBatisMapper {

    List<Long> findUsedCostCenterIds(@Param("workgroupId") Long workgroupId);

    CostCenterDTO getCostCenterDetails(@Param("costCenterId") Long costCenterId);

    List<CostCenterDTO> getCostCenterDTOList(@Param("ownerWgId") Long ownerWgId, @Param("buClientWgId") Long buClientWgId, @Param("isActive") Boolean isActive);

    List<CostCenterDTO> getNoClientCostCenterDTOList(@Param("ownerWgId") Long ownerWgId, @Param("isActive") Boolean isActive);

    List<CostCenterDTO> findByNameAndDescription(@Param("workgroupId") Long workgroupId, @Param("name") String name, @Param("description") String description);
}
