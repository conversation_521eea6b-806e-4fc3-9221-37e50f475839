package com.noosh.app.repository.mybatis.costcenter;


import com.noosh.app.commons.dto.costcenter.CostCenterAllocationDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * User: yangx
 * Date: 6/19/2020
 */
public interface CostCenterMyBatisMapper {


    List<CostCenterAllocationDTO> findItemLevelCostAllocationByOrderVersionId(Long orderVersionId);

    List<CostCenterAllocationDTO> findOrderLevelCostAllocationByOrderId(Long orderId);

    void deleteCostCenterWithIds(@Param("ids") List<Long> ids);
}
