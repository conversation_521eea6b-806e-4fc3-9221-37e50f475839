package com.noosh.app.repository.mybatis.collaboration;

import com.noosh.app.commons.dto.document.TgTagDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FilesMyBatisMapper {
    public List<TgTagDTO> findByOwnerWgIdAndObjectClassId(@Param("ownerWgId") Long ownerWgId,
                                                          @Param("objectClassId") Long objectClassId,
                                                          @Param("isActive") boolean isActive);
}
