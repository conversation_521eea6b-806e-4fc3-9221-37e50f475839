package com.noosh.app.repository.mybatis.localization;

import com.noosh.app.commons.dto.localization.LocalizationDTO;
import com.noosh.app.commons.dto.localization.PortalDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * User: leilaz
 * Date: 11/27/23
 */
public interface LocalizationMyBatisMapper {
    public List<LocalizationDTO> getLocalizationList(@Param("portal") String portal,
                                                     @Param("language") String language,
                                                     @Param("resourceName") String resourceName,
                                                     @Param("resourceValue") String resourceValue,
                                                     @Param("ids")List<Long> ids);

    public List<LocalizationDTO> getFullLocalization(@Param("portal") String portal,
                                                     @Param("language") String language,
                                                     @Param("isNeedDefault") boolean isNeedDefault,
                                                     @Param("ids")List<Long> ids);

    public void deleteByPortal(@Param("portal") String portal);

    public void updateByPortal(@Param("newPortal") String newPortal, @Param("oldPortal") String oldPortal);

    public void deleteByIds(@Param("ids")List<Long> ids);

    public List<PortalDTO> getPortalList(@Param("name") String name);
}
