package com.noosh.app.repository.mybatis.order;

import com.noosh.app.commons.dto.order.OrderDTO;
import com.noosh.app.commons.dto.order.ProjectOrderWidgetDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 0.0.1
 */
public interface OrderMyBatisMapper {

    Long findSoldOrdersCount(@Param("projectId") Long projectId,
                             @Param("ownerGroupId") Long ownerWorkgroupId,
                             @Param("isClientNotOnNoosh") Boolean isClientNotOnNoosh);

    List<Long> findChangeOrderIds(Long orderId);

    List<OrderDTO> getOriginalOrderList(@Param("projectId") Long projectId,
                                        @Param("orderType") String orderType,
                                        @Param("isClientNotOnNoosh") boolean isClientNotOnNoosh,
                                        @Param("cogFilter") List<Long> cogFilter);

    List<Long> getOriginalOrderItemIdList(@Param("projectId") Long projectId,
                                        @Param("orderType") String orderType,
                                        @Param("isClientNotOnNoosh") boolean isClientNotOnNoosh,
                                        @Param("cogFilter") List<Long> cogFilter);

    List<OrderDTO> getChangeOrderListByParent(@Param("projectId") Long projectId,
                                              @Param("orderIdList") List<Long> orderIdList,
                                              @Param("includePendingSubmissionCO") boolean includePendingSubmissionCO,
                                              @Param("cogFilter") List<Long> cogFilter);

    List<OrderDTO> getChangeOrderListByOriginalOrderId(@Param("projectId") Long projectId,
                                              @Param("originalOrderId") Long originalOrderId);

    OrderDTO findOrderById(@Param("projectId") Long projectId,
                           @Param("orderId") Long orderId);

    List<ProjectOrderWidgetDTO> findOriginalOrderByProjectId(@Param("projectId") Long projectId,
                                                             @Param("isBuy") boolean isBuy,
                                                             @Param("isSell") boolean isSell);

    List<ProjectOrderWidgetDTO> findChangeOrderByParentOrderId(@Param("orderId") Long orderId);

    List<ProjectOrderWidgetDTO> getProjectOrderListWithBuyCheck(@Param("projectId") Long projectId,
                                                                @Param("type") String type,
                                                                @Param("isClientNotOnNoosh") boolean isClientNotOnNoosh,
                                                                @Param("cogFilter") List<Long> cogFilter);

    Long countTotalProjectOrderListWithBuyCheck(@Param("projectId") Long projectId,
                                                @Param("isClientNotOnNoosh") boolean isClientNotOnNoosh,
                                                                @Param("type") String type);

    List<OrderDTO> findOrdersByProjectId(@Param("projectId") Long projectId,
                                         @Param("ownerWorkgroupId") Long ownerWorkgroupId,
                                         @Param("isClientNotOnNoosh") boolean isClientNotOnNoosh,
                                         @Param("isForCreateInvoice") boolean isForCreateInvoice);

    Long checkOrderTitleExists(@Param("projectId") Long projectId, @Param("title") String title,
                               @Param("parentObjectId") Long parentObjectId);
}
