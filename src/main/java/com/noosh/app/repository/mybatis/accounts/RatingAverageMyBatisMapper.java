package com.noosh.app.repository.mybatis.accounts;

import com.noosh.app.commons.dto.accounts.RatingAverageDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface RatingAverageMyBatisMapper {

    List<RatingAverageDTO> calculateAvgFromAllRatings(@Param("byWorkgroupId") long byWorkgroupId);

    List<RatingAverageDTO> calculateAvgFromRatings(@Param("byWorkgroupId") long byWorkgroupId, @Param("newSpanCount") int newSpanCount);
}