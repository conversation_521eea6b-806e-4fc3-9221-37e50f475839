package com.noosh.app.repository.mybatis.property;

import com.noosh.app.commons.dto.property.PropertyAttributeDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PropertyMyBatisMapper {

    List<PropertyAttributeDTO> getPaperDetailProperty(@Param("propertyId") Long propertyId);

    List<Long> getPaperDetailCount(@Param("specId") Long specId, @Param("name") String name);

    List<PropertyAttributeDTO> getSpecPaperFields(@Param("paperPropertyIdList") List<Long> paperPropertyIdList, @Param("name") String name);

    List<PropertyAttributeDTO> findBillToByPropertyId(@Param("propertyId") Long propertyId);

    List<PropertyAttributeDTO> findSpecPropertyByPropertyId(@Param("propertyId") Long propertyId);

    List<PropertyAttributeDTO> findPropertyAttributes(@Param("propertyId") Long propertyId);

    void deletePropertyWithPropertyIds(@Param("propertyIds") List<Long> propertyIds);

    void deleteAttributeWithPropertyIds(@Param("propertyIds") List<Long> propertyIds);

    List<PropertyAttributeDTO> getEstItemPaperDetailProperty(@Param("propertyId") Long propertyId);

    List<PropertyAttributeDTO> getChildrenProperty(@Param("parentPropertyId") Long parentPropertyId, @Param("name") String name);

}
