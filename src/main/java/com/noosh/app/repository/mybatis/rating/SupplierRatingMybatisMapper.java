package com.noosh.app.repository.mybatis.rating;


import com.noosh.app.repository.util.MybatisExtendedLanguageDriver;
import org.apache.ibatis.annotations.*;

/**
 * User: leilaz
 * Date: 9/30/19
 */
@Mapper
public interface SupplierRatingMybatisMapper {
    @Insert("insert into sr_rating (SR_RATING_ID,\n" +
            "  SR_QUESTIONNAIRE_ID,\n" +
            "  RATED_BY_AC_WORKGROUP_ID,\n" +
            "  RATED_FOR_AC_WORKGROUP_ID,\n" +
            "  OC_OBJECT_ID,\n" +
            "  OC_OBJECT_CLASS_ID,\n" +
            "  COMMENTS,\n" +
            "  CREATE_DATE,\n" +
            "  MOD_DATE,\n" +
            "  CREATE_USER_ID,\n" +
            "  MOD_USER_ID,\n" +
            "  AVERAGE_GRADE,\n" +
            "  GRANULARITY,\n" +
            "  NA_ALLOWED\n" +
            "  ) values (SR_RATING_SEQ.NEXTVAL, #{questionId,jdbcType=NUMERIC}, \n" +
            "  #{rateByWgId,jdbcType=NUMERIC}, #{rateForWgId,jdbcType=NUMERIC}, " +
            "  #{objectId,jdbcType=NUMERIC}, #{objectClassId,jdbcType=NUMERIC}, #{comment}, SYSDATE, SYSDATE, #{userId,jdbcType=NUMERIC}" +
            " ,#{userId,jdbcType=NUMERIC}, #{grade,jdbcType=NUMERIC}, #{granularity,jdbcType=NUMERIC}, #{na,jdbcType=NUMERIC})")
    int insertRating(@Param("questionId") Long questionId, @Param("rateByWgId") Long rateByWgId,
                     @Param("rateForWgId") Long rateForWgId, @Param("objectId") Long objectId,
                     @Param("objectClassId") Long objectClassId, @Param("comment") String comment,
                     @Param("userId") Long userId, @Param("grade") Long grade, @Param("granularity") Short granularity,
                     @Param("na") Short na);

    @Insert("insert into sr_rating_item (SR_RATING_ITEM_ID,\n" +
            "  SR_RATING_ID,\n" +
            "  SR_QUESTION_ID,\n" +
            "  COMMENTS,\n" +
            "  GRADE,\n" +
            "  CREATE_DATE,\n" +
            "  MOD_DATE,\n" +
            "  CREATE_USER_ID,\n" +
            "  MOD_USER_ID \n" +
            "  ) values (SR_RATING_ITEM_SEQ.NEXTVAL, #{ratingId,jdbcType=NUMERIC}, #{questionId,jdbcType=NUMERIC}, \n" +
            "  #{comment}, #{grade,jdbcType=NUMERIC}, SYSDATE, SYSDATE, #{userId,jdbcType=NUMERIC}" +
            " ,#{userId,jdbcType=NUMERIC})")
    int insertRatingItem(@Param("ratingId") Long ratingId, @Param("questionId") Long questionId, @Param("comment") String comment,
                         @Param("userId") Long userId, @Param("grade") Long grade);

    @Update("update sr_rating set " +
            "  AVERAGE_GRADE = #{grade,jdbcType=NUMERIC} \n" +
            "        <if test=\"isComplete\">\n" +
            "            ,COMPLETE_DATE = SYSDATE\n" +
            "        </if>\n" +
            "  where sr_rating_id = #{ratingId}")
    @Lang(MybatisExtendedLanguageDriver.class)
    int updateRatingGrade(@Param("ratingId") Long ratingId, @Param("grade") Long grade, @Param("isComplete") Boolean isComplete);

    @Insert("update sr_rating_item set " +
            "  COMMENTS = #{comment},\n" +
            "  GRADE = #{grade,jdbcType=NUMERIC} \n" +
            "  where sr_rating_item_id = #{ratingItemId}")
    int updateRatingItem(@Param("comment") String comment,
                         @Param("ratingItemId") Long ratingItemId, @Param("grade") Long grade);
}
