package com.noosh.app.repository.mybatis.accounts;

import com.noosh.app.commons.dto.accounts.SupplierWorkgroupDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SupplierWorkgroupMyBatisMapper {

    SupplierWorkgroupDTO find(@Param("supplierId") Long supplierId);

    /***
     * general query method
     * @param ownerWorkgroupId
     * @param supplierWorkgroupName
     * @param clientWorkgroupFilterSql
     * @return SupplierWorkgroupDTO
     */
    List<SupplierWorkgroupDTO> findSupplierWorkgroups(@Param("ownerWorkgroupId") Long ownerWorkgroupId,
                                                      @Param("supplierWorkgroupName") String supplierWorkgroupName,
                                                      @Param("clientWorkgroupFilterSql") String clientWorkgroupFilterSql,
                                                      @Param("hHCertFilterSql") String hHCertFilterSql);
    List<SupplierWorkgroupDTO> findSupplierWorkgroupsByName(@Param("ownerWorkgroupId") Long ownerWorkgroupId,
                                                      @Param("supplierWorkgroupName") String supplierWorkgroupName);

    List<SupplierWorkgroupDTO> findByOwnerAndSupplierWorkgroupId(@Param("ownerWorkgroupId") Long ownerWorkgroupId,
                                                                 @Param("supplierWorkgroupId") Long supplierWorkgroupId,
                                                                 @Param("supplierId") Long supplierId);
    /***
     * find outsourcer supplier that the client doesn't have
     * @param ownerWorkgroupId
     * @param clientWorkgroupId
     * @return SupplierWorkgroupDTO
     */
    List<SupplierWorkgroupDTO> findOutsourcerSuppliers(@Param("ownerWorkgroupId") Long ownerWorkgroupId,
                                                       @Param("clientWorkgroupId") Long clientWorkgroupId,
                                                       @Param("hHCertFilterSql") String hHCertFilterSql);

    /***
     * find approved clients for the supplier
     * @param ownerWorkgroupId
     * @param supplierWorkgroupId
     * @return
     */
    List<SupplierWorkgroupDTO> findApprovedClients(@Param("ownerWorkgroupId") Long ownerWorkgroupId, @Param("supplierWorkgroupId") Long supplierWorkgroupId, @Param("searchStr") String searchStr);
}