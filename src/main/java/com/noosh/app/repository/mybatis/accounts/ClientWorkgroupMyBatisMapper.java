package com.noosh.app.repository.mybatis.accounts;

import com.noosh.app.commons.dto.accounts.ClientWorkgroupDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 5/4/2022
 */
public interface ClientWorkgroupMyBatisMapper {

    List<ClientWorkgroupDTO> findClientsByProjectLevelActive(@Param("ownerWorkgroupId") long ownerWorkgroupId, @Param("searchStr") String searchStr, @Param("isProjectLevelActive") boolean isProjectLevelActive);

    List<ClientWorkgroupDTO> findClients(@Param("ownerWorkgroupId") long ownerWorkgroupId);

    @Select("SELECT CLIENT_AC_WORKGROUP_ID FROM BU_CLIENT_WORKGROUP WHERE OWNER_AC_WORKGROUP_ID = #{ownerWorkgroupId} AND IS_INACTIVE != 1")
    List<Long> findActiveClientWgIds(@Param("ownerWorkgroupId") long ownerWorkgroupId);

    List<ClientWorkgroupDTO> findAllSimpleClientList(@Param("ownerWorkgroupId") long ownerWorkgroupId);
}
