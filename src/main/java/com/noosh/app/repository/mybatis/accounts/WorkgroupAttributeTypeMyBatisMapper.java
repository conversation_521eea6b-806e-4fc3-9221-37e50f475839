package com.noosh.app.repository.mybatis.accounts;

import java.util.List;

import com.noosh.app.commons.dto.accounts.WorkgroupAttributeTypeDTO;
import org.apache.ibatis.annotations.Param;

public interface WorkgroupAttributeTypeMyBatisMapper {

    List<WorkgroupAttributeTypeDTO> findUnrestrictedTypesByWgId(@Param("workgroupId") Long workgroupId);

    WorkgroupAttributeTypeDTO findTypesByWgIdAndLabel(@Param("workgroupId") Long workgroupId, @Param("label") String label);
}