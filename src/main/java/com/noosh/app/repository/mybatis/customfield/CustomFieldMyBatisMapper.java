package com.noosh.app.repository.mybatis.customfield;

import com.noosh.app.commons.dto.customfield.UserFieldDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * User: leilaz
 * Date: 9/23/20
 */
@Component
public interface CustomFieldMyBatisMapper {
    List<UserFieldDTO> getAllUserFieldsByWorkgroupId(@Param("workgroupId") Long workgroupId, @Param("classId") Long classId,
                                                     @Param("excludeInvisibleSupplierField") boolean excludeInvisibleSupplierField);
}
