package com.noosh.app.repository.mybatis.order;

import com.noosh.app.commons.vo.chart.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderChartMyBatisMapper {

    List<ProjectChartItemVO> findProjectEfficiency(@Param("workgroupId") Long workgroupId, @Param("filter") Integer filter, @Param("isClient") boolean isClient);

    List<ProjectRFESentVO> findProjectRFEDetailInfo(@Param("workgroupId") Long workgroupId, @Param("filter") Integer filter);

    List<ProjectBuyOrderVO> findProjectOrderDetailInfo(@Param("workgroupId") Long workgroupId,
                                                       @Param("filter") Integer filter,
                                                       @Param("type") String type);

    List<ProjectInvoiceVO> findProjectInvoiceDetailInfo(@Param("workgroupId") Long workgroupId,
                                                       @Param("filter") Integer filter,
                                                       @Param("type") String type);

    List<ProjectChartItemVO> findTimeToOrder(@Param("workgroupId") Long workgroupId, @Param("filter") Integer filter);

    List<InvoiceForecastChartItemVO> findInvoiceForecast(@Param("workgroupId") Long workgroupId,
                                                         @Param("start") String start,
                                                         @Param("end") String end);
}
