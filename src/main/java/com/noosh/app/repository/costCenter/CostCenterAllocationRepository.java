package com.noosh.app.repository.costCenter;

import com.noosh.app.commons.entity.costcenter.CostCenterAllocation;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: leilaz
 * Date: 9/10/20
 */

public interface CostCenterAllocationRepository extends JpaRepository<CostCenterAllocation, Long> {

    List<CostCenterAllocation> findByObjectIdAndObjectClassId(Long objectId, Long objectClassId);

    List<CostCenterAllocation> findByObjectIdAndObjectClassIdAndTypeId(Long objectId, Long objectClassId, Long typeId);

    List<CostCenterAllocation> findByObjectIdInAndObjectClassId(List<Long> objectIds, Long objectClassId);

}
