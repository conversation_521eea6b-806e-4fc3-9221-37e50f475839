package com.noosh.app.repository.collaboration;

import com.noosh.app.commons.entity.collaboration.SyContainable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 11/8/15
 */

public interface CollaborationRepository extends JpaRepository<SyContainable, Long> {
    public List<SyContainable> findByObjectIdAndObjectClassId(Long objectId, Long objectClassId);

    public SyContainable findFirstByObjectIdAndObjectClassIdOrderByCreateDateDesc(Long objectId, Long objectClassId);

    @Query("select Sy from SyContainable Sy where Sy.objectId = ?1 and Sy.objectClassId = ?3 and Sy.parentObjectId = ?2 and Sy.parentObjectClassId = ?4")
    public com.noosh.app.commons.entity.collaboration.SyContainable findByObjectIdAndParentObjectId(Long objectId, Long parentObjectId, Long objectClassId, Long parentObjectClassId);

    @Query("select Sy from SyContainable Sy where Sy.objectId in ?1 and Sy.objectClassId = ?3 and Sy.parentObjectId in ?2 and Sy.parentObjectClassId = ?4")
    public List<SyContainable> findByObjectIdInAndParentObjectIdIn(List<Long> objectIds, List<Long> parentObjectId, Long objectClassId, Long parentObjectClassId);

}
