package com.noosh.app.repository.breakout;

import com.noosh.app.commons.entity.breakout.Breakout;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 10/9/17
 */

public interface BreakoutRepository extends JpaRepository<Breakout, Long> {
    List<Breakout> findByObjectIdAndObjectClassId(Long objectId, Long objectClassId);

    @Query("select Bk from Breakout Bk where Bk.objectId = ?1 and Bk.objectClassId = ?2 and Bk.nestingLevel = ?3 order by Bk.breakoutType.sortOrder")
    List<Breakout> findByObjectIdAndObjectClassIdAndNestingLevel(Long objectId, Long objectClassId, Long nestingLevel);

    @Query("select Bk from Breakout Bk where Bk.objectId = ?1 and Bk.objectClassId = ?2 and Bk.breakoutType.parentTypeId = ?3 order by Bk.breakoutType.sortOrder")
    List<Breakout> findByParent(Long objectId, Long objectClassId, Long parentTypeId);
}
