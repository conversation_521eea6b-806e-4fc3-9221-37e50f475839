package com.noosh.app.repository.estimate;

import com.noosh.app.commons.entity.estimate.EstimateItemPrice;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * User: leilaz
 * Date: 5/7/17
 */

public interface EstimateItemPriceRepository extends JpaRepository<EstimateItemPrice, Long> {
    List<EstimateItemPrice> findByEstimateItemIdOrderByIdAsc(Long estimateItemId);
    EstimateItemPrice findEstimateItemPriceById(Long estimateItemPriceId);
    List<EstimateItemPrice> findByIdIn(List<Long> estimateItemPriceIds);
}
