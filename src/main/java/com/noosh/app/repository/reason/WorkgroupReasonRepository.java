package com.noosh.app.repository.reason;

import com.noosh.app.commons.entity.reason.WorkgroupReason;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * User: leilaz
 * Date: 8/4/20
 */

public interface WorkgroupReasonRepository extends JpaRepository<WorkgroupReason, Long> {
    public List<WorkgroupReason> findByWorkgroupIdAndIsActive(Long workgroupId, boolean isActive);
}
