package com.noosh.app.repository.jpa.property;

import com.noosh.app.commons.entity.property.PropertyAttribute;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Set;

/**
 * User: leilaz
 * Date: 12/30/15
 * Time: 10:24 PM
 */

public interface PropertyAttributeRepository extends JpaRepository<PropertyAttribute, Long> {
    PropertyAttribute findByPrPropertyIdAndPrPropertyParamId(Long propertyId, Long paramId);

    Set<PropertyAttribute> findByPrPropertyId(Long propertyId);

    int deleteByPrPropertyId(Long propertyId);

    @Modifying
    @Query("delete from PropertyAttribute pa where pa.prPropertyId in ?1")
    void deleteWithPropertyIds(List<Long> propertyIds);
}
