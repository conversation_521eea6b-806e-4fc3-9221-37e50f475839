package com.noosh.app.repository.jpa.quote;

import com.noosh.app.commons.entity.quote.QuoteItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * @Author: neals
 * @Date: 12/09/2015
 */

public interface QuoteItemRepository extends JpaRepository<QuoteItem, Long> {
    List<QuoteItem> findByQuoteId(final Long quoteId);

    @Query("select QT from QuoteItem QT, Quote Q where QT.quoteId = Q.id and QT.jobId in ?1 and Q.stateId in ?2")
    List<QuoteItem> findByJobIdsAndStateIds(List<Long> jobIds, List<Long> stateIds);
}