package com.noosh.app.repository.jpa.localization;

import com.noosh.app.commons.entity.localization.Localization;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * User: leilaz
 * Date: 11/22/23
 */
public interface LocalizationRepository extends JpaRepository<Localization, Long> {
    List<Localization> findByResourceNameAndPortalAndLanguage(String resourceName, String portal, String language);

    List<Localization> findByPortalAndLanguage(String portal, String language);
}
