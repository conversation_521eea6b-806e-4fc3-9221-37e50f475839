package com.noosh.app.repository.jpa.invoice;

import com.noosh.app.commons.entity.invoice.Invoice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * @Author: Lukez
 * @Date: 12/28/2015
 */

public interface InvoiceRepository extends JpaRepository<Invoice, Long> {
    @Query("select invoice from Invoice invoice, SyContainable sc where invoice.orderId = ?1 " +
            " and invoice.isTemplate = false and sc.parentObjectId = ?2 and sc.parentObjectClassId = ?3 and " +
            " sc.objectId = invoice.id and sc.objectClassId = 1000119")
    List<Invoice> findInvoiceByOrderAndParentId(Long orderId, Long parentId, Long parentClassId);

    @Query("select invoice from Invoice invoice where invoice.isTemplate = true and invoice.orderId = ?1")
    Invoice findFinalInvoiceTemplate(Long parentOrderId);

    List<Invoice> findByOrderIdOrderById(Long orderId);
    
    List<Invoice> findByOrderId(Long orderId);
}
