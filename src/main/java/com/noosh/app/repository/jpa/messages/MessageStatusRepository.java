package com.noosh.app.repository.jpa.messages;

import com.noosh.app.commons.entity.messages.MessageStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface MessageStatusRepository extends JpaRepository<MessageStatus, Long> {

    @Query("SELECT OBJECT(ejb) FROM MessageStatus ejb WHERE ejb.messageId = ?1 AND ejb.sendToUserId = ?2")
    MessageStatus findByMessageAndUser(long messId, long userId);

    @Query("SELECT OBJECT(ejb) FROM MessageStatus ejb WHERE ejb.sendToUserId = ?1")
    List<MessageStatus> findByUser(long userId);

    @Query("SELECT OBJECT(ejb) FROM MessageStatus ejb WHERE ejb.sendToUserId = ?1 AND ejb.objectStateId = ?2")
    List<MessageStatus> findByUserAndStatus(long userId, long statusId);

    @Query("SELECT OBJECT(ejb) FROM MessageStatus ejb WHERE ejb.messageId = ?1")
    List<MessageStatus> findByMessage(long messId);

}