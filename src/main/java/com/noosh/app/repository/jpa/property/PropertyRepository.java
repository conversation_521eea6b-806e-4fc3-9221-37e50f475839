package com.noosh.app.repository.jpa.property;

import com.noosh.app.commons.entity.property.Property;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 12/30/15
 * Time: 6:50 PM
 */

public interface PropertyRepository extends JpaRepository<Property, Long> {
    @Query("select P from Property P where objectId = ?1 and objectClassId = ?2 and prPropertyTypeId = ?3")
    public Property findByObjectIdAndObjectClassIdAndPrPropertyTypeId(Long objectId, Long objectClassId, Long prPropertyTypeId);

    public List<Property> findByParentPropertyIdAndPropertyName(Long parentPropertyId, String name);

    @Query("select P from Property P where prPropertyId = ?1 OR parentPropertyId = ?1")
    public List<Property> findByParentPropertyId(Long parentPropertyId);
}
