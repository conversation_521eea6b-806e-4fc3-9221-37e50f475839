package com.noosh.app.repository.jpa.job;

import com.noosh.app.commons.entity.job.PcJob;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 5/19/16
 */

public interface JobRepository extends JpaRepository<PcJob, Long> {
    @Query("select J FROM PcJob J, SyContainable C, SpecReference SR " +
            "WHERE J.pcJobId=C.objectId  AND J.spSpecReferenceId = SR.spSpecReferenceId  " +
            "AND C.objectClassId=1000020  AND C.parentObjectClassId=1000000  " +
            "AND C.parentObjectId = ?1  AND J.spSpecReferenceId = ?2 " +
            "AND ( J.ocObjectStateId is null OR J.ocObjectStateId=2000130) ")
    public List<PcJob> findJobBySpecRefIdAndProjectId(Long projectId, Long specReferenceId);

    List<PcJob> findBySpSpecReferenceId(Long specReferenceId);

}
