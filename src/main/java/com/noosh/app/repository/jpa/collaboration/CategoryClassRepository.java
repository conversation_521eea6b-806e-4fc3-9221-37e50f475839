package com.noosh.app.repository.jpa.collaboration;

import com.noosh.app.commons.entity.collaboration.CategoryClass;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 4/29/20
 */
public interface CategoryClassRepository extends JpaRepository<CategoryClass, Long> {
    @Modifying
    @Query("delete from CategoryClass cc where cc.id in ?1")
    void deleteCategoryClassWithIds(List<Long> ids);

    List<CategoryClass> findByCategoryId(Long categoryId);
}
