package com.noosh.app.repository.jpa.order;

import com.noosh.app.commons.entity.order.Order;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


public interface OrderRepository extends JpaRepository<Order,Long> {
    @Query("select oo from Order oo, OrderState os where oo.id = os.orderId " +
            " and oo.parentOrderId = ?1 and os.isCurrent = true and os.objectStateId in ?2")
    List<Order> findAllOrdersByParentOrderId(Long parentOrderId, List<Long> stateIds);

    List<Order> findByParentOrderIdOrderByIdAsc(Long parentOrderId);

    @Query("select id from Order where parentOrderId = ?1 and id != parentOrderId")
    List<Long> findChangeOrderIds(Long parentOrderId);
}
