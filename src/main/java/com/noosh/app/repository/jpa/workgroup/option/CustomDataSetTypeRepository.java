package com.noosh.app.repository.jpa.workgroup.option;

import com.noosh.app.commons.entity.workgroup.option.CustomDataSetType;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * User: leilaz
 * Date: 4/2/23
 */
public interface CustomDataSetTypeRepository extends JpaRepository<CustomDataSetType, Long> {
    public List<CustomDataSetType> findByWorkgroupId(Long workgroupId);

    CustomDataSetType findByWorkgroupIdAndName(Long workgroupId, String name);
}
