package com.noosh.app.repository.jpa.team;

import com.noosh.app.commons.entity.team.TeamMember;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * @Author: neals
 * @Date: 12/29/2015
 */

public interface TeamMemberRepository extends JpaRepository<TeamMember, Long> {
    TeamMember findByTeamIdAndUserId(Long teamId, Long userId);
    TeamMember findByTeamIdAndUserIdAndIsCurrent(Long teamId, Long userId, boolean isCurrent);
}
