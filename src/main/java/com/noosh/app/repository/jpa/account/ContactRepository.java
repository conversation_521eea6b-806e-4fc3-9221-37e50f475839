package com.noosh.app.repository.jpa.account;

import com.noosh.app.commons.entity.account.Contact;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 9/11/17
 */

public interface ContactRepository extends JpaRepository<Contact, Long> {
    @Query("select c from Contact c where c.isLiveUser = ?3 and c.contactOwnerTypeId = ?2 and c.ownerObjectId = ?1")
    List<Contact> findByObjectIdAndType(Long objectId, Long objectTypeId, Boolean isLive);

    @Query("select c from Contact c where c.isObsolete = ?3 and c.contactOwnerTypeId = 1000001 and c.contactTypeId = ?2 and c.ownerObjectId = ?1")
    List<Contact> findNonObsolete(Long objectId, Long objectTypeId, Boolean isObsolete);

    @Query("select c from Contact c where c.contactOwnerTypeId = 1000001 and c.ownerObjectId = ?1 and c.contactTypeId = ?2")
    List<Contact> findByWorkgroupAndType(Long workgroupId, Long objectTypeId);
}
