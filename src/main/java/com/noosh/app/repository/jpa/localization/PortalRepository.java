package com.noosh.app.repository.jpa.localization;

import com.noosh.app.commons.entity.localization.Portal;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * User: leilaz
 * Date: 1/9/24
 */
public interface PortalRepository extends JpaRepository<Portal, Long> {
    public Portal findByName(String name);

    public List<Portal> findAllByOrderByNameAsc();
}
