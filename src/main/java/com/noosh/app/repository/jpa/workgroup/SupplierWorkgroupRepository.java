package com.noosh.app.repository.jpa.workgroup;

import com.noosh.app.commons.entity.workgroup.SupplierWorkgroup;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * @Author: neals
 * @Date: 08/04/2016
 */

public interface SupplierWorkgroupRepository extends JpaRepository<SupplierWorkgroup, Long> {

    List<SupplierWorkgroup> findByClientWorkgroupIdAndOwnerWorkgroupId(Long clientWorkgroupId, Long ownerWorkgroupId);

    List<SupplierWorkgroup> findBySupplierWorkgroupIdAndOwnerWorkgroupId(Long supplierWorkgroupId, Long ownerWorkgroupId);
}
