package com.noosh.app.repository.jpa.reason;

import com.noosh.app.commons.entity.reason.Reason;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ReasonRepository extends JpaRepository<Reason, Long> {

    @Query("from Reason where isSystem = true and typeId = 2")
    List<Reason> findSystemReasons();
}
