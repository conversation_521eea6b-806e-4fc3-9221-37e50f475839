package com.noosh.app.repository.jpa.accounts;

import com.noosh.app.commons.entity.accounts.WorkgroupAttributeRegistration;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


public interface WorkgroupAttributeRegistrationRepository extends JpaRepository<WorkgroupAttributeRegistration, Long> {

    @Query("SELECT R FROM WorkgroupAttributeRegistration R WHERE R.ownerWorkgroupId = ?1 AND R.workgroupAttributeId = ?2")
    WorkgroupAttributeRegistration findByGroupAndAttributeId(long ownerWorkgroupId, long workgroupAttributeId);

    List<WorkgroupAttributeRegistration> findByWorkgroupAttributeIdIn(List<Long> workgroupAttributeIds);

    @Query("SELECT R FROM WorkgroupAttributeRegistration R, WorkgroupAttribute A " +
            "WHERE R.workgroupAttributeId = A.id AND R.ownerWorkgroupId = ?1 AND A.typeId = ?2 AND R.isActive = ?3 " +
            "ORDER BY R.ordinalNumber, R.id ASC")
    List<WorkgroupAttributeRegistration> findAllWithWgIdAndTypeId(Long ownerWorkgroupId, long typeId, boolean isActive);

    @Query("SELECT R FROM WorkgroupAttributeRegistration R, WorkgroupAttribute A " +
            "WHERE R.workgroupAttributeId = A.id AND R.ownerWorkgroupId = ?1 AND A.typeId = ?2 AND R.isActive = ?3 " +
            "ORDER BY A.labelStr ASC")
    List<WorkgroupAttributeRegistration> findAllWithWgIdAndTypeIdOrderBYLabel(Long ownerWorkgroupId, long typeId, boolean isActive);
}