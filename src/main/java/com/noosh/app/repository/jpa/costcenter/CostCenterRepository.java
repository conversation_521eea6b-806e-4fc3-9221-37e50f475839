package com.noosh.app.repository.jpa.costcenter;

import com.noosh.app.commons.entity.costcenter.CostCenter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 12/5/21
 */

public interface CostCenterRepository extends JpaRepository<CostCenter, Long> {
    @Query("select Sy from CostCenter Sy where Sy.ownerWorkgroupId = ?1 and (Sy.clientId = ?2 or Sy.clientId is null)")
    public List<CostCenter> findByOwnerWorkgroupIdAndClientId(Long ownerWorkgroupId, Long clientId);

    public List<CostCenter> findByOwnerWorkgroupId(Long ownerWorkgroupId);
}
