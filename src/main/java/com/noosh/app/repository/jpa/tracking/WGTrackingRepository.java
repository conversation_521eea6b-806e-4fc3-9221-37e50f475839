package com.noosh.app.repository.jpa.tracking;

import com.noosh.app.commons.entity.tracking.WGTracking;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 3/6/24
 */
public interface WGTrackingRepository extends JpaRepository<WGTracking, Long> {
    @Query("select w from WGTracking w where w.objectId = ?1 and w.objectClassId = 1000100 and w.trackingTypeId in ?2")
    public List<WGTracking> findByWorkgroup(Long workgroupId, List<Long> typeIds);
}
