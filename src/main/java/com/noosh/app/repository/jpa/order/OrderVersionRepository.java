package com.noosh.app.repository.jpa.order;

import com.noosh.app.commons.entity.order.OrderVersion;
import org.springframework.data.jpa.repository.JpaRepository;


/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 11/16/15
 */

public interface OrderVersionRepository extends JpaRepository<OrderVersion, Long>{

    OrderVersion findFirstByOrderIdAndIsCurrent(Long orderId, Boolean isCurrent);

    OrderVersion findByOrderIdAndVersion(Long orderId, Long version);
}
