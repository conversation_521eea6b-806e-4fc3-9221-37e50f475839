package com.noosh.app.repository.jpa.order;

import com.noosh.app.commons.entity.order.OrderVersion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 11/16/15
 */

public interface OrderVersionRepository extends JpaRepository<OrderVersion, Long>{

    OrderVersion findFirstByOrderIdAndIsCurrent(Long orderId, Boolean isCurrent);

    OrderVersion findByOrderIdAndVersion(Long orderId, Long version);

    @Query("select o from OrderVersion o, OrderState os where o.isCurrent = true and o.orderTypeId != 1000002 and o.orderId = os.orderId " +
            " and os.isCurrent = true and o.id in (select oi.orderVersionId from OrderItem oi where oi.jobId in ?1) " +
            " and   os.objectStateId NOT IN (2000079, 2000083, 2000080, 2000084) and o.buyerWorkgroupId = ?2 and o.buClientWorkgroupId is null")
    List<OrderVersion> findOrderByBuyWgIdAndJobIdOutsourcer(List<Long> jobIds, Long workgroupId);

    @Query("select o from OrderVersion o, OrderState os where o.isCurrent = true and o.orderTypeId != 1000002 and o.orderId = os.orderId " +
            " and os.isCurrent = true and o.id in (select oi.orderVersionId from OrderItem oi where oi.jobId in ?1) " +
            " and   os.objectStateId NOT IN (2000079, 2000083, 2000080, 2000084) and o.buyerWorkgroupId = ?2")
    List<OrderVersion> findOrderByBuyWgIdAndJobIdNonOutsourcer(List<Long> jobIds, Long workgroupId);

    @Query("select o from OrderVersion o, OrderState os where o.isCurrent = true and o.orderTypeId != 1000002 and o.orderId = os.orderId " +
            " and os.isCurrent = true and o.id in (select oi.orderVersionId from OrderItem oi where oi.jobId in ?1) " +
            " and   os.objectStateId NOT IN (2000079, 2000083, 2000080, 2000084) and o.supplierWorkgroupId = ?2 and o.buClientWorkgroupId is not null")
    List<OrderVersion> findOrderBySupplierWgIdAndJobIdOutsourcer(List<Long> jobIds, Long workgroupId);

    @Query("select o from OrderVersion o, OrderState os where o.isCurrent = true and o.orderTypeId != 1000002 and o.orderId = os.orderId " +
            " and os.isCurrent = true and o.id in (select oi.orderVersionId from OrderItem oi where oi.jobId in ?1) " +
            " and   os.objectStateId NOT IN (2000079, 2000083, 2000080, 2000084) and o.supplierWorkgroupId = ?2")
    List<OrderVersion> findOrderBySupplierWgIdAndJobIdNonOutsourcer(List<Long> jobIds, Long workgroupId);

    @Query("select o from OrderVersion o where o.order.invoiceAdjustmentParentOrderId = ?1")
    List<OrderVersion> findInvoiceAdjustmentOrderByParentOrderId(Long parentOrderId);
    
    @Query("SELECT  OV FROM OrderVersion OV, Order OO, OrderState OS " +
            " WHERE  OO.parentOrderId = ?1  AND OO.isClosing = true " +
            " AND OV.orderId = OO.id AND OV.isCurrent = true " +
            " AND OS.orderId = OO.id AND OS.isCurrent = true AND OS.objectStateId in (2000030, 2500045) ")
    OrderVersion findPendingOrCompletedClosingChangeOrder(Long originalOrderId);
}
