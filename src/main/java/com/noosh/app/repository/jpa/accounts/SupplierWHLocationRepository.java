package com.noosh.app.repository.jpa.accounts;

import java.util.List;

import com.noosh.app.commons.entity.accounts.SupplierWHLocation;
import org.springframework.data.jpa.repository.JpaRepository;

public interface SupplierWHLocationRepository extends JpaRepository<SupplierWHLocation, Long> {

    SupplierWHLocation findBySupplierIdAndWarehouseLocationId(long supplierId, long warehouseLocationId);
    List<SupplierWHLocation> findAllBySupplierIdIn(List<Long> supplierIds);

}