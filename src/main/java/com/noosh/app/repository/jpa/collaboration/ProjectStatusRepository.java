package com.noosh.app.repository.jpa.collaboration;

import com.noosh.app.commons.entity.collaboration.ProjectStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 7/6/22
 */

public interface ProjectStatusRepository extends JpaRepository<ProjectStatus, Long> {
    public List<ProjectStatus> findByWorkgroupIdOrderByStatusOrder(Long workgroupId);

    @Query("select ps from ProjectStatus ps where ps.workgroupId = ?1 and ps.isDefault = true")
    public ProjectStatus findDefaultByWorkgroupId(Long workgroupId);

    @Query("SELECT ejb FROM ProjectStatus ejb WHERE ejb.workgroupId = ?1")
    List<ProjectStatus> findByWorkgroupId(long workgroupId);

    @Query("SELECT ejb FROM ProjectStatus ejb WHERE ejb.workgroupId = ?1 AND ejb.statusOrder = ?2")
    ProjectStatus findByWorkgroupIdAndOrder(long workgroupId, long statusOrder);
}
