package com.noosh.app.repository.jpa.order;

import com.noosh.app.commons.entity.order.OrderItem;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 11/3/15
 */

public interface OrderItemRepository extends JpaRepository<OrderItem,Long> {

    List<OrderItem> findByOrderVersionId(final long orderVersionId);

    List<OrderItem> findByOrderVersionIdOrderByItemIndex(final long orderVersionId);

    List<OrderItem> findByJobId(Long jobId);

    OrderItem findByJobIdAndOrderVersionId(Long jobId, Long orderVersionId);

    List<OrderItem> findBySpecId(Long specId);

    List<OrderItem> findByOrderVersionIdIn(List<Long> orderVersionIds);
}
