package com.noosh.app.repository.jpa.accounts;

import com.noosh.app.commons.entity.accounts.WorkgroupAttribute;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;


public interface WorkgroupAttributeRepository extends JpaRepository<WorkgroupAttribute, Long> {

    @Query("SELECT A FROM WorkgroupAttribute A, WorkgroupAttributeType T, WorkgroupAttributeRegistration R " +
            "WHERE A.typeId = T.id AND R.workgroupAttributeId = A.id AND A.id = ?1 AND R.ownerWorkgroupId = ?2")
    WorkgroupAttribute findByIdAndWorkgroupId(Long id, Long workgroupId);

    @Query("SELECT A FROM WorkgroupAttribute A, WorkgroupAttributeType T, WorkgroupAttributeRegistration R " +
            "WHERE A.typeId = T.id AND R.workgroupAttributeId = A.id AND A.id IN ?1 AND R.ownerWorkgroupId = ?2")
    List<WorkgroupAttribute> findByIdsAndWorkgroupId(List<Long> optionIds, Long workgroupId);

    @Query("SELECT A FROM WorkgroupAttribute A, WorkgroupAttributeType T, WorkgroupAttributeRegistration R " +
            "WHERE A.typeId = T.id AND R.workgroupAttributeId = A.id AND T.id = ?1 AND R.ownerWorkgroupId = ?2")
    List<WorkgroupAttribute> findByListIdAndWorkgroupId(Long listId, Long workgroupId);
}