package com.noosh.app.repository.jpa.userfield;

import com.noosh.app.commons.entity.userfield.CustomField;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * @Author: neals
 * @Date: 05/19/2016
 */

public interface CustomFieldRepository extends JpaRepository<CustomField, Long> {

    List<CustomField> findByOwnerWorkgroupIdAndCustomFieldClassId(Long ownerWorkgroupId, Long customFieldClassId);

    CustomField findByOwnerWorkgroupIdAndId(Long ownerWgId, Long id);

    List<CustomField> findByOwnerWorkgroupIdAndCustomFieldClassIdOrderByOrdinalNumber(Long ownerWorkgroupId, Long customFieldClassId);
}
