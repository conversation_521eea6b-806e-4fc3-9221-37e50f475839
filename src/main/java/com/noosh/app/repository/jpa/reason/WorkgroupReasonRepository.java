package com.noosh.app.repository.jpa.reason;

import com.noosh.app.commons.entity.reason.WorkgroupReason;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface WorkgroupReasonRepository extends JpaRepository<WorkgroupReason, Long> {

    @Query("from WorkgroupReason where workgroupId = :workgroupId and isActive = true")
    List<WorkgroupReason> findActiveByWorkgroupId(@Param("workgroupId") Long workgroupId);
}
