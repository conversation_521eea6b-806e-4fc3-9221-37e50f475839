package com.noosh.app.repository.jpa.document;

import com.noosh.app.commons.entity.document.TgTag;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 4/12/22
 */

public interface TgTagRepository extends JpaRepository<TgTag, Long> {
    @Query("select Sy from TgTag Sy where Sy.ownerWorkgroupId = ?1 and Sy.objectClassId = ?2 and Sy.isActive = ?3")
    public List<TgTag> findByOwnerWgIdAndObjectClassId(Long workgroupId, Long objectClassId, boolean isActive);

    @Query("select Sy from TgTag Sy where Sy.ownerWorkgroupId = ?1 and Sy.objectClassId = ?2 and Sy.isActive = ?3 and Sy.name like ?4")
    public List<TgTag> findByNameAndWorkgroupId(Long workgroupId, Long objectClassId, boolean isActive, String name);
}
