package com.noosh.app.repository.jpa.messages;

import com.noosh.app.commons.entity.messages.Message;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface MessageRepository extends JpaRepository<Message, Long> {

    @Query("SELECT OBJECT(ejb) FROM Message ejb WHERE ejb.authorUserId = ?1")
    List<Message> findByOwner(long owner);

    @Query("SELECT OBJECT(ejb) FROM Message ejb WHERE ejb.subject = ?1")
    List<Message> findBySubject(String subj);

}