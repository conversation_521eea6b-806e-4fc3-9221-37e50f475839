package com.noosh.app.repository.jpa.property;

import com.noosh.app.commons.entity.property.CustomField;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * @Author: neals
 * @Date: 05/19/2016
 */

public interface CustomFieldRepository extends JpaRepository<CustomField, Long> {

    List<CustomField> findByOwnerWorkgroupIdAndCustomFieldClassId(Long ownerWorkgroupId, Long customFieldClassId);

    CustomField findByOwnerWorkgroupIdAndId(Long ownerWgId, Long id);

    @Query("select cm from CustomField cm where cm.ownerWorkgroupId = ?1 and cm.customFieldClassId = ?2 and cm.propertyParamId = ?3")
    CustomField findByWgIdAndFieldClassIdAndParamId(Long ownerWgId, Long customFieldClassId, Long propertyParamId);
}
