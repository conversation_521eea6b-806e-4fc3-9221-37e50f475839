package com.noosh.app.repository.jpa.property;

import com.noosh.app.commons.entity.property.PropertyParam;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

/**
 * User: leilaz
 * Date: 12/30/15
 */

public interface PropertyParamRepository extends JpaRepository<PropertyParam, Long> {
    PropertyParam findByParamNameAndPrPropertyTypeId(String name, Long prPropertyTypeId);
    PropertyParam findByParamNameAndPrDataTypeId(String name, Long prDataTypeId);

    @Query("select P from PropertyParam P where P.paramName = ?1 and P.prPropertyTypeId = ?2 and P.prDataTypeId = ?3")
    public PropertyParam findByNameAndPropertyAndTypeId(String name, Long prPropertyTypeId, Long prDataTypeId);
}
