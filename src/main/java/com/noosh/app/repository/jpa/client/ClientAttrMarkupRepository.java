package com.noosh.app.repository.jpa.client;

import com.noosh.app.commons.entity.client.ClientAttrMarkup;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * User: leilaz
 * Date: 2/9/17
 */

public interface ClientAttrMarkupRepository extends JpaRepository<ClientAttrMarkup, Long> {

    List<ClientAttrMarkup> findClientAttrMarkupsByClientWorkgroupId(Long clientId);

}
