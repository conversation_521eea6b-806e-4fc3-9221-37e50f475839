package com.noosh.app.repository.jpa.collaboration;

import com.noosh.app.commons.entity.collaboration.WorkgroupDeactivationReason;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface WorkgroupDeactivationReasonRepository extends JpaRepository<WorkgroupDeactivationReason, Long> {
    
    @Query("SELECT wdr FROM WorkgroupDeactivationReason wdr WHERE wdr.workgroupId = ?1 AND wdr.isActive = true ORDER BY wdr.reasonOrder")
    List<WorkgroupDeactivationReason> findActiveByWorkgroupIdOrderByReasonOrder(Long workgroupId);
}