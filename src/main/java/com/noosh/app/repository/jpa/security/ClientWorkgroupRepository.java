package com.noosh.app.repository.jpa.security;

import java.util.List;

import com.noosh.app.commons.entity.security.ClientWorkgroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

/**
 * User: leilaz
 * Date: 2/9/17
 */

public interface ClientWorkgroupRepository extends JpaRepository<ClientWorkgroup, Long> {
    @Query("select Sy from ClientWorkgroup Sy where Sy.clientAcWorkgroupId = ?1 and Sy.ownerAcWorkgroupId = ?2")
    public ClientWorkgroup findByClientIdAndOwnerId(Long clientWgId, Long ownerAcWorkgroupId);
    @Query("select Sy.clientAcWorkgroupId from ClientWorkgroup Sy where Sy.ownerAcWorkgroupId = ?1 and Sy.isNoosh = true and Sy.isInactive != true")
    List<Long> findByOwnerId(Long ownerAcWorkgroupId);
}
