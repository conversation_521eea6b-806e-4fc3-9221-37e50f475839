package com.noosh.app.repository.jpa.security;

import com.noosh.app.commons.entity.security.ClientWorkgroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * User: leilaz
 * Date: 2/9/17
 */

public interface ClientWorkgroupRepository extends JpaRepository<ClientWorkgroup, Long> {

    @Query("select cw from ClientWorkgroup cw where cw.ownerAcWorkgroupId = ?1 and cw.isNoosh = 1 and cw.isInactive <> 1 ")
    List<ClientWorkgroup> findClientWorkgroups(long ownerAcWorkgroupId);

}
