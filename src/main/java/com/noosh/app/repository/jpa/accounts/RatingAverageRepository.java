package com.noosh.app.repository.jpa.accounts;

import com.noosh.app.commons.entity.accounts.RatingAverage;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

/**
 * author: Yang
 * Date: 11/6/2022
 */
public interface RatingAverageRepository extends JpaRepository<RatingAverage, Long> {
    Optional<RatingAverage> findByRatedByWorkGroupIdAndRatedForWorkGroupIdAndObjectClassId(long ratedByWorkGroupId, long ratedForWorkGroupId, long objectClassId);
}
