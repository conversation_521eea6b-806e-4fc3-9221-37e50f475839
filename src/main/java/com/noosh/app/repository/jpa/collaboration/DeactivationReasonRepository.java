package com.noosh.app.repository.jpa.collaboration;

import com.noosh.app.commons.entity.collaboration.DeactivationReason;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface DeactivationReasonRepository extends JpaRepository<DeactivationReason, Long> {
    
    @Query("SELECT dr FROM DeactivationReason dr WHERE dr.isSystem = true")
    List<DeactivationReason> findSystemReasons();
}