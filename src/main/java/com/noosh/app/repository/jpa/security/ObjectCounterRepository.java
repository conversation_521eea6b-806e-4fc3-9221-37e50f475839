package com.noosh.app.repository.jpa.security;

import com.noosh.app.commons.entity.security.ObjectCounter;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;


public interface ObjectCounterRepository extends JpaRepository<ObjectCounter, Long> {

    @Query("SELECT ejb FROM ObjectCounter ejb WHERE ejb.workgroupId = ?1 AND ejb.counterTypeId = ?2")
    ObjectCounter findByWorkgroupAndType(long workgroupId, long counterTypeId);

    @Query("SELECT c FROM ObjectCounter c, ObjectCounterType ct WHERE c.workgroupId = ?1 AND c.counterTypeId = ct.id AND ct.workgroupId = ?1 AND ct.labelStr = ?2")
    ObjectCounter findByWorkgroupAndLabel(long workgroupId, String label);

}