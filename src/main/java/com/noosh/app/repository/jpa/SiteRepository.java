package com.noosh.app.repository.jpa;

import com.noosh.app.commons.entity.security.Site;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

/**
 * User: leilaz
 * Date: 4/6/16
 */

public interface SiteRepository extends JpaRepository<Site, Long> {

    @Query("select s from Site s, ClientWorkgroup cw  where s.buClientWorkgroupId = cw.id and cw.clientAcWorkgroupId = ?1")
    Site findByClientWorkgroupId(Long currentUserWorkGroupId);

}
