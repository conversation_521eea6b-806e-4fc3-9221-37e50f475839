package com.noosh.app.repository.quote;

import com.noosh.app.commons.entity.quote.QuotePrice;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * @Author: neals
 * @Date: 12/09/2015
 */

public interface QuotePriceRepository extends JpaRepository<QuotePrice,Long> {
    List<QuotePrice> findByQuoteItemId(final Long quoteItemId);

    List<QuotePrice> findByParentQuotePriceId(Long parentQuotePriceId);

    List<QuotePrice> findByRootQuotePriceIdAndIsVisibleToBuyer(Long rootQuotePriceId, boolean isVisibleToBuyer);
}