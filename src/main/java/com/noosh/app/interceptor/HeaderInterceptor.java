package com.noosh.app.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

@Component
public class HeaderInterceptor implements RequestInterceptor {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    public final String headerAuthorization = "Authorization";

    @Override
    public void apply(RequestTemplate template) {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return;
        }
        String authorization = requestAttributes.getRequest().getHeader(headerAuthorization);
        if (authorization != null) {
            template.header(headerAuthorization, authorization);
        }
    }
}