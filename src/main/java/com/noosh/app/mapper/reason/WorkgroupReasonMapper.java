package com.noosh.app.mapper.reason;

import com.noosh.app.commons.dto.reason.WorkgroupReasonDTO;
import com.noosh.app.commons.entity.reason.WorkgroupReason;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring", uses = {ReasonMapper.class})
public interface WorkgroupReasonMapper {

    WorkgroupReasonDTO toDTO(WorkgroupReason workgroupReason);

    List<WorkgroupReasonDTO> toDTOs(List<WorkgroupReason> workgroupReasons);
}
