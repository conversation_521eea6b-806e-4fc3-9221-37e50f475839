package com.noosh.app.mapper.collaboration;

import com.noosh.app.commons.dto.collaboration.DeactivationReasonDTO;
import com.noosh.app.commons.entity.collaboration.DeactivationReason;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class DeactivationReasonMapper {

    public abstract List<DeactivationReasonDTO> toDTOs(List<DeactivationReason> entities);

    public abstract DeactivationReasonDTO toDTO(DeactivationReason entity);

    public abstract DeactivationReason toEntity(DeactivationReasonDTO dto);
}