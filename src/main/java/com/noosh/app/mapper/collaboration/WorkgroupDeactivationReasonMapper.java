package com.noosh.app.mapper.collaboration;

import com.noosh.app.commons.dto.collaboration.WorkgroupDeactivationReasonDTO;
import com.noosh.app.commons.entity.collaboration.WorkgroupDeactivationReason;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;

@Mapper(componentModel = "spring", uses = {DeactivationReasonMapper.class}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class WorkgroupDeactivationReasonMapper {

    @Mapping(target = "reason", source = "deactivationReason")
    public abstract WorkgroupDeactivationReasonDTO toDTO(WorkgroupDeactivationReason entity);

    @Mapping(target = "reason", source = "deactivationReason")
    public abstract List<WorkgroupDeactivationReasonDTO> toDTOs(List<WorkgroupDeactivationReason> entities);

    @Mapping(target = "deactivationReason", source = "reason")
    public abstract WorkgroupDeactivationReason toEntity(WorkgroupDeactivationReasonDTO dto);
}