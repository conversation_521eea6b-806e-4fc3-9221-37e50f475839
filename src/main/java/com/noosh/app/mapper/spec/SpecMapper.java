package com.noosh.app.mapper.spec;

import com.noosh.app.commons.dto.spec.SpecDTO;
import com.noosh.app.commons.entity.security.ObjectState;
import com.noosh.app.commons.entity.spec.Spec;
import com.noosh.app.commons.vo.spec.SpecVO;
import com.noosh.app.repository.jpa.security.ObjectStateRepository;
import com.noosh.app.service.util.I18NUtils;
import com.noosh.app.service.util.NooshOneUrlUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import jakarta.inject.Inject;

/**
 * User: leilaz
 * Date: 5/31/16
 */
@Mapper(componentModel = "spring", uses = {}, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class SpecMapper {
    @Inject
    private I18NUtils i18NUtils;
    @Inject
    private SpecTypeMapper specTypeMapper;
    @Inject
    private ObjectStateRepository objectStateRepository;
    @Inject
    private SpecReferenceMapper specReferenceMapper;

    @Mapping(source = "creator", target = "creator", ignore = true)
    @Mapping(target = "isLocked", expression = "java(specDTO.getIsLocked() ? (short) 1 : (short) 0)")
    @Mapping(target = "isItemVersion", expression = "java(specDTO.getIsItemVersion() ? (short) 1 : (short) 0)")
    @Mapping(target = "specName", source = "name")
    public abstract Spec convertToEntity(SpecDTO specDTO);

    public SpecDTO convertToSpecDTO(Spec spec) {
        SpecDTO specDTO = new SpecDTO();
        specDTO.setName(spec.getSpecName());
        specDTO.setSpecUserStateId(spec.getSpecUserStateId());
        specDTO.setSpecUserState(spec.getSpecUserStateId() == null ? null : i18NUtils.getObjectStateMessage(spec.getSpecUserStateId()));
        ObjectState specUserState = objectStateRepository.findById(spec.getSpecUserStateId() == null ? (long) -1 : spec.getSpecUserStateId()).orElse(null);
        specDTO.setSpecUserStateStrId(specUserState != null ? specUserState.getDescriptionStrId().toString() : null);
        specDTO.setId(spec.getId());
        specDTO.setIsImmutable((spec.getIsLocked() != null && spec.getIsLocked() == (short) 1)
                || (spec.getLockCount() != null && spec.getLockCount().longValue() > 0));
        specDTO.setIsItemVersion(spec.getIsItemVersion() != null && spec.getIsItemVersion() == (short) 1);
        specDTO.setIsLocked(spec.getIsLocked() != null && spec.getIsLocked() == (short) 1);
        specDTO.setCreateDate(spec.getCreateDate());
        specDTO.setModDate(spec.getModDate());
        specDTO.setCreateUserId(spec.getCreateUserId());
        specDTO.setIncludeAup(spec.getIncludeAup());
        specDTO.setSpSpecTypeId(spec.getSpSpecTypeId());
        specDTO.setLockCount(spec.getLockCount());
        specDTO.setCreator(spec.getCreator() != null ? spec.getCreator().getPerson().getFullName() : null);
        specDTO.setVersionNumber(spec.getVersionNumber());
        specDTO.setProductNumber(spec.getSpecReference().getRefNumber() + "-" + spec.getVersionNumber());
        specDTO.setFullName(specDTO.getIsItemVersion() ? specDTO.getProductNumber() : (specDTO.getName() + " ("+spec.getSpecReference().getRefNumber()+")"));
        specDTO.setNodeId(spec.getSpecNode() != null ? spec.getSpecNode().getId() : null);
        specDTO.setPropertyId(spec.getPrPropertyId());
        specDTO.setQuantity1(spec.getQuantity1());
        specDTO.setSpecType(specTypeMapper.toDTO(spec.getSpecType()));
        specDTO.setSpecReference(specReferenceMapper.toDTO(spec.getSpecReference()));

        return specDTO;
    }

    public abstract SpecVO toVOWithoutLink(SpecDTO specDTO);

    public SpecVO toVOForOrderItem(long specNodeId, SpecDTO specDTO, Long projectId, String back) {
        SpecVO specVO = toVOWithoutLink(specDTO);
        if (specDTO.getSpecReference() != null) {
            specVO.setSpecReferenceId(specDTO.getSpecReference().getSpSpecReferenceId());
        }
        specVO.setSpecLink(specDTO.getSpecLink() == null ? NooshOneUrlUtil.composeViewSpecLinkForOrderItemToEnterprise(
                specNodeId, specDTO.getId(), projectId, back) : specDTO.getSpecLink());
        if (specVO.getFullName() == null && specDTO.getSpecReference() != null) {
            String versionNumber = specDTO.getVersionNumber() == null ? "" : specDTO.getVersionNumber();
            String productNumber = specDTO.getSpecReference().getRefNumber() + "-" + versionNumber;
            specVO.setFullName(specDTO.getIsItemVersion() ? productNumber : (specDTO.getName() + " (" + specDTO.getSpecReference().getRefNumber() + ")"));
        }
        return specVO;
    }
}
