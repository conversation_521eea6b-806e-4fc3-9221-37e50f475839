package com.noosh.app.commons.entity.shipment;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.constant.RequestTypeID;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.account.Address;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 12/1/17
 */
@Entity
@Table(name = "SH_REQUEST")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Request extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "SH_REQUEST_ID")
    private Long id;

    @Column(name = "SH_SHIPMENT_ID")
    private Long shipmentId;

    @Column(name = "PHONE_NUMBER")
    private String phone;

    @Column(name = "COMPANY_NAME")
    private String company;

    @Column(name = "AC_ADDRESS_ID")
    private Long addressId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "DELIVERY_DATE")
    private LocalDateTime deliveryDate;

    @Column(name = "SH_CARRIER_ID")
    private Long carrierId;

    @Column(name = "CARRIER_OTHER")
    private String carrierOther;

    @Column(name = "SH_METHOD_ID")
    private Long methodId;

    @Column(name = "METHOD_OTHER")
    private String methodOther;

    @Column(name = "QUANTITY")
    private Long quantity;

    @Column(name = "SHIPPING_INSTRUCTION")
    private String instruction;

    @Column(name = "EMAIL")
    private String email;

    @Column(name = "FIRST_NAME")
    private String firstName;

    @Column(name = "LAST_NAME")
    private String lastName;

    @Column(name = "MIDDLE_NAME")
    private String middleName;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "SH_REQUEST_TYPE_ID")
    private Long requestTypeId;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long propertyId;

    @Column(name = "ORDINAL_NUMBER")
    private Long ordinalNumber;

    @Column(name = "USE_SPEC_PACKAGING")
    private Boolean useSpecPackaging;

    @Column(name = "WORKGROUP_NAME")
    private String workgroupName;

    @Column(name = "REQUESTEE_USER_ID")
    private Long requesteeUserId;

    @Column(name = "AC_WHLOCATION_ID")
    private Long whLocationId;

    @Column(name = "CM_CONTACT_ID")
    private Long contactId;

    @Column(name = "AC_SOURCE_TYPE_ID")
    private Long sourceTypeId;

    @Column(name = "PC_INVOICE_ITEM_ID")
    private Long invoiceItemId;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "request")
    private List<Delivery> deliveries;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SH_REQUEST_TYPE_ID", insertable = false, updatable = false)
    private RequestType requestType;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SH_METHOD_ID", insertable = false, updatable = false)
    private Method method;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SH_CARRIER_ID", insertable = false, updatable = false)
    private Carrier carrier;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SH_SHIPMENT_ID", referencedColumnName = "SH_SHIPMENT_ID", insertable = false, updatable = false)
    private Shipment shipment;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "AC_ADDRESS_ID", insertable = false, updatable = false)
    private Address address;

    public Shipment getShipment() {
        return shipment;
    }

    public void setShipment(Shipment shipment) {
        this.shipment = shipment;
    }

    public List<Delivery> getDeliveries() {
        return deliveries;
    }

    public void setDeliveries(List<Delivery> deliveries) {
        this.deliveries = deliveries;
    }

    public RequestType getRequestType() {
        return requestType;
    }

    public void setRequestType(RequestType requestType) {
        this.requestType = requestType;
    }

    public Method getMethod() {
        return method;
    }

    public void setMethod(Method method) {
        this.method = method;
    }

    public Carrier getCarrier() {
        return carrier;
    }

    public void setCarrier(Carrier carrier) {
        this.carrier = carrier;
    }

    public boolean isNormal() {
        return this.getRequestTypeId() == RequestTypeID.SHIPMENT_REQUEST_TYPE_NORMAL;
    }

    public boolean isProof() {
        return this.getRequestTypeId() == RequestTypeID.SHIPMENT_REQUEST_TYPE_PROOF;
    }

    public boolean isSample() {
        return this.getRequestTypeId() == RequestTypeID.SHIPMENT_REQUEST_TYPE_SAMPLE;
    }

    public double getTotalShippedQty()
    {
        double total = 0;

        if (deliveries != null && deliveries.size() > 0) {
            for (Delivery d : deliveries) {
                total += d.getShippedQuantity().doubleValue();
            }
        }
        return total;
    }

    public double getTotalReceivedQty()
    {
        long total = 0;

        if (deliveries != null && deliveries.size() > 0) {
            for (Delivery d : deliveries) {
                total += d.getReceivedQuantity().doubleValue();
            }
        }
        return total;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShipmentId() {
        return shipmentId;
    }

    public void setShipmentId(Long shipmentId) {
        this.shipmentId = shipmentId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public Long getAddressId() {
        return addressId;
    }

    public void setAddressId(Long addressId) {
        this.addressId = addressId;
    }

    public LocalDateTime getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(LocalDateTime deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public String getCarrierOther() {
        return carrierOther;
    }

    public void setCarrierOther(String carrierOther) {
        this.carrierOther = carrierOther;
    }

    public Long getMethodId() {
        return methodId;
    }

    public void setMethodId(Long methodId) {
        this.methodId = methodId;
    }

    public String getMethodOther() {
        return methodOther;
    }

    public void setMethodOther(String methodOther) {
        this.methodOther = methodOther;
    }

    public Long getQuantity() {
        return quantity;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public String getInstruction() {
        return instruction;
    }

    public void setInstruction(String instruction) {
        this.instruction = instruction;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getMiddleName() {
        return middleName;
    }

    public void setMiddleName(String middleName) {
        this.middleName = middleName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getRequestTypeId() {
        return requestTypeId;
    }

    public void setRequestTypeId(Long requestTypeId) {
        this.requestTypeId = requestTypeId;
    }

    public Long getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Long propertyId) {
        this.propertyId = propertyId;
    }

    public Long getOrdinalNumber() {
        return ordinalNumber;
    }

    public void setOrdinalNumber(Long ordinalNumber) {
        this.ordinalNumber = ordinalNumber;
    }

    public boolean getUseSpecPackaging() {
        return useSpecPackaging != null && useSpecPackaging.booleanValue();
    }

    public void setUseSpecPackaging(Boolean useSpecPackaging) {
        this.useSpecPackaging = useSpecPackaging;
    }

    public String getWorkgroupName() {
        return workgroupName;
    }

    public void setWorkgroupName(String workgroupName) {
        this.workgroupName = workgroupName;
    }

    public Long getRequesteeUserId() {
        return requesteeUserId;
    }

    public void setRequesteeUserId(Long requesteeUserId) {
        this.requesteeUserId = requesteeUserId;
    }

    public Long getWhLocationId() {
        return whLocationId;
    }

    public void setWhLocationId(Long whLocationId) {
        this.whLocationId = whLocationId;
    }

    public Long getContactId() {
        return contactId;
    }

    public void setContactId(Long contactId) {
        this.contactId = contactId;
    }

    public Long getSourceTypeId() {
        return sourceTypeId;
    }

    public void setSourceTypeId(Long sourceTypeId) {
        this.sourceTypeId = sourceTypeId;
    }

    public Long getInvoiceItemId() {
        return invoiceItemId;
    }

    public void setInvoiceItemId(Long invoiceItemId) {
        this.invoiceItemId = invoiceItemId;
    }

    public Address getAddress() {
        return address;
    }

    public void setAddress(Address address) {
        this.address = address;
    }

    public String getFullName() {
        String firstName = "";
        String middleName = "";
        String lastName = "";
        if (getFirstName() != null) firstName = getFirstName().trim()+" ";
        if (getMiddleName() != null) middleName = getMiddleName().trim()+" ";
        if (getLastName() != null) lastName = getLastName().trim();
        return firstName+middleName+lastName;
    }
}
