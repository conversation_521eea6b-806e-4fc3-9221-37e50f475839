package com.noosh.app.commons.entity.collaboration;

import com.noosh.app.commons.entity.NooshAuditingEntity;

import jakarta.persistence.*;

@Entity
@Table(name="PM_DEACTIVATION_REASON")
public class DeactivationReason extends NooshAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "pm_deactivation_reason_id_generated")
    @SequenceGenerator(name = "pm_deactivation_reason_id_generated", sequenceName = "PM_DEACTIVATION_REASON_SEQ", allocationSize = 1)
    @Column(name="PM_DEACTIVATION_REASON_ID")
    public Long id;

    @Column(name="CONSTANT_TOKEN")
    public String constantToken;

    @Column(name="DESCRIPTION_STR_ID")
    public Long descriptionStrId;

    @Column(name="NAME_STR")
    public String nameStr;

    @Column(name="IS_SYSTEM")
    public Boolean isSystem;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getConstantToken() {
        return constantToken;
    }

    public void setConstantToken(String constantToken) {
        this.constantToken = constantToken;
    }

    public Long getDescriptionStrId() {
        return descriptionStrId;
    }

    public void setDescriptionStrId(Long descriptionStrId) {
        this.descriptionStrId = descriptionStrId;
    }

    public String getNameStr() {
        return nameStr;
    }

    public void setNameStr(String nameStr) {
        this.nameStr = nameStr;
    }

    public Boolean getIsSystem() {
        return isSystem;
    }

    public void setIsSystem(Boolean isSystem) {
        this.isSystem = isSystem;
    }
}