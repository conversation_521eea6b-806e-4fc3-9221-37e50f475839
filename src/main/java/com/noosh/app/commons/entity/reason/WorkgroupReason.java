package com.noosh.app.commons.entity.reason;

import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "workgroup_reason")
public class WorkgroupReason {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "reason_id")
    private Long reasonId;

    @Column(name = "workgroup_id")
    private Long workgroupId;

    @Column(name = "is_active")
    private Boolean isActive;

    @ManyToOne
    @JoinColumn(name = "reason_id", insertable = false, updatable = false)
    private Reason reason;
}
