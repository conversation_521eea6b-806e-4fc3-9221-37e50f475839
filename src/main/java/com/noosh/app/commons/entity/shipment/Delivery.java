package com.noosh.app.commons.entity.shipment;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 12/3/17
 */
@Entity
@Table(name = "SH_DELIVERY")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Delivery extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = -5059821562257287060L;
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "delivery_id_generated")
    @SequenceGenerator(name = "delivery_id_generated", sequenceName = "SH_DELIVERY_SEQ", allocationSize = 1)
    @Column(name = "SH_DELIVERY_ID")
    private Long id;

    @Column(name = "SH_REQUEST_ID")
    private Long requestId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "SHIPPED_DATE")
    private LocalDateTime shippedDate;

    @Column(name = "SHIPPED_QUANTITY")
    private BigDecimal shippedQuantity;

    @Column(name = "SH_CARRIER_ID")
    private Long carrierId;

    @Column(name = "CARRIER_OTHER")
    private String carrierOther;

    @Column(name = "SH_METHOD_ID")
    private Long methodId;

    @Column(name = "METHOD_OTHER")
    private String methodOther;

    @Column(name = "UNITS")
    private Long units;

    @Column(name = "SH_UOFM_ID")
    private Long uofmId;

    @Column(name = "WEIGHTS")
    private BigDecimal weights;

    @Column(name = "SH_WEIGHT_UOFM_ID")
    private Long weightUofmId;

    @Column(name = "TRACKING_NUMBER")
    private String trackingnumber;

    @Column(name = "COST")
    private BigDecimal cost;

    @Column(name = "COST_AC_CURRENCY_ID")
    private Long costCurrencyId;

    @Column(name = "TAX")
    private BigDecimal tax;

    @Column(name = "TAX_AC_CURRENCY_ID")
    private Long taxCurrencyId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name = "RECEIVED_DATE")
    private LocalDateTime receivedDate;

    @Column(name = "RECEIVED_QUANTITY")
    private BigDecimal receivedQuantity;

    @Column(name = "COMMENTS")
    private String comments;

    @Column(name = "RECEIVED_COMMENTS")
    private String receivedComments;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name = "AC_SOURCE_TYPE_ID")
    private Long sourceTypeId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SH_METHOD_ID", insertable = false, updatable = false)
    private Method method;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "SH_CARRIER_ID", insertable = false, updatable = false)
    private Carrier carrier;

    @ManyToOne
    @JoinColumn(name = "SH_REQUEST_ID", referencedColumnName = "SH_REQUEST_ID", insertable = false, updatable = false)
    private Request request;

    public Request getRequest() {
        return request;
    }

    public void setRequest(Request request) {
        this.request = request;
    }

    public Method getMethod() {
        return method;
    }

    public void setMethod(Method method) {
        this.method = method;
    }

    public Carrier getCarrier() {
        return carrier;
    }

    public void setCarrier(Carrier carrier) {
        this.carrier = carrier;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRequestId() {
        return requestId;
    }

    public void setRequestId(Long requestId) {
        this.requestId = requestId;
    }

    public LocalDateTime getShippedDate() {
        return shippedDate;
    }

    public void setShippedDate(LocalDateTime shippedDate) {
        this.shippedDate = shippedDate;
    }

    public BigDecimal getShippedQuantity() {
        return shippedQuantity;
    }

    public void setShippedQuantity(BigDecimal shippedQuantity) {
        this.shippedQuantity = shippedQuantity;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public String getCarrierOther() {
        return carrierOther;
    }

    public void setCarrierOther(String carrierOther) {
        this.carrierOther = carrierOther;
    }

    public Long getMethodId() {
        return methodId;
    }

    public void setMethodId(Long methodId) {
        this.methodId = methodId;
    }

    public String getMethodOther() {
        return methodOther;
    }

    public void setMethodOther(String methodOther) {
        this.methodOther = methodOther;
    }

    public Long getUnits() {
        return units;
    }

    public void setUnits(Long units) {
        this.units = units;
    }

    public Long getUofmId() {
        return uofmId;
    }

    public void setUofmId(Long uofmId) {
        this.uofmId = uofmId;
    }

    public BigDecimal getWeights() {
        return weights;
    }

    public void setWeights(BigDecimal weights) {
        this.weights = weights;
    }

    public Long getWeightUofmId() {
        return weightUofmId;
    }

    public void setWeightUofmId(Long weightUofmId) {
        this.weightUofmId = weightUofmId;
    }

    public String getTrackingnumber() {
        return trackingnumber;
    }

    public void setTrackingnumber(String trackingnumber) {
        this.trackingnumber = trackingnumber;
    }

    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    public Long getCostCurrencyId() {
        return costCurrencyId;
    }

    public void setCostCurrencyId(Long costCurrencyId) {
        this.costCurrencyId = costCurrencyId;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public Long getTaxCurrencyId() {
        return taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public LocalDateTime getReceivedDate() {
        return receivedDate;
    }

    public void setReceivedDate(LocalDateTime receivedDate) {
        this.receivedDate = receivedDate;
    }

    public BigDecimal getReceivedQuantity() {
        return receivedQuantity;
    }

    public void setReceivedQuantity(BigDecimal receivedQuantity) {
        this.receivedQuantity = receivedQuantity;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getReceivedComments() {
        return receivedComments;
    }

    public void setReceivedComments(String receivedComments) {
        this.receivedComments = receivedComments;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Long getSourceTypeId() {
        return sourceTypeId;
    }

    public void setSourceTypeId(Long sourceTypeId) {
        this.sourceTypeId = sourceTypeId;
    }
}
