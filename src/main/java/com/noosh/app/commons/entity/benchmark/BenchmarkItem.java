package com.noosh.app.commons.entity.benchmark;

import org.hibernate.annotations.*;

import jakarta.persistence.*;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 2/27/22
 */
@Entity
@Table(name = "PC_BENCHMARK_ITEM")
@org.hibernate.annotations.Cache(usage = CacheConcurrencyStrategy.NONE)
public class BenchmarkItem implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "PC_BENCHMARK_ITEM_ID")
    private Long id;

    @Column(name = "OR_ORDER_ID")
    private Long orderId;

    @Column(name = "ITEM_INDEX")
    private Long itemIndex;

    @Column(name = "SP_SPEC_TYPE_ID")
    private Long specTypeId;

    @Column(name = "PRODUCT_TYPE_ID")
    private Long productTypeId;

    @Column(name = "SAVING_AMT")
    private BigDecimal savingAmt;

    @Column(name = "SAVING_AMT_AC_CURRENCY_ID")
    private Long savingAmtCurrencyId;

    @Column(name = "COMMENTS")
    private String comments;

    @Column(name = "IS_LOCKED")
    private Boolean isLocked;

    @Column(name = "CUSTOM_PR_PROPERTY_ID")
    private Long propertyId;

    @Column(name = "SAVING_PERCENT")
    private BigDecimal savingPercent;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getItemIndex() {
        return itemIndex;
    }

    public void setItemIndex(Long itemIndex) {
        this.itemIndex = itemIndex;
    }

    public Long getSpecTypeId() {
        return specTypeId;
    }

    public void setSpecTypeId(Long specTypeId) {
        this.specTypeId = specTypeId;
    }

    public Long getProductTypeId() {
        return productTypeId;
    }

    public void setProductTypeId(Long productTypeId) {
        this.productTypeId = productTypeId;
    }

    public BigDecimal getSavingAmt() {
        return savingAmt;
    }

    public void setSavingAmt(BigDecimal savingAmt) {
        this.savingAmt = savingAmt;
    }

    public Long getSavingAmtCurrencyId() {
        return savingAmtCurrencyId;
    }

    public void setSavingAmtCurrencyId(Long savingAmtCurrencyId) {
        this.savingAmtCurrencyId = savingAmtCurrencyId;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Boolean getLocked() {
        return isLocked;
    }

    public void setLocked(Boolean locked) {
        isLocked = locked;
    }

    public Long getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(Long propertyId) {
        this.propertyId = propertyId;
    }

    public BigDecimal getSavingPercent() {
        return savingPercent;
    }

    public void setSavingPercent(BigDecimal savingPercent) {
        this.savingPercent = savingPercent;
    }
}
