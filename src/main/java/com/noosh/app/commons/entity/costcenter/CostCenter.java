package com.noosh.app.commons.entity.costcenter;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: yangx
 * Date: 6/19/20
 */
@Entity
@Table(name = "AC_COSTCENTER")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class CostCenter extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "ac_costcenter_id_generated")
    @SequenceGenerator(name = "ac_costcenter_id_generated", sequenceName = "AC_COSTCENTER_SEQ", allocationSize = 1)
    @Column(name="AC_COSTCENTER_ID")
    private Long id;

    @Column(name="OWNER_AC_WORKGROUP_ID")
    private Long ownerWorkgroupId;

    @Column(name="NAME")
    private String name;

    @Column(name="DESCRIPTION")
    private String description;

    @Column(name="IS_ACTIVE")
    private Boolean isActive;

    @Column(name="BU_CLIENT_WORKGROUP_ID")
    private Long clientId;

    public CostCenter() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getOwnerWorkgroupId() {
        return ownerWorkgroupId;
    }

    public void setOwnerWorkgroupId(Long ownerWorkgroupId) {
        this.ownerWorkgroupId = ownerWorkgroupId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getActive() {
        return isActive;
    }

    public void setActive(Boolean active) {
        isActive = active;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }
}
