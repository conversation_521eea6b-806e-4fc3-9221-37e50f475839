package com.noosh.app.commons.entity.shipment;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;

/**
 * User: leilaz
 * Date: 12/1/17
 */
@Entity
@Table(name = "SH_REQUEST_TYPE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class RequestType extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "SH_REQUEST_TYPE_ID")
    private Long id;

    @Column(name = "NAME_STR_ID")
    private Long nameStrId;

    @Column(name = "NAME_STR")
    private String nameStr;

    @Column(name = "DESCRIPTION_STR_ID")
    private Long descriptionStrId;

    @Column(name = "DESCRIPTION_STR")
    private String descriptionStr;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getNameStrId() {
        return nameStrId;
    }

    public void setNameStrId(Long nameStrId) {
        this.nameStrId = nameStrId;
    }

    public String getNameStr() {
        return nameStr;
    }

    public void setNameStr(String nameStr) {
        this.nameStr = nameStr;
    }

    public Long getDescriptionStrId() {
        return descriptionStrId;
    }

    public void setDescriptionStrId(Long descriptionStrId) {
        this.descriptionStrId = descriptionStrId;
    }

    public String getDescriptionStr() {
        return descriptionStr;
    }

    public void setDescriptionStr(String descriptionStr) {
        this.descriptionStr = descriptionStr;
    }
}
