package com.noosh.app.commons.entity.externalItem;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.property.Property;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * User: leilaz
 * Date: 5/4/17
 */
@Entity
@Table(name="PC_EXTERNAL_ITEM")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class ExternalItem extends NooshAuditingEntity implements Serializable {
    private static final long serialVersionUID = 7107291985538640691L;
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "PC_EXTERNAL_ITEM_ID_generated")
    @SequenceGenerator(name = "PC_EXTERNAL_ITEM_ID_generated", sequenceName = "PC_EXTERNAL_ITEM_SEQ", allocationSize = 1)
    @Column(name = "PC_EXTERNAL_ITEM_ID")
    private Long id;

    @Column(name = "OC_OBJECT_ID")
    private Long objectId;

    @Column(name = "OC_OBJECT_CLASS_ID")
    private Long objectClassId;

    @Column(name = "SP_SPEC_ID")
    private Long specId;

    @Column(name = "ITEM_PR_PROPERTY_ID")
    private Long itemPropertyId;

    @Column(name = "IS_MANUALLY_CREATED")
    private Boolean isManuallyCreated;

    @Column(name = "ITEM_INDEX")
    private Long itemIndex;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "SKU")
    private String sku;

    @Column(name = "QTY_UOM")
    private String qtyUom;

    @Column(name = "QTY_UOM_FACTOR")
    private Long qtyUomFactor;

    @Column(name = "PRICE_UOM")
    private String priceUom;

    @Column(name = "PRICE_UOM_FACTOR")
    private Long priceUomFactor;

    @Column(name = "UNIT_PRICE")
    private BigDecimal unitPrice;

    @Column(name = "UNIT_PRICE_AC_CURRENCY_ID")
    private Long unitPriceCurrencyId;

    @Column(name = "MIN_UOM_QTY")
    private BigDecimal minUomQty;

    @Column(name = "QTY_INCREMENT")
    private Long qtyIncrement;

    @Column(name = "UOM_FACTORS")
    private String uomFactors;

    @Column(name = "FOR_QTY1")
    private Long forQty1;

    @Column(name = "QTY1")
    private BigDecimal qty1;

    @Column(name = "PRICE1")
    private BigDecimal price1;

    @Column(name = "PRICE1_AC_CURRENCY_ID")
    private Long price1CurrencyId;

    @Column(name = "FOR_QTY2")
    private Long forQty2;

    @Column(name = "QTY2")
    private BigDecimal qty2;

    @Column(name = "PRICE2")
    private BigDecimal price2;

    @Column(name = "PRICE2_AC_CURRENCY_ID")
    private Long price2CurrencyId;

    @Column(name = "FOR_QTY3")
    private Long forQty3;

    @Column(name = "QTY3")
    private BigDecimal qty3;

    @Column(name = "PRICE3")
    private BigDecimal price3;

    @Column(name = "PRICE3_AC_CURRENCY_ID")
    private Long price3CurrencyId;

    @Column(name = "FOR_QTY4")
    private Long forQty4;

    @Column(name = "QTY4")
    private BigDecimal qty4;

    @Column(name = "PRICE4")
    private BigDecimal price4;

    @Column(name = "PRICE4_AC_CURRENCY_ID")
    private Long price4CurrencyId;

    @Column(name = "FOR_QTY5")
    private Long forQty5;

    @Column(name = "QTY5")
    private BigDecimal qty5;

    @Column(name = "PRICE5")
    private BigDecimal price5;

    @Column(name = "PRICE5_AC_CURRENCY_ID")
    private Long price5CurrencyId;

    @Column(name = "UNIT_PRICE1")
    private BigDecimal unitPrice1;

    @Column(name = "UNIT_PRICE1_AC_CURRENCY_ID")
    private Long unitPrice1CurrencyId;

    @Column(name = "UP1_UOM")
    private String up1Uom;

    @Column(name = "UNIT_PRICE2")
    private BigDecimal unitPrice2;

    @Column(name = "UNIT_PRICE2_AC_CURRENCY_ID")
    private Long unitPrice2CurrencyId;

    @Column(name = "UP2_UOM")
    private String up2Uom;

    @Column(name = "UNIT_PRICE3")
    private BigDecimal unitPrice3;

    @Column(name = "UNIT_PRICE3_AC_CURRENCY_ID")
    private Long unitPrice3CurrencyId;

    @Column(name = "UP3_UOM")
    private String up3Uom;

    @Column(name = "UNIT_PRICE4")
    private BigDecimal unitPrice4;

    @Column(name = "UNIT_PRICE4_AC_CURRENCY_ID")
    private Long unitPrice4CurrencyId;

    @Column(name = "UP4_UOM")
    private String up4Uom;

    @Column(name = "UNIT_PRICE5")
    private BigDecimal unitPrice5;

    @Column(name = "UNIT_PRICE5_AC_CURRENCY_ID")
    private Long unitPrice5CurrencyId;

    @Column(name = "UP5_UOM")
    private String up5Uom;

    @Column(name = "PAPER_WEIGHT1")
    private Float paperWeight1;

    @Column(name = "PAPER_WEIGHT2")
    private Float paperWeight2;

    @Column(name = "PAPER_WEIGHT3")
    private Float paperWeight3;

    @Column(name = "PAPER_WEIGHT4")
    private Float paperWeight4;

    @Column(name = "PAPER_WEIGHT5")
    private Float paperWeight5;

    @Column(name = "ACTUAL_QTY1")
    private BigDecimal actualQty1;

    @Column(name = "ACTUAL_QTY2")
    private BigDecimal actualQty2;

    @Column(name = "ACTUAL_QTY3")
    private BigDecimal actualQty3;

    @Column(name = "ACTUAL_QTY4")
    private BigDecimal actualQty4;

    @Column(name = "ACTUAL_QTY5")
    private BigDecimal actualQty5;

    @Column(name = "PRICE_P_TON")
    private BigDecimal pricePerTon;

    @Column(name = "PRICE_P_TON_AC_CURRENCY_ID")
    private Long pricePerTonCurrencyId;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ITEM_PR_PROPERTY_ID", referencedColumnName = "PR_PROPERTY_ID", insertable = false, updatable = false)
    private Property property;

    public Property getProperty() {
        return property;
    }

    public void setProperty(Property property) {
        this.property = property;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public Long getItemPropertyId() {
        return itemPropertyId;
    }

    public void setItemPropertyId(Long itemPropertyId) {
        this.itemPropertyId = itemPropertyId;
    }

    public Boolean getIsManuallyCreated() {
        return isManuallyCreated == null ? false : isManuallyCreated;
    }

    public void setIsManuallyCreated(Boolean manuallyCreated) {
        isManuallyCreated = manuallyCreated;
    }

    public Long getItemIndex() {
        return itemIndex;
    }

    public void setItemIndex(Long itemIndex) {
        this.itemIndex = itemIndex;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getQtyUom() {
        return qtyUom;
    }

    public void setQtyUom(String qtyUom) {
        this.qtyUom = qtyUom;
    }

    public Long getQtyUomFactor() {
        return qtyUomFactor;
    }

    public void setQtyUomFactor(Long qtyUomFactor) {
        this.qtyUomFactor = qtyUomFactor;
    }

    public String getPriceUom() {
        return priceUom;
    }

    public void setPriceUom(String priceUom) {
        this.priceUom = priceUom;
    }

    public Long getPriceUomFactor() {
        return priceUomFactor;
    }

    public void setPriceUomFactor(Long priceUomFactor) {
        this.priceUomFactor = priceUomFactor;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Long getUnitPriceCurrencyId() {
        return unitPriceCurrencyId;
    }

    public void setUnitPriceCurrencyId(Long unitPriceCurrencyId) {
        this.unitPriceCurrencyId = unitPriceCurrencyId;
    }

    public BigDecimal getMinUomQty() {
        return minUomQty;
    }

    public void setMinUomQty(BigDecimal minUomQty) {
        this.minUomQty = minUomQty;
    }

    public Long getQtyIncrement() {
        return qtyIncrement;
    }

    public void setQtyIncrement(Long qtyIncrement) {
        this.qtyIncrement = qtyIncrement;
    }

    public String getUomFactors() {
        return uomFactors;
    }

    public void setUomFactors(String uomFactors) {
        this.uomFactors = uomFactors;
    }

    public Long getForQty1() {
        return forQty1;
    }

    public void setForQty1(Long forQty1) {
        this.forQty1 = forQty1;
    }

    public BigDecimal getQty1() {
        return qty1;
    }

    public void setQty1(BigDecimal qty1) {
        this.qty1 = qty1;
    }

    public BigDecimal getPrice1() {
        return price1;
    }

    public void setPrice1(BigDecimal price1) {
        this.price1 = price1;
    }

    public Long getPrice1CurrencyId() {
        return price1CurrencyId;
    }

    public void setPrice1CurrencyId(Long price1CurrencyId) {
        this.price1CurrencyId = price1CurrencyId;
    }

    public Long getForQty2() {
        return forQty2;
    }

    public void setForQty2(Long forQty2) {
        this.forQty2 = forQty2;
    }

    public BigDecimal getQty2() {
        return qty2;
    }

    public void setQty2(BigDecimal qty2) {
        this.qty2 = qty2;
    }

    public BigDecimal getPrice2() {
        return price2;
    }

    public void setPrice2(BigDecimal price2) {
        this.price2 = price2;
    }

    public Long getPrice2CurrencyId() {
        return price2CurrencyId;
    }

    public void setPrice2CurrencyId(Long price2CurrencyId) {
        this.price2CurrencyId = price2CurrencyId;
    }

    public Long getForQty3() {
        return forQty3;
    }

    public void setForQty3(Long forQty3) {
        this.forQty3 = forQty3;
    }

    public BigDecimal getQty3() {
        return qty3;
    }

    public void setQty3(BigDecimal qty3) {
        this.qty3 = qty3;
    }

    public BigDecimal getPrice3() {
        return price3;
    }

    public void setPrice3(BigDecimal price3) {
        this.price3 = price3;
    }

    public Long getPrice3CurrencyId() {
        return price3CurrencyId;
    }

    public void setPrice3CurrencyId(Long price3CurrencyId) {
        this.price3CurrencyId = price3CurrencyId;
    }

    public Long getForQty4() {
        return forQty4;
    }

    public void setForQty4(Long forQty4) {
        this.forQty4 = forQty4;
    }

    public BigDecimal getQty4() {
        return qty4;
    }

    public void setQty4(BigDecimal qty4) {
        this.qty4 = qty4;
    }

    public BigDecimal getPrice4() {
        return price4;
    }

    public void setPrice4(BigDecimal price4) {
        this.price4 = price4;
    }

    public Long getPrice4CurrencyId() {
        return price4CurrencyId;
    }

    public void setPrice4CurrencyId(Long price4CurrencyId) {
        this.price4CurrencyId = price4CurrencyId;
    }

    public Long getForQty5() {
        return forQty5;
    }

    public void setForQty5(Long forQty5) {
        this.forQty5 = forQty5;
    }

    public BigDecimal getQty5() {
        return qty5;
    }

    public void setQty5(BigDecimal qty5) {
        this.qty5 = qty5;
    }

    public BigDecimal getPrice5() {
        return price5;
    }

    public void setPrice5(BigDecimal price5) {
        this.price5 = price5;
    }

    public Long getPrice5CurrencyId() {
        return price5CurrencyId;
    }

    public void setPrice5CurrencyId(Long price5CurrencyId) {
        this.price5CurrencyId = price5CurrencyId;
    }

    public BigDecimal getUnitPrice1() {
        return unitPrice1;
    }

    public void setUnitPrice1(BigDecimal unitPrice1) {
        this.unitPrice1 = unitPrice1;
    }

    public Long getUnitPrice1CurrencyId() {
        return unitPrice1CurrencyId;
    }

    public void setUnitPrice1CurrencyId(Long unitPrice1CurrencyId) {
        this.unitPrice1CurrencyId = unitPrice1CurrencyId;
    }

    public String getUp1Uom() {
        return up1Uom;
    }

    public void setUp1Uom(String up1Uom) {
        this.up1Uom = up1Uom;
    }

    public BigDecimal getUnitPrice2() {
        return unitPrice2;
    }

    public void setUnitPrice2(BigDecimal unitPrice2) {
        this.unitPrice2 = unitPrice2;
    }

    public Long getUnitPrice2CurrencyId() {
        return unitPrice2CurrencyId;
    }

    public void setUnitPrice2CurrencyId(Long unitPrice2CurrencyId) {
        this.unitPrice2CurrencyId = unitPrice2CurrencyId;
    }

    public String getUp2Uom() {
        return up2Uom;
    }

    public void setUp2Uom(String up2Uom) {
        this.up2Uom = up2Uom;
    }

    public BigDecimal getUnitPrice3() {
        return unitPrice3;
    }

    public void setUnitPrice3(BigDecimal unitPrice3) {
        this.unitPrice3 = unitPrice3;
    }

    public Long getUnitPrice3CurrencyId() {
        return unitPrice3CurrencyId;
    }

    public void setUnitPrice3CurrencyId(Long unitPrice3CurrencyId) {
        this.unitPrice3CurrencyId = unitPrice3CurrencyId;
    }

    public String getUp3Uom() {
        return up3Uom;
    }

    public void setUp3Uom(String up3Uom) {
        this.up3Uom = up3Uom;
    }

    public BigDecimal getUnitPrice4() {
        return unitPrice4;
    }

    public void setUnitPrice4(BigDecimal unitPrice4) {
        this.unitPrice4 = unitPrice4;
    }

    public Long getUnitPrice4CurrencyId() {
        return unitPrice4CurrencyId;
    }

    public void setUnitPrice4CurrencyId(Long unitPrice4CurrencyId) {
        this.unitPrice4CurrencyId = unitPrice4CurrencyId;
    }

    public String getUp4Uom() {
        return up4Uom;
    }

    public void setUp4Uom(String up4Uom) {
        this.up4Uom = up4Uom;
    }

    public BigDecimal getUnitPrice5() {
        return unitPrice5;
    }

    public void setUnitPrice5(BigDecimal unitPrice5) {
        this.unitPrice5 = unitPrice5;
    }

    public Long getUnitPrice5CurrencyId() {
        return unitPrice5CurrencyId;
    }

    public void setUnitPrice5CurrencyId(Long unitPrice5CurrencyId) {
        this.unitPrice5CurrencyId = unitPrice5CurrencyId;
    }

    public String getUp5Uom() {
        return up5Uom;
    }

    public void setUp5Uom(String up5Uom) {
        this.up5Uom = up5Uom;
    }

    public Float getPaperWeight1() {
        return paperWeight1;
    }

    public void setPaperWeight1(Float paperWeight1) {
        this.paperWeight1 = paperWeight1;
    }

    public Float getPaperWeight2() {
        return paperWeight2;
    }

    public void setPaperWeight2(Float paperWeight2) {
        this.paperWeight2 = paperWeight2;
    }

    public Float getPaperWeight3() {
        return paperWeight3;
    }

    public void setPaperWeight3(Float paperWeight3) {
        this.paperWeight3 = paperWeight3;
    }

    public Float getPaperWeight4() {
        return paperWeight4;
    }

    public void setPaperWeight4(Float paperWeight4) {
        this.paperWeight4 = paperWeight4;
    }

    public Float getPaperWeight5() {
        return paperWeight5;
    }

    public void setPaperWeight5(Float paperWeight5) {
        this.paperWeight5 = paperWeight5;
    }

    public Boolean getManuallyCreated() {
        return isManuallyCreated;
    }

    public void setManuallyCreated(Boolean manuallyCreated) {
        isManuallyCreated = manuallyCreated;
    }

    public BigDecimal getActualQty1() {
        return actualQty1;
    }

    public void setActualQty1(BigDecimal actualQty1) {
        this.actualQty1 = actualQty1;
    }

    public BigDecimal getActualQty2() {
        return actualQty2;
    }

    public void setActualQty2(BigDecimal actualQty2) {
        this.actualQty2 = actualQty2;
    }

    public BigDecimal getActualQty3() {
        return actualQty3;
    }

    public void setActualQty3(BigDecimal actualQty3) {
        this.actualQty3 = actualQty3;
    }

    public BigDecimal getActualQty4() {
        return actualQty4;
    }

    public void setActualQty4(BigDecimal actualQty4) {
        this.actualQty4 = actualQty4;
    }

    public BigDecimal getActualQty5() {
        return actualQty5;
    }

    public void setActualQty5(BigDecimal actualQty5) {
        this.actualQty5 = actualQty5;
    }

    public BigDecimal getPricePerTon() {
        return pricePerTon;
    }

    public void setPricePerTon(BigDecimal pricePerTon) {
        this.pricePerTon = pricePerTon;
    }

    public Long getPricePerTonCurrencyId() {
        return pricePerTonCurrencyId;
    }

    public void setPricePerTonCurrencyId(Long pricePerTonCurrencyId) {
        this.pricePerTonCurrencyId = pricePerTonCurrencyId;
    }

    public boolean hasUomQty() {
        if (getQty1().compareTo(BigDecimal.ZERO) > 0
                || getQty2().compareTo(BigDecimal.ZERO) > 0
                || getQty3().compareTo(BigDecimal.ZERO) > 0
                || getQty4().compareTo(BigDecimal.ZERO) > 0
                || getQty5().compareTo(BigDecimal.ZERO) > 0)
            return true;
        return false;
    }

    public boolean isAllQuantitiesPriced() {
        boolean hasQty = false;
        if (getQty1().compareTo(BigDecimal.ZERO) > 0) {
            hasQty = true;
            if (getPrice1() == null || getUnitPrice1() == null)
                return false;
        }
        if (getQty2().compareTo(BigDecimal.ZERO) > 0) {
            hasQty = true;
            if (getPrice2() == null || getUnitPrice2() == null)
                return false;
        }
        if (getQty3().compareTo(BigDecimal.ZERO) > 0) {
            hasQty = true;
            if (getPrice3() == null || getUnitPrice3() == null)
                return false;
        }
        if (getQty4().compareTo(BigDecimal.ZERO) > 0) {
            hasQty = true;
            if (getPrice4() == null || getUnitPrice4() == null)
                return false;
        }
        if (getQty5().compareTo(BigDecimal.ZERO) > 0) {
            hasQty = true;
            if (getPrice5() == null || getUnitPrice5() == null)
                return false;
        }
        return hasQty;
    }
}
