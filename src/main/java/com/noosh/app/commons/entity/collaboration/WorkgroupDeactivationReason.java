package com.noosh.app.commons.entity.collaboration;

import com.noosh.app.commons.entity.NooshAuditingEntity;

import jakarta.persistence.*;

@Entity
@Table(name="PM_WG_DEACT_REASON")
public class WorkgroupDeactivationReason extends NooshAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "pm_wg_deact_reason_id_generated")
    @SequenceGenerator(name = "pm_wg_deact_reason_id_generated", sequenceName = "PM_WG_DEACT_REASON_SEQ", allocationSize = 1)
    @Column(name="PM_WG_DEACT_REASON_ID")
    public Long id;

    @Column(name="PM_DEACTIVATION_REASON_ID")
    public Long reasonId;

    @Column(name="AC_WORKGROUP_ID")
    public Long workgroupId;

    @Column(name="IS_ACTIVE")
    public Boolean isActive;

    @Column(name="IS_DEFAULT")
    public Boolean isDefault;

    @Column(name="REASON_ORDER")
    public Long reasonOrder;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PM_DEACTIVATION_REASON_ID", insertable = false, updatable = false)
    public DeactivationReason deactivationReason;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getReasonId() {
        return reasonId;
    }

    public void setReasonId(Long reasonId) {
        this.reasonId = reasonId;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public Long getReasonOrder() {
        return reasonOrder;
    }

    public void setReasonOrder(Long reasonOrder) {
        this.reasonOrder = reasonOrder;
    }

    public DeactivationReason getDeactivationReason() {
        return deactivationReason;
    }

    public void setDeactivationReason(DeactivationReason deactivationReason) {
        this.deactivationReason = deactivationReason;
    }
}