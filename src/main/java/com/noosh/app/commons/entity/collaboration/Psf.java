package com.noosh.app.commons.entity.collaboration;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: neals
 * @Date: 08/05/2016
 */
@Entity
@Table(name="PP_PSF")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Psf extends NooshAuditingEntity implements Serializable {

    private static final long serialVersionUID = 2290802530002863751L;
    @Id
    @Column(name="PP_PSF_ID")
    private Long id;

    @Column(name="PSF_NAME")
    private String psfName;

    @Column(name="TA_TASK_ID")
    private Long taskId;

    @Column(name="PM_PROJECT_ID")
    private Long projectId;

    @Column(name="PC_QUOTE_ID")
    private Long quoteId;

    @Column(name="USER_ID")
    private Long userId;

    @Column(name="OC_OBJECT_CLASS_ID")
    private Long objectClassId;

    @Column(name="OC_OBJECT_STATE_ID")
    private Long objectStateId;

    @Column(name="CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name="OWNER_AC_WORKGROUP_ID")
    private Long ownerWorkgroupId;

    @Column(name="SUPPLIER_AC_WORKGROUP_ID")
    private Long supplierWorkgroupId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name="SUBMIT_DATE")
    private LocalDateTime submitDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name="RESPONSE_DATE")
    private LocalDateTime responseDate;

    @Column(name="BU_CLIENT_WORKGROUP_ID")
    private Long clientId;

    @Column(name="REQUIRES_PRICING")
    private Boolean requiresPricing;

    @Column(name="BU_PAYMENT_METHOD_ID")
    private Long paymentMethodId;

    @Column(name="PO_NUMBER")
    private String poNumber;

    @Column(name="STATE_CHANGE_COMMENTS")
    private String stateChangeComments;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name="REJECT_DATE")
    private LocalDateTime rejectDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name="ACCEPT_DATE")
    private LocalDateTime acceptDate;

    @Column(name="PRICE")
    private BigDecimal price;

    @Column(name="TAX")
    private BigDecimal tax;

    @Column(name="SHIPPING")
    private BigDecimal shipping;

    @Column(name="IS_SUPPLIER_DIRECT")
    private Boolean isSupplierDirect;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPsfName() {
        return psfName;
    }

    public void setPsfName(String psfName) {
        this.psfName = psfName;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getQuoteId() {
        return quoteId;
    }

    public void setQuoteId(Long quoteId) {
        this.quoteId = quoteId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getObjectStateId() {
        return objectStateId;
    }

    public void setObjectStateId(Long objectStateId) {
        this.objectStateId = objectStateId;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Long getOwnerWorkgroupId() {
        return ownerWorkgroupId;
    }

    public void setOwnerWorkgroupId(Long ownerWorkgroupId) {
        this.ownerWorkgroupId = ownerWorkgroupId;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public LocalDateTime getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDateTime submitDate) {
        this.submitDate = submitDate;
    }

    public LocalDateTime getResponseDate() {
        return responseDate;
    }

    public void setResponseDate(LocalDateTime responseDate) {
        this.responseDate = responseDate;
    }

    public Long getClientId() {
        return clientId;
    }

    public void setClientId(Long clientId) {
        this.clientId = clientId;
    }

    public Boolean getRequiresPricing() {
        return requiresPricing;
    }

    public void setRequiresPricing(Boolean requiresPricing) {
        this.requiresPricing = requiresPricing;
    }

    public Long getPaymentMethodId() {
        return paymentMethodId;
    }

    public void setPaymentMethodId(Long paymentMethodId) {
        this.paymentMethodId = paymentMethodId;
    }

    public String getPoNumber() {
        return poNumber;
    }

    public void setPoNumber(String poNumber) {
        this.poNumber = poNumber;
    }

    public String getStateChangeComments() {
        return stateChangeComments;
    }

    public void setStateChangeComments(String stateChangeComments) {
        this.stateChangeComments = stateChangeComments;
    }

    public LocalDateTime getRejectDate() {
        return rejectDate;
    }

    public void setRejectDate(LocalDateTime rejectDate) {
        this.rejectDate = rejectDate;
    }

    public LocalDateTime getAcceptDate() {
        return acceptDate;
    }

    public void setAcceptDate(LocalDateTime acceptDate) {
        this.acceptDate = acceptDate;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public Boolean getIsSupplierDirect() {
        return isSupplierDirect == null ? false : isSupplierDirect;
    }

    public void setIsSupplierDirect(Boolean isSupplierDirect) {
        this.isSupplierDirect = isSupplierDirect;
    }
}
