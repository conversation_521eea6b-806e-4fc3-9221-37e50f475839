package com.noosh.app.commons.entity.reason;

import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "reason")
public class Reason {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name_str_id")
    private Long nameStrId;

    @Column(name = "name_str")
    private String nameStr;

    @Column(name = "constant_token")
    private String constantToken;

    @Column(name = "is_system")
    private Boolean isSystem;

    @Column(name = "type_id")
    private Long typeId;
}
