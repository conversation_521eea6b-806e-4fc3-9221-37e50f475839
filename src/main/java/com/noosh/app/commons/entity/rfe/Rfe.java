package com.noosh.app.commons.entity.rfe;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeDeserializer;
import com.noosh.app.commons.entity.util.CustomLocalDateTimeSerializer;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.Type;
import java.time.LocalDateTime;

import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name="EM_RFE")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Rfe extends NooshAuditingEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;

    @Id
    @Column(name="EM_RFE_ID")
    private Long id;

    @Column(name="REFERENCE")
    private String reference;

    @Column(name="OWNER_REFERENCE")
    private String ownerReference;

    @Column(name="TITLE")
    private String title;

    @Column(name="DESCRIPTION")
    private String description;

    @Column(name="COMMENTS")
    private String comments;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name="DUE_DATE")
    private LocalDateTime dueDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name="PROPOSED_COMPLETION_DATE")
    private LocalDateTime proposedCompletionDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name="SUBMIT_DATE")
    private LocalDateTime submitDate;

    @Column(name="SUBMIT_USER_ID")
    private Long submitUserId;

    @Column(name="CUSTOM_PR_PROPERTY_ID")
    private Long customPropertyId;

    @Column(name="EM_BID_TYPE_ID")
    private Long bidTypeId;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name="BID_START_DATE")
    private LocalDateTime bidStartDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name="BID_END_DATE")
    private LocalDateTime bidEndDate;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name="ORIGINAL_BID_END_DATE")
    private LocalDateTime origBidEndDate;

    @Column(name="IS_LIMITED_EXTENSION")
    private Boolean isLimitedExtension;

    @Column(name="NUMBER_OF_EXTENSIONS")
    private Long numberOfExtensions;

    @Column(name="MAX_EXTENSIONS")
    private Long maxExtensions;

    @Column(name="TIME_INCREMENT")
    private Long timeIncrement;

    @Column(name="IS_AUTO_EXTEND")
    private Boolean isAutoExtend;

    @Column(name="AUTO_EXTEND_MINUTES")
    private Long autoExtendMinutes;

    @Column(name="EM_PRICING_RULE_ID")
    private Long pricingRuleId;

    @Column(name="EM_BIDDING_RULE_ID")
    private Long biddingRuleId;

    @Column(name="OWNER_AC_WORKGROUP_ID")
    private Long ownerWorkgroupId;

    @Column(name="OWNER_USER_ID")
    private Long ownerUserId;

    @Column(name="AC_TERMS_ID")
    private Long termsId;

    @Column(name="CO_SERVICE_ID")
    private Long serviceId;

    @Column(name="OC_OBJECT_STATE_ID")
    private Long stateId;

    @Column(name="REQUEST_ALL_OR_NONE")
    private Boolean requestAllOrNone;

    @Column(name="ALLOW_SELF_INVITE")
    private Boolean allowSelfInvite;

    @Column(name="MAX_SELF_INVITE")
    private Long maxSelfInvite;

    @Column(name="BLIND_BIDDING")
    private Boolean blindBidding;

    @Column(name="IS_AUTO_PRICING")
    private Boolean isAutoPricing;


    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    @Column(name="CONTENT_MOD_DATE")
    private LocalDateTime contentModifiedDate;

    @Column(name="SEALED_BIDDING")
    private Boolean sealedBidding;

    @Column(name="IS_SEALED")
    private Boolean isSealed;

    @Column(name="IS_AUTO_CLOSE")
    private Boolean isAutoClose;

    @Column(name="IS_DISMISS_SUPPLIER")
    private Boolean isDismissSupplier;

    @Column(name="ITEMIZED_TNS")
    private Boolean itemizedTaxAndShipping;

    @Column(name="INIT_PRICE")
    private BigDecimal initialPrice;

    @Column(name="AUCTION_PRICE")
    private BigDecimal auctionPrice;

    @Column(name="DECREMENT_PRICE_PERCENT")
    private Double decrementPricePercent;

    @Column(name="DEC_PRICE")
    private BigDecimal decrementPrice;

    @Column(name="PRICE_INCLUDES_TAX")
    private Boolean priceIncludesTax;

    @Column(name="PRICE_INCLUDES_SHIPPING")
    private Boolean priceIncludesShipping;

    @Column(name="MANUAL_SELECT_WINNING_BIDDER")
    private Boolean manualSelectWinningBidder;

    @Column(name="ALLOW_HIGHER_BID")
    private Boolean allowHigherBid;

    @Column(name="EXTEND_FROM_LAST_BID")
    private Boolean extendFromLastBid;

    @Column(name="REQUIRE_SUPPLIER_ACCEPT")
    private Boolean requireSupplierAcceptance;

    @Column(name="AC_SOURCE_TYPE_ID")
    private Long sourceTypeId;

    @JsonIgnore
    @OneToMany(mappedBy = "rfe")
    private List<RfeItem> rfeItems;

    @JsonIgnore
    @OneToMany(mappedBy = "rfe")
    private List<RfeSupplier> rfeSuppliers;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getOwnerReference() {
        return ownerReference;
    }

    public void setOwnerReference(String ownerReference) {
        this.ownerReference = ownerReference;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }

    public LocalDateTime getProposedCompletionDate() {
        return proposedCompletionDate;
    }

    public void setProposedCompletionDate(LocalDateTime proposedCompletionDate) {
        this.proposedCompletionDate = proposedCompletionDate;
    }

    public LocalDateTime getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(LocalDateTime submitDate) {
        this.submitDate = submitDate;
    }

    public Long getSubmitUserId() {
        return submitUserId;
    }

    public void setSubmitUserId(Long submitUserId) {
        this.submitUserId = submitUserId;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Long getBidTypeId() {
        return bidTypeId;
    }

    public void setBidTypeId(Long bidTypeId) {
        this.bidTypeId = bidTypeId;
    }

    public LocalDateTime getBidStartDate() {
        return bidStartDate;
    }

    public void setBidStartDate(LocalDateTime bidStartDate) {
        this.bidStartDate = bidStartDate;
    }

    public LocalDateTime getBidEndDate() {
        return bidEndDate;
    }

    public void setBidEndDate(LocalDateTime bidEndDate) {
        this.bidEndDate = bidEndDate;
    }

    public LocalDateTime getOrigBidEndDate() {
        return origBidEndDate;
    }

    public void setOrigBidEndDate(LocalDateTime origBidEndDate) {
        this.origBidEndDate = origBidEndDate;
    }

    public Boolean getIsLimitedExtension() {
        return isLimitedExtension;
    }

    public void setIsLimitedExtension(Boolean isLimitedExtension) {
        this.isLimitedExtension = isLimitedExtension;
    }

    public Long getNumberOfExtensions() {
        return numberOfExtensions;
    }

    public void setNumberOfExtensions(Long numberOfExtensions) {
        this.numberOfExtensions = numberOfExtensions;
    }

    public Long getMaxExtensions() {
        return maxExtensions;
    }

    public void setMaxExtensions(Long maxExtensions) {
        this.maxExtensions = maxExtensions;
    }

    public Long getTimeIncrement() {
        return timeIncrement;
    }

    public void setTimeIncrement(Long timeIncrement) {
        this.timeIncrement = timeIncrement;
    }

    public Boolean getIsAutoExtend() {
        return isAutoExtend;
    }

    public void setIsAutoExtend(Boolean isAutoExtend) {
        this.isAutoExtend = isAutoExtend;
    }

    public Long getAutoExtendMinutes() {
        return autoExtendMinutes;
    }

    public void setAutoExtendMinutes(Long autoExtendMinutes) {
        this.autoExtendMinutes = autoExtendMinutes;
    }

    public Long getPricingRuleId() {
        return pricingRuleId;
    }

    public void setPricingRuleId(Long pricingRuleId) {
        this.pricingRuleId = pricingRuleId;
    }

    public Long getBiddingRuleId() {
        return biddingRuleId;
    }

    public void setBiddingRuleId(Long biddingRuleId) {
        this.biddingRuleId = biddingRuleId;
    }

    public Long getOwnerWorkgroupId() {
        return ownerWorkgroupId;
    }

    public void setOwnerWorkgroupId(Long ownerWorkgroupId) {
        this.ownerWorkgroupId = ownerWorkgroupId;
    }

    public Long getOwnerUserId() {
        return ownerUserId;
    }

    public void setOwnerUserId(Long ownerUserId) {
        this.ownerUserId = ownerUserId;
    }

    public Long getTermsId() {
        return termsId;
    }

    public void setTermsId(Long termsId) {
        this.termsId = termsId;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }

    public Boolean getRequestAllOrNone() {
        return requestAllOrNone;
    }

    public void setRequestAllOrNone(Boolean requestAllOrNone) {
        this.requestAllOrNone = requestAllOrNone;
    }

    public Boolean getAllowSelfInvite() {
        return allowSelfInvite;
    }

    public void setAllowSelfInvite(Boolean allowSelfInvite) {
        this.allowSelfInvite = allowSelfInvite;
    }

    public Long getMaxSelfInvite() {
        return maxSelfInvite;
    }

    public void setMaxSelfInvite(Long maxSelfInvite) {
        this.maxSelfInvite = maxSelfInvite;
    }

    public Boolean getBlindBidding() {
        return blindBidding;
    }

    public void setBlindBidding(Boolean blindBidding) {
        this.blindBidding = blindBidding;
    }

    public Boolean getIsAutoPricing() {
        return isAutoPricing;
    }

    public void setIsAutoPricing(Boolean isAutoPricing) {
        this.isAutoPricing = isAutoPricing;
    }

    public LocalDateTime getContentModifiedDate() {
        return contentModifiedDate;
    }

    public void setContentModifiedDate(LocalDateTime contentModifiedDate) {
        this.contentModifiedDate = contentModifiedDate;
    }

    public Boolean getSealedBidding() {
        return sealedBidding;
    }

    public void setSealedBidding(Boolean sealedBidding) {
        this.sealedBidding = sealedBidding;
    }

    public Boolean getIsSealed() {
        return isSealed;
    }

    public void setIsSealed(Boolean isSealed) {
        this.isSealed = isSealed;
    }

    public Boolean getIsAutoClose() {
        return isAutoClose;
    }

    public void setIsAutoClose(Boolean isAutoClose) {
        this.isAutoClose = isAutoClose;
    }

    public Boolean getIsDismissSupplier() {
        return isDismissSupplier;
    }

    public void setIsDismissSupplier(Boolean isDismissSupplier) {
        this.isDismissSupplier = isDismissSupplier;
    }

    public Boolean getItemizedTaxAndShipping() {
        return itemizedTaxAndShipping;
    }

    public void setItemizedTaxAndShipping(Boolean itemizedTaxAndShipping) {
        this.itemizedTaxAndShipping = itemizedTaxAndShipping;
    }

    public BigDecimal getInitialPrice() {
        return initialPrice;
    }

    public void setInitialPrice(BigDecimal initialPrice) {
        this.initialPrice = initialPrice;
    }

    public BigDecimal getAuctionPrice() {
        return auctionPrice;
    }

    public void setAuctionPrice(BigDecimal auctionPrice) {
        this.auctionPrice = auctionPrice;
    }

    public Double getDecrementPricePercent() {
        return decrementPricePercent;
    }

    public void setDecrementPricePercent(Double decrementPricePercent) {
        this.decrementPricePercent = decrementPricePercent;
    }

    public BigDecimal getDecrementPrice() {
        return decrementPrice;
    }

    public void setDecrementPrice(BigDecimal decrementPrice) {
        this.decrementPrice = decrementPrice;
    }

    public Boolean getPriceIncludesTax() {
        return priceIncludesTax;
    }

    public void setPriceIncludesTax(Boolean priceIncludesTax) {
        this.priceIncludesTax = priceIncludesTax;
    }

    public Boolean getPriceIncludesShipping() {
        return priceIncludesShipping;
    }

    public void setPriceIncludesShipping(Boolean priceIncludesShipping) {
        this.priceIncludesShipping = priceIncludesShipping;
    }

    public Boolean getManualSelectWinningBidder() {
        return manualSelectWinningBidder;
    }

    public void setManualSelectWinningBidder(Boolean manualSelectWinningBidder) {
        this.manualSelectWinningBidder = manualSelectWinningBidder;
    }

    public Boolean getAllowHigherBid() {
        return allowHigherBid;
    }

    public void setAllowHigherBid(Boolean allowHigherBid) {
        this.allowHigherBid = allowHigherBid;
    }

    public Boolean getExtendFromLastBid() {
        return extendFromLastBid;
    }

    public void setExtendFromLastBid(Boolean extendFromLastBid) {
        this.extendFromLastBid = extendFromLastBid;
    }

    public Boolean getRequireSupplierAcceptance() {
        return requireSupplierAcceptance;
    }

    public void setRequireSupplierAcceptance(Boolean requireSupplierAcceptance) {
        this.requireSupplierAcceptance = requireSupplierAcceptance;
    }

    public Long getSourceTypeId() {
        return sourceTypeId;
    }

    public void setSourceTypeId(Long sourceTypeId) {
        this.sourceTypeId = sourceTypeId;
    }

    public List<RfeItem> getRfeItems() {
        return rfeItems;
    }

    public void setRfeItems(List<RfeItem> rfeItems) {
        this.rfeItems = rfeItems;
    }

    public List<RfeSupplier> getRfeSuppliers() {
        return rfeSuppliers;
    }

    public void setRfeSuppliers(List<RfeSupplier> rfeSuppliers) {
        this.rfeSuppliers = rfeSuppliers;
    }


}