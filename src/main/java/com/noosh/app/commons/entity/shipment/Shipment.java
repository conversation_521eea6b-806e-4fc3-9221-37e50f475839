package com.noosh.app.commons.entity.shipment;

import com.noosh.app.commons.entity.NooshAuditingEntity;
import com.noosh.app.commons.entity.job.PcJob;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 5/19/16
 */
@Entity
@Table(name = "SH_SHIPMENT")
@Cache(usage = CacheConcurrencyStrategy.NONE)
public class Shipment extends NooshAuditingEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "shipment_id_generated")
    @SequenceGenerator(name = "shipment_id_generated", sequenceName = "SH_SHIPMENT_SEQ", allocationSize = 1)
    @Column(name = "SH_SHIPMENT_ID")
    private Long shShipmentId;

    private String name;

    private String comments;

    private Long ownerAcWorkgroupId;

    private Long customPrPropertyId;

    private Long ocObjectStateId;

    @Column(name = "PC_JOB_ID")
    private Long pcJobId;

    private Short isTemplate;

    private static final long serialVersionUID = 1L;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PC_JOB_ID", insertable = false, updatable = false)
    private PcJob job;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "shipment")
    private List<Request> requests;

    public long getTotalReqQty() {
        long total = 0;
        if (requests != null && requests.size() > 0) {
            for (Request r : requests) {
                if (r.isNormal()) {
                    total += r.getQuantity();
                }
            }
        }
        return  total;
    }

    public double getTotalShippedQty()
    {
        double total = 0;
        if (requests != null && requests.size() > 0)
        {
            for (Request r : requests)
                if (r.isNormal()) {
                    total += r.getTotalShippedQty();
                }
        }
        return total;
    }

    public double getTotalReceivedQty()
    {
        double total = 0;

        if (requests != null && requests.size() > 0)
        {
            for (Request r : requests)
            {
                if (r.isNormal()) {
                    total += r.getTotalReceivedQty();
                }
            }
        }
        return total;
    }


    public List<Request> getRequests() {
        return requests;
    }

    public void setRequests(List<Request> requests) {
        this.requests = requests;
    }

    public PcJob getJob() {
        return job;
    }

    public void setJob(PcJob job) {
        this.job = job;
    }

    public Long getShShipmentId() {
        return shShipmentId;
    }

    public void setShShipmentId(Long shShipmentId) {
        this.shShipmentId = shShipmentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments == null ? null : comments.trim();
    }

    public Long getOwnerAcWorkgroupId() {
        return ownerAcWorkgroupId;
    }

    public void setOwnerAcWorkgroupId(Long ownerAcWorkgroupId) {
        this.ownerAcWorkgroupId = ownerAcWorkgroupId;
    }

    public Long getCustomPrPropertyId() {
        return customPrPropertyId;
    }

    public void setCustomPrPropertyId(Long customPrPropertyId) {
        this.customPrPropertyId = customPrPropertyId;
    }

    public Long getOcObjectStateId() {
        return ocObjectStateId;
    }

    public void setOcObjectStateId(Long ocObjectStateId) {
        this.ocObjectStateId = ocObjectStateId;
    }

    public Long getPcJobId() {
        return pcJobId;
    }

    public void setPcJobId(Long pcJobId) {
        this.pcJobId = pcJobId;
    }

    public Short getIsTemplate() {
        return isTemplate;
    }

    public void setIsTemplate(Short isTemplate) {
        this.isTemplate = isTemplate;
    }
}