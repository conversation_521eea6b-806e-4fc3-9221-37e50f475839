package com.noosh.app.commons.vo.navbar;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.noosh.app.commons.dto.Saml2ResultDTO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Schema(description = "Top navigation bar")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TopNavBarVO {
    @Schema(description = "Saml2")
    private List<Saml2ResultDTO> saml2ResultDTOList;
    private String amasHref;
    private String betaOnHref;
    private String betaOffHref;
    @Schema(description = "Is show beta")
    private boolean isShowBeta;
    private boolean isShowPerformance;
    private String logoImgHref;
    @Schema(description = "Is multiple choose workgroup, true: multiple workgroup, false: single or empty workgroup ")
    private Boolean isMutipleWorkgroup;
    private String chooseGroupHref;
    private String homeSearchHref;
    private String profileImgHref;
    private String neLoginHref;
    private String neLogoutHref;
    @Schema(description = "Logout message")
    private String logoutMessage;
    private String myDeskHref;
    private String summaryHref;
    private String myDeskCustomizeHref;
    private String eventHref;
    private String taskHref;
    private String timecardHref;
    private String timecardMyHref;
    private String timecardReceivedHref;
    private String messageHref;
    private String dashboardHref;
    private String projectHref;
    private String projectListHref;
    private String projectMilestoneHref;
    private String projectOverviewUpdateHref;
    private String specHref;
    private String rfeHref;
    private String estimateHref;
    private String orderHref;
    private String rfqHref;
    private String quoteHref;
    private String prHref;
    private String jobHref;
    private String inventoryHref;
    private String inventoryUploadHref;
    private String itemMasterHref;
    private String projectCategoryHref;
    private String invoiceHref;
    //Contact
    private String contactHref;
    private String contactListHref;
    private String contactCategoryHref;
    private String templateHref;
    // Report
    private String standardHref;
    private String standardFavoritesHref;
    private String standardStandardHref;
    private String standardSavedHref;
    // Business Analytics
    private String advancedReportDashboardHref;
    private String advancedReportAllHref;
    private String advancedReportMyHref;
    private String advancedReportAdvancedHref;
    // Client PSFs
    private String siteListUrl;
    private String newDashboardHref;
    // ADMIN
    private String personalHref;
    private String workgroupHref;
    // help and support
    @Schema(description = "Is disable zendesk")
    private boolean isDisableZendesk;
    private String helpURL;
    private String supportURL;
    @Schema(description = "Can view settings")
    private boolean canViewSettings;
    // create project
    private String createProjectHref;
    // recent projects
    private String recentProjectsHref;
    //INTERNAL-admin
    @Schema(description = "Can view accounts")
    private boolean canViewAccounts;
    @Schema(description = "Can view support")
    private boolean canViewSupport;
    @Schema(description = "Can view prof svs")
    private boolean canViewProfSvs;
    @Schema(description = "Can manager")
    private boolean canManager;
    private String internalHref;
    private String accountsHref;
    private String supportHref;
    @Schema(description = "Can view system announcements")
    private boolean canViewSystemAnnouncements;
    @Schema(description = "Can view announcements")
    private boolean canViewAnnouncements;
    @Schema(description = "Can view users")
    private boolean canViewUsers;
    @Schema(description = "Can view events")
    private boolean canViewEvents;
    @Schema(description = "Can sessions")
    private boolean canSessions;
    private String systemAnnouncementsHref;
    private String announcementsHref;
    private String usersHref;
    private String eventsHref;
    private String sessionsHref;
    private String customHref;
    private String customSpecsHref;
    private String customRolesHref;
    private String customPropertyHref;
    private String customResourcesHref;
    private String customDxAccountsHref;
    private String customDxGroupsHref;
    private String customDxRequestsHref;
    private String partnerHref;
    @Schema(description = "Can view partner svs")
    private boolean canViewPartnerSvs;
    private String managedSpecsHref;
    private String managedRolesHref;
    private String adminManagementHref;
    private String adminHref;
    @Schema(description = "Navigation bar list")
    private List<NavBarVO> navBars;

    public String getInvoiceHref() {
        return invoiceHref;
    }

    public void setInvoiceHref(String invoiceHref) {
        this.invoiceHref = invoiceHref;
    }

    public List<Saml2ResultDTO> getSaml2ResultDTOList() {
        return saml2ResultDTOList;
    }

    public void setSaml2ResultDTOList(List<Saml2ResultDTO> saml2ResultDTOList) {
        this.saml2ResultDTOList = saml2ResultDTOList;
    }

    public String getAmasHref() {
        return amasHref;
    }

    public void setAmasHref(String amasHref) {
        this.amasHref = amasHref;
    }

    public String getBetaOnHref() {
        return betaOnHref;
    }

    public void setBetaOnHref(String betaOnHref) {
        this.betaOnHref = betaOnHref;
    }

    public String getBetaOffHref() {
        return betaOffHref;
    }

    public void setBetaOffHref(String betaOffHref) {
        this.betaOffHref = betaOffHref;
    }

    public boolean isShowBeta() {
        return isShowBeta;
    }

    public void setShowBeta(boolean showBeta) {
        isShowBeta = showBeta;
    }

    public boolean isShowPerformance() {
        return isShowPerformance;
    }

    public void setShowPerformance(boolean showPerformance) {
        isShowPerformance = showPerformance;
    }

    public String getLogoImgHref() {
        return logoImgHref;
    }

    public void setLogoImgHref(String logoImgHref) {
        this.logoImgHref = logoImgHref;
    }

    public Boolean getIsMutipleWorkgroup() {
        return isMutipleWorkgroup;
    }

    public void setIsMutipleWorkgroup(Boolean isMutipleWorkgroup) {
        this.isMutipleWorkgroup = isMutipleWorkgroup;
    }

    public String getChooseGroupHref() {
        return chooseGroupHref;
    }

    public void setChooseGroupHref(String chooseGroupHref) {
        this.chooseGroupHref = chooseGroupHref;
    }

    public String getHomeSearchHref() {
        return homeSearchHref;
    }

    public void setHomeSearchHref(String homeSearchHref) {
        this.homeSearchHref = homeSearchHref;
    }

    public String getProfileImgHref() {
        return profileImgHref;
    }

    public void setProfileImgHref(String profileImgHref) {
        this.profileImgHref = profileImgHref;
    }

    public String getNeLoginHref() {
        return neLoginHref;
    }

    public void setNeLoginHref(String neLoginHref) {
        this.neLoginHref = neLoginHref;
    }

    public String getNeLogoutHref() {
        return neLogoutHref;
    }

    public void setNeLogoutHref(String neLogoutHref) {
        this.neLogoutHref = neLogoutHref;
    }

    public String getLogoutMessage() {
        return logoutMessage;
    }

    public void setLogoutMessage(String logoutMessage) {
        this.logoutMessage = logoutMessage;
    }

    public String getMyDeskHref() {
        return myDeskHref;
    }

    public void setMyDeskHref(String myDeskHref) {
        this.myDeskHref = myDeskHref;
    }

    public String getSummaryHref() {
        return summaryHref;
    }

    public void setSummaryHref(String summaryHref) {
        this.summaryHref = summaryHref;
    }

    public String getMyDeskCustomizeHref() {
        return myDeskCustomizeHref;
    }

    public void setMyDeskCustomizeHref(String myDeskCustomizeHref) {
        this.myDeskCustomizeHref = myDeskCustomizeHref;
    }

    public String getEventHref() {
        return eventHref;
    }

    public void setEventHref(String eventHref) {
        this.eventHref = eventHref;
    }

    public String getTaskHref() {
        return taskHref;
    }

    public void setTaskHref(String taskHref) {
        this.taskHref = taskHref;
    }

    public String getTimecardHref() {
        return timecardHref;
    }

    public void setTimecardHref(String timecardHref) {
        this.timecardHref = timecardHref;
    }

    public String getTimecardMyHref() {
        return timecardMyHref;
    }

    public void setTimecardMyHref(String timecardMyHref) {
        this.timecardMyHref = timecardMyHref;
    }

    public String getTimecardReceivedHref() {
        return timecardReceivedHref;
    }

    public void setTimecardReceivedHref(String timecardReceivedHref) {
        this.timecardReceivedHref = timecardReceivedHref;
    }

    public String getMessageHref() {
        return messageHref;
    }

    public void setMessageHref(String messageHref) {
        this.messageHref = messageHref;
    }

    public String getDashboardHref() {
        return dashboardHref;
    }

    public void setDashboardHref(String dashboardHref) {
        this.dashboardHref = dashboardHref;
    }

    public String getProjectHref() {
        return projectHref;
    }

    public void setProjectHref(String projectHref) {
        this.projectHref = projectHref;
    }

    public String getProjectListHref() {
        return projectListHref;
    }

    public void setProjectListHref(String projectListHref) {
        this.projectListHref = projectListHref;
    }

    public String getProjectMilestoneHref() {
        return projectMilestoneHref;
    }

    public void setProjectMilestoneHref(String projectMilestoneHref) {
        this.projectMilestoneHref = projectMilestoneHref;
    }

    public String getProjectOverviewUpdateHref() {
        return projectOverviewUpdateHref;
    }

    public void setProjectOverviewUpdateHref(String projectOverviewUpdateHref) {
        this.projectOverviewUpdateHref = projectOverviewUpdateHref;
    }

    public String getSpecHref() {
        return specHref;
    }

    public void setSpecHref(String specHref) {
        this.specHref = specHref;
    }

    public String getRfeHref() {
        return rfeHref;
    }

    public void setRfeHref(String rfeHref) {
        this.rfeHref = rfeHref;
    }

    public String getEstimateHref() {
        return estimateHref;
    }

    public void setEstimateHref(String estimateHref) {
        this.estimateHref = estimateHref;
    }

    public String getOrderHref() {
        return orderHref;
    }

    public void setOrderHref(String orderHref) {
        this.orderHref = orderHref;
    }

    public String getRfqHref() {
        return rfqHref;
    }

    public void setRfqHref(String rfqHref) {
        this.rfqHref = rfqHref;
    }

    public String getQuoteHref() {
        return quoteHref;
    }

    public void setQuoteHref(String quoteHref) {
        this.quoteHref = quoteHref;
    }

    public String getPrHref() {
        return prHref;
    }

    public void setPrHref(String prHref) {
        this.prHref = prHref;
    }

    public String getJobHref() {
        return jobHref;
    }

    public void setJobHref(String jobHref) {
        this.jobHref = jobHref;
    }

    public String getInventoryHref() {
        return inventoryHref;
    }

    public void setInventoryHref(String inventoryHref) {
        this.inventoryHref = inventoryHref;
    }

    public String getInventoryUploadHref() {
        return inventoryUploadHref;
    }

    public void setInventoryUploadHref(String inventoryUploadHref) {
        this.inventoryUploadHref = inventoryUploadHref;
    }

    public String getItemMasterHref() {
        return itemMasterHref;
    }

    public void setItemMasterHref(String itemMasterHref) {
        this.itemMasterHref = itemMasterHref;
    }

    public String getProjectCategoryHref() {
        return projectCategoryHref;
    }

    public void setProjectCategoryHref(String projectCategoryHref) {
        this.projectCategoryHref = projectCategoryHref;
    }

    public String getContactHref() {
        return contactHref;
    }

    public void setContactHref(String contactHref) {
        this.contactHref = contactHref;
    }

    public String getContactListHref() {
        return contactListHref;
    }

    public void setContactListHref(String contactListHref) {
        this.contactListHref = contactListHref;
    }

    public String getContactCategoryHref() {
        return contactCategoryHref;
    }

    public void setContactCategoryHref(String contactCategoryHref) {
        this.contactCategoryHref = contactCategoryHref;
    }

    public String getTemplateHref() {
        return templateHref;
    }

    public void setTemplateHref(String templateHref) {
        this.templateHref = templateHref;
    }

    public String getStandardHref() {
        return standardHref;
    }

    public void setStandardHref(String standardHref) {
        this.standardHref = standardHref;
    }

    public String getStandardFavoritesHref() {
        return standardFavoritesHref;
    }

    public void setStandardFavoritesHref(String standardFavoritesHref) {
        this.standardFavoritesHref = standardFavoritesHref;
    }

    public String getStandardStandardHref() {
        return standardStandardHref;
    }

    public void setStandardStandardHref(String standardStandardHref) {
        this.standardStandardHref = standardStandardHref;
    }

    public String getStandardSavedHref() {
        return standardSavedHref;
    }

    public void setStandardSavedHref(String standardSavedHref) {
        this.standardSavedHref = standardSavedHref;
    }

    public String getAdvancedReportDashboardHref() {
        return advancedReportDashboardHref;
    }

    public void setAdvancedReportDashboardHref(String advancedReportDashboardHref) {
        this.advancedReportDashboardHref = advancedReportDashboardHref;
    }

    public String getAdvancedReportAllHref() {
        return advancedReportAllHref;
    }

    public void setAdvancedReportAllHref(String advancedReportAllHref) {
        this.advancedReportAllHref = advancedReportAllHref;
    }

    public String getAdvancedReportMyHref() {
        return advancedReportMyHref;
    }

    public void setAdvancedReportMyHref(String advancedReportMyHref) {
        this.advancedReportMyHref = advancedReportMyHref;
    }

    public String getAdvancedReportAdvancedHref() {
        return advancedReportAdvancedHref;
    }

    public void setAdvancedReportAdvancedHref(String advancedReportAdvancedHref) {
        this.advancedReportAdvancedHref = advancedReportAdvancedHref;
    }

    public String getSiteListUrl() {
        return siteListUrl;
    }

    public void setSiteListUrl(String siteListUrl) {
        this.siteListUrl = siteListUrl;
    }

    public String getNewDashboardHref() {
        return newDashboardHref;
    }

    public void setNewDashboardHref(String newDashboardHref) {
        this.newDashboardHref = newDashboardHref;
    }

    public String getPersonalHref() {
        return personalHref;
    }

    public void setPersonalHref(String personalHref) {
        this.personalHref = personalHref;
    }

    public String getWorkgroupHref() {
        return workgroupHref;
    }

    public void setWorkgroupHref(String workgroupHref) {
        this.workgroupHref = workgroupHref;
    }

    public boolean getIsDisableZendesk() {
        return isDisableZendesk;
    }

    public void setIsDisableZendesk(boolean isDisableZendesk) {
        this.isDisableZendesk = isDisableZendesk;
    }

    public String getHelpURL() {
        return helpURL;
    }

    public void setHelpURL(String helpURL) {
        this.helpURL = helpURL;
    }

    public String getSupportURL() {
        return supportURL;
    }

    public void setSupportURL(String supportURL) {
        this.supportURL = supportURL;
    }

    public boolean getCanViewSettings() {
        return canViewSettings;
    }

    public void setCanViewSettings(boolean canViewSettings) {
        this.canViewSettings = canViewSettings;
    }

    public String getCreateProjectHref() {
        return createProjectHref;
    }

    public void setCreateProjectHref(String createProjectHref) {
        this.createProjectHref = createProjectHref;
    }

    public String getRecentProjectsHref() {
        return recentProjectsHref;
    }

    public void setRecentProjectsHref(String recentProjectsHref) {
        this.recentProjectsHref = recentProjectsHref;
    }

    public boolean getCanViewAccounts() {
        return canViewAccounts;
    }

    public void setCanViewAccounts(boolean canViewAccounts) {
        this.canViewAccounts = canViewAccounts;
    }

    public boolean getCanViewSupport() {
        return canViewSupport;
    }

    public void setCanViewSupport(boolean canViewSupport) {
        this.canViewSupport = canViewSupport;
    }

    public boolean getCanViewProfSvs() {
        return canViewProfSvs;
    }

    public void setCanViewProfSvs(boolean canViewProfSvs) {
        this.canViewProfSvs = canViewProfSvs;
    }

    public boolean getCanManager() {
        return canManager;
    }

    public void setCanManager(boolean canManager) {
        this.canManager = canManager;
    }

    public String getInternalHref() {
        return internalHref;
    }

    public void setInternalHref(String internalHref) {
        this.internalHref = internalHref;
    }

    public String getAccountsHref() {
        return accountsHref;
    }

    public void setAccountsHref(String accountsHref) {
        this.accountsHref = accountsHref;
    }

    public String getSupportHref() {
        return supportHref;
    }

    public void setSupportHref(String supportHref) {
        this.supportHref = supportHref;
    }

    public boolean getCanViewSystemAnnouncements() {
        return canViewSystemAnnouncements;
    }

    public void setCanViewSystemAnnouncements(boolean canViewSystemAnnouncements) {
        this.canViewSystemAnnouncements = canViewSystemAnnouncements;
    }

    public boolean getCanViewAnnouncements() {
        return canViewAnnouncements;
    }

    public void setCanViewAnnouncements(boolean canViewAnnouncements) {
        this.canViewAnnouncements = canViewAnnouncements;
    }

    public boolean getCanViewUsers() {
        return canViewUsers;
    }

    public void setCanViewUsers(boolean canViewUsers) {
        this.canViewUsers = canViewUsers;
    }

    public boolean getCanViewEvents() {
        return canViewEvents;
    }

    public void setCanViewEvents(boolean canViewEvents) {
        this.canViewEvents = canViewEvents;
    }

    public boolean getCanSessions() {
        return canSessions;
    }

    public void setCanSessions(boolean canSessions) {
        this.canSessions = canSessions;
    }

    public String getSystemAnnouncementsHref() {
        return systemAnnouncementsHref;
    }

    public void setSystemAnnouncementsHref(String systemAnnouncementsHref) {
        this.systemAnnouncementsHref = systemAnnouncementsHref;
    }

    public String getAnnouncementsHref() {
        return announcementsHref;
    }

    public void setAnnouncementsHref(String announcementsHref) {
        this.announcementsHref = announcementsHref;
    }

    public String getUsersHref() {
        return usersHref;
    }

    public void setUsersHref(String usersHref) {
        this.usersHref = usersHref;
    }

    public String getEventsHref() {
        return eventsHref;
    }

    public void setEventsHref(String eventsHref) {
        this.eventsHref = eventsHref;
    }

    public String getSessionsHref() {
        return sessionsHref;
    }

    public void setSessionsHref(String sessionsHref) {
        this.sessionsHref = sessionsHref;
    }

    public String getCustomHref() {
        return customHref;
    }

    public void setCustomHref(String customHref) {
        this.customHref = customHref;
    }

    public String getCustomSpecsHref() {
        return customSpecsHref;
    }

    public void setCustomSpecsHref(String customSpecsHref) {
        this.customSpecsHref = customSpecsHref;
    }

    public String getCustomRolesHref() {
        return customRolesHref;
    }

    public void setCustomRolesHref(String customRolesHref) {
        this.customRolesHref = customRolesHref;
    }

    public String getCustomPropertyHref() {
        return customPropertyHref;
    }

    public void setCustomPropertyHref(String customPropertyHref) {
        this.customPropertyHref = customPropertyHref;
    }

    public String getCustomResourcesHref() {
        return customResourcesHref;
    }

    public void setCustomResourcesHref(String customResourcesHref) {
        this.customResourcesHref = customResourcesHref;
    }

    public String getCustomDxAccountsHref() {
        return customDxAccountsHref;
    }

    public void setCustomDxAccountsHref(String customDxAccountsHref) {
        this.customDxAccountsHref = customDxAccountsHref;
    }

    public String getCustomDxGroupsHref() {
        return customDxGroupsHref;
    }

    public void setCustomDxGroupsHref(String customDxGroupsHref) {
        this.customDxGroupsHref = customDxGroupsHref;
    }

    public String getCustomDxRequestsHref() {
        return customDxRequestsHref;
    }

    public void setCustomDxRequestsHref(String customDxRequestsHref) {
        this.customDxRequestsHref = customDxRequestsHref;
    }

    public String getPartnerHref() {
        return partnerHref;
    }

    public void setPartnerHref(String partnerHref) {
        this.partnerHref = partnerHref;
    }

    public boolean getCanViewPartnerSvs() {
        return canViewPartnerSvs;
    }

    public void setCanViewPartnerSvs(boolean canViewPartnerSvs) {
        this.canViewPartnerSvs = canViewPartnerSvs;
    }

    public String getManagedSpecsHref() {
        return managedSpecsHref;
    }

    public void setManagedSpecsHref(String managedSpecsHref) {
        this.managedSpecsHref = managedSpecsHref;
    }

    public String getManagedRolesHref() {
        return managedRolesHref;
    }

    public void setManagedRolesHref(String managedRolesHref) {
        this.managedRolesHref = managedRolesHref;
    }

    public String getAdminManagementHref() {
        return adminManagementHref;
    }

    public void setAdminManagementHref(String adminManagementHref) {
        this.adminManagementHref = adminManagementHref;
    }

    public String getAdminHref() {
        return adminHref;
    }

    public void setAdminHref(String adminHref) {
        this.adminHref = adminHref;
    }

    public List<NavBarVO> getNavBars() {
        return navBars;
    }

    public void setNavBars(List<NavBarVO> navBars) {
        this.navBars = navBars;
    }
}
