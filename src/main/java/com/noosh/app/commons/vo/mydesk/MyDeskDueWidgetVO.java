package com.noosh.app.commons.vo.mydesk;

import com.noosh.app.commons.vo.invoice.InvoicePendingMyDeskVO;
import com.noosh.app.commons.vo.order.OrderPendingMyDeskVO;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 4/15/24
 */
public class MyDeskDueWidgetVO implements Serializable {
    private static final long serialVersionUID = -4808460141064999751L;

    private boolean canViewOrders;

    private boolean canViewInvoices;

    private List<OrderPendingMyDeskVO> orders;

    private List<InvoicePendingMyDeskVO> invoices;

    public boolean getCanViewOrders() {
        return canViewOrders;
    }

    public void setCanViewOrders(boolean canViewOrders) {
        this.canViewOrders = canViewOrders;
    }

    public boolean getCanViewInvoices() {
        return canViewInvoices;
    }

    public void setCanViewInvoices(boolean canViewInvoices) {
        this.canViewInvoices = canViewInvoices;
    }

    public List<OrderPendingMyDeskVO> getOrders() {
        return orders;
    }

    public void setOrders(List<OrderPendingMyDeskVO> orders) {
        this.orders = orders;
    }

    public List<InvoicePendingMyDeskVO> getInvoices() {
        return invoices;
    }

    public void setInvoices(List<InvoicePendingMyDeskVO> invoices) {
        this.invoices = invoices;
    }
}
