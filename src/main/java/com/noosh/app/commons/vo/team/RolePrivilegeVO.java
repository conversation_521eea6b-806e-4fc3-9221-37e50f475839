package com.noosh.app.commons.vo.team;

import com.noosh.app.commons.dto.role.PrivilegeDTO;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 3/22/20
 */
public class RolePrivilegeVO implements Serializable {
    private static final long serialVersionUID = 5744803520408529563L;

    private String role;

    private String roleNameDesc;

    private String roleNameStrId;

    private List<PrivilegeDTO> privileges;

    public String getRoleNameDesc() {
        return roleNameDesc;
    }

    public void setRoleNameDesc(String roleNameDesc) {
        this.roleNameDesc = roleNameDesc;
    }

    public String getRoleNameStrId() {
        return roleNameStrId;
    }

    public void setRoleNameStrId(String roleNameStrId) {
        this.roleNameStrId = roleNameStrId;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public List<PrivilegeDTO> getPrivileges() {
        return privileges;
    }

    public void setPrivileges(List<PrivilegeDTO> privileges) {
        this.privileges = privileges;
    }
}
