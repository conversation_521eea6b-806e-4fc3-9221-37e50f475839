package com.noosh.app.commons.vo.collaboration;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * User: leilaz
 * Date: 7/6/22
 */
public class ProjectStatusVO implements Serializable {
    private static final long serialVersionUID = -4168334658326699384L;

    @Schema(description = "Project status id")
    private Long id;

    @Schema(description = "Project status name")
    private String name;

    @Schema(description = "Project status is default or not")
    private Boolean isDefault;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean aDefault) {
        isDefault = aDefault;
    }
}
