package com.noosh.app.commons.vo.speclist;

import com.noosh.app.commons.vo.spec.QuantityVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.util.StringUtils;

/**
 * @auther mario
 * @date 7/9/2020
 */
public class SpecVO {

    private String preferredSpecName;
    private Boolean isItemVersion = false;
    private Long specReferenceId;
    private Long specNodeId;
    private String iconPath;
    private String specTypeName;
    private String specUserState;
    private String specUserStateStrId;
    private Long specUserStateId = -1L;
    private boolean isLockSpec;
    private BigDecimal baseline;
    private Long originalSpecId;
    private Long serviceKey;

    private Long jobId;
    private String client;
    private String clientStrId;
    private BigDecimal clientPrice;
    private Long clientPriceCurrencyId;
    private String supplier;
    private String supplierStrId;
    private BigDecimal supplierPrice;
    private String supplierWorkgroupName;
    private Long supplierPriceCurrencyId;

    private Long quantity;
    private List<QuantityVO> quantitys;
    private Boolean isQuantityReadonly;

    private Double rateCardPrice;
    private String rateCardSupplier;
    private String createdByUser;
    private LocalDateTime createdDate;
    private LocalDateTime lastUpdatedDate;

    private String viewSpecExternalLink;
    private String copySpecExternalLink;
    private String procurementSellExternalLink;
    private String procurementBuyExternalLink;

    private SpecLastOrderPriceVO specLastOrderPrice;

    private List<SpecOptionVO> optionSpecs;

    private Map<String, Object> customAttributes;

	private Long propertyId;

    private String invoiceBuyStrId;

    private String invoiceSellStrId;

    private long rfeCount;

    private long estimateCount;

    private long quoteCount;

    private long buyOrderCount;

    private long sellOrderCount;

    private long buyInvoiceCount;

    private long sellInvoiceCount;

    public String getSupplierWorkgroupName() {
        return supplierWorkgroupName;
    }

    public void setSupplierWorkgroupName(String supplierWorkgroupName) {
        this.supplierWorkgroupName = supplierWorkgroupName;
    }

    public long getRfeCount() {
        return rfeCount;
    }

    public void setRfeCount(long rfeCount) {
        this.rfeCount = rfeCount;
    }

    public long getEstimateCount() {
        return estimateCount;
    }

    public void setEstimateCount(long estimateCount) {
        this.estimateCount = estimateCount;
    }

    public long getQuoteCount() {
        return quoteCount;
    }

    public void setQuoteCount(long quoteCount) {
        this.quoteCount = quoteCount;
    }

    public long getBuyOrderCount() {
        return buyOrderCount;
    }

    public void setBuyOrderCount(long buyOrderCount) {
        this.buyOrderCount = buyOrderCount;
    }

    public long getSellOrderCount() {
        return sellOrderCount;
    }

    public void setSellOrderCount(long sellOrderCount) {
        this.sellOrderCount = sellOrderCount;
    }

    public long getBuyInvoiceCount() {
        return buyInvoiceCount;
    }

    public void setBuyInvoiceCount(long buyInvoiceCount) {
        this.buyInvoiceCount = buyInvoiceCount;
    }

    public long getSellInvoiceCount() {
        return sellInvoiceCount;
    }

    public void setSellInvoiceCount(long sellInvoiceCount) {
        this.sellInvoiceCount = sellInvoiceCount;
    }

    public String getInvoiceSellStrId() {
        return invoiceSellStrId;
    }

    public void setInvoiceSellStrId(String invoiceSellStrId) {
        this.invoiceSellStrId = invoiceSellStrId;
    }

    public String getProcurementBuyExternalLink() {
        return procurementBuyExternalLink;
    }

    public void setProcurementBuyExternalLink(String procurementBuyExternalLink) {
        this.procurementBuyExternalLink = procurementBuyExternalLink;
    }

    public String getInvoiceBuyStrId() {
        return invoiceBuyStrId;
    }

    public void setInvoiceBuyStrId(String invoiceBuyStrId) {
        this.invoiceBuyStrId = invoiceBuyStrId;
    }

    public String getProcurementSellExternalLink() {
        return procurementSellExternalLink;
    }

    public void setProcurementSellExternalLink(String procurementSellExternalLink) {
        this.procurementSellExternalLink = procurementSellExternalLink;
    }

    @JsonIgnore
	public Long getPropertyId() {
		return propertyId;
	}

	public void setPropertyId(Long propertyId) {
		this.propertyId = propertyId;
	}

    public Long getServiceKey() {
        return serviceKey;
    }

    public void setServiceKey(Long serviceKey) {
        this.serviceKey = serviceKey;
    }

    public Map<String, Object> getCustomAttributes() {
		return customAttributes;
	}

	public void setCustomAttributes(Map<String, Object> customAttributes) {
		this.customAttributes = customAttributes;
	}

	public String getSpecUserStateStrId() {
        return specUserStateStrId;
    }

    public void setSpecUserStateStrId(String specUserStateStrId) {
        this.specUserStateStrId = specUserStateStrId;
    }

    public Long getOriginalSpecId() {
        return originalSpecId;
    }

    public void setOriginalSpecId(Long originalSpecId) {
        this.originalSpecId = originalSpecId;
    }

    public BigDecimal getBaseline() {
        return baseline;
    }

    public void setBaseline(BigDecimal baseline) {
        this.baseline = baseline;
    }

    public Double getRateCardPrice() {
        return rateCardPrice;
    }

    public void setRateCardPrice(Double rateCardPrice) {
        this.rateCardPrice = rateCardPrice;
    }

    public String getSupplierStrId() {
        return supplierStrId;
    }

    public void setSupplierStrId(String supplierStrId) {
        this.supplierStrId = supplierStrId;
    }

    public String getClientStrId() {
        return clientStrId;
    }

    public void setClientStrId(String clientStrId) {
        this.clientStrId = clientStrId;
    }

    public String getSpecUserState() {
        return specUserState;
    }

    public void setSpecUserState(String specUserState) {
        this.specUserState = specUserState;
    }

    public Boolean getIsItemVersion() {
        return isItemVersion;
    }

    public void setIsItemVersion(Boolean isItemVersion) {
        this.isItemVersion = isItemVersion;
    }

    public String getViewSpecExternalLink() {
        return viewSpecExternalLink;
    }

    public void setViewSpecExternalLink(String viewSpecExternalLink) {
        this.viewSpecExternalLink = viewSpecExternalLink;
    }

    public String getCopySpecExternalLink() {
        return copySpecExternalLink;
    }

    public void setCopySpecExternalLink(String copySpecExternalLink) {
        this.copySpecExternalLink = copySpecExternalLink;
    }

    public String getPreferredSpecName() {
        return preferredSpecName;
    }

    public void setPreferredSpecName(String preferredSpecName) {
        this.preferredSpecName = preferredSpecName;
    }

    public Long getSpecReferenceId() {
        return specReferenceId;
    }

    public void setSpecReferenceId(Long specReferenceId) {
        this.specReferenceId = specReferenceId;
    }

    public Long getSpecNodeId() {
        return specNodeId;
    }

    public void setSpecNodeId(Long specNodeId) {
        this.specNodeId = specNodeId;
    }

    public String getIconPath() {
        return iconPath;
    }

    public void setIconPath(String iconPath) {
        this.iconPath = iconPath;
    }

    public String getSpecTypeName() {
        return specTypeName;
    }

    public void setSpecTypeName(String specTypeName) {
        this.specTypeName = specTypeName;
    }

    public Long getSpecUserStateId() {
        return specUserStateId;
    }

    public void setSpecUserStateId(Long specUserStateId) {
        this.specUserStateId = specUserStateId;
    }

    public boolean isLockSpec() {
        return isLockSpec;
    }

    public boolean getIsLockSpec() {
        return isLockSpec;
    }

    public void setIsLockSpec(boolean lockSpec) {
        isLockSpec = lockSpec;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client;
    }

    public BigDecimal getClientPrice() {
        return clientPrice;
    }

    public void setClientPrice(BigDecimal clientPrice) {
        this.clientPrice = clientPrice;
    }

    public Long getClientPriceCurrencyId() {
        return clientPriceCurrencyId;
    }

    public void setClientPriceCurrencyId(Long clientPriceCurrencyId) {
        this.clientPriceCurrencyId = clientPriceCurrencyId;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public BigDecimal getSupplierPrice() {
        return supplierPrice;
    }

    public void setSupplierPrice(BigDecimal supplierPrice) {
        this.supplierPrice = supplierPrice;
    }

    public Long getSupplierPriceCurrencyId() {
        return supplierPriceCurrencyId;
    }

    public void setSupplierPriceCurrencyId(Long supplierPriceCurrencyId) {
        this.supplierPriceCurrencyId = supplierPriceCurrencyId;
    }

    public Long getQuantity() {
        return quantity;
    }

    public void setQuantity(Long quantity1) {
        this.quantity = quantity1;
    }

    public List<QuantityVO> getQuantitys() {
        return quantitys;
    }

    public void setQuantitys(List<QuantityVO> quantitys) {
        this.quantitys = quantitys;
    }

    public Boolean getIsQuantityReadonly() {
        return isQuantityReadonly;
    }

    public void setIsQuantityReadonly(Boolean quantityReadonly) {
        isQuantityReadonly = quantityReadonly;
    }

    public String getCreatedByUser() {
        return createdByUser;
    }

    public void setCreatedByUser(String createdByUser) {
        this.createdByUser = createdByUser;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public SpecLastOrderPriceVO getSpecLastOrderPrice() {
        return specLastOrderPrice;
    }

    public void setSpecLastOrderPrice(SpecLastOrderPriceVO specLastOrderPrice) {
        this.specLastOrderPrice = specLastOrderPrice;
    }

    public List<SpecOptionVO> getOptionSpecs() {
        return optionSpecs;
    }

    public void setOptionSpecs(List<SpecOptionVO> optionSpecs) {
        this.optionSpecs = optionSpecs;
    }

    public String getRateCardSupplier() {
        return rateCardSupplier;
    }

    public void setRateCardSupplier(String rateCardSupplier) {
        this.rateCardSupplier = rateCardSupplier;
    }

    @JsonIgnore
    public String getSupplierStatusSort() {
        if (StringUtils.hasLength(getInvoiceBuyStrId())) {
            return getInvoiceBuyStrId();
        } else if (StringUtils.hasLength(getSupplierStrId())) {
            return getSupplierStrId();
        }
        return null;
    }
}
