package com.noosh.app.commons.vo.workgroup.collaboration;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 4/25/20
 */
public class CategoryVO implements Serializable {

    private static final long serialVersionUID = -3983735352888799795L;

    private Long id;

    private String name;

    private String description;

    private Long userId;

    private Long workgroupId;

    private List<CategoryClassVO> categoryClasses;

    public List<CategoryClassVO> getCategoryClasses() {
        return categoryClasses;
    }

    public void setCategoryClasses(List<CategoryClassVO> categoryClasses) {
        this.categoryClasses = categoryClasses;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }
}
