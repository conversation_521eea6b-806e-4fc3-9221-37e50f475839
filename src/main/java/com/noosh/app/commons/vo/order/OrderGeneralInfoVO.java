package com.noosh.app.commons.vo.order;

import com.noosh.app.commons.vo.account.AccountUserVO;
import com.noosh.app.commons.vo.project.ProjectVO;
import com.noosh.app.commons.vo.workgroup.WorkgroupVO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 3/24/20
 */
public class OrderGeneralInfoVO implements Serializable {

    private static final long serialVersionUID = -3320362172532169056L;

    private Long orderId;

    private Long orderVersionId;

    private Long rfeId;

    private Long orderTypeId;

    private Long versionNumber;

    private String title;

    private LocalDateTime completionDate;

    private LocalDateTime creationDate;

    private String reference;

    private boolean isPaperOrder; //todo add collaborateOrders, gangedOrders

    private String supplierReference;

    private boolean isRejected;

    private boolean hasOfflineBuyer;

    private String comments;

    private String paymentMethod;

    private String paymentMethodStrId;

    private String paymentReference;

    private boolean isUserBuyer;

    private boolean isUserSupplier;

    private String reasonOther;

    private String reason;

    private String reasonStrId;

    private String reasonNameStr;

    private Long reasonId;

    private boolean canViewProjectBudget;

    private String budgetTypeField;

    private boolean invoicingEnable;

    private double oversPercent;

    private double undersPercent;

    private double taxPercent;

    private BigDecimal subTotal;
    private Long subTotalCurrencyId;
    private BigDecimal tax;
    private Long taxCurrencyId;
    private BigDecimal shipping;
    private Long shippingCurrencyId;
    private BigDecimal grandTotal;
    private Long grandTotalCurrencyId;
    private BigDecimal discountOrSurcharge;
    private Long discountOrSurchargeCurrencyId;
    private BigDecimal discountOrSurchargeTotal;
    private Long discountOrSurchargeTotalCurrencyId;
    private BigDecimal orderItemDiscountSurchargeTotal;
    private Long orderItemDiscountSurchargeTotalCurrencyId;

    //dual currency
    private boolean isDualCurrency;
    private boolean hideBaseCurrency;
    private BigDecimal rate;
    private Long exCurrencyId;
    private BigDecimal exTax;
    private Long exTaxCurrencyId;
    private BigDecimal exShipping;
    private Long exShippingCurrencyId;
    private BigDecimal exSubTotal;
    private Long exSubTotalCurrencyId;
    private BigDecimal exGrandTotal;
    private Long exGrandTotalCurrencyId;
    private BigDecimal exDiscountOrSurcharge;
    private Long exDiscountOrSurchargeCurrencyId;
    private BigDecimal exDiscountOrSurchargeTotal;
    private Long exDiscountOrSurchargeTotalCurrencyId;
    private BigDecimal exOrderItemDiscountSurchargeTotal;
    private Long exOrderItemDiscountSurchargeTotalCurrencyId;

    private boolean itemizedTaxAndShippingEnabled;

    private boolean pcAllowBreakouts;

    private boolean closeOrderNegotiation;
    private boolean hideOversAndUnders;

    private boolean canManageShipment;

    private boolean isAccepted;

    private boolean isDraft;

    private boolean isPending;

    private boolean isPendingSubmission;

    private boolean isQuickOrder;

    private boolean isReordered;

    private boolean isRetracted;

    private boolean noSelection;

    private boolean showPendingApproval; //means preference show pending approval

    private boolean isCloseChangeOrder;

    private boolean isChangeOrder;

    private boolean isPendingApproval; //means order is under routing

    private boolean isReplaced;

    private boolean isCompleted;

    private boolean isOutsourcingSellOrder;

    private boolean isBrokerSellOrder;

    private Boolean isSensitive;

    private boolean isShowSensitive;

    private boolean canAccept;

    private boolean canAcceptInPaperFlow;

    private boolean isCompletedAndHasAcceptedClosingChangeOrder;

    private boolean allAtOnce = false;

    private boolean managerAllAtOnce = false;

    private boolean autoTimeOut = false;

    private boolean autoMgrTimeOut = false;

    private boolean quickOrderApproval = false;

    private boolean isEnableComplexVAT;
    private String vatCode;
    private BigDecimal vatRate;
    private List vatsInfo;

    private boolean canSeePrice;
    private boolean enableSupplierAddPaperDetails;
    private boolean isSelectionReasonRequired;
    private boolean canShowBenchmark;

    private boolean enableLogistics;
    private LocalDateTime orderSendToSupplierDate;

    private String taxLabelString;

    private String valueCurrency;

    private Long paymentMethodId;

    private Long budgetTypeId;

    private Boolean isInvoiceAdjustmentOrder;

    private Long invoiceAdjustmentParentOrderId;

    private String invoiceAdjustmentParentOrderName;

    private String invoiceAdjustmentParentOrderLink;

    private List<Map<String, Object>> invoiceAdjustmentOrders;

    private WorkgroupVO buyerWorkgroup;

    private WorkgroupVO supplierWorkgroup;

    private AccountUserVO buyer;

    private AccountUserVO supplier;

    private OrderStateVO orderState;

    private ProjectVO parent;

    private ProjectVO buyerProject;

    private RoutingSlipVO routingSlip;

    private List<BillingRecipientVO> billingRecipients;

    private List<OrderItemVO> orderItems;

    private List<SlipRecipientVO> approverRecipients;

    private List<SlipRecipientVO> managerRecipients;

    @Schema(description = "PropertyId to get user fields")
    private Long customPropertyId;
    private Map customAttributes = new HashMap();

    private Boolean contractPricingEnabled;
    private Boolean isCompletionDateRequired;
    private Boolean isPaperFlow;

    public boolean getCanSeePrice() {
        return canSeePrice;
    }

    public void setCanSeePrice(boolean canSeePrice) {
        this.canSeePrice = canSeePrice;
    }

    public boolean getIsSelectionReasonRequired() {
        return this.isSelectionReasonRequired;
    }

    public void setIsSelectionReasonRequired(boolean selectionReasonRequired) {
        this.isSelectionReasonRequired = selectionReasonRequired;
    }

    public boolean getCanShowBenchmark() {
        return this.canShowBenchmark;
    }

    public void setCanShowBenchmark(boolean canShowBenchmark) {
        this.canShowBenchmark = canShowBenchmark;
    }

    public boolean getIsShowSensitive() {
        return isShowSensitive;
    }

    public void setIsShowSensitive(boolean showSensitive) {
        isShowSensitive = showSensitive;
    }

    public Boolean getIsSensitive() {
        return isSensitive;
    }

    public void setIsSensitive(Boolean sensitive) {
        isSensitive = sensitive;
    }

    public String getReasonNameStr() {
        return reasonNameStr;
    }

    public void setReasonNameStr(String reasonNameStr) {
        this.reasonNameStr = reasonNameStr;
    }

    public String getReasonStrId() {
        return reasonStrId;
    }

    public void setReasonStrId(String reasonStrId) {
        this.reasonStrId = reasonStrId;
    }

    public String getPaymentMethodStrId() {
        return paymentMethodStrId;
    }

    public void setPaymentMethodStrId(String paymentMethodStrId) {
        this.paymentMethodStrId = paymentMethodStrId;
    }

    public Long getReasonId() {
        return reasonId;
    }

    public void setReasonId(Long reasonId) {
        this.reasonId = reasonId;
    }

    public boolean getIsEnableComplexVAT() {
        return isEnableComplexVAT;
    }

    public void setIsEnableComplexVAT(boolean enableComplexVAT) {
        isEnableComplexVAT = enableComplexVAT;
    }

    public String getVatCode() {
        return vatCode;
    }

    public void setVatCode(String vatCode) {
        this.vatCode = vatCode;
    }

    public BigDecimal getVatRate() {
        return vatRate;
    }

    public void setVatRate(BigDecimal vatRate) {
        this.vatRate = vatRate;
    }

    public List getVatsInfo() {
        return vatsInfo;
    }

    public void setVatsInfo(List vatsInfo) {
        this.vatsInfo = vatsInfo;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getOrderVersionId() {
        return orderVersionId;
    }

    public void setOrderVersionId(Long orderVersionId) {
        this.orderVersionId = orderVersionId;
    }

    public Long getRfeId() {
        return rfeId;
    }

    public void setRfeId(Long rfeId) {
        this.rfeId = rfeId;
    }

    public Long getOrderTypeId() {
        return orderTypeId;
    }

    public void setOrderTypeId(Long orderTypeId) {
        this.orderTypeId = orderTypeId;
    }

    public Long getVersionNumber() {
        return this.versionNumber;
    }

    public void setVersionNumber(Long versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public LocalDateTime getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(LocalDateTime creationDate) {
        this.creationDate = creationDate;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public boolean getIsPaperOrder() {
        return isPaperOrder;
    }

    public void setIsPaperOrder(boolean paperOrder) {
        isPaperOrder = paperOrder;
    }

    public String getSupplierReference() {
        return supplierReference;
    }

    public void setSupplierReference(String supplierReference) {
        this.supplierReference = supplierReference;
    }

    public boolean getIsRejected() {
        return isRejected;
    }

    public void setIsRejected(boolean rejected) {
        isRejected = rejected;
    }

    public boolean isHasOfflineBuyer() {
        return hasOfflineBuyer;
    }

    public void setHasOfflineBuyer(boolean hasOfflineBuyer) {
        this.hasOfflineBuyer = hasOfflineBuyer;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    public boolean getIsUserBuyer() {
        return isUserBuyer;
    }

    public void setIsUserBuyer(boolean userBuyer) {
        isUserBuyer = userBuyer;
    }

    public boolean getIsUserSupplier() {
        return this.isUserSupplier;
    }

    public void setIsUserSupplier(boolean userSupplier) {
        this.isUserSupplier = userSupplier;
    }

    public String getReasonOther() {
        return reasonOther;
    }

    public void setReasonOther(String reasonOther) {
        this.reasonOther = reasonOther;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public boolean isCanViewProjectBudget() {
        return canViewProjectBudget;
    }

    public void setCanViewProjectBudget(boolean canViewProjectBudget) {
        this.canViewProjectBudget = canViewProjectBudget;
    }

    public String getBudgetTypeField() {
        return budgetTypeField;
    }

    public void setBudgetTypeField(String budgetTypeField) {
        this.budgetTypeField = budgetTypeField;
    }

    public boolean isInvoicingEnable() {
        return invoicingEnable;
    }

    public void setInvoicingEnable(boolean invoicingEnable) {
        this.invoicingEnable = invoicingEnable;
    }

    public double getOversPercent() {
        return oversPercent;
    }

    public void setOversPercent(double oversPercent) {
        this.oversPercent = oversPercent;
    }

    public double getUndersPercent() {
        return undersPercent;
    }

    public void setUndersPercent(double undersPercent) {
        this.undersPercent = undersPercent;
    }

    public BigDecimal getSubTotal() {
        return subTotal;
    }

    public void setSubTotal(BigDecimal subTotal) {
        this.subTotal = subTotal;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public double getTaxPercent() {
        return this.taxPercent;
    }

    public void setTaxPercent(double taxPercent) {
        this.taxPercent = taxPercent;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public BigDecimal getGrandTotal() {
        return grandTotal;
    }

    public void setGrandTotal(BigDecimal grandTotal) {
        this.grandTotal = grandTotal;
    }

    public BigDecimal getDiscountOrSurcharge() {
        return discountOrSurcharge;
    }

    public void setDiscountOrSurcharge(BigDecimal discountOrSurcharge) {
        this.discountOrSurcharge = discountOrSurcharge;
    }

    public BigDecimal getDiscountOrSurchargeTotal() {
        return discountOrSurchargeTotal;
    }

    public void setDiscountOrSurchargeTotal(BigDecimal discountOrSurchargeTotal) {
        this.discountOrSurchargeTotal = discountOrSurchargeTotal;
    }

    public BigDecimal getOrderItemDiscountSurchargeTotal() {
        return orderItemDiscountSurchargeTotal;
    }

    public void setOrderItemDiscountSurchargeTotal(BigDecimal orderItemDiscountSurchargeTotal) {
        this.orderItemDiscountSurchargeTotal = orderItemDiscountSurchargeTotal;
    }

    public boolean getIsDualCurrency() {
        return this.isDualCurrency;
    }

    public void setIsDualCurrency(boolean dualCurrency) {
        this.isDualCurrency = dualCurrency;
    }

    public boolean getHideBaseCurrency() {
        return this.hideBaseCurrency;
    }

    public void setHideBaseCurrency(boolean hideBaseCurrency) {
        this.hideBaseCurrency = hideBaseCurrency;
    }

    public BigDecimal getRate() {
        return this.rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public Long getExCurrencyId() {
        return this.exCurrencyId;
    }

    public void setExCurrencyId(Long exCurrencyId) {
        this.exCurrencyId = exCurrencyId;
    }

    public BigDecimal getExTax() {
        return this.exTax;
    }

    public void setExTax(BigDecimal exTax) {
        this.exTax = exTax;
    }

    public BigDecimal getExShipping() {
        return this.exShipping;
    }

    public void setExShipping(BigDecimal exShipping) {
        this.exShipping = exShipping;
    }

    public BigDecimal getExSubTotal() {
        return this.exSubTotal;
    }

    public void setExSubTotal(BigDecimal exSubTotal) {
        this.exSubTotal = exSubTotal;
    }

    public BigDecimal getExGrandTotal() {
        return this.exGrandTotal;
    }

    public void setExGrandTotal(BigDecimal exGrandTotal) {
        this.exGrandTotal = exGrandTotal;
    }

    public BigDecimal getExDiscountOrSurcharge() {
        return this.exDiscountOrSurcharge;
    }

    public void setExDiscountOrSurcharge(BigDecimal exDiscountOrSurcharge) {
        this.exDiscountOrSurcharge = exDiscountOrSurcharge;
    }

    public BigDecimal getExDiscountOrSurchargeTotal() {
        return this.exDiscountOrSurchargeTotal;
    }

    public void setExDiscountOrSurchargeTotal(BigDecimal exDiscountOrSurchargeTotal) {
        this.exDiscountOrSurchargeTotal = exDiscountOrSurchargeTotal;
    }

    public BigDecimal getExOrderItemDiscountSurchargeTotal() {
        return this.exOrderItemDiscountSurchargeTotal;
    }

    public void setExOrderItemDiscountSurchargeTotal(BigDecimal exOrderItemDiscountSurchargeTotal) {
        this.exOrderItemDiscountSurchargeTotal = exOrderItemDiscountSurchargeTotal;
    }

    public Long getSubTotalCurrencyId() {
        return this.subTotalCurrencyId;
    }

    public void setSubTotalCurrencyId(Long subTotalCurrencyId) {
        this.subTotalCurrencyId = subTotalCurrencyId;
    }

    public Long getTaxCurrencyId() {
        return this.taxCurrencyId;
    }

    public void setTaxCurrencyId(Long taxCurrencyId) {
        this.taxCurrencyId = taxCurrencyId;
    }

    public Long getShippingCurrencyId() {
        return this.shippingCurrencyId;
    }

    public void setShippingCurrencyId(Long shippingCurrencyId) {
        this.shippingCurrencyId = shippingCurrencyId;
    }

    public Long getGrandTotalCurrencyId() {
        return this.grandTotalCurrencyId;
    }

    public void setGrandTotalCurrencyId(Long grandTotalCurrencyId) {
        this.grandTotalCurrencyId = grandTotalCurrencyId;
    }

    public Long getDiscountOrSurchargeCurrencyId() {
        return this.discountOrSurchargeCurrencyId;
    }

    public void setDiscountOrSurchargeCurrencyId(Long discountOrSurchargeCurrencyId) {
        this.discountOrSurchargeCurrencyId = discountOrSurchargeCurrencyId;
    }

    public Long getDiscountOrSurchargeTotalCurrencyId() {
        return this.discountOrSurchargeTotalCurrencyId;
    }

    public void setDiscountOrSurchargeTotalCurrencyId(Long discountOrSurchargeTotalCurrencyId) {
        this.discountOrSurchargeTotalCurrencyId = discountOrSurchargeTotalCurrencyId;
    }

    public Long getOrderItemDiscountSurchargeTotalCurrencyId() {
        return this.orderItemDiscountSurchargeTotalCurrencyId;
    }

    public void setOrderItemDiscountSurchargeTotalCurrencyId(Long orderItemDiscountSurchargeTotalCurrencyId) {
        this.orderItemDiscountSurchargeTotalCurrencyId = orderItemDiscountSurchargeTotalCurrencyId;
    }

    public Long getExTaxCurrencyId() {
        return this.exTaxCurrencyId;
    }

    public void setExTaxCurrencyId(Long exTaxCurrencyId) {
        this.exTaxCurrencyId = exTaxCurrencyId;
    }

    public Long getExShippingCurrencyId() {
        return this.exShippingCurrencyId;
    }

    public void setExShippingCurrencyId(Long exShippingCurrencyId) {
        this.exShippingCurrencyId = exShippingCurrencyId;
    }

    public Long getExSubTotalCurrencyId() {
        return this.exSubTotalCurrencyId;
    }

    public void setExSubTotalCurrencyId(Long exSubTotalCurrencyId) {
        this.exSubTotalCurrencyId = exSubTotalCurrencyId;
    }

    public Long getExGrandTotalCurrencyId() {
        return this.exGrandTotalCurrencyId;
    }

    public void setExGrandTotalCurrencyId(Long exGrandTotalCurrencyId) {
        this.exGrandTotalCurrencyId = exGrandTotalCurrencyId;
    }

    public Long getExDiscountOrSurchargeCurrencyId() {
        return this.exDiscountOrSurchargeCurrencyId;
    }

    public void setExDiscountOrSurchargeCurrencyId(Long exDiscountOrSurchargeCurrencyId) {
        this.exDiscountOrSurchargeCurrencyId = exDiscountOrSurchargeCurrencyId;
    }

    public Long getExDiscountOrSurchargeTotalCurrencyId() {
        return this.exDiscountOrSurchargeTotalCurrencyId;
    }

    public void setExDiscountOrSurchargeTotalCurrencyId(Long exDiscountOrSurchargeTotalCurrencyId) {
        this.exDiscountOrSurchargeTotalCurrencyId = exDiscountOrSurchargeTotalCurrencyId;
    }

    public Long getExOrderItemDiscountSurchargeTotalCurrencyId() {
        return this.exOrderItemDiscountSurchargeTotalCurrencyId;
    }

    public void setExOrderItemDiscountSurchargeTotalCurrencyId(Long exOrderItemDiscountSurchargeTotalCurrencyId) {
        this.exOrderItemDiscountSurchargeTotalCurrencyId = exOrderItemDiscountSurchargeTotalCurrencyId;
    }

    public boolean isItemizedTaxAndShippingEnabled() {
        return itemizedTaxAndShippingEnabled;
    }

    public void setItemizedTaxAndShippingEnabled(boolean itemizedTaxAndShippingEnabled) {
        this.itemizedTaxAndShippingEnabled = itemizedTaxAndShippingEnabled;
    }

    public boolean isPcAllowBreakouts() {
        return pcAllowBreakouts;
    }

    public void setPcAllowBreakouts(boolean pcAllowBreakouts) {
        this.pcAllowBreakouts = pcAllowBreakouts;
    }

    public boolean isCloseOrderNegotiation() {
        return closeOrderNegotiation;
    }

    public void setCloseOrderNegotiation(boolean closeOrderNegotiation) {
        this.closeOrderNegotiation = closeOrderNegotiation;
    }

    public boolean getHideOversAndUnders() {
        return hideOversAndUnders;
    }

    public void setHideOversAndUnders(boolean hideOversAndUnders) {
        this.hideOversAndUnders = hideOversAndUnders;
    }

    public boolean isCanManageShipment() {
        return canManageShipment;
    }

    public void setCanManageShipment(boolean canManageShipment) {
        this.canManageShipment = canManageShipment;
    }

    public boolean getIsAccepted() {
        return isAccepted;
    }

    public void setIsAccepted(boolean accepted) {
        isAccepted = accepted;
    }

    public boolean getIsDraft() {
        return isDraft;
    }

    public void setIsDraft(boolean draft) {
        isDraft = draft;
    }

    public boolean getIsPending() {
        return isPending;
    }

    public void setIsPending(boolean pending) {
        isPending = pending;
    }

    public boolean getIsPendingSubmission() {
        return this.isPendingSubmission;
    }

    public void setIsPendingSubmission(boolean pendingSubmission) {
        this.isPendingSubmission = pendingSubmission;
    }

    public boolean getIsQuickOrder() {
        return isQuickOrder;
    }

    public void setIsQuickOrder(boolean quickOrder) {
        isQuickOrder = quickOrder;
    }

    public boolean getIsReordered() {
        return isReordered;
    }

    public void setIsReordered(boolean reordered) {
        isReordered = reordered;
    }

    public boolean getIsRetracted() {
        return isRetracted;
    }

    public void setIsRetracted(boolean retracted) {
        isRetracted = retracted;
    }

    public boolean getNoSelection() {
        return noSelection;
    }

    public void setNoSelection(boolean noSelection) {
        this.noSelection = noSelection;
    }

    public boolean getShowPendingApproval() {
        return showPendingApproval;
    }

    public void setShowPendingApproval(boolean showPendingApproval) {
        this.showPendingApproval = showPendingApproval;
    }

    public boolean getIsCloseChangeOrder() {
        return isCloseChangeOrder;
    }

    public void setIsCloseChangeOrder(boolean closeChangeOrder) {
        isCloseChangeOrder = closeChangeOrder;
    }

    public boolean getIsChangeOrder() {
        return isChangeOrder;
    }

    public void setIsChangeOrder(boolean changeOrder) {
        isChangeOrder = changeOrder;
    }

    public boolean getIsPendingApproval() {
        return isPendingApproval;
    }

    public void setIsPendingApproval(boolean pendingApproval) {
        isPendingApproval = pendingApproval;
    }

    public boolean getIsReplaced() {
        return isReplaced;
    }

    public void setIsReplaced(boolean replaced) {
        isReplaced = replaced;
    }

    public boolean getIsCompleted() {
        return isCompleted;
    }

    public void setIsCompleted(boolean completed) {
        isCompleted = completed;
    }

    public boolean getIsOutsourcingSellOrder() {
        return isOutsourcingSellOrder;
    }

    public void setIsOutsourcingSellOrder(boolean outsourcingSellOrder) {
        isOutsourcingSellOrder = outsourcingSellOrder;
    }

    public boolean getIsBrokerSellOrder() {
        return isBrokerSellOrder;
    }

    public void setIsBrokerSellOrder(boolean brokerSellOrder) {
        isBrokerSellOrder = brokerSellOrder;
    }

    public boolean getCanAccept() {
        return canAccept;
    }

    public void setCanAccept(boolean canAccept) {
        this.canAccept = canAccept;
    }

    public boolean getCanAcceptInPaperFlow() {
        return canAcceptInPaperFlow;
    }

    public void setCanAcceptInPaperFlow(boolean canAcceptInPaperFlow) {
        this.canAcceptInPaperFlow = canAcceptInPaperFlow;
    }

    public boolean getCompletedAndHasAcceptedClosingChangeOrder() {
        return isCompletedAndHasAcceptedClosingChangeOrder;
    }

    public void setCompletedAndHasAcceptedClosingChangeOrder(boolean completedAndHasAcceptedClosingChangeOrder) {
        isCompletedAndHasAcceptedClosingChangeOrder = completedAndHasAcceptedClosingChangeOrder;
    }

    public boolean getAllAtOnce() {
        return allAtOnce;
    }

    public void setAllAtOnce(boolean allAtOnce) {
        this.allAtOnce = allAtOnce;
    }

    public boolean getManagerAllAtOnce() {
        return managerAllAtOnce;
    }

    public void setManagerAllAtOnce(boolean managerAllAtOnce) {
        this.managerAllAtOnce = managerAllAtOnce;
    }

    public boolean getAutoTimeOut() {
        return autoTimeOut;
    }

    public void setAutoTimeOut(boolean autoTimeOut) {
        this.autoTimeOut = autoTimeOut;
    }

    public boolean getAutoMgrTimeOut() {
        return autoMgrTimeOut;
    }

    public void setAutoMgrTimeOut(boolean autoMgrTimeOut) {
        this.autoMgrTimeOut = autoMgrTimeOut;
    }

    public boolean getQuickOrderApproval() {
        return quickOrderApproval;
    }

    public void setQuickOrderApproval(boolean quickOrderApproval) {
        this.quickOrderApproval = quickOrderApproval;
    }

    public LocalDateTime getOrderSendToSupplierDate() {
        return orderSendToSupplierDate;
    }

    public void setOrderSendToSupplierDate(LocalDateTime orderSendToSupplierDate) {
        this.orderSendToSupplierDate = orderSendToSupplierDate;
    }

    public String getTaxLabelString() {
        return taxLabelString;
    }

    public void setTaxLabelString(String taxLabelString) {
        this.taxLabelString = taxLabelString;
    }

    public String getValueCurrency() {
        return valueCurrency;
    }

    public void setValueCurrency(String valueCurrency) {
        this.valueCurrency = valueCurrency;
    }

    public Long getPaymentMethodId() {
        return paymentMethodId;
    }

    public void setPaymentMethodId(Long paymentMethodId) {
        this.paymentMethodId = paymentMethodId;
    }

    public Long getBudgetTypeId() {
        return budgetTypeId;
    }

    public void setBudgetTypeId(Long budgetTypeId) {
        this.budgetTypeId = budgetTypeId;
    }

    public Boolean getInvoiceAdjustmentOrder() {
        return isInvoiceAdjustmentOrder;
    }

    public void setInvoiceAdjustmentOrder(Boolean invoiceAdjustmentOrder) {
        isInvoiceAdjustmentOrder = invoiceAdjustmentOrder;
    }

    public Long getInvoiceAdjustmentParentOrderId() {
        return invoiceAdjustmentParentOrderId;
    }

    public void setInvoiceAdjustmentParentOrderId(Long invoiceAdjustmentParentOrderId) {
        this.invoiceAdjustmentParentOrderId = invoiceAdjustmentParentOrderId;
    }

    public String getInvoiceAdjustmentParentOrderName() {
        return invoiceAdjustmentParentOrderName;
    }

    public void setInvoiceAdjustmentParentOrderName(String invoiceAdjustmentParentOrderName) {
        this.invoiceAdjustmentParentOrderName = invoiceAdjustmentParentOrderName;
    }

    public String getInvoiceAdjustmentParentOrderLink() {
        return invoiceAdjustmentParentOrderLink;
    }

    public void setInvoiceAdjustmentParentOrderLink(String invoiceAdjustmentParentOrderLink) {
        this.invoiceAdjustmentParentOrderLink = invoiceAdjustmentParentOrderLink;
    }

    public List<Map<String, Object>> getInvoiceAdjustmentOrders() {
        return invoiceAdjustmentOrders;
    }

    public void setInvoiceAdjustmentOrders(List<Map<String, Object>> invoiceAdjustmentOrders) {
        this.invoiceAdjustmentOrders = invoiceAdjustmentOrders;
    }

    public WorkgroupVO getBuyerWorkgroup() {
        return buyerWorkgroup;
    }

    public void setBuyerWorkgroup(WorkgroupVO buyerWorkgroup) {
        this.buyerWorkgroup = buyerWorkgroup;
    }

    public WorkgroupVO getSupplierWorkgroup() {
        return supplierWorkgroup;
    }

    public void setSupplierWorkgroup(WorkgroupVO supplierWorkgroup) {
        this.supplierWorkgroup = supplierWorkgroup;
    }

    public AccountUserVO getBuyer() {
        return buyer;
    }

    public void setBuyer(AccountUserVO buyer) {
        this.buyer = buyer;
    }

    public AccountUserVO getSupplier() {
        return supplier;
    }

    public void setSupplier(AccountUserVO supplier) {
        this.supplier = supplier;
    }

    public OrderStateVO getOrderState() {
        return orderState;
    }

    public void setOrderState(OrderStateVO orderState) {
        this.orderState = orderState;
    }

    public ProjectVO getParent() {
        return parent;
    }

    public void setParent(ProjectVO parent) {
        this.parent = parent;
    }

    public ProjectVO getBuyerProject() {
        return buyerProject;
    }

    public void setBuyerProject(ProjectVO buyerProject) {
        this.buyerProject = buyerProject;
    }

    public RoutingSlipVO getRoutingSlip() {
        return routingSlip;
    }

    public void setRoutingSlip(RoutingSlipVO routingSlip) {
        this.routingSlip = routingSlip;
    }

    public List<BillingRecipientVO> getBillingRecipients() {
        return billingRecipients;
    }

    public void setBillingRecipients(List<BillingRecipientVO> billingRecipients) {
        this.billingRecipients = billingRecipients;
    }

    public List<OrderItemVO> getOrderItems() {
        return orderItems;
    }

    public void setOrderItems(List<OrderItemVO> orderItems) {
        this.orderItems = orderItems;
    }

    public List<SlipRecipientVO> getApproverRecipients() {
        return approverRecipients;
    }

    public void setApproverRecipients(List<SlipRecipientVO> approverRecipients) {
        this.approverRecipients = approverRecipients;
    }

    public List<SlipRecipientVO> getManagerRecipients() {
        return managerRecipients;
    }

    public void setManagerRecipients(List<SlipRecipientVO> managerRecipients) {
        this.managerRecipients = managerRecipients;
    }


    public Long getCustomPropertyId() {
        return this.customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Map getCustomAttributes() {
        return this.customAttributes;
    }

    public void setCustomAttributes(Map customAttributes) {
        this.customAttributes = customAttributes;
    }
    public boolean getEnableSupplierAddPaperDetails() {
        return enableSupplierAddPaperDetails;
    }

    public void setEnableSupplierAddPaperDetails(boolean enableSupplierAddPaperDetails) {
        this.enableSupplierAddPaperDetails = enableSupplierAddPaperDetails;
    }

    public Boolean getContractPricingEnabled() {
        return this.contractPricingEnabled;
    }

    public void setContractPricingEnabled(Boolean contractPricingEnabled) {
        this.contractPricingEnabled = contractPricingEnabled;
    }

    public Boolean getIsCompletionDateRequired() {
        return this.isCompletionDateRequired;
    }

    public void setIsCompletionDateRequired(Boolean completionDateRequired) {
        this.isCompletionDateRequired = completionDateRequired;
    }

    public boolean getEnableLogistics() {
        return enableLogistics;
    }

    public void setEnableLogistics(boolean enableLogistics) {
        this.enableLogistics = enableLogistics;
    }

    public Boolean getIsPaperFlow() {
        return isPaperFlow;
    }

    public void setIsPaperFlow(Boolean paperFlow) {
        isPaperFlow = paperFlow;
    }
}
