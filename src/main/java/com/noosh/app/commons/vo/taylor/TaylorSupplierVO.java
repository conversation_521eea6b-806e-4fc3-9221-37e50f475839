package com.noosh.app.commons.vo.taylor;

import com.noosh.app.commons.vo.supplier.SupplierFlagVO;

/**
 * author: Yang
 * Date: 3/6/2025
 */
public class TaylorSupplierVO {
    private Long userId;
    private String fullName;
    private String firstName;
    private String lastName;
    private String workgroup;
    private SupplierFlagVO supplierFlag;
    private String specTypeName;
    private Long specNodeId;
    private Long min;
    private Long max;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getWorkgroup() {
        return workgroup;
    }

    public void setWorkgroup(String workgroup) {
        this.workgroup = workgroup;
    }

    public SupplierFlagVO getSupplierFlag() {
        return supplierFlag;
    }

    public void setSupplierFlag(SupplierFlagVO supplierFlag) {
        this.supplierFlag = supplierFlag;
    }

    public String getSpecTypeName() {
        return specTypeName;
    }

    public void setSpecTypeName(String specTypeName) {
        this.specTypeName = specTypeName;
    }

    public Long getSpecNodeId() {
        return specNodeId;
    }

    public void setSpecNodeId(Long specNodeId) {
        this.specNodeId = specNodeId;
    }

    public Long getMin() {
        return min;
    }

    public void setMin(Long min) {
        this.min = min;
    }

    public Long getMax() {
        return max;
    }

    public void setMax(Long max) {
        this.max = max;
    }
}
