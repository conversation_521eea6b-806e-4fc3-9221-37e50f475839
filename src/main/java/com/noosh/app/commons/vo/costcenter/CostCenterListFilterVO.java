package com.noosh.app.commons.vo.costcenter;

public class CostCenterListFilterVO {
    public final static String COSTCENTER_LIST_FILTER_PREFIX = "COSTCENTER_LIST_FILTER_";
    public final static String PAGE_SIZE = "pageSize";
    public final static String SORT = "sort";
    public final static String ORDER = "order";
    public final static Integer PAGE_SIZE_DEFAULT_VALUE = 10;

    private Integer pageSize;
    private String sort;
    private String order;

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getSort() {
        return sort;
    }

    public void setSortBy(String sort) {
        this.sort = sort;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }
}
