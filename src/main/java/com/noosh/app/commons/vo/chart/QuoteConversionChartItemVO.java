package com.noosh.app.commons.vo.chart;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 * @date 8/12/2021
 */
public class QuoteConversionChartItemVO {

    @Schema(description = "Client Name")
    private String clientName;
    @Schema(description = "total of quote submit")
    private Double quoteSubmittedCount;
    @Schema(description = "total of quote submit and sell order accept")
    private Double sellOrderAcceptedCount;


    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public Double getQuoteSubmittedCount() {
        return quoteSubmittedCount;
    }

    public void setQuoteSubmittedCount(Double quoteSubmittedCount) {
        this.quoteSubmittedCount = quoteSubmittedCount;
    }

    public Double getSellOrderAcceptedCount() {
        return sellOrderAcceptedCount;
    }

    public void setSellOrderAcceptedCount(Double sellOrderAcceptedCount) {
        this.sellOrderAcceptedCount = sellOrderAcceptedCount;
    }
}