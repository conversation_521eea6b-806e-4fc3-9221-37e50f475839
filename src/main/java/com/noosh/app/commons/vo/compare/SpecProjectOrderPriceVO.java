package com.noosh.app.commons.vo.compare;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: <PERSON>henyu Hu
 * @Date: 2/8/2022
 */
public class SpecProjectOrderPriceVO implements Serializable {
    private static final long serialVersionUID = 1964770033847032274L;

    private Long specId;
    private Long nodeId;
    private String specName;
    private String specLink;
    private Long projectId;
    private String projectName;
    private String projectLink;
    private Long orderId;
    private String orderName;
    private LocalDateTime orderDate;
    private String orderLink;

    private BigDecimal price;
    private Long priceCurrencyId;
    private BigDecimal quantity;
    private String workgroupName;

    private List<SpecRfeEstimateVO> specRfeEstimateVOList;

    private List<SpecEstimateItemPriceVO> specEstimateItemPriceVOList;

    public SpecProjectOrderPriceVO() {
    }

    public SpecProjectOrderPriceVO(Long specId, Long nodeId, String specName, Long projectId, String projectName, Long orderId, String orderName, LocalDateTime orderDate, String workgroupName) {
        this.specId = specId;
        this.nodeId = nodeId;
        this.specName = specName;
        this.projectId = projectId;
        this.projectName = projectName;
        this.orderId = orderId;
        this.orderName = orderName;
        this.orderDate = orderDate;
        this.workgroupName = workgroupName;
    }

    public Long getSpecId() {
        return specId;
    }

    public void setSpecId(Long specId) {
        this.specId = specId;
    }

    public Long getNodeId() {
        return nodeId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public String getSpecName() {
        return specName;
    }

    public void setSpecName(String specName) {
        this.specName = specName;
    }

    public String getSpecLink() {
        return specLink;
    }

    public void setSpecLink(String specLink) {
        this.specLink = specLink;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectLink() {
        return projectLink;
    }

    public void setProjectLink(String projectLink) {
        this.projectLink = projectLink;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderName() {
        return orderName;
    }

    public void setOrderName(String orderName) {
        this.orderName = orderName;
    }

    public LocalDateTime getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(LocalDateTime orderDate) {
        this.orderDate = orderDate;
    }

    public String getOrderLink() {
        return orderLink;
    }

    public void setOrderLink(String orderLink) {
        this.orderLink = orderLink;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getPriceCurrencyId() {
        return priceCurrencyId;
    }

    public void setPriceCurrencyId(Long priceCurrencyId) {
        this.priceCurrencyId = priceCurrencyId;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getWorkgroupName() {
        return workgroupName;
    }

    public void setWorkgroupName(String workgroupName) {
        this.workgroupName = workgroupName;
    }

    public List<SpecRfeEstimateVO> getSpecRfeEstimateVOList() {
        return specRfeEstimateVOList;
    }

    public void setSpecRfeEstimateVOList(List<SpecRfeEstimateVO> specRfeEstimateVOList) {
        this.specRfeEstimateVOList = specRfeEstimateVOList;
    }

    public List<SpecEstimateItemPriceVO> getSpecEstimateItemPriceVOList() {
        return specEstimateItemPriceVOList;
    }

    public void setSpecEstimateItemPriceVOList(List<SpecEstimateItemPriceVO> specEstimateItemPriceVOList) {
        this.specEstimateItemPriceVOList = specEstimateItemPriceVOList;
    }
}
