package com.noosh.app.commons.vo.fedex;

import com.noosh.app.commons.dto.security.CountryDTO;

import java.util.List;
import java.util.Map;

public class FedexInitVO {
    private Map<String, Object> centerData;
    private List<CountryDTO> countryList;
    private List<CenterNumberVO> centerNumberList;

    public Map<String, Object> getCenterData() {
        return centerData;
    }

    public void setCenterData(Map<String, Object> centerData) {
        this.centerData = centerData;
    }

    public List<CountryDTO> getCountryList() {
        return countryList;
    }

    public void setCountryList(List<CountryDTO> countryList) {
        this.countryList = countryList;
    }

    public List<CenterNumberVO> getCenterNumberList() {
        return centerNumberList;
    }

    public void setCenterNumberList(List<CenterNumberVO> centerNumberList) {
        this.centerNumberList = centerNumberList;
    }
}
