package com.noosh.app.commons.vo.order;

import com.noosh.app.commons.dto.order.ProjectOrderWidgetDTO;
import com.noosh.app.commons.vo.project.ProjectVO;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * <AUTHOR>
 * @date 9/8/2021
 */
@Schema(description = "Order")
public class OrderDeskoidVO {
    @Schema(description = "can View Order")
    private Boolean canViewOrder;
    @Schema(description = "whether to display dual currency")
    private Boolean isDualCurrency;
    @Schema(description = "for client/supplier only, whether to hide base currency, only to display transactional currency")
    private Boolean hideBaseCurrency;
    @Schema(description = "parent project")
    private ProjectVO parent;
    @Schema(description = "order list")
    private List<ProjectOrderWidgetDTO> list;
    @Schema(description = "Creat quick order link, redirect to Enterprise")
    private String createQuickOrderExternalLink;
    private Long total = 0L;

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }
    
    public Boolean getCanViewOrder() {
        return canViewOrder;
    }

    public void setCanViewOrder(Boolean canViewOrder) {
        this.canViewOrder = canViewOrder;
    }

    public Boolean getIsDualCurrency() {
        return this.isDualCurrency;
    }

    public void setIsDualCurrency(Boolean dualCurrency) {
        this.isDualCurrency = dualCurrency;
    }

    public Boolean getHideBaseCurrency() {
        return this.hideBaseCurrency;
    }

    public void setHideBaseCurrency(Boolean hideBaseCurrency) {
        this.hideBaseCurrency = hideBaseCurrency;
    }

    public ProjectVO getParent() {
        return this.parent;
    }

    public void setParent(ProjectVO parent) {
        this.parent = parent;
    }

    public List<ProjectOrderWidgetDTO> getList() {
        return list;
    }

    public void setList(List<ProjectOrderWidgetDTO> list) {
        this.list = list;
    }

    public String getCreateQuickOrderExternalLink() {
        return createQuickOrderExternalLink;
    }

    public void setCreateQuickOrderExternalLink(String createQuickOrderExternalLink) {
        this.createQuickOrderExternalLink = createQuickOrderExternalLink;
    }
}
