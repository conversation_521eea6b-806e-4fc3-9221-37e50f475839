package com.noosh.app.commons.dto.spec;

import com.noosh.app.commons.dto.property.PropertyAttributeDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import java.io.Serializable;
import java.util.List;

/**
 * User: leilaz
 * Date: 5/30/16
 */
public class SpecDTO implements Serializable {

    private static final long serialVersionUID = 4375566533882483889L;

    private Long id;
    private String name;
    private Long originalSpecId;
    private Boolean isLocked;
    private Boolean isImmutable;
    private Boolean isItemVersion;
    private Long nodeId;
    private Long specUserStateId;
    private String specUserStateStrId;
    private Long lockCount;
    private String specUserState;
    private String specLink;
    private String createOptionLink;
    private String editSpecLink;
    private String deleteSpecLink;
    private String creator;
    private String creatorAliasName;
    private LocalDateTime createDate;
    private LocalDateTime modDate;
    private Long createUserId;
    private String productNumber;
    private String versionNumber;
    private Long specTypeId;
    private Long propertyId;
    private Short includeAup;
    private String fullName;
    private BigDecimal quantity1;
    private BigDecimal quantity2;
    private BigDecimal quantity3;
    private BigDecimal quantity4;
    private BigDecimal quantity5;
    private Long productTypeId;
    private SpecTypeDTO specTypeDTO;

    private List<PropertyAttributeDTO> customFields;

    public Long getProductTypeId() {
        return productTypeId;
    }

    public void setProductTypeId(Long productTypeId) {
        this.productTypeId = productTypeId;
    }

    public Long getPropertyId() {
		return propertyId;
	}

	public void setPropertyId(Long propertyId) {
		this.propertyId = propertyId;
	}

	public List<PropertyAttributeDTO> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(List<PropertyAttributeDTO> customFields) {
        this.customFields = customFields;
    }

    public String getSpecUserStateStrId() {
        return specUserStateStrId;
    }

    public void setSpecUserStateStrId(String specUserStateStrId) {
        this.specUserStateStrId = specUserStateStrId;
    }

    public Long getOriginalSpecId() {
        return originalSpecId;
    }

    public void setOriginalSpecId(Long originalSpecId) {
        this.originalSpecId = originalSpecId;
    }

    public SpecTypeDTO getSpecTypeDTO() {
        return specTypeDTO;
    }

    public void setSpecTypeDTO(SpecTypeDTO specTypeDTO) {
        this.specTypeDTO = specTypeDTO;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public Short getIncludeAup() {
        return includeAup;
    }

    public void setIncludeAup(Short includeAup) {
        this.includeAup = includeAup;
    }

    public Long getSpecTypeId() {
        return specTypeId;
    }

    public void setSpecTypeId(Long specTypeId) {
        this.specTypeId = specTypeId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getProductNumber() {
        return productNumber;
    }

    public void setProductNumber(String productNumber) {
        this.productNumber = productNumber;
    }

    public Long getLockCount() {
        return lockCount;
    }

    public void setLockCount(Long lockCount) {
        this.lockCount = lockCount;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreatorAliasName() {
        return creatorAliasName;
    }

    public void setCreatorAliasName(String creatorAliasName) {
        this.creatorAliasName = creatorAliasName;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getModDate() {
        return modDate;
    }

    public void setModDate(LocalDateTime modDate) {
        this.modDate = modDate;
    }

    public Long getSpecUserStateId() {
        return specUserStateId;
    }

    public void setSpecUserStateId(Long specUserStateId) {
        this.specUserStateId = specUserStateId;
    }

    public String getSpecUserState() {
        return specUserState;
    }

    public void setSpecUserState(String specUserState) {
        this.specUserState = specUserState;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getIsLocked() {
        return isLocked != null ? isLocked.booleanValue() : false;
    }

    public void setIsLocked(Boolean locked) {
        isLocked = locked;
    }

    public Boolean getIsImmutable() {
        return isImmutable;
    }

    public void setIsImmutable(Boolean immutable) {
        isImmutable = immutable;
    }

    public Boolean getIsItemVersion() {
        return isItemVersion != null ? isItemVersion.booleanValue() : false;
    }

    public void setIsItemVersion(Boolean itemVersion) {
        isItemVersion = itemVersion;
    }

    public Long getNodeId() {
        return nodeId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public String getSpecLink() {
        return specLink;
    }

    public void setSpecLink(String specLink) {
        this.specLink = specLink;
    }

    public String getCreateOptionLink() {
        return createOptionLink;
    }

    public void setCreateOptionLink(String createOptionLink) {
        this.createOptionLink = createOptionLink;
    }

    public String getEditSpecLink() {
        return editSpecLink;
    }

    public void setEditSpecLink(String editSpecLink) {
        this.editSpecLink = editSpecLink;
    }

    public String getDeleteSpecLink() {
        return deleteSpecLink;
    }

    public void setDeleteSpecLink(String deleteSpecLink) {
        this.deleteSpecLink = deleteSpecLink;
    }

    public BigDecimal getQuantity1() {
        return quantity1;
    }

    public void setQuantity1(BigDecimal quantity1) {
        this.quantity1 = quantity1;
    }

    public BigDecimal getQuantity2() {
        return quantity2;
    }

    public void setQuantity2(BigDecimal quantity2) {
        this.quantity2 = quantity2;
    }

    public BigDecimal getQuantity3() {
        return quantity3;
    }

    public void setQuantity3(BigDecimal quantity3) {
        this.quantity3 = quantity3;
    }

    public BigDecimal getQuantity4() {
        return quantity4;
    }

    public void setQuantity4(BigDecimal quantity4) {
        this.quantity4 = quantity4;
    }

    public BigDecimal getQuantity5() {
        return quantity5;
    }

    public void setQuantity5(BigDecimal quantity5) {
        this.quantity5 = quantity5;
    }
}
