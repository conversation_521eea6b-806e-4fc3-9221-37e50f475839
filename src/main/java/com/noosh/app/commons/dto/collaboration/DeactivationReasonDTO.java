package com.noosh.app.commons.dto.collaboration;

/**
 * DTO for Project Reason - corresponds to DeactivationReasonBean
 * Represents the core deactivation reason information
 */
public class DeactivationReasonDTO {
    
    private Long id;
    private String constantToken;
    private Long descriptionStrId;
    private String nameStr;
    private Boolean isSystem;
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getConstantToken() {
        return constantToken;
    }
    
    public void setConstantToken(String constantToken) {
        this.constantToken = constantToken;
    }
    
    public Long getDescriptionStrId() {
        return descriptionStrId;
    }
    
    public void setDescriptionStrId(Long descriptionStrId) {
        this.descriptionStrId = descriptionStrId;
    }
    
    public String getNameStr() {
        return nameStr;
    }
    
    public void setNameStr(String nameStr) {
        this.nameStr = nameStr;
    }

    public Boolean getIsSystem() {
        return isSystem;
    }
    
    public void setIsSystem(Boolean isSystem) {
        this.isSystem = isSystem;
    }
}