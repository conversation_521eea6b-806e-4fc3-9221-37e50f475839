package com.noosh.app.commons.dto.project;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.ObjectStateID;
import com.noosh.app.commons.entity.project.Project;
import java.time.LocalDateTime;

import java.util.List;
import java.util.Map;

public class ProjectDTO {

    private Long id;
    private String reference;
    private String projectNumber;
    private String name;
    private String clientAccount;
    private Long objectStateId;
    private Long ownerWorkgroupId;
    private Long masterProjectId;
    private Long originalProjectId;
    private Long projectStatusId;
    private LocalDateTime completionDate;
    private Long customPropertyId;
    private Boolean isLinked;
    private Long objectClassId;
    private Long clientWorkgroupId;
    private Long portalWorkgroupId;
    private Long parentProjectId;
    private Long buClientWorkgroupId;
    private Boolean isHot;
    private Long psfId;
    private String clientOwner;
    private LocalDateTime activityDate;
    private Long sourceTypeId;
    private Boolean isNGEFileUI;
    private Boolean isDraft;
    private LocalDateTime createDate;
    private LocalDateTime modDate;
    private Long createUserId;
    private Long modUserId;
    //attribute from project_status
    private String status;
    private Long statusStrId;
    private String externalLink;
    private List<String> projectOwners;
    private boolean canUpdateStatus;
    private boolean canUpdateCompletionDate;
    private boolean isActivate;
    private boolean isHotProject;
    private boolean isPaperFlow;
    private boolean isPaperDirect;
    Map<String, Object> customAttributes;

    public ProjectDTO() {}

    public ProjectDTO(Project project) {
        this.id = project.getId();
        this.reference = project.getReference();
        this.projectNumber = project.getProjectNumber();
        this.name = project.getName();
        this.clientAccount = project.getClientAccount();
        this.objectStateId = project.getObjectStateId();
        this.ownerWorkgroupId = project.getOwnerWorkgroupId();
        this.masterProjectId = project.getMasterProjectId();
        this.originalProjectId = project.getOriginalProjectId();
        this.projectStatusId = project.getProjectStatusId();
        this.completionDate = project.getCompletionDate();
        this.customPropertyId = project.getCustomPropertyId();
        this.isLinked = project.getIsLinked();
        this.objectClassId = project.getObjectClassId();
        this.clientWorkgroupId = project.getClientWorkgroupId();
        this.portalWorkgroupId = project.getPortalWorkgroupId();
        this.parentProjectId = project.getParentProjectId();
        this.buClientWorkgroupId = project.getBuClientWorkgroupId();
        this.isHot = project.getIsHot();
        this.psfId = project.getPsfId();
        this.activityDate = project.getActivityDate();
        this.isDraft = project.getIsDraft();
        this.createDate = project.getCreateDate();
        this.modDate = project.getModDate();
        this.createUserId = project.getCreateUserId();
        this.modUserId = project.getModUserId();
        this.sourceTypeId = project.getSourceTypeId();
        this.isNGEFileUI = project.getIsNGEFileUI();
        this.isActivate = project.getObjectStateId() == null || project.getObjectStateId() != 2000066;
        this.isPaperFlow = project.getIsPaperFlow();
        this.isPaperDirect = project.getIsPaperDirect();
    }


    public Map<String, Object> getCustomAttributes() {
        return customAttributes;
    }

    public void setCustomAttributes(Map<String, Object> customAttributes) {
        this.customAttributes = customAttributes;
    }

    public String getClientOwner() {
        return clientOwner;
    }

    public void setClientOwner(String clientOwner) {
        this.clientOwner = clientOwner;
    }

    public boolean getIsPaperDirect() {
        return isPaperDirect;
    }

    public void setIsPaperDirect(boolean paperDirect) {
        isPaperDirect = paperDirect;
    }

    public boolean getIsPaperFlow() {
        return isPaperFlow;
    }

    public void setIsPaperFlow(boolean paperFlow) {
        isPaperFlow = paperFlow;
    }

    public boolean getIsActivate() {
        return isActivate;
    }

    public void setIsActivate(boolean activate) {
        isActivate = activate;
    }

    public boolean getIsHotProject() {
        return isHotProject;
    }

    public void setIsHotProject(boolean hotProject) {
        isHotProject = hotProject;
    }

    public boolean isCanUpdateCompletionDate() {
        return canUpdateCompletionDate;
    }

    public void setCanUpdateCompletionDate(boolean canUpdateCompletionDate) {
        this.canUpdateCompletionDate = canUpdateCompletionDate;
    }

    public boolean isCanUpdateStatus() {
        return canUpdateStatus;
    }

    public void setCanUpdateStatus(boolean canUpdateStatus) {
        this.canUpdateStatus = canUpdateStatus;
    }

    public Long getStatusStrId() {
        return statusStrId;
    }

    public void setStatusStrId(Long statusStrId) {
        this.statusStrId = statusStrId;
    }

    public List<String> getProjectOwners() {
        return projectOwners;
    }

    public void setProjectOwners(List<String> projectOwners) {
        this.projectOwners = projectOwners;
    }

    public Long getPsfId() {
        return psfId;
    }

    public void setPsfId(Long psfId) {
        this.psfId = psfId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getProjectNumber() {
        return projectNumber;
    }

    public void setProjectNumber(String projectNumber) {
        this.projectNumber = projectNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getClientAccount() {
        return clientAccount;
    }

    public void setClientAccount(String clientAccount) {
        this.clientAccount = clientAccount;
    }

    public Long getObjectStateId() {
        return objectStateId;
    }

    public void setObjectStateId(Long objectStateId) {
        this.objectStateId = objectStateId;
    }

    public Long getOwnerWorkgroupId() {
        return ownerWorkgroupId;
    }

    public void setOwnerWorkgroupId(Long ownerWorkgroupId) {
        this.ownerWorkgroupId = ownerWorkgroupId;
    }

    public Long getMasterProjectId() {
        return masterProjectId;
    }

    public void setMasterProjectId(Long masterProjectId) {
        this.masterProjectId = masterProjectId;
    }

    public Long getOriginalProjectId() {
        return originalProjectId;
    }

    public void setOriginalProjectId(Long originalProjectId) {
        this.originalProjectId = originalProjectId;
    }

    public Long getProjectStatusId() {
        return projectStatusId;
    }

    public void setProjectStatusId(Long projectStatusId) {
        this.projectStatusId = projectStatusId;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public Long getCustomPropertyId() {
        return customPropertyId;
    }

    public void setCustomPropertyId(Long customPropertyId) {
        this.customPropertyId = customPropertyId;
    }

    public Boolean getIsLinked() {
        return isLinked;
    }

    public void setIsLinked(Boolean isLinked) {
        this.isLinked = isLinked;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getClientWorkgroupId() {
        return clientWorkgroupId;
    }

    public void setClientWorkgroupId(Long clientWorkgroupId) {
        this.clientWorkgroupId = clientWorkgroupId;
    }

    public Long getPortalWorkgroupId() {
        return portalWorkgroupId;
    }

    public void setPortalWorkgroupId(Long portalWorkgroupId) {
        this.portalWorkgroupId = portalWorkgroupId;
    }

    public Long getParentProjectId() {
        return parentProjectId;
    }

    public void setParentProjectId(Long parentProjectId) {
        this.parentProjectId = parentProjectId;
    }

    public Long getBuClientWorkgroupId() {
        return buClientWorkgroupId;
    }

    public void setBuClientWorkgroupId(Long buClientWorkgroupId) {
        this.buClientWorkgroupId = buClientWorkgroupId;
    }

    public Boolean getIsHot() {
        return isHot;
    }

    public void setIsHot(Boolean isHot) {
        this.isHot = isHot;
    }

    public LocalDateTime getActivityDate() {
        return activityDate;
    }

    public void setActivityDate(LocalDateTime activityDate) {
        this.activityDate = activityDate;
    }

    public Boolean getIsDraft() {
        return isDraft;
    }

    public void setIsDraft(Boolean isDraft) {
        this.isDraft = isDraft;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getModDate() {
        return modDate;
    }

    public void setModDate(LocalDateTime modDate) {
        this.modDate = modDate;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Long getModUserId() {
        return modUserId;
    }

    public void setModUserId(Long modUserId) {
        this.modUserId = modUserId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getExternalLink() {
//        HashMap<String, String> params = new HashMap<>();
//        params.put("objectClassId", "" + ObjectClassID.OBJECT_CLASS_PROJECT);
//        params.put("objectId", "" + this.id);
//        this.externalLink = NooshOneUrlUtil.composeLinkToEnterprise(EnterpriseLinkDestination.VIEW_PROJECT_HOME, params);
        return externalLink;
    }

    public void setExternalLink(String externalLink) {
        this.externalLink = externalLink;
    }

    
    public long getProjectClassId() {
        return this.getObjectClassId();
    }

    
    public boolean isSupplierProject() {
        return (getProjectClassId() == ObjectClassID.PROJECT_CLASS_SUPPLIER);
    }

    
    public boolean isBuyerProject() {
        return (getProjectClassId() == ObjectClassID.PROJECT_CLASS_BUYER
                || getProjectClassId() == ObjectClassID.PROJECT_CLASS_BROKER
                || getProjectClassId() == ObjectClassID.PROJECT_CLASS_OUTSOURCER
                || getProjectClassId() == ObjectClassID.PROJECT_CLASS_CLIENT);
    }

    
    public boolean isClientProject() {
        return (getProjectClassId() == ObjectClassID.PROJECT_CLASS_CLIENT);
    }

    
    public boolean isBrokerProject() {
        return (getProjectClassId() == ObjectClassID.PROJECT_CLASS_BROKER);
    }

    
    public boolean isOutsourcerProject() {
        return (getProjectClassId() == ObjectClassID.PROJECT_CLASS_OUTSOURCER);
    }

    
    public boolean isBrokerOutsourcerProject() {
        return isBrokerProject() || isOutsourcerProject();
    }

    
    public boolean isClientOnNoosh() {
        return (isBrokerOutsourcerProject() && this.getClientWorkgroupId() > 0 );
    }

    
    public boolean isClientNotOnNoosh() {
        return (isBrokerOutsourcerProject() && this.getClientWorkgroupId() < 0);
    }

    
    public boolean isMaster() {
        return (this.getMasterProjectId() == null);
    }

    
    public long getMasterPId() {
        if (isMaster()) {
            return this.getId();
        } else {
            return this.getMasterProjectId();
        }
    }

    
    public boolean isSlave() {
        return (!isMaster());
    }

    
    public boolean isInactive() {
        return (this.getObjectStateId() == ObjectStateID.PROJECT_INACTIVATED);
    }

    
    public boolean isOwnerProject() {
        return this.isMaster();
    }

    public Long getSourceTypeId() {
        return sourceTypeId;
    }

    public void setSourceTypeId(Long sourceTypeId) {
        this.sourceTypeId = sourceTypeId;
    }

    public Boolean getIsNGEFileUI() {
        return isNGEFileUI;
    }

    public void setIsNGEFileUI(Boolean isNGEFileUI) {
        this.isNGEFileUI = isNGEFileUI;
    }

    public String getTitle() {
        String prNum = getProjectNumber();
        if (prNum == null || prNum.trim().length() == 0) {
            prNum = getReference();
        }
        return getName() + " (" + prNum + ")";
    }

}
