package com.noosh.app.commons.dto.tracking;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * User: leilaz
 * Date: 3/6/24
 */
public class WGTrackingDTO implements Serializable {
    private static final long serialVersionUID = -2476728002018751857L;

    private Long id;

    private Long trackingTypeId;

    private String comments;

    private String i18nData;

    private Long acSourceTypeId;

    private Long objectId;

    private Long objectClassId;

    private Map i18nDataMap;

    private Long createUserId;

    private String trackingName;

    private String trackingDesc;

    private String creator;

    private Long nameStrId;

    private Long descStrId;

    private String customNameStr;

    private LocalDateTime createDate;

    private String oldResourceValue;

    private String oldResourceName;

    private String newResourceValue;

    private String newResourceName;

    private String resourceName;

    private String resourceValue;

    private String language;

    private String portal;

    private String resourceDownloadPath;

    private String resourceFileRowCount;

    private String newResourceCount;

    private String duplicatedCount;

    public String getNewResourceCount() {
        return newResourceCount;
    }

    public void setNewResourceCount(String newResourceCount) {
        this.newResourceCount = newResourceCount;
    }

    public String getDuplicatedCount() {
        return duplicatedCount;
    }

    public void setDuplicatedCount(String duplicatedCount) {
        this.duplicatedCount = duplicatedCount;
    }

    public String getResourceFileRowCount() {
        return resourceFileRowCount;
    }

    public void setResourceFileRowCount(String resourceFileRowCount) {
        this.resourceFileRowCount = resourceFileRowCount;
    }

    public String getResourceDownloadPath() {
        return resourceDownloadPath;
    }

    public void setResourceDownloadPath(String resourceDownloadPath) {
        this.resourceDownloadPath = resourceDownloadPath;
    }

    public String getNewResourceValue() {
        return newResourceValue;
    }

    public void setNewResourceValue(String newResourceValue) {
        this.newResourceValue = newResourceValue;
    }

    public String getNewResourceName() {
        return newResourceName;
    }

    public void setNewResourceName(String newResourceName) {
        this.newResourceName = newResourceName;
    }

    public String getOldResourceName() {
        return oldResourceName;
    }

    public void setOldResourceName(String oldResourceName) {
        this.oldResourceName = oldResourceName;
    }

    public String getOldResourceValue() {
        return oldResourceValue;
    }

    public void setOldResourceValue(String oldResourceValue) {
        this.oldResourceValue = oldResourceValue;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getResourceValue() {
        return resourceValue;
    }

    public void setResourceValue(String resourceValue) {
        this.resourceValue = resourceValue;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getPortal() {
        return portal;
    }

    public void setPortal(String portal) {
        this.portal = portal;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public String getCustomNameStr() {
        return customNameStr;
    }

    public void setCustomNameStr(String customNameStr) {
        this.customNameStr = customNameStr;
    }

    public Long getNameStrId() {
        return nameStrId;
    }

    public void setNameStrId(Long nameStrId) {
        this.nameStrId = nameStrId;
    }

    public Long getDescStrId() {
        return descStrId;
    }

    public void setDescStrId(Long descStrId) {
        this.descStrId = descStrId;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getTrackingName() {
        return trackingName;
    }

    public void setTrackingName(String trackingName) {
        this.trackingName = trackingName;
    }

    public String getTrackingDesc() {
        return trackingDesc;
    }

    public void setTrackingDesc(String trackingDesc) {
        this.trackingDesc = trackingDesc;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public Map getI18nDataMap() {
        return i18nDataMap;
    }

    public void setI18nDataMap(Map i18nDataMap) {
        this.i18nDataMap = i18nDataMap;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public Long getObjectId() {
        return objectId;
    }

    public void setObjectId(Long objectId) {
        this.objectId = objectId;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments == null ? null : comments.trim();
    }

    public String getI18nData() {
        return i18nData;
    }

    public void setI18nData(String i18nData) {
        this.i18nData = i18nData == null ? null : i18nData.trim();
    }

    public Long getAcSourceTypeId() {
        return acSourceTypeId;
    }

    public void setAcSourceTypeId(Long acSourceTypeId) {
        this.acSourceTypeId = acSourceTypeId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTrackingTypeId() {
        return trackingTypeId;
    }

    public void setTrackingTypeId(Long trackingTypeId) {
        this.trackingTypeId = trackingTypeId;
    }
}
