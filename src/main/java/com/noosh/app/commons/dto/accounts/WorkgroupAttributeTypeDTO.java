package com.noosh.app.commons.dto.accounts;

public class WorkgroupAttributeTypeDTO {

    private Long id;
    private String labelStr;
    private Long labelStrId;
    private String constantToken;
    private Boolean isRestricted;
    private Long workgroupId;
    private String options;

    // association 
    // collection 

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLabelStr() {
        return labelStr;
    }

    public void setLabelStr(String labelStr) {
        this.labelStr = labelStr;
    }

    public Long getLabelStrId() {
        return labelStrId;
    }

    public void setLabelStrId(Long labelStrId) {
        this.labelStrId = labelStrId;
    }

    public String getConstantToken() {
        return constantToken;
    }

    public void setConstantToken(String constantToken) {
        this.constantToken = constantToken;
    }

    public Boolean getIsRestricted() {
        return isRestricted;
    }

    public void setIsRestricted(Boolean isRestricted) {
        this.isRestricted = isRestricted;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public String getOptions() {
        return options;
    }

    public void setOptions(String options) {
        this.options = options;
    }

}