package com.noosh.app.commons.dto.collaboration;


import java.util.List;

public class CategoryDTO {

    private Long id;
    private String name;
    private String description;
    private Long userId;
    private Long workgroupId;
    private String objectAttr;
    private Boolean isDisabled;

    // association 
    // collection
    private List<CategoryClassDTO> categoryClasses;

    public List<CategoryClassDTO> getCategoryClasses() {
        return categoryClasses;
    }

    public void setCategoryClasses(List<CategoryClassDTO> categoryClasses) {
        this.categoryClasses = categoryClasses;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getWorkgroupId() {
        return workgroupId;
    }

    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }

    public String getObjectAttr() {
        return objectAttr;
    }

    public void setObjectAttr(String objectAttr) {
        this.objectAttr = objectAttr;
    }

    public Boolean getIsDisabled() {
        return isDisabled;
    }

    public void setIsDisabled(Boolean isDisabled) {
        this.isDisabled = isDisabled;
    }

}