package com.noosh.app.commons.dto.quote;

import java.time.LocalDateTime;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by jennyc on 12/23/2018.
 */
public class QuoteDTO implements Serializable {

    private static final long serialVersionUID = -4542611818143009302L;

    private Long quoteId;
    private String title;
    private Long stateId;
    private LocalDateTime creationDate;
    private LocalDateTime expirationDate;
    private LocalDateTime completionDate;

    private Long projectId;
    private String projectName;

    private Long clientWorkgroupId;
    private String clientWorkgroupName;
    private Long supplierWorkgroupId;
    private String supplierWorkgroupName;

    private BigDecimal tax;
    private BigDecimal shipping;
    private Short isRead;

    public Long getQuoteId() {
        return quoteId;
    }

    public void setQuoteId(Long quoteId) {
        this.quoteId = quoteId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getStateId() {
        return stateId;
    }

    public void setStateId(Long stateId) {
        this.stateId = stateId;
    }

    public LocalDateTime getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(LocalDateTime creationDate) {
        this.creationDate = creationDate;
    }

    public LocalDateTime getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(LocalDateTime expirationDate) {
        this.expirationDate = expirationDate;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Long getClientWorkgroupId() {
        return clientWorkgroupId;
    }

    public void setClientWorkgroupId(Long clientWorkgroupId) {
        this.clientWorkgroupId = clientWorkgroupId;
    }

    public String getClientWorkgroupName() {
        return clientWorkgroupName;
    }

    public void setClientWorkgroupName(String clientWorkgroupName) {
        this.clientWorkgroupName = clientWorkgroupName;
    }

    public Long getSupplierWorkgroupId() {
        return supplierWorkgroupId;
    }

    public void setSupplierWorkgroupId(Long supplierWorkgroupId) {
        this.supplierWorkgroupId = supplierWorkgroupId;
    }

    public String getSupplierWorkgroupName() {
        return supplierWorkgroupName;
    }

    public void setSupplierWorkgroupName(String supplierWorkgroupName) {
        this.supplierWorkgroupName = supplierWorkgroupName;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public BigDecimal getShipping() {
        return shipping;
    }

    public void setShipping(BigDecimal shipping) {
        this.shipping = shipping;
    }

    public Short getIsRead() {
        return isRead;
    }

    public void setIsRead(Short isRead) {
        this.isRead = isRead;
    }
}
