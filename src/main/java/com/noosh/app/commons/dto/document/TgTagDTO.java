package com.noosh.app.commons.dto.document;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * User: leilaz
 * Date: 4/12/22
 */
public class TgTagDTO implements Serializable {
    private static final long serialVersionUID = 5622157059341473830L;

    private Long id;

    private String name;

    private Long ownerWorkgroupId;

    private Long objectClassId;

    private boolean isSystemGenerated;

    private boolean isActive;

    private LocalDateTime createDate;

    private LocalDateTime modDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getOwnerWorkgroupId() {
        return ownerWorkgroupId;
    }

    public void setOwnerWorkgroupId(Long ownerWorkgroupId) {
        this.ownerWorkgroupId = ownerWorkgroupId;
    }

    public Long getObjectClassId() {
        return objectClassId;
    }

    public void setObjectClassId(Long objectClassId) {
        this.objectClassId = objectClassId;
    }

    public boolean getIsSystemGenerated() {
        return isSystemGenerated;
    }

    public void setIsSystemGenerated(boolean systemGenerated) {
        isSystemGenerated = systemGenerated;
    }

    public boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(boolean active) {
        isActive = active;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getModDate() {
        return modDate;
    }

    public void setModDate(LocalDateTime modDate) {
        this.modDate = modDate;
    }
}
