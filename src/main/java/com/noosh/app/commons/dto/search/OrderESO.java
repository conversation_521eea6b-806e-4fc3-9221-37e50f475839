package com.noosh.app.commons.dto.search;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import java.io.Serializable;

public class OrderESO extends BaseESO implements Serializable {

    private static final long serialVersionUID = -212796479819935525L;
    @JsonProperty("or_order_id")
    private Long id;

    @JsonProperty("hl_or_order_id")
    private String highlightId;

    private String reference;

    @JsonProperty("payment_reference")
    private String paymentReference;

    private String title;

    @JsonProperty("or_order_type_id")
    private Long orderTypeId;

    @JsonProperty("parent_or_order_id")
    private Long parentOrderId;

    @JsonProperty("prj_owner_ac_workgroup_id")
    private Long projectOwnerWorkgroupId;

    @JsonProperty("prj_pm_project_id")
    private Long projectId;

    @JsonProperty("oc_object_state_id")
    private Long objectStateId;

    @JsonProperty("is_closing")
    private Boolean isClosing;

    @JsonProperty("completion_date")
    private LocalDateTime completionDate;

    private LocalDateTime localCompletionDate;

    @JsonProperty("create_date")
    private LocalDateTime createDate = LocalDateTime.now();

    @JsonProperty("mod_date")
    private LocalDateTime modDate = LocalDateTime.now();


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getHighlightId() {
        return highlightId;
    }

    public void setHighlightId(String highlightId) {
        this.highlightId = highlightId;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getOrderTypeId() {
        return orderTypeId;
    }

    public void setOrderTypeId(Long orderTypeId) {
        this.orderTypeId = orderTypeId;
    }

    public Long getParentOrderId() {
        return parentOrderId;
    }

    public void setParentOrderId(Long parentOrderId) {
        this.parentOrderId = parentOrderId;
    }

    public Long getProjectOwnerWorkgroupId() {
        return projectOwnerWorkgroupId;
    }

    public void setProjectOwnerWorkgroupId(Long projectOwnerWorkgroupId) {
        this.projectOwnerWorkgroupId = projectOwnerWorkgroupId;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public Long getObjectStateId() {
        return objectStateId;
    }

    public void setObjectStateId(Long objectStateId) {
        this.objectStateId = objectStateId;
    }

    public Boolean getIsClosing() {
        return isClosing;
    }

    public void setIsClosing(Boolean isClosing) {
        this.isClosing = isClosing;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public LocalDateTime getCreateDate() {
        return createDate;
    }

    public void setCreateDate(LocalDateTime createDate) {
        this.createDate = createDate;
    }

    public LocalDateTime getModDate() {
        return modDate;
    }

    public void setModDate(LocalDateTime modDate) {
        this.modDate = modDate;
    }

    public String getPaymentReference() {
        return paymentReference;
    }

    public void setPaymentReference(String paymentReference) {
        this.paymentReference = paymentReference;
    }

    public LocalDateTime getLocalCompletionDate() {
        return localCompletionDate;
    }

    public void setLocalCompletionDate(LocalDateTime localCompletionDate) {
        this.localCompletionDate = localCompletionDate;
    }
}
