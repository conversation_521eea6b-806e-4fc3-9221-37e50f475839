package com.noosh.app.commons.dto.collaboration;

/**
 * DTO for Workgroup Project Reason - corresponds to WorkgroupDeactivationReasonBean
 * Represents the workgroup-specific configuration of a project reason
 */
public class WorkgroupDeactivationReasonDTO {
    
    private Long id;
    private Long reasonId;
    private Long workgroupId;
    private Boolean isActive;
    private Boolean isDefault;
    private Long reasonOrder;
    private String direction; // for reordering: "up" or "down"
    
    // Nested reason information
    private DeactivationReasonDTO reason;
    
    // System reason IDs for bulk operations
    private Long[] systemReasonIds;
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getReasonId() {
        return reasonId;
    }
    
    public void setReasonId(Long reasonId) {
        this.reasonId = reasonId;
    }
    
    public Long getWorkgroupId() {
        return workgroupId;
    }
    
    public void setWorkgroupId(Long workgroupId) {
        this.workgroupId = workgroupId;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public Boolean getIsDefault() {
        return isDefault;
    }
    
    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }
    
    public Long getReasonOrder() {
        return reasonOrder;
    }
    
    public void setReasonOrder(Long reasonOrder) {
        this.reasonOrder = reasonOrder;
    }
    
    public String getDirection() {
        return direction;
    }
    
    public void setDirection(String direction) {
        this.direction = direction;
    }
    
    public DeactivationReasonDTO getReason() {
        return reason;
    }
    
    public void setReason(DeactivationReasonDTO reason) {
        this.reason = reason;
    }
    
    public Long[] getSystemReasonIds() {
        return systemReasonIds;
    }
    
    public void setSystemReasonIds(Long[] systemReasonIds) {
        this.systemReasonIds = systemReasonIds;
    }
}