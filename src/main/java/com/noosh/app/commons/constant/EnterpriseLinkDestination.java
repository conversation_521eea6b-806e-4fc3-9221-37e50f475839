package com.noosh.app.commons.constant;

/**
 * manage enterprise link
 *
 * @Author: neals
 * @Date: 01/25/2016
 */
public interface EnterpriseLinkDestination {

    // change password
    public static final String LOGIN_PAGE = "/noosh/home/<USER>";

    // project
    String VIEW_PROJECT_HOME = "/noosh/project/home";
    String GOTO_PROJECT_HOME = "/noosh/project/home";
    String SHOW_PENDING_APPROVAL = "/noosh/project/showApprovers";

    // message
    String VIEW_MESSAGE_DETAIL = "/noosh/messages/detailMessage";

    // task
    String VIEW_TASK = "/noosh/tasks/view";
    String TASK_BATCH = "/noosh/tasks/batch/batchAction";
    String DELETE_TASK = "/noosh/tasks/delete";
    String EDIT_TASK = "/noosh/tasks/edit";
    String PRINT_TASK = "/noosh/tasks/plan/printableSchedule";

    // tacking
    String VIEW_TRACKING_MANAGER = "/noosh/tracking/trackingManager";
    String VIEW_USER_TYPE = "/noosh/tracking/viewUserType";
    String EDIT_USER_TYPE = "/noosh/tracking/editUserType";
    String DELETE_USER_TYPE = "/noosh/tracking/deleteTrackingItem";

    // rfe & estimate
    String VIEW_RFE_HOME = "/noosh/procurement/estimating/rfe/home";
    String VIEW_ESTIMATE_HOME = "/noosh/procurement/estimating/estimate/home";
    String VIEW_RFE_DETAIL = "/noosh/procurement/estimating/rfe/detail";
    String VIEW_RFE_SPEC_DETAIL = "/noosh/procurement/estimating/rfe/rfeDetail";
    String REJECT_ESTIMATE_LINK = "/noosh/procurement/estimating/estimate/rejectEstimate";
    String ACTIVATE_ESTIMATE_LINK = "/noosh/procurement/estimating/estimate/activateEstimate";
    String INVALIDATE_ESTIMATE_LINK = "/noosh/procurement/estimating/estimate/invalidateEstimate";
    String CREATE_QUOTE_FROM_ESTIMATE_LINK = "/noosh/procurement/estimating/quote/createQuoteFromEstimate";
    String ACTION_GOTO_DISMISS_SUPPLIER = "/noosh/procurement/estimating/rfe/dismissSuppliers";
    String ACTION_GOTO_ESTIMATE_DETAILS = "/noosh/procurement/estimating/rfe/detail";

    // rfq & quote
    String VIEW_RFQ = "/noosh/procurement/estimating/quote/viewRfq";
    String VIEW_QUOTE = "/noosh/procurement/estimating/quote/viewQuote";
    String VIEW_PROPOSAL = "/noosh/procurement/estimating/quote/viewProposal";

    // order
    String VIEW_ORDER = "/noosh/procurement/ordering/order/viewOrder";
    String ACTION_DELETE_ORDER_DRAFT = "/noosh/procurement/ordering/order/viewOrder";
    String ACTION_RETRACT_ORDER = "/noosh/procurement/ordering/order/viewOrder";
    String ACTION_CANCEL_ORDER = "/noosh/procurement/ordering/order/viewOrder";
    String ACTION_ACCEPT_ORDER = "/noosh/procurement/ordering/order/viewOrder";
    String REJECT_ORDER = "/noosh/procurement/ordering/order/rejectOrder";
    String REJECT_CHANGE_ORDER = "/noosh/procurement/ordering/change/rejectChangeOrder";
    String VIEW_CHANGE_ORDER = "/noosh/procurement/ordering/change/viewChangeOrder";
    String ACTION_CARRY_CHANGE_ORDER_OVER = "/noosh/procurement/ordering/change/viewChangeOrder";
    String ACTION_ACCEPT_AND_CARRY_CHANGE_ORDER_OVER = "/noosh/procurement/ordering/change/viewChangeOrder";
    String ACTION_RETRACT_CHANGE_ORDER = "/noosh/procurement/ordering/change/viewChangeOrder";
    String ACTION_CANCEL_CHANGE_ORDER = "/noosh/procurement/ordering/change/viewChangeOrder";
    String ACTION_ACCEPT_CHANGE_ORDER = "/noosh/procurement/ordering/change/viewChangeOrder";
    String ACTION_PRE_ACCEPT_CHANGE_ORDER = "/noosh/procurement/ordering/change/viewChangeOrder";
    String CREATE_ORDER = "/noosh/procurement/ordering/order/createOrder";
    String CREATE_QUICK_ORDER = "/noosh/procurement/ordering/order/selectSpecs";
    String CREATE_ORDER_CONFIRM = "/noosh/procurement/ordering/order/createOrderConfirm";
    String CREATE_INVOICE_ADJUSTMENT_ORDER = "/noosh/procurement/ordering/order/createInvoiceAdjustmentOrder";
    String EDIT_ORDER = "/noosh/procurement/ordering/order/editOrder";
    String EDIT_CHANGE_ORDER = "/noosh/procurement/ordering/change/editChangeOrder";
    String COPY_ORDER = "/noosh/procurement/ordering/order/copyOrder";
    String CREATE_CHANGE_ORDER = "/noosh/procurement/ordering/change/createChangeOrder";
    String UPDATE_ORDER = "/noosh/procurement/ordering/order/updateOrderStatus";
    String EDIT_SUPPLIER_REF = "/noosh/procurement/ordering/order/editSupplierRef";
    String EDIT_GENERAL_INFO = "/noosh/procurement/ordering/order/editGeneralInfo";
    String COST_CENTER_ALLOCATION = "/noosh/procurement/costcenter/view";
    String AGGREGATED_COST_CENTER_ALLOCATION = "/noosh/procurement/costcenter/viewAggregatedCostCenterAlloc";
    String EDIT_COST_CENTER_ALLOCATION = "/noosh/procurement/costcenter/edit";
    String EDIT_CHANGE_ORDER_COST_CENTER_ALLOCATION = "/noosh/procurement/costcenter/editChangeOrder";
    String EDIT_AGGREGATED_ORDER_COST_CENTER_ALLOCATION = "/noosh/procurement/costcenter/editAggregatedCostCenterAlloc";
    String CHANGE_ORDER_COST_CENTER_ALLOCATION = "/noosh/procurement/costcenter/viewChangeOrder";
    String EDIT_SHIPMENT = "/noosh/procurement/shipment/batch/edit";
    String EDIT_ORDER_ITEM_SHIPMENT = "/noosh/procurement/shipment/edit";
    String SOURCING_STRATEGIES = "/noosh/procurement/sourcing/viewSourcing";
    String EDIT_SOURCING_STRATEGIES = "/noosh/procurement/sourcing/editSourcing";
    String CLOSING_ORDER = "/noosh/procurement/ordering/order/closeOrder";
    String ROUTING_RESPONSE = "/noosh/pmf/routing/respond";
    String LIST_ORDERS = "/noosh/procurement/ordering/order/listOrders";

    //invoice
    String INVOICE_LIST = "/noosh/procurement/invoice/list";
    String VIEW_INVOICE = "/noosh/procurement/invoice/view";
    String CREATE_INVOICE = "/noosh/procurement/invoice/create";
    String PRINT_INVOICE = "/noosh/procurement/invoice/printPreview";
    String COPY_INVOICE = "/noosh/procurement/invoice/list";
    String EDIT_INVOICE = "/noosh/procurement/invoice/edit";
    String DELETE_INVOICE = "/noosh/procurement/invoice/delete";
    String REJECT_INVOICE = "/noosh/procurement/invoice/reject";
    String RETRACT_INVOICE = "/noosh/procurement/invoice/retract";
    String ACCEPT_INVOICE = "/noosh/procurement/invoice/accept";
    String REPLACE_INVOICE = "/noosh/procurement/invoice/replace";

    //spec
    String PRODUCTIZE_SPEC = "/noosh/procurement/job/viewSpec";
    String GOTO_VIEW_SPEC = "/noosh/procurement/job/viewSpec";
    String COPY_SPEC = "/noosh/procurement/job/copySpecReference";
    String VIEW_SPEC = "/noosh/spec/view";
    String CREATE_SPEC_OPTION = "/noosh/spec/copy";
    String EDIT_SPEC_OPTION = "/noosh/spec/edit";
    String DELETE_SPEC_OPTION = "/noosh/procurement/job/deleteSpec";
    String SPEC_BATCH = "/noosh/procurement/job/batch/batchAction";

    //supplier rating
    String SUPPLIER_RATING_FROM_TASK = "/noosh/procurement/rating/viewRatingFromTask";
    String SUPPLIER_RATING = "/noosh/procurement/rating/viewRating";

    //report
    String REPORT_RUN = "/noosh/report/run";
    String EXPORT_SPEC = "/noosh/procurement/job/exportSpec";

    //pr
    String CREATE_PURCHASE_REQUEST = "/noosh/procurement/pr/createFromEst";

    // batch action
    String GOTO_BATCH_ACTION = "/noosh/project/batch/batchAction";
    String ACTION_RUN_PROJECT_EXCEL = "/noosh/project/projectManager";
    String ACTION_RUN_PRODUCTION_MATRIX_EXCEL = "/noosh/project/productionMatrix/run";

    // workspace
    String ACTION_AGGREGATE_SPECS = "/noosh/project/spec/findSpecs";
}
