package com.noosh.app.commons.constant;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 10/15/15
 */
public enum OrderStateEnum {
    PendingSubmission(2000030),
    AllocationActive(2000040),
    AllocationCancelled(2000041),
    Accepted(2000078),
    Draft(2000079),
    Rejected(2000080),
    SupplierToAccept(2000081),
    BuyerToAccept(2000082),
    Cancelled(2000083),
    Retracted(2000084),
    OutsourceToAccept(2000085),
    ClientToAccept(2000086),
    Shipped(2500043),
    Delivered(2500044),
    Completed(2500045),
    PartiallyShipped(2500064),
    AcceptedNotYetShipped(2500065),
    Finalized(2500074),
    Replaced(2500213), ;


    private long objectStateId;

    OrderStateEnum(final long objectStateId) {
        this.objectStateId = objectStateId;
    }

    public long getObjectStateId() {
        return objectStateId;
    }

    public static OrderStateEnum getOrderState(final long objectStateId) {
        for(OrderStateEnum os : OrderStateEnum.values()) {
            if(os.getObjectStateId() == objectStateId) return os;
        }
        throw new IllegalArgumentException("Wrong objectStateId for OrderStateEnum");
    }

}
