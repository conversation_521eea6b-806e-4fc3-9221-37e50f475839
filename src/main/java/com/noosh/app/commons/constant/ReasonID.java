package com.noosh.app.commons.constant;

/**
 * Constant interface: ReasonID
 * Constant source:    DB
 * Constant query:     select PC_REASON_id, constant_token from PC_REASON order by 1 asc
 *
 * Copyright 1998-2005 Noosh, Inc.
 *
 * <AUTHOR>
 * @since  Mon Nov 07 12:32:14 GMT 2005
 */
public interface ReasonID
{
    public static final long REASON_DESC_LOWEST_PRICE = 1000000;
    public static final long REASON_DESC_LOWEST_PRICE_NOT_MEETING_DELIVERY_REQUIREMENTS = 1000001;
    public static final long REASON_DESC_LOWEST_PRICE_NOT_MEETING_QUALITY_REQUIREMENTS = 1000002;
    public static final long REASON_DESC_CLIENT_DICTATED_SUPPLIER = 1000003;
    public static final long REASON_DESC_AGENCY_DICATED_SUPPLIER = 1000004;
    public static final long REASON_DESC_MWOB = 1000005;
    public static final long REASON_DESC_UNDER_CONTRACT = 1000006;
    public static final long REASON_DESC_REPRINT_TO_PREVIOUS_SUPPLIER = 1000007;
    public static final long REASON_DESC_OTHER = 1000008;
}
// Max ID: 1000008
// Count:  9
