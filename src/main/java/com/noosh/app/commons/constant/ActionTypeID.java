package com.noosh.app.commons.constant;

/**
 * Constant interface: ActionTypeID
 * Constant source:    DB
 * Constant query:     select RE_ACTION_TYPE_id, constant_token from RE_ACTION_TYPE order by 1 asc
 *
 * Copyright 1998-2004 Noosh, Inc.
 *
 * <AUTHOR>
 * @since  Thu Oct 07 18:09:07 PDT 2004
 */
public interface ActionTypeID
{
    public static final long TEST1 = 1000001;
    public static final long TEST2 = 1000002;
    public static final long TEST3 = 1000003;
    public static final long CREATE = 1000032;
    public static final long EDIT = 1000033;
    public static final long VIEW = 1000034;
    public static final long DELETE = 1000035;
    public static final long SEND = 1000036;
    public static final long ACCEPT = 1000037;
    public static final long REJECT = 1000038;
    public static final long CANCEL = 1000039;
    public static final long INVITE = 1000040;
    public static final long ARCHIVE = 1000060;
    public static final long CLOSE = 1000061;
    public static final long PERFORM = 1000062;
    public static final long COMPLETE = 1000063;
    public static final long OUTMODE = 1000064;
    public static final long ACTIVATE = 1000080;
    public static final long DEACTIVATE = 1000081;
    public static final long ATTACH = 1000100;
    public static final long DETACH = 1000101;
    public static final long RESPOND = 2000020;
    public static final long RETRACT = 2000068;
    public static final long RECEIVE = 2000090;
    public static final long MANAGE = 2500000;
    public static final long COPY = 2500020;
    public static final long CREATE_OPEN_BID = 2500040;
    public static final long DISMISS = 2500060;
    public static final long MARK_ACCEPTED = 2500100;
    public static final long MARK_SHIPPED = 2500120;
    public static final long MARK_DELIVERED = 2500121;
    public static final long MARK_COMPLETED = 2500122;
    public static final long MARK_PARTIALLY_SHIPPED = 2500123;
    public static final long MARK_ACCEPTED_NOT_YET_SHIPPED = 2500124;
    public static final long QUERY = 2500140;
    public static final long IMPERSONATE = 2500141;
    public static final long APPLY = 2500160;
    public static final long ASSIGN = 2500180;
    public static final long RUN = 2500202;
    public static final long UPDATE = 2500220;
    public static final long TRANSFER = 2500401;
    public static final long ADD = 2500402;
    public static final long APPROVE = 2500403;
    //public static final long DISAPPROVE = 2500404;
    public static final long ANNOTATE = 2500420;
    public static final long OWN = 2500441;
    public static final long REVISE = 2500502;
    public static final long SHARE = 2500503;
    public static final long REGISTER = 2500522;
    public static final long MOVE = 2500569;
    public static final long LIST = 2500570;
    public static final long CUSTOM_EDIT = 2500581;
    public static final long OPEN = 2500601;
    public static final long RECALL = 2500602;
    public static final long INVALIDATE = 2500603;
    public static final long ADJUST = 2500604;
    public static final long RESET = 2500605;
    public static final long REVIEW = 2500607;
    public static final long SET = 2500626;
    public static final long ROUTE = 2500627;
    public static final long DECLINE = 2500647;
}
// Max ID: 2500604
// Count:  54
