/**
 * Generated on Mon Feb 24 10:08:14 PST 2003
 * Copyright 1998-2003 Noosh
 **/
package com.noosh.app.commons.constant;


public interface TrackingTypeID
{
    public static long CREATE_PROJECT = 1000000;
    public static long TEAMMEMBER_ADDED = 1000004;
    public static long CREATE_RFE = 1000005;
    public static long UPDATE_RFE = 1000006;
    public static long SEND_RFE = 1000007;
    public static long DELETE_RFE = 1000008;
    public static long REJECT_RFE = 1000009;
    public static long SEND_AUCTION_RFE = 1000010;
    public static long END_AUCTION_RFE = 1000011;
    public static long ACCEPT_RFE = 1000012;
    public static long CREATE_ESTIMATE = 1000029;
    public static long UPDATE_ESTIMATE = 1000030;
    public static long SEND_ESTIMATE = 1000032;
    public static long DELETE_ESTIMATE = 1000033;
    public static long REJECT_ESTIMATE = 1000034;
    public static long CANCEL_ESTIMATE = 1000035;
    public static long TEAMMEMBER_REMOVED = 1000040;
    public static long TEAMMEMBER_ROLE_CHANGE = 1000041;
    public static long TEAMMEMBER_USER_CHANGE = 1000042;
    public static long PROJECT_DELETED = 1000060;
    public static long PROJECT_ACTIVATED = 1000080;
    public static long PROJECT_DEACTIVATED = 1000081;
    public static long PROJECT_NAME_CHANGED = 1000082;
    public static long PROJECT_COMPLETION_DATE_CHANGED = 1000083;
    public static long ORDER_DRAFT_CREATED = 1000097;
    public static long ORDER_DRAFT_UPDATED = 1000098;
    public static long ORDER_DRAFT_DELETED = 1000099;
    public static long QUICK_ORDER_CREATED = 1000101;
    public static long ORDER_CREATED = 1000102;
    public static long ORDER_UPDATED = 1000103;
    public static long ORDER_REJECTED = 1000104;
    public static long ORDER_RETRACTED = 1000105;
    public static long ORDER_ACCEPTED = 1000106;
    public static long ORDER_CANCELLED = 1000107;
    public static long PROJECT_EVENT = 1000110;
    public static long FILES_RECEIVED = 1000111;
    public static long FILES_SENT = 1000112;
    public static long CHANGE_ORDER_CREATED = 1000113;
    public static long CHANGE_ORDER_UPDATED = 1000114;
    public static long CHANGE_ORDER_REJECTED = 1000115;
    public static long CHANGE_ORDER_RETRACTED = 1000116;
    public static long CHANGE_ORDER_ACCEPTED = 1000117;
    public static long CHANGE_ORDER_CANCELLED = 1000118;
    public static long PROJECT_STATUS_CHANGED = 1000119;
    public static long DISMISS_SUPPLIER = 1000120;
    public static long ORDER_MARKED_ACCEPTED = 1000121;
    public static long ORDER_MARKED_SHIPPED = 1000122;
    public static long ORDER_MARKED_DELIVERED = 1000123;
    public static long ORDER_MARKED_COMPLETED = 1000124;
    public static long ORDER_MARKED_PARTIALLY_SHIPPED = 1000125;
    public static long PRINTING_COMPLETED = 1000126;
    public static long POSTPRESS_COMPLETED = 1000127;
    public static long PROOFS_SENT = 1000128;
    public static long PROOFS_RECEIVED = 1000129;
    public static long PROOFS_OK = 1000130;
    public static long PRESS_CHECK_SCHEDULED = 1000131;
    public static long PRESS_CHECK_OK = 1000132;
    public static long SAMPLES_SENT = 1000133;
    public static long POSTAGE_REVISED = 1000134;
    public static long MATERIALS_RECEVIED = 1000135;
    public static long MATERIALS_SENT = 1000136;
    public static long PRINTING_STARTED = 1000137;
    public static long RFQ_SENT = 1000140;
    public static long RFQ_CLOSED = 1000141;
    public static long RFQ_CANCELLED = 1000142;
    public static long QUOTE_SENT = 1000143;
    public static long QUOTE_REJECTED = 1000144;
    public static long QUOTE_REVISED = 1000145;
    public static long QUOTE_ACCEPTED = 1000146;
    public static long QUOTE_RETRACTED = 1000147;
    public static long PROPOSAL_CREATED = 1000148;
    public static long PROPOSAL_UPDATED = 1000149;
    public static long PROPOSAL_DELETED = 1000150;
    public static long PROPOSAL_SENT = 1000151;
    public static long PROPOSAL_REJECTED = 1000152;
    public static long PROPOSAL_RETRACTED = 1000153;
    public static long PROPOSAL_ACCEPTED = 1000154;
    public static long PROPOSAL_STATUS = 1000155;
    public static long QUOTE_CREATED = 1000156;
    public static long QUOTE_UPDATED = 1000157;
    public static long QUOTE_DELETED = 1000158;
    public static long QUOTE_SELECTION_CHANGED = 1000159;
    public static long QUOTE_CLOSED = 1000160;
    public static long QUOTE_REQUEST_FOR_APPROVAL = 1000161;
    public static long QUOTE_CANCELLED = 1000162;
    public static long PROPOSAL_CANCELLED = 1000163;
    public static long FILE_CREATED = 1000200;
    public static long FILE_EDITED = 1000201;
    public static long FILE_DELETED = 1000202;
    public static long FILE_VERSION_CREATED = 1000203;
    public static long FILE_VERSION_DELETED = 1000199;
    public static long FILE_MOVED = 1000204;
    public static long FILE_PRIVILEGES_CHANGED = 1000205;
    public static long FILE_DOWNLOADED = 1000206;
    public static long FILE_PRE_FLIGHTING = 1000198;
    public static long ORDER_MARKED_ACCEPTED_NOT_YET_SHIPPED = 1000207;
    public static long SEND_RFE_ADDITIONAL_SUPPLIER = 1000208;
    public static long TASK_ASSIGNED = 1000220;
    public static long TASK_REASSIGNED = 1000221;
    public static long TASK_DELETED = 1000222;
    public static long TASK_COMPLETED = 1000223;
    public static long TASK_UPDATED = 1000224;
    public static long TASK_READY_TO_COMPLETE = 1000225;
    public static long TASK_READY_TO_START = 1000226;
    public static long SPEC_CREATED = 1000227;
    public static long SPEC_EDITED = 1000228;
    public static long SPEC_DELETED = 1000229;
    public static long QUESTIONNAIRE_COMPLETED = 1000230;
    public static long QUESTIONNAIRE_REVISED = 1000231;
    public static long TASK_RENAMED = 1000232;
    public static long SHIPMENT_UPDATED = 1000233;
    public static long QUESTIONNAIRE_EDITED = 1000234;
    public static long SHIPMENT_COPIED = 1000235;
    public static long PURCHASE_REQUEST_CREATED = 1000240;
    public static long PURCHASE_REQUEST_SENT = 1000241;
    public static long PURCHASE_REQUEST_APPROVED = 1000242;
    public static long PURCHASE_REQUEST_DELETED = 1000243;
    public static long PROJECT_TRANSFER = 1000244;
    public static long ORDER_COST_CENTER_UPDATED = 1000245;
    public static long SCHEDULE_CREATED = 1000246;
    public static long SCHEDULE_UPDATED = 1000247;
    public static long SEND_ESTIMATE_FAILED = 1000248;
    public static long REVISE_RFE_DATES = 1000249;
    public static long PROJECT_COST_CENTER_UPDATED = 1000250;
    public static long SPEC_ITEM_CREATED = 1000251;
    public static long SPEC_ITEM_EDITED = 1000252;
    public static long PROJECT_BUDGET_UPDATED = 1000253;
    public static long ESTIMATE_ACTIVATED = 1000254;
    public static long INVOICE_CREATED = 1000255;
    public static long INVOICE_DELETED = 1000256;
    public static long INVOICE_SENT = 1000257;
    public static long INVOICE_REVISED = 1000258;
    public static long INVOICE_ACCEPTED = 1000259;
    public static long INVOICE_REJECTED = 1000260;
    public static long INVOICE_RETRACTED = 1000261;
    public static long RFE_RECALLED = 1000262;
    public static long RFE_REOPENED = 1000263;
    public static long RFE_CLOSED = 1000264;
    public static long ESTIMATE_RETRACTED = 1000265;
    public static long ESTIMATE_INVALIDATED = 1000266;
    public static long INVOICE_APPROVED = 1000267;
    public static long SUPPLIER_INVITED = 1000268;
    public static long SUPPLIER_UNINVITED = 1000269;
    public static long CLOSING_CHANGE_ORDER_CREATED = 1000270;
    public static long CLOSING_CHANGE_ORDER_UPDATED = 1000271;
    public static long CLOSING_CHANGE_ORDER_REJECTED = 1000272;
    public static long CLOSING_CHANGE_ORDER_RETRACTED = 1000273;
    public static long CLOSING_CHANGE_ORDER_ACCEPTED = 1000274;
    public static long CLOSING_CHANGE_ORDER_CANCELLED = 1000275;
    public static long ORDER_COMPLETED = 1000276;
    public static long ROUTING_OBJECT_ROUTED = 1000277;
    public static long ROUTING_OBJECT_APPROVED = 1000278;
    public static long ROUTING_OBJECT_DISAPPROVED = 1000279;
    public static long ROUTING_OBJECT_COMPLETED = 1000280;
    public static long ROUTING_OBJECT_CANCELLED = 1000281;
    public static long ROUTING_OBJECT_UPDATED = 1000282;
    public static long PURCHASE_REQUEST_REVISED = 1000287;
    public static long ITEM_MASTER_ATTACHED = 1000288;
    public static long RFQ_DECLINED = 1000289;
    public static long CLOSING_CHANGE_ORDER_REPLACED = 1000290;
    public static long MESSAGE_POST = 1000291;
    public static long SEND_REMINDER_TO_SUPPLIER_SIGNUP = 1000292;
    public static long SEND_REMINDER_TO_SUPPLIER_SEND_ESTIMATE = 1000293;
    public static long PROOFHQ_EVENT_CHANGESTATE = 1000294;
    public static long PROOFHQ_EVENT_DECISIONUPDATE = 1000295;
    public static long PROOFHQ_EVENT_PROCESSED = 1000296;
    public static long PROOFHQ_EVENT_NEW_COMMENT = 1000297;
    public static long PROOFHQ_EVENT_REPLY_TO_A_COMMENT = 1000298;
    public static long AUTOMATION_RULE_APPLIED = 1000300;
    public static long PARTIALLY_ACCEPT_RFE = 1000301;
    public static long ORDER_MARKED_FINALIZED = 1000302;
    public static long PROPOSAL_EMAILED_TO_CLIENT = 1000307;
    public static long PREMEDIA_INVITED = 1000308;
    public static long PREMEDIA_ARTWORK_STATUS_CHANGE = 1000309;
    public static long ORDER_TITLE_UPDATED = 1000108;
}
