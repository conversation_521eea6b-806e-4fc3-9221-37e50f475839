package com.noosh.app.commons.constant;

public interface PreferenceID {

    public static final String AUTO_DATE_POPULATOR_ID = "AUTO_DATE_POPULATOR_ID";

    public static final String CONSOLE_PERFORMANCE_FOOTER = "CONSOLE_PERFORMANCE_FOOTER";

    public static final String RECENT_PROJECTS_LIST_ENTRIES = "RECENT_PROJECTS_LIST_ENTRIES";
    public static final String HOME_PAGE_REDIRECT = "HOME_PAGE_REDIRECT";

    public static final String AUTO_COMPLETE_REVIEW_ESTIMATE_ON_ESTIMATE_DETAIL = "AUTO_COMPLETE_REVIEW_ESTIMATE_ON_ESTIMATE_DETAIL";
    public static final String AUTO_COMPLETE_REVIEW_ESTIMATE_ON_RFE_DETAIL = "AUTO_COMPLETE_REVIEW_ESTIMATE_ON_RFE_DETAIL";

    // default currency for project budget
    public static final String DEFAULT_CURRENCY_BUDGET_ID = "PM_DEFAULT_CURRENCY_BUDGET_ID";

    // default contact category
    public static final String DEFAULT_CONTACT_CATEGORY_ID = "PM_CREATE_PROJECT_CONTACT_CATEGORY_ID";

    // contact filters
    public static final String CONTACT_SEARCH_FIELD_PREF = "CONTACT_SEARCH_FIELD_PREF";

    // create force transfer
    public static final String PROJECT_CREATE_OWNER_USER_ID = "PM_CREATE_PROJECT_OWNER_USER_ID";

    public static final String PROJECT_FILTER_PREF = "PM_PROJECT_FILTER_PREF";
    public static final String PROJECT_FILTER_CATEGORY_LIMITED_PREF = "PROJECT_FILTER_CATEGORY_LIMITED_PREF";
    public static final String PROJECT_FILTER_CATEGORIES_PREF = "PM_PROJECT_FILTER_CATEGORIES_PREF";
    public static final String PROJECT_FILTER_STATE_PREF = "PM_PROJECT_FILTER_STATE_PREF";
    public static final String PROJECT_FILTER_PROJECT_STATUS_LIMITED_PREF = "PROJECT_FILTER_PROJECT_STATUS_LIMITED_PREF";
    public static final String PROJECT_FILTER_STATUSES_PREF = "PM_PROJECT_FILTER_STATUSES_PREF";
    public static final String PROJECT_FILTER_SHOW_ALL_SUBPROJECTS_PREF = "PM_PROJECT_FILTER_ALL_SUBPRJ_PREF";
    public static final String PROJECT_FILTER_DATE_OPTION_PREF = "PM_PROJECT_DATE_OPTION_PREF";
    public static final String PROJECT_FILTER_DATE_FROM = "PM_PROJECT_COMPLETIONDATE_FROM_PREF";
    public static final String PROJECT_FILTER_DATE_TO = "PM_PROJECT_COMPLETIONDATE_TO_PREF";
    public static final String PROJECT_FILTER_DATE_RANGE = "PM_DATE_RANGE_PREF";
    public static final String PROJECT_FILTER_SEARCH_FIELD_PREF = "PM_PROJECT_SEARCH_FIELD_PREF";
    public static final String PROJECT_FILTER_PROJECT_OWNER_PREF = "PM_PROJECT_PROJECT_OWNER_PREF";
    public static final String PROJECT_STRUCTURE_HIDE_SPECS = "PROJECT_STRUCTURE_HIDE_SPECS";

    public static final String PROJECT_SEARCH_FILTER_LIMITED_TO_PREF = "PROJECT_LIMITED_TO_PREF";
    public static final String PROJECT_SEARCH_FILTER_RELATIVE_DATE_OPTION = "PROJECT_FILTER_RELATIVE_DATE_OPTION";

    public static final String INVENTORY_MGR_FILTER_SKU = "INVENTORY_MGR_FILTER_SKU";
    public static final String INVENTORY_MGR_FILTER_SPECTYPE = "INVENTORY_MGR_FILTER_SPECTYPE";
    public static final String INVENTORY_MGR_FILTER_QUANTITYBELOW = "INVENTORY_MGR_FILTER_QUANTITYBELOW";
    public static final String INVENTORY_MGR_FILTER_QUANTITYABOVE = "INVENTORY_MGR_FILTER_QUANTITYABOVE";
    public static final String INVENTORY_MGR_FILTER_LASTUPDATEDATEFROM = "INVENTORY_MGR_FILTER_LASTUPDATEDATEFROM";
    public static final String INVENTORY_MGR_FILTER_LASTUPDATEDATETO = "INVENTORY_MGR_FILTER_LASTUPDATEDATETO";
    public static final String INVENTORY_VIEW_FILTER_RECEIVED = "INVENTORY_VIEW_FILTER_RECEIVED";
    public static final String INVENTORY_VIEW_FILTER_REMOVED = "INVENTORY_VIEW_FILTER_REMOVED";
    public static final String INVENTORY_VIEW_FILTER_ADJUSTMENT = "INVENTORY_VIEW_FILTER_ADJUSTMENT";

    public static final String BU_RFE_UNREGIS_SUP = "BU_RFE_UNREGIS_SUP";
    public static final String BU_ORDER_UNREGIS_SUP = "BU_ORDER_UNREGIS_SUP";
    public static final String BU_SUP_CANCEL_ORDER = "BU_SUP_CANCEL_ORDER";
    public static final String BU_WORKGROUP_ORDER_DISMISS_SUPPLIER_PREF = "BU_WORKGROUP_ORDER_DISMISS_SUPPLIER_PREF";
    public static final String BU_WORKGROUP_AUTO_CLOSE_RFE_PREF = "BU_WORKGROUP_AUTO_CLOSE_RFE_PREF";
    public static final String BU_WORKGROUP_RFE_DISMISS_SUPPLIER_PREF = "BU_WORKGROUP_RFE_DISMISS_SUPPLIER_PREF";
    public static final String BU_WORKGROUP_AUTO_EXTENSION_OPENBID_PREF = "BU_WORKGROUP_AUTO_EXTENSION_OPENBID_PREF";
    public static final String BU_WORKGROUP_ENABLE_MAX_EXTENSION_OPENBID_PREF = "BU_WORKGROUP_ENABLE_MAX_EXTENSION_OPENBID_PREF";
    public static final String BU_WORKGROUP_MAX_EXTENSION_OPENBID_PREF = "BU_WORKGROUP_MAX_EXTENSION_OPENBID_PREF";
    public static final String BU_WORKGROUP_BID_TIME_INCREMENT_OPENBID_PREF = "BU_WORKGROUP_BID_TIME_INCREMENT_OPENBID_PREF";
    public static final String BU_WORKGROUP_RFE_INCLUDE_SUPPLIERS_PREF = "BU_WORKGROUP_RFE_INCLUDE_SUPPLIERS_PREF";

    public static final String BU_WORKGROUP_ALLOW_AUTO_EXTENSION_REVERSE_AUCTION_PREF = "BU_WORKGROUP_ALLOW_AUTO_EXTENSION_REVERSE_AUCTION_PREF";
    public static final String BU_WORKGROUP_AUTO_EXTENSION_MINUTES_REVERSE_AUCTION_PREF = "BU_WORKGROUP_AUTO_EXTENSION_MINUTES_REVERSE_AUCTION_PREF";
    public static final String BU_WORKGROUP_ENABLE_MAX_EXTENSION_REVERSE_AUCTION_PREF = "BU_WORKGROUP_ENABLE_MAX_EXTENSION_REVERSE_AUCTION_PREF";
    public static final String BU_WORKGROUP_MAX_EXTENSION_REVERSE_AUCTION_PREF = "BU_WORKGROUP_MAX_EXTENSION_REVERSE_AUCTION_PREF";
    public static final String BU_WORKGROUP_BID_TIME_INCREMENT_REVERSE_AUCTION_PREF = "BU_WORKGROUP_BID_TIME_INCREMENT_REVERSE_AUCTION_PREF";
    public static final String BU_WORKGROUP_BID_INCLUDES_TAX_REVERSE_AUCTION_PREF = "BU_WORKGROUP_BID_INCLUDES_TAX_REVERSE_AUCTION_PREF";
    public static final String BU_WORKGROUP_BID_INCLUDES_SHIPPING_REVERSE_AUCTION_PREF = "BU_WORKGROUP_BID_INCLUDES_SHIPPING_REVERSE_AUCTION_PREF";
    public static final String BU_WORKGROUP_MANUAL_SELECT_WINNING_BIDDER_PREF = "BU_WORKGROUP_MANUAL_SELECT_WINNING_BIDDER_PREF";
    public static final String BU_WORKGROUP_EXTEND_BID_TIME_FROM_LAST_SUPPLIER_BID_REVERSE_AUCTION_PREF = "BU_WORKGROUP_EXTEND_BID_TIME_FROM_LAST_SUPPLIER_BID_REVERSE_AUCTION_PREF";
    public static final String BU_WORKGROUP_EXTEND_BID_TIME_FROM_BID_END_TIME_REVERSE_AUCTION_PREF = "BU_WORKGROUP_EXTEND_BID_TIME_FROM_BID_END_TIME_REVERSE_AUCTION_PREF";
    public static final String BU_WORKGROUP_BID_HIGHER_CURRENT_BID_AUCTION_PREF = "BU_WORKGROUP_BID_HIGHER_CURRENT_BID_AUCTION_PREF";
    public static final String BU_RFE_DUE_DATE_REQUIRED = "BU_RFE_DUE_DATE_REQUIRED";
    public static final String BU_RFE_PROPOSED_ORDER_COMPLETION_DATE_REQUIRED = "BU_RFE_PROPOSED_ORDER_COMPLETION_DATE_REQUIRED";

    public static final String WORKGROUP_TIME_INCREMENT = "WORKGROUP_TIME_INCREMENT"; //use same workgroup time increment

    //quote
    public static final String PC_QUOTE_INCLUDE_ALL = "PC_QUOTE_INCLUDE_ALL";
    public static final String PC_QUOTE_SHOW_SUPPLIER_INFO = "PC_QUOTE_SHOW_SUPPLIER_INFO";

    // other services
    public static final String WORKGROUP_OPTION_APPROVALS_ROUTING = "WORKGROUP_OPTION_APPROVALS_ROUTING";
    public static final String WORKGROUP_OPTION_FILE_APPROVALS = "WORKGROUP_OPTION_FILE_APPROVALS";
    public static final String WORKGROUP_OPTION_PROJECT_APPROVALS = "WORKGROUP_OPTION_PROJECT_APPROVALS";
    public static final String WORKGROUP_OPTION_TIMECARD = "WORKGROUP_OPTION_TIMECARD";
    public static final String WORKGROUP_OPTION_TIMECARD_STOP_WATCH = "WORKGROUP_OPTION_TIMECARD_STOP_WATCH";
    public static final String WORKGROUP_OPTION_SIMPLE_ORDER_ROUTING = "WORKGROUP_OPTION_SIMPLE_ORDER_ROUTING";
    public static final String WORKGROUP_OPTION_SHOW_PENDING_APPROVALS = "WORKGROUP_OPTION_SHOW_PENDING_APPROVALS";
    public static final String WORKGROUP_OPTION_UNIQUE_ORDER_TITLE = "WORKGROUP_OPTION_UNIQUE_ORDER_TITLE";
    public static final String WORKGROUP_OPTION_SUPPLIER_RATING = "WORKGROUP_OPTION_SUPPLIER_RATING";
    public static final String WORKGROUP_OPTION_BASELINE_RATING = "WORKGROUP_OPTION_BASELINE_RATING";
    public static final String WORKGROUP_OPTION_SOURCING_STRATEGIES = "WORKGROUP_OPTION_SOURCING_STRATEGIES";
    public static final String WORKGROUP_OPTION_PROPOSAL_WRITER = "WORKGROUP_OPTION_PROPOSAL_WRITER";
    public static final String WORKGROUP_OPTION_PROPOSAL_SPEC_SUMMARY_REG = "WORKGROUP_OPTION_PROPOSAL_SPEC_SUMMARY_REG";
    public static final String WORKGROUP_OPTION_BROKERING = "WORKGROUP_OPTION_BROKERING";
    public static final String WORKGROUP_OPTION_OUTSOURCING = "WORKGROUP_OPTION_OUTSOURCING";
    public static final String WORKGROUP_OPTION_OUTSOURCING_WITH_RFQ = "WORKGROUP_OPTION_OUTSOURCING_WITH_RFQ";
    public static final String WORKGROUP_OPTION_CLIENT = "WORKGROUP_OPTION_CLIENT";
    public static final String WORKGROUP_OPTION_OPENBID = "WORKGROUP_OPTION_OPENBID";
    public static final String WORKGROUP_OPTION_REVERSE_AUCTION = "WORKGROUP_OPTION_REVERSE_AUCTION";
    public static final String WORKGROUP_OPTION_RFE_REQUIRE_ACCEPTANCE = "WORKGROUP_OPTION_RFE_REQUIRE_ACCEPTANCE";
    public static final String WORKGROUP_OPTION_ORDER_CLOSE_DATE = "WORKGROUP_OPTION_ORDER_CLOSE_DATE";
    public static final String WORKGROUP_OPTION_PROJECT_BUDGET = "WORKGROUP_OPTION_PROJECT_BUDGET";
    public static final String WORKGROUP_OPTION_SHOW_BUDGET_PER_UNIT = "WORKGROUP_OPTION_SHOW_BUDGET_PER_UNIT";
    public static final String WORKGROUP_OPTION_UNIQUE_PROJECT_NAME = "WORKGROUP_OPTION_UNIQUE_PROJECT_NAME";
    public static final String WORKGROUP_OPTION_PROCUREMENT = "WORKGROUP_OPTION_PROCUREMENT";
    public static final String WORKGROUP_OPTION_CONTRACT_PRICING = "WORKGROUP_OPTION_CONTRACT_PRICING";
    public static final String WORKGROUP_OPTION_GENERATE_ESTIMATE_FROM_PRICING = "WORKGROUP_OPTION_GENERATE_ESTIMATE_FROM_PRICING";
    public static final String WORKGROUP_OPTION_SEND_FINAL_INVOICE = "WORKGROUP_OPTION_SEND_FINAL_INVOICE";
    public static final String WORKGROUP_OPTION_SHOW_INVOICE_AMOUNT ="WORKGROUP_OPTION_SHOW_INVOICE_AMOUNT";
    public static final String WORKGROUP_OPTION_DISCOUNT_SURCHARGE = "WORKGROUP_OPTION_DISCOUNT_SURCHARGE";

    public static final String WORKGROUP_OPTION_PROJECT_PLANNING = "WORKGROUP_OPTION_PROJECT_PLANNING";
    public static final String WORKGROUP_OPTION_SUPPLIER_INVITE = "WORKGROUP_OPTION_SUPPLIER_INVITE";
    public static final String WORKGROUP_OPTION_REQUEST_FOR_ALL_OR_NONE = "WORKGROUP_OPTION_REQUEST_FOR_ALL_OR_NONE";
    public static final String WORKGROUP_OPTION_SPEC_QUERY = "WORKGROUP_OPTION_SPEC_QUERY";
    public static final String WORKGROUP_OPTION_ORDER_BLASTER = "WORKGROUP_OPTION_ORDER_BLASTER";
    public static final String WORKGROUP_OPTION_ENABLE_ADDITEM = "WORKGROUP_OPTION_ENABLE_ADDITEM";
    public static final String WORKGROUP_OPTION_DISALLOW_CREATE_INVOICES = "WORKGROUP_OPTION_DISALLOW_CREATE_INVOICES";
    public static final String WORKGROUP_OPTION_REUSE_SPEC_ON_REORDER = "WORKGROUP_OPTION_REUSE_SPEC_ON_REORDER";
    public static final String WORKGROUP_OPTION_PLACE_COPIED_JOBS_WITH_ORIG = "WORKGROUP_OPTION_PLACE_COPIED_JOBS_WITH_ORIG";
    public static final String WORKGROUP_OPTION_ORDER_INVOICING = "WORKGROUP_OPTION_ORDER_INVOICING";
    public static final String WORKGROUP_OPTION_EMAIL_INTEGRATION = "WORKGROUP_OPTION_EMAIL_INTEGRATION";
    public static final String WORKGROUP_OPTION_CLOSE_ORDER_NEGOTIATION = "WORKGROUP_OPTION_CLOSE_ORDER_NEGOTIATION";
    public static final String WORKGROUP_OPTION_INVENTORY = "WORKGROUP_OPTION_INVENTORY";
    public static final String WORKGROUP_OPTION_INVENTORY_AUTOCREATE = "WORKGROUP_OPTION_INVENTORY_AUTOCREATE";
    public static final String WORKGROUP_OPTION_INVENTORY_ACTIVITY_EXTERNALLY_MANAGE = "WORKGROUP_OPTION_INVENTORY_ACTIVITY_EXTERNALLY_MANAGE";
    public static final String WORKGROUP_OPTION_INVENTORY_DISPLAY_EXTERNAL_QTY = "WORKGROUP_OPTION_INVENTORY_DISPLAY_EXTERNAL_QTY";
    public static final String WORKGROUP_OPTION_ALLOW_OBSOLETE_ITEM_MASTER_UPDATE = "WORKGROUP_OPTION_ALLOW_OBSOLETE_ITEM_MASTER_UPDATE";
    public static final String WORKGROUP_OPTION_SEVEN_DAY_TIMECARD = "WORKGROUP_OPTION_SEVEN_DAY_TIMECARD";
    public static final String WORKGROUP_OPTION_SIMPLIFY_TASK = "WORKGROUP_OPTION_SIMPLIFY_TASK";
    public static final String WORKGROUP_OPTION_INLINE_TASK_EDIT = "WORKGROUP_OPTION_INLINE_TASK_EDIT";
    public static final String WORKGROUP_OPTION_DISABLE_CAPABILITY_SEARCH = "WORKGROUP_OPTION_DISABLE_CAPABILITY_SEARCH";
    public static final String WORKGROUP_OPTION_EXTENDED_NOTIFICATION = "WORKGROUP_OPTION_EXTENDED_NOTIFICATION";
    public static final String WORKGROUP_OPTION_WAREHOUSE = "WORKGROUP_OPTION_WAREHOUSE";
    public static final String WORKGROUP_OPTION_SPENDING_LIMIT = "WORKGROUP_OPTION_SPENDING_LIMIT";
    public static final String WORKGROUP_OPTION_PURCHASE_REQUEST = "WORKGROUP_OPTION_PURCHASE_REQUEST";
    public static final String WORKGROUP_OPTION_ARIBA_PO = "WORKGROUP_OPTION_ARIBA_PO";
    public static final String WORKGROUP_OPTION_SAP_PO = "WORKGROUP_OPTION_SAP_PO";
    public static final String WORKGROUP_OPTION_COSTCENTER = "WORKGROUP_OPTION_COSTCENTER";
    public static final String WORKGROUP_OPTION_PRAG_CHOICE = "WORKGROUP_OPTION_PRAG_CHOICE";
    public static final String WORKGROUP_OPTION_ITEMIZED_TAX_SHIPPING = "WORKGROUP_OPTION_ITEMIZED_TAX_SHIPPING";
    public static final String WORKGROUP_OPTION_DX = "WORKGROUP_OPTION_DX";
    public static final String WORKGROUP_OPTION_PROJECT_FILE_PROCESSOR = "WORKGROUP_OPTION_PROJECT_FILE_PROCESSOR";
    public static final String WORKGROUP_OPTION_PROJECT_FILE_PROCESSOR_SUPPLIER = "WORKGROUP_OPTION_PROJECT_FILE_PROCESSOR_SUPPLIER";
    public static final String WORKGROUP_OPTION_SPEC_FILE_PROCESSOR = "WORKGROUP_OPTION_SPEC_FILE_PROCESSOR";
    public static final String WORKGROUP_OPTION_PAPER_FLOW = "WORKGROUP_OPTION_PAPER_FLOW";
    public static final String WORKGROUP_OPTION_PAPER_ORDER_DIRECT = "WORKGROUP_OPTION_PAPER_ORDER_DIRECT";
    public static final String WORKGROUP_OPTION_PAPER_ORDER_DIRECTED = "WORKGROUP_OPTION_PAPER_ORDER_DIRECTED";
    public static final String WORKGROUP_OPTION_PAPER_ORDER_DEFAULT_OPTION = "WORKGROUP_OPTION_PAPER_ORDER_DEFAULT_OPTION";
    public static final String WORKGROUP_OPTION_PSF_ORDER_PREFERENCES = "WORKGROUP_OPTION_PSF_ORDER_PREFERENCES";
    public static final String WORKGROUP_OPTION_PROCUREMENT_DATE_PREFERENCES = "WORKGROUP_OPTION_PROCUREMENT_DATE_PREFERENCES";
    public static final String WORKGROUP_OPTION_WORKSPACE_MANAGER_FILTERS_SORTING_BY_OWNER = "WORKGROUP_OPTION_WORKSPACE_MANAGER_FILTERS_SORTING_BY_OWNER";
    public static final String WORKGROUP_OPTION_UNISOURCE_FREIGHT_LINK = "WORKGROUP_OPTION_UNISOURCE_FREIGHT_LINK";
    public static final String WORKGROUP_OPTION_LARGE_CAMPAIGN_WORKFLOW = "WORKGROUP_OPTION_LARGE_CAMPAIGN_WORKFLOW";
    public static final String WORKGROUP_OPTION_LC_SUPPORT_QTY_KIT = "WORKGROUP_OPTION_LC_SUPPORT_QTY_KIT";
    public static final String WORKGROUP_OPTION_LC_SUPPORT_KIND = "WORKGROUP_OPTION_LC_SUPPORT_KIND";
    public static final String WORKGROUP_OPTION_LC_SUPPORT_COPY_KIT = "WORKGROUP_OPTION_LC_SUPPORT_COPY_KIT";
    public static final String WORKGROUP_OPTION_LC_SUPPORT_PROCUREMENT_GRID = "WORKGROUP_OPTION_LC_SUPPORT_PROCUREMENT_GRID";
    public static final String WORKGROUP_OPTION_LC_SUPPORT_PROJECT_GRID = "WORKGROUP_OPTION_LC_SUPPORT_PROJECT_GRID";
    public static final String WORKGROUP_OPTION_LC_SUPPORT_ENHANCED_PROJECT_COPY = "WORKGROUP_OPTION_LC_SUPPORT_ENHANCED_PROJECT_COPY";

    public static final String WORKGROUP_OPTION_PAPER_FLOW_SUPPLIER = "WORKGROUP_OPTION_PAPER_FLOW_SUPPLIER";
    public static final String WORKGROUP_OPTION_CUSTOMER_PROGRAM = "WORKGROUP_OPTION_CUSTOMER_PROGRAM";
    public static final String WORKGROUP_OPTION_FULL_CATALOG = "WORKGROUP_OPTION_FULL_CATALOG";
    public static final String WORKGROUP_OPTION_CUSTOM_PAPER = "WORKGROUP_OPTION_CUSTOM_PAPER";
    public static final String WORKGROUP_OPTION_PAPER_WEIGHT_CALCULATION = "WORKGROUP_OPTION_PAPER_WEIGHT_CALCULATION";
    public static final String WORKGROUP_OPTION_DISPLAY_FULL_CATALOG_TAB_NAME = "WORKGROUP_OPTION_DISPLAY_FULL_CATALOG_TAB_NAME";
    public static final String WORKGROUP_OPTION_CUSTOMER_PROGRAM_TAB_NAME = "WORKGROUP_OPTION_CUSTOMER_PROGRAM_TAB_NAME";
    public static final String PAPER_FLOW_FAV_PREFIX = "PAPER_FLOW_FAV_PREFIX";
    public static final String WORKGROUP_OPTION_TC_HTMLFORMAT = "WORKGROUP_OPTION_TC_HTMLFORMAT";

    public static final String PC_ALLOW_BREAKOUTS_OVERRIDE = "PC_ALLOW_BREAKOUTS_OVERRIDE";
    public static final String PC_CALC_TOTAL_FROM_BREAKOUTS = "PC_CALC_TOTAL_FROM_BREAKOUTS";
    public static final String PC_PRICE_BREAKOUTS_FOR_ORDERS = "PC_PRICE_BREAKOUTS_FOR_ORDERS";
    public static final String PC_INVOICE_INCLUDE_PRICE_BREAKOUT = "PC_INVOICE_INCLUDE_PRICE_BREAKOUT";
    public static final String PC_INVOICE_REQUIRES_APPROVAL = "PC_INVOICE_REQUIRES_APPROVAL";
    public static final String PC_INVOICE_AUTO_ACCEPT_FINAL_INVOICE = "PC_INVOICE_AUTO_ACCEPT_FINAL_INVOICE";
    public static final String PC_INVOICE_SHOW_COSTCENTER = "PC_INVOICE_SHOW_COSTCENTER";
    public static final String PC_INVOICE_NO_VALIDATION = "PC_INVOICE_NO_VALIDATION";
    public static final String PC_ORDER_UNDERS_PERCENTAGE = "PC_ORDER_UNDERS_PERCENTAGE";
    public static final String PC_ORDER_OVERS_PERCENTAGE = "PC_ORDER_OVERS_PERCENTAGE";
    public static final String PC_TAX_PERCENTAGE = "PC_TAX_PERCENTAGE";
    public static final String PC_TAX_LABEL_STRING = "PC_TAX_LABEL_STRING";
    public static final String PC_ORDER_PSF_DISSALLOW_ORDER_ACCEPT = "PC_ORDER_PSF_DISSALLOW_ORDER_ACCEPT";
    public static final String PC_ORDER_PSF_DISSALLOW_CHANGE_ORDER = "PC_ORDER_PSF_DISSALLOW_CHANGE_ORDER";
    public static final String PS_ORDER_COMPLETION_DATE_REQUIRED = "PS_ORDER_COMPLETION_DATE_REQUIRED";

    public static final String PS_AUTOMATIC_SUBMIT_UPON_RESPONSE = "PS_AUTOMATIC_SUBMIT_UPON_RESPONSE";
    public static final String PS_AUTOMATIC_CANCEL_UPON_RESPONSE = "PS_AUTOMATIC_CANCEL_UPON_RESPONSE";
    public static final String PS_AUTOMATIC_ACCEPTANCE_UPON_APPROVAL = "PS_AUTOMATIC_ACCEPTANCE_UPON_APPROVAL";
    public static final String PS_NOTIFY_LOWER_THRESHOLD_RECIPIENTS = "PS_NOTIFY_LOWER_THRESHOLD_RECIPIENTS";
    public static final String PS_ALL_AT_ONCE = "PS_ALL_AT_ONCE";
    public static final String PS_TIMEOUT_PERIOD_ENABLED = "PS_TIMEOUT_PERIOD_ENABLED";
    public static final String PS_TIMEOUT_PERIOD = "PS_TIMEOUT_PERIOD";
    public static final String PS_ROUTE_CLOSING_CHANGE_ORDERS_ONLY = "PS_ROUTE_CLOSING_CHANGE_ORDERS_ONLY";
    public static final String PS_REQUIRE_SEQUENTIAL_APPROVALS = "PS_REQUIRE_SEQUENTIAL_APPROVALS";
    public static final String PS_REQUIRE_MANAGER_APPROVAL = "PS_REQUIRE_MANAGER_APPROVAL";
    public static final String PS_IGNORE_SUPPLIERS = "PS_IGNORE_SUPPLIERS";
    public static final String PS_QUICK_ORDER_APPROVAL = "PS_QUICK_ORDER_APPROVAL";
    public static final String PS_QUICK_ORDER_APPROVERS = "PS_QUICK_ORDER_APPROVERS";
    public static final String PS_BYPASS_MANAGER_APPROVAL_FROM_ESTIMATE = "PS_BYPASS_MANAGER_APPROVAL_FROM_ESTIMATE";

    public static final String WORKGROUP_OPTION_DISABLE_CANCEL_ORDER_BUTTON  = "WORKGROUP_OPTION_DISABLE_CANCEL_ORDER_BUTTON";

    public static final String PS_M_ALL_AT_ONCE = "PS_M_ALL_AT_ONCE";
    public static final String PS_M_TIMEOUT_PERIOD_ENABLED = "PS_M_TIMEOUT_PERIOD_ENABLED";
    public static final String PS_M_TIMEOUT_PERIOD = "PS_M_TIMEOUT_PERIOD";
    public static final String PS_M_REQUIRE_SEQUENTIAL_APPROVALS = "PS_M_REQUIRE_SEQUENTIAL_APPROVALS";
    // variable - the following prefs ending in underscore are non-existent without a appended number
    public static final String PS_NUM_COST_LEVELS = "PS_NUM_COST_LEVELS";
    public static final String PS_COST_LEVEL_ENABLED_ = "PS_COST_LEVEL_ENABLED_";
    public static final String PS_COST_LEVEL_FROM_ = "PS_COST_LEVEL_FROM_";
    public static final String PS_COST_LEVEL_TO_ = "PS_COST_LEVEL_TO_";
    public static final String PS_COST_LEVEL_APPROVERS_ = "PS_COST_LEVEL_APPROVERS_";
    public static final String PS_COST_LEVEL_MANAGERS_ = "PS_COST_LEVEL_MANAGERS_";

    // security related workgroup options
    public static final String WORKGROUP_OPTION_SECURE_LOGIN_ONLY = "WORKGROUP_OPTION_SECURE_LOGIN_ONLY";
    public static final String WORKGROUP_OPTION_LOGOUT_ALERT = "WORKGROUP_OPTION_LOGOUT_ALERT";
    public static final String WORKGROUP_OPTION_INVALID_ENTRY_ALERT = "WORKGROUP_OPTION_INVALID_ENTRY_ALERT";
    public static final String WORKGROUP_OPTION_PASSWORD_STRONG = "WORKGROUP_OPTION_PASSWORD_STRONG";
    public static final String WORKGROUP_OPTION_PASSWORD_EXPIRATION = "WORKGROUP_OPTION_PASSWORD_EXPIRATION";
    public static final String WORKGROUP_OPTION_PASSWORD_EXPIRATION_DAYS = "WORKGROUP_OPTION_PASSWORD_EXPIRATION_DAYS";
    public static final String WORKGROUP_OPTION_PASSWORD_HISTORY = "WORKGROUP_OPTION_PASSWORD_HISTORY";
    public static final String WORKGROUP_OPTION_PASSWORD_HISTORY_LENGTH = "WORKGROUP_OPTION_PASSWORD_HISTORY_LENGTH";
    public static final String WORKGROUP_OPTION_LOCKOUT_USERS = "WORKGROUP_OPTION_LOCKOUT_USERS";
    public static final String WORKGROUP_OPTION_LOCKOUT_USERS_NUM_ATTEMPTS = "WORKGROUP_OPTION_LOCKOUT_USERS_NUM_ATTEMPTS";
    public static final String WORKGROUP_OPTION_PASSWORD_ALLOW_RESETS = "WORKGROUP_OPTION_PASSWORD_ALLOW_RESETS";
    public static final String WORKGROUP_OPTION_TEMP_PASSWORD_EMAIL_DISABLED = "WORKGROUP_OPTION_TEMP_PASSWORD_EMAIL_DISABLED";
    public static final String WORKGROUP_OPTION_SESSION_IDLE_TIMEOUT = "WORKGROUP_OPTION_SESSION_IDLE_TIMEOUT";
    public static final String WORKGROUP_OPTION_DO_NOT_ALLOW_USERS_RESET_PASSWORD = "WORKGROUP_OPTION_DO_NOT_ALLOW_USERS_RESET_PASSWORD";
    public static final String WORKGROUP_OPTION_DISABLE_ZENDESK_SUPPORT = "WORKGROUP_OPTION_DISABLE_ZENDESK_SUPPORT";
    public static final String WORKGROUP_OPTION_ENABLE_FTP_FILE_UPLOAD = "WORKGROUP_OPTION_ENABLE_FTP_FILE_UPLOAD";
    public static final String WORKGROUP_OPTION_MIXED_EMAIL_FORMAT = "WORKGROUP_OPTION_MIXED_EMAIL_FORMAT";

    // search related workgroup options
    public static final String WORKGROUP_OPTION_SEARCH_TIMELIMIT = "WORKGROUP_OPTION_SEARCH_TIMELIMIT";

    // pdf annotation (fdf) related workgroup options
    public static final String WORKGROUP_OPTION_ANNOTATIONS = "WORKGROUP_OPTION_ANNOTATIONS";
    public static final String WORKGROUP_OPTION_ANNOTATIONS_REDIRECT = "WORKGROUP_OPTION_ANNOTATIONS_REDIRECT";
    public static final String WORKGROUP_OPTION_ANNOTATIONS_REPOSITORY = "WORKGROUP_OPTION_ANNOTATIONS_REPOSITORY";

    // shipment workgroup options available at user's MyGroup->Workgroup Options->Shipment settings
    public static final String WORKGROUP_OPTION_SHOW_SHIPMENT_DETAIL_IN_SPEC = "WORKGROUP_OPTION_SHOW_SHIPMENT_DETAIL_IN_SPEC";
    public static final String WORKGROUP_OPTION_SHIPMENT_DEFAULT_SAMPLE_QTY = "WORKGROUP_OPTION_SHIPMENT_DEFAULT_SAMPLE_QTY";
    public static final String WORKGROUP_OPTION_SHIPMENT_DEFAULT_PROOF_QTY = "WORKGROUP_OPTION_SHIPMENT_DEFAULT_PROOF_QTY";
    public static final String WORKGROUP_OPTION_SHIPMENT_DEFAULT_DELIVERY_DATE_OFFSET = "WORKGROUP_OPTION_SHIPMENT_DEFAULT_DELIVERY_DATE_OFFSET";
    public static final String WORKGROUP_OPTION_SHIPMENT_DEFAULT_WEIGHT_UOM = "WORKGROUP_OPTION_SHIPMENT_DEFAULT_WEIGHT_UOM";
    public static final String WORKGROUP_OPTION_SHIPMENT_DEFAULT_SHIPPING_UOM = "WORKGROUP_OPTION_SHIPMENT_DEFAULT_SHIPPING_UOM";
    public static final String WORKGROUP_OPTION_ALLOW_SUPPLIER_ADD_DELIVERY_LOC = "WORKGROUP_OPTION_ALLOW_SUPPLIER_ADD_DELIVERY_LOC";

    // Shipment filter
    public static final String SHIPMENT_FILTER_VIEW_SHIPMENT_FOR_PREF = "SHIPMENT_FILTER_VIEW_SHIPMENT_FOR_PREF";

    // report related options
    public static final String WORKGROUP_OPTION_DASHBOARD_REPORTS = "WORKGROUP_OPTION_DASHBOARD_REPORTS";
    public static final String WORKGROUP_OPTION_HIERARCHICAL_WORKGROUP_REPORTS = "WORKGROUP_OPTION_HIERARCHICAL_WORKGROUP_REPORTS";
    public static final String WORKGROUP_OPTION_UNCUT_SHIPMENT_ORDER_REPORT = "WORKGROUP_OPTION_UNCUT_SHIPMENT_ORDER_REPORT";
    public static final String WORKGROUP_OPTION_ADVANCED_REPORTS = "WORKGROUP_OPTION_ADVANCED_REPORTS";

    // Workgroup file preference options
    public static final String WORKGROUP_OPTION_DEFAULT_SHARING = "WORKGROUP_OPTION_DEFAULT_SHARING";
    public static final String WORKGROUP_OPTION_DEFAULT_DOCUMENT_PRIVILEGES = "WORKGROUP_OPTION_DEFAULT_DOCUMENT_PRIVILEGES";

    // Sealed Bidding Options
    public static final String WORKGROUP_OPTION_SEALED_BID = "WORKGROUP_OPTION_SEALED_BID";
    public static final String WORKGROUP_OPTION_SEALED_BID_DATE_ONLY = "WORKGROUP_OPTION_SEALED_BID_DATE_ONLY";

    //Supplier Selected Workgroups
    public static final String SUPPLIER_FILTER_WORKGROUP_LIMITED_PREF = "SUPPLIER_FILTER_WORKGROUP_LIMITED_PREF";
    public static final String SUPPLIER_FILTER_WORKGROUP_PREF = "SUPPLIER_FILTER_WORKGROUP_PREF";

    //Job Spec User State Filter
    public static final String JOB_SPEC_USER_STATE_SHOW_ARCHIVED_PREF = "JOB_SPEC_USER_STATE_SHOW_ARCHIVED_PREF";
    public static final String JOB_SPEC_SHOW_ALL_JOBS_PREF = "JOB_SPEC_SHOW_ALL_JOBS_PREF";
    public static final String JOB_SPEC_SEARCH_FIELD_PREF = "JOB_SPEC_SEARCH_FIELD_PREF";
    public static final String JOB_SPEC_DATE_FIELD_PREF = "JOB_SPEC_DATE_FIELD_PREF";
    public static final String JOB_SPEC_DATE_FROM_PREF = "JOB_SPEC_DATE_FROM_PREF";
    public static final String JOB_SPEC_DATE_TO_PREF = "JOB_SPEC_DATE_TO_PREF";
    public static final String JOB_SPEC_CREATED_BY_PREF = "JOB_SPEC_CREATED_BY_PREF";
    public static final String JOB_SPEC_CREATED_BY_LIMITED_PREF = "JOB_SPEC_CREATED_BY_LIMITED_PREF";

    //Psf Filter
    public static final String PSF_FILTER_SEARCH_FIELD_PREF = "PSF_FILTER_SEARCH_FIELD_PREF";
    public static final String PSF_FILTER_DATE_FIELD_PREF = "PSF_FILTER_DATE_FIELD_PREF";
    public static final String PSF_FILTER_DATE_FROM_PREF = "PSF_FILTER_DATE_FROM_PREF";
    public static final String PSF_FILTER_DATE_TO_PREF = "PSF_FILTER_DATE_TO_PREF";
    public static final String PSF_FILTER_STATE_LIMITED_PREF = "PSF_FILTER_STATE_LIMITED_PREF";
    public static final String PSF_FILTER_STATE_PREF = "PSF_FILTER_STATE_PREF";

    //Psf Workgroup Preferences
    public static final String WORKGROUP_OPTION_PSF_PRICING = "WORKGROUP_OPTION_PSF_PRICING";
    public static final String WORKGROUP_OPTION_PSF_SPEC_TEMPLATE = "WORKGROUP_OPTION_PSF_SPEC_TEMPLATE";
    public static final String WORKGROUP_OPTION_PSF_DIGITAL_PDF_PREFLIGHTING = "WORKGROUP_OPTION_PSF_DIGITAL_PDF_PREFLIGHTING";
    public static final String WORKGROUP_OPTION_PSF_DIGITAL_SUPPLIER_DIRECT = "WORKGROUP_OPTION_PSF_DIGITAL_SUPPLIER_DIRECT";
    public static final String WORKGROUP_OPTION_PSF_LANDING_PAGE = "WORKGROUP_OPTION_PSF_LANDING_PAGE";
    public static final String WORKGROUP_OPTION_PSF_LANDING_PAGE_INTEGRATION = "WORKGROUP_OPTION_PSF_LANDING_PAGE_INTEGRATION";

    //Noosh Lite Workgroup Preferences
    public static final String WORKGROUP_OPTION_ENABLE_NGE = "WORKGROUP_OPTION_ENABLE_NGE";
    public static final String WORKGROUP_OPTION_NL_SITE_MANAGER = "WORKGROUP_OPTION_NL_SITE_MANAGER";
    public static final String WORKGROUP_OPTION_ENABLE_NGE_SMART_FORM_MANAGEMENT = "WORKGROUP_OPTION_ENABLE_NGE_SMART_FORM_MANAGEMENT";
    public static final String WORKGROUP_OPTION_NGE_HIDE_BRANDING = "WORKGROUP_OPTION_NGE_HIDE_BRANDING";

    public static final String WORKGROUP_OPTION_SUPPLIER_NETWORK = "WORKGROUP_OPTION_SUPPLIER_NETWORK";
    public static final String WORKGROUP_OPTION_SHOW_ONBOARDING_BANNER = "WORKGROUP_OPTION_SHOW_ONBOARDING_BANNER";

    //Internal List Accounts Filter
    public static final String ACCOUNT_FILTER_SEARCH_FIELD_PREF = "ACCOUNT_FILTER_SEARCH_FIELD_PREF";
    public static final String ACCOUNT_FILTER_ACCOUNT_TYPE_PREF = "ACCOUNT_FILTER_ACCOUNT_TYPE_PREF";
    public static final String ACCOUNT_FILTER_GROUP_TYPE_PREF = "ACCOUNT_FILTER_GROUP_TYPE_PREF";
    public static final String ACCOUNT_FILTER_DATE_FIELD_PREF = "ACCOUNT_FILTER_DATE_FIELD_PREF";
    public static final String ACCOUNT_FILTER_DATE_FROM_PREF = "ACCOUNT_FILTER_DATE_FROM_PREF";
    public static final String ACCOUNT_FILTER_DATE_TO_PREF = "ACCOUNT_FILTER_DATE_TO_PREF";

    //Order Deskoid dropdown Preference
    public static final String PROJECT_ORDER_DESKOID_SHOW_PREF = "PROJECT_ORDER_DESKOID_SHOW_PREF";

    //Project default client user preference
    public static final String PROJECT_DEFAULT_CLIENT_USER_PREF = "PROJECT_DEFAULT_CLIENT_USER_PREF";

    //Site Project Info preference
    public static final String SITE_PROJECT_INFO_STATUS_PREF = "SITE_PROJECT_INFO_STATUS_PREF";

    public static final String USER_PROJECT_FILTER_PREF = "USER_PROJECT_FILTER_PREF";
    public static final String USER_PROJECT_STATE_FILTER_PREF = "USER_PROJECT_STATE_FILTER_PREF";
    public static final String USER_GOOGLE_CONTACTS_IMPORT_ALL = "USER_GOOGLE_CONTACTS_IMPORT_ALL";

    //Large Campaign spec grid columns display user preference
    public static final String LC_SPEC_GRID_USER_PREF = "LC_SPEC_GRID_USER_PREF";
    public static final String LC_PROJECT_GRID_USER_PREF = "LC_PROJECT_GRID_USER_PREF";
    public static final String LC_SPEC_TEMPLATE_FILTER_USER_PREF = "LC_SPEC_FILTER_USER_PREF";

    //FTP FILE UPLOAD DEFAULT SETTINGS
    public static final String FTP_DEFAULT_SETTINGS_URL = "FTP_DEFAULT_SETTINGS_URL";
    public static final String FTP_DEFAULT_SETTINGS_PROTOCOL = "FTP_DEFAULT_SETTINGS_PROTOCOL";
    public static final String FTP_DEFAULT_SETTINGS_HOST = "FTP_DEFAULT_SETTINGS_HOST";
    public static final String FTP_DEFAULT_SETTINGS_PORT = "FTP_DEFAULT_SETTINGS_PORT";
    public static final String FTP_DEFAULT_SETTINGS_PATH = "FTP_DEFAULT_SETTINGS_PATH";
    public static final String FTP_DEFAULT_SETTINGS_LOGINNAME = "FTP_DEFAULT_SETTINGS_LOGINNAME";
    public static final String FTP_DEFAULT_SETTINGS_PASSWORD = "FTP_DEFAULT_SETTINGS_PASSWORD";
    public static final String FTP_DEFAULT_IS_ENABLE_OVERRIDE_PL_HT_PT = "FTP_DEFAULT_IS_ENABLE_OVERRIDE_PL_HT_PT";
    public static final String FTP_DEFAULT_IS_ENABLE_OVERRIDE_PH = "FTP_DEFAULT_IS_ENABLE_OVERRIDE_PH";

    //Noosh Enterprise Terms
    public static final String WORKGROUP_OPTION_RFE_TC = "WORKGROUP_OPTION_RFE_TC";
    public static final String WORKGROUP_OPTION_ESTIMATE_TC = "WORKGROUP_OPTION_ESTIMATE_TC";
    public static final String WORKGROUP_OPTION_PURCHASE_TC = "WORKGROUP_OPTION_PURCHASE_TC";
    public static final String WORKGROUP_OPTION_ORDER_TC = "WORKGROUP_OPTION_ORDER_TC";
    public static final String WORKGROUP_OPTION_PROPOSAL_TC = "WORKGROUP_OPTION_PROPOSAL_TC";

    public static final String WORKGROUP_OPTION_PROJECT_OVERVIEW_SHOW_CLIENT_OWNER = "WORKGROUP_OPTION_PROJECT_OVERVIEW_SHOW_CLIENT_OWNER";
    public static final String WORKGROUP_OPTION_BASELINE_FIELDS = "WORKGROUP_OPTION_BASELINE_FIELDS";
    public static final String WORKGROUP_OPTION_ENABLE_NGE_FILE_UI = "WORKGROUP_OPTION_ENABLE_NGE_FILE_UI";
    public static final String WORKGROUP_OPTION_NGE_ENABLE_SELLING = "WORKGROUP_OPTION_NGE_ENABLE_SELLING";
    public static final String WORKGROUP_OPTION_NGE_ENABLE_QUOTES = "WORKGROUP_OPTION_NGE_ENABLE_QUOTES";
    public static final String WORKGROUP_OPTION_NGE_ENABLE_BUYING = "WORKGROUP_OPTION_NGE_ENABLE_BUYING";
    public static final String WORKGROUP_OPTION_NGE_ENABLE_PO = "WORKGROUP_OPTION_NGE_ENABLE_PO";
    public static final String WORKGROUP_OPTION_NGE_ENABLE_RFE = "WORKGROUP_OPTION_NGE_ENABLE_RFE";
    public static final String WORKGROUP_OPTION_NGE_ENABLE_MARKUP = "WORKGROUP_OPTION_NGE_ENABLE_MARKUP";
    public static final String WORKGROUP_OPTION_NGE_DEFAULT_MARKUP = "WORKGROUP_OPTION_NGE_DEFAULT_MARKUP";
    public static final String WORKGROUP_OPTION_NGE_ENABLE_MARGIN = "WORKGROUP_OPTION_NGE_ENABLE_MARGIN";
    public static final String WORKGROUP_OPTION_NGE_DEFAULT_MARGIN = "WORKGROUP_OPTION_NGE_DEFAULT_MARGIN";
    public static final String WORKGROUP_OPTION_NGE_QUOTE_TERMS_CONDITION = "WORKGROUP_OPTION_NGE_QUOTE_TERMS_CONDITION";
    public static final String WORKGROUP_OPTION_NGE_QUOTE_TERMS_CONDITION_SIGNATURE = "WORKGROUP_OPTION_NGE_QUOTE_TERMS_CONDITION_SIGNATURE";
    public static final String WORKGROUP_OPTION_NGE_SELL_ORDER_TERMS_CONDITION = "WORKGROUP_OPTION_NGE_SELL_ORDER_TERMS_CONDITION";
    public static final String WORKGROUP_OPTION_NGE_SELL_ORDER_TERMS_CONDITION_ACCEPTANCE = "WORKGROUP_OPTION_NGE_SELL_ORDER_TERMS_CONDITION_ACCEPTANCE";
    public static final String WORKGROUP_OPTION_NGE_BUYING_ORDER_TERMS_CONDITION = "WORKGROUP_OPTION_NGE_BUYING_ORDER_TERMS_CONDITION";
    public static final String WORKGROUP_OPTION_NGE_SHIPPING_IN_TAX = "WORKGROUP_OPTION_NGE_SHIPPING_IN_TAX";
    public static final String WORKGROUP_OPTION_NGE_DEFAULT_TAX = "WORKGROUP_OPTION_NGE_DEFAULT_TAX";
    public static final String WORKGROUP_OPTION_NGE_UNIT_PRICE_PRECISION = "WORKGROUP_OPTION_NGE_UNIT_PRICE_PRECISION";

    public static final String WORKGROUP_OPTION_ALLOW_RC_QUOTE_AS_SOURCE = "WORKGROUP_OPTION_ALLOW_RC_QUOTE_AS_SOURCE";
//    public static final String WORKGROUP_OPTION_IMPORT_PRICE_SHEET = "WORKGROUP_OPTION_IMPORT_PRICE_SHEET";
//    public static final String WORKGROUP_OPTION_ENABLE_ADDITEM = "WORKGROUP_OPTION_ENABLE_ADDITEM";

    public static final String USER_NGE_SHOW_ONBOARDING_MESSAGE = "USER_NGE_SHOW_ONBOARDING_MESSAGE";

    // add deskoid expand state
    public static final String PROJECT_DESKOID_EXPANSION_STATE = "PROJECT_DESKOID_EXPANSION_STATE";
    public static final String DOCUMENT_LIST_DESKOID_EXPASION_STATE = "DOCUMENT_LIST_DESKOID_EXPASION_STATE";

    // switch ui to NGE style - new noosh branding
    public static final String WORKGROUP_OPTION_MODERN_NOOSH_UI = "WORKGROUP_OPTION_MODERN_NOOSH_UI";
    public static final String WORKGROUP_OPTION_USE_PROOFHQ = "WORKGROUP_OPTION_USE_PROOFHQ";
    //For POINT file preFlighting
    public static final String WORKGROUP_OPTION_INTEGRATIONS_CALLAS_SDK_PREFLIGHT = "WORKGROUP_OPTION_INTEGRATIONS_CALLAS_SDK_PREFLIGHT";
    //For noosh one
    public static final String MYDESK_NOOSH_ONE_LAYOUT = "MYDESK_NOOSH_ONE_LAYOUT";
    public static final String MOBILE_NOOSH_ONE_SETTINGS = "MOBILE_NOOSH_ONE_SETTINGS";
    public static final String WORKGROUP_OPTION_SPEC_CREATE_FROM_CSV = "WORKGROUP_OPTION_SPEC_CREATE_FROM_CSV";
    public static final String OVERVIEW_DESKOID_NOOSH_ONE_LAYOUT = "OVERVIEW_DESKOID_NOOSH_ONE_LAYOUT";
    public static final String MYDESK_NOOSH_ONE_CALENDAR = "MYDESK_NOOSH_ONE_CALENDAR";
    public static final String MYDESK_NOOSH_ONE_TASK = "MYDESK_NOOSH_ONE_TASK";
    public static final String MYDESK_NOOSH_ONE_ORDER = "MYDESK_NOOSH_ONE_ORDER";
    public static final String MYDESK_NOOSH_ONE_QUOTE = "MYDESK_NOOSH_ONE_QUOTE";
    public static final String MYDESK_NOOSH_ONE_ESTIMATE = "MYDESK_NOOSH_ONE_ESTIMATE";
    public static final String MYDESK_NOOSH_ONE_MESSAGE = "MYDESK_NOOSH_ONE_MESSAGE";
    public static final String MYDESK_NOOSH_ONE_ACTIVITY = "MYDESK_NOOSH_ONE_ACTIVITY";
    public static final String MYDESK_NOOSH_ONE_INVOICE = "MYDESK_NOOSH_ONE_INVOICE";
    public static final String MYDESK_NOOSH_ONE_RFQ = "MYDESK_NOOSH_ONE_RFQ";
    public static final String MYDESK_NOOSH_ONE_CLIENT_PROJECT = "MYDESK_NOOSH_ONE_CLIENT_PROJECT";
    public static final String MYDESK_NOOSH_ONE_RECENT_PROJECT = "MYDESK_NOOSH_ONE_RECENT_PROJECT";
//    public static final String MYDESK_NOOSH_ONE_MRM = "MYDESK_NOOSH_ONE_MRM";
//    public static final String MYDESK_NOOSH_ONE_MAIN_CHART = "MYDESK_NOOSH_ONE_MAIN_CHART";
    public static final String MYDESK_NOOSH_ONE_RFE = "MYDESK_NOOSH_ONE_RFE";
    public static final String WORKGROUP_OPTION_SHOW_BETA = "WORKGROUP_OPTION_SHOW_BETA";
    public static final String NOOSH_ONE_LEFTNAVBAR = "NOOSH_ONE_LEFTNAVBAR";
    public static final String PROJECT_OVERVIEW_NOOSH_ONE_EVENT = "PROJECT_OVERVIEW_NOOSH_ONE_EVENT";
    public static final String PROJECT_OVERVIEW_NOOSH_ONE_TASK = "PROJECT_OVERVIEW_NOOSH_ONE_TASK";
    public static final String SPECS_NOOSH_ONE_FILTER = "SPECS_NOOSH_ONE_FILTER";
    public static final String SEARCH_NOOSH_ONE_DATE_FROM = "SEARCH_NOOSH_ONE_DATE_FROM";
    public static final String SEARCH_NOOSH_ONE_DATE_TO = "SEARCH_NOOSH_ONE_DATE_TO";
    public static final String SEARCH_NOOSH_ONE_DATE_TYPE = "SEARCH_NOOSH_ONE_DATE_TYPE";
    public static final String SEARCH_NOOSH_ONE_QUERY_OBJECTS = "SEARCH_NOOSH_ONE_QUERY_OBJECTS";
    public static final String SEARCH_NOOSH_ONE_COG_FILTER = "SEARCH_NOOSH_ONE_COG_FILTER";
    public static final String WORKGROUP_OPTION_PROJECT_OVERVIEW_XCOMMENTS = "WORKGROUP_OPTION_PROJECT_OVERVIEW_XCOMMENTS";
    public static final String WORKGROUP_OPTION_PROJECT_OVERVIEW_XDESCRIPTION = "WORKGROUP_OPTION_PROJECT_OVERVIEW_XDESCRIPTION";
    public static final String WORKGROUP_OPTION_PROJECT_OVERVIEW_XCLIENT = "WORKGROUP_OPTION_PROJECT_OVERVIEW_XCLIENT";

    public static final String PS_ROUTE_CCO_EXCLUDE_TAX_SHIPPING = "PS_ROUTE_CCO_EXCLUDE_TAX_SHIPPING";
    //NooshOne new my desk page
    public static final String WORKGROUP_OPTION_NOOSHONE_MY_DESK = "WORKGROUP_OPTION_NOOSHONE_MY_DESK";

    public static final String WHITELIST_ALLOW_PARTNER_NOOSHONE_SUPPLIER = "WHITELIST_ALLOW_PARTNER_NOOSHONE_SUPPLIER";
    public static final String WHITELIST_OVERRIDE_PARTNER = "WHITELIST_OVERRIDE_PARTNER";
    public static final String WHITELIST_PROJECT_TRACKING_LIST = "WHITELIST_PROJECT_TRACKING_LIST";
    public static final String WHITELIST_PROJECT_OVERVIEW = "WHITELIST_PROJECT_OVERVIEW";
    public static final String WHITELIST_PROJECT_MANAGER = "WHITELIST_PROJECT_MANAGER";
    public static final String WHITELIST_PROJECT_SPEC_LIST = "WHITELIST_PROJECT_SPEC_LIST";
    public static final String WHITELIST_REACT_SPEC_LIST = "WHITELIST_REACT_SPEC_LIST";
    public static final String WHITELIST_PROJECT_RFE_ESTIMATE_LIST = "WHITELIST_PROJECT_RFE_ESTIMATE_LIST";
    public static final String WHITELIST_PROJECT_ORDER_LIST = "WHITELIST_PROJECT_ORDER_LIST";
    public static final String WHITELIST_REACT_ORDER_LIST = "WHITELIST_REACT_ORDER_LIST";
    public static final String WHITELIST_PROJECT_MESSAGE_LIST = "WHITELIST_PROJECT_MESSAGE_LIST";
    public static final String WHITELIST_ORDER_DETAIL = "WHITELIST_ORDER_DETAIL";
    public static final String WHITELIST_REACT_ORDER_DETAIL = "WHITELIST_REACT_ORDER_DETAIL";
    public static final String WHITELIST_CHANGE_ORDER_DETAIL = "WHITELIST_CHANGE_ORDER_DETAIL";
    public static final String WHITELIST_REACT_CHANGE_ORDER_DETAIL = "WHITELIST_REACT_CHANGE_ORDER_DETAIL";
    public static final String WHITELIST_SUPPLIER_RATING = "WHITELIST_SUPPLIER_RATING";
    public static final String WHITELIST_SPEC_MANAGER = "WHITELIST_SPEC_MANAGER";
    public static final String WHITELIST_PROJECT_QUOTE_LIST = "WHITELIST_PROJECT_QUOTE_LIST";
    public static final String WHITELIST_QUOTE_MANAGER = "WHITELIST_QUOTE_MANAGER";
    public static final String WHITELIST_INVOICE_MANAGER = "WHITELIST_INVOICE_MANAGER";
    public static final String WHITELIST_RFE_MANAGER = "WHITELIST_RFE_MANAGER";
    public static final String WHITELIST_ESTIMATE_MANAGER = "WHITELIST_ESTIMATE_MANAGER";
    public static final String WHITELIST_JOB_ID_MANAGER = "WHITELIST_JOB_ID_MANAGER";
    public static final String WHITELIST_RFE_DETAIL = "WHITELIST_RFE_DETAIL";
    public static final String WHITELIST_PROJECT_INVOICE_LIST = "WHITELIST_PROJECT_INVOICE_LIST";
    public static final String WHITELIST_PROJECT_INVOICE_DETAIL = "WHITELIST_PROJECT_INVOICE_DETAIL";
    public static final String WHITELIST_ORDER_MANAGER = "WHITELIST_ORDER_MANAGER";
    public static final String WHITELIST_RFQ_MANAGER = "WHITELIST_RFQ_MANAGER";
    public static final String WHITELIST_USER_INFO = "WHITELIST_USER_INFO";
    public static final String WHITELIST_PROJECT_TEAM_LIST = "WHITELIST_PROJECT_TEAM_LIST";
    public static final String WHITELIST_PROJECT_TASK_LIST = "WHITELIST_PROJECT_TASK_LIST";
    public static final String WHITELIST_REACT_ADDRESS_BOOK = "WHITELIST_REACT_ADDRESS_BOOK";
    public static final String WHITELIST_REACT_CREATE_RFE = "WHITELIST_REACT_CREATE_RFE";
    public static final String WHITELIST_REACT_ELASTICSEARCH = "WHITELIST_REACT_ELASTICSEARCH";
    public static final String WHITELIST_REACT_REPORT_LIST = "WHITELIST_REACT_REPORT_LIST";
    public static final String WHITELIST_FILE_MANAGER = "WHITELIST_FILE_MANAGER";
    public static final String WHITELIST_REACT_MY_GROUP = "WHITELIST_REACT_MY_GROUP";
    public static final String WORKGROUP_OPTION_IMPORT_PRICE_SHEET = "WORKGROUP_OPTION_IMPORT_PRICE_SHEET";
    public static final String WORKGROUP_OPTION_BASELINE = "WORKGROUP_OPTION_BASELINE";
    public static final String WORKGROUP_OPTION_SHOW_SPEC_PRICE_LOOKUP = "WORKGROUP_OPTION_SHOW_SPEC_PRICE_LOOKUP";
    public static final String WORKGROUP_OPTION_MULTIPLE_CURRENCY = "WORKGROUP_OPTION_MULTIPLE_CURRENCY";
    public static final String WORKGROUP_OPTION_NGE_HIDE_NEW_ACCOUNT_SETTINGS_LINK = "WORKGROUP_OPTION_NGE_HIDE_NEW_ACCOUNT_SETTINGS_LINK";
    public static final String WORKGROUP_OPTION_NGE_HIDE_CLIENT_WORKSPACES_LINK = "WORKGROUP_OPTION_NGE_HIDE_CLIENT_WORKSPACES_LINK";
}
