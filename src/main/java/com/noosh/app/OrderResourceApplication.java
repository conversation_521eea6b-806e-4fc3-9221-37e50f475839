package com.noosh.app;

import com.noosh.app.commons.entity.security.Locale;
import com.noosh.app.repository.jpa.security.LocaleRepository;
import com.noosh.app.service.util.I18NUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.MessageSource;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.EnableAsync;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * User: leilaz
 * Date: 9/23/19
 */
@EnableDiscoveryClient
@SpringBootApplication
@EnableFeignClients
@EnableAsync
public class OrderResourceApplication {
    private final Logger logger = LoggerFactory.getLogger(OrderResourceApplication.class);

    @Autowired
    private Environment env;
    @Autowired
    private MessageSource messageSource;
    @Autowired
    private LocaleRepository localeRepository;
    @Autowired
    private I18NUtils i18nUtils;

    private static Map<String, String> properties = new HashMap();

    @PostConstruct
    public void initApplication() throws IOException {
        properties.put("instance", env.getProperty("noosh.inst.name"));
        properties.put("enterpriseDomain", env.getProperty("enterprise.domain"));
        properties.put("awsUri", env.getProperty("aws.s3.uri"));
        properties.put("hrefEncryption", env.getProperty("server.href.encryption"));
        properties.put("hrefEncryptionKey", env.getProperty("server.href.key"));

        initI18nDefaultData();
        asyncInitI18nRemainingData();
    }

    public static void main(String[] args) {
        SpringApplication.run(OrderResourceApplication.class, args);
    }

    public static String getInstance(){
        return properties.get("instance");
    }

    public static String getAWSUri(){
        return properties.get("awsUri");
    }

    public static boolean getHrefEncryption(){
        return properties.get("hrefEncryption").equalsIgnoreCase("true");
    }

    public static String getHrefEncryptionKey(){
        return properties.get("hrefEncryptionKey");
    }

    public static String getEnterpriseDomain() {
        return properties.get("enterpriseDomain");
    }

    private void initI18nDefaultData() {
        if (env.getProperty("server.i18nLoad").equalsIgnoreCase("true")) {
            logger.info("Message Source: Initializing message source for " + I18NUtils.DEFAULT_LOCALE.toString());
            Long now = System.currentTimeMillis();
            messageSource.getMessage("error.title", new String[] {}, I18NUtils.DEFAULT_LOCALE);
            Long after = System.currentTimeMillis();
            logger.info("Message Source: initialization completed for " + I18NUtils.DEFAULT_LOCALE.toString() + " in " + (after-now) + " ms");
        }
    }

    private void asyncInitI18nRemainingData() {
        if (env.getProperty("server.i18nLoad").equalsIgnoreCase("true")) {
            List<Locale> locales = localeRepository.findAll(Sort.by(Sort.Direction.ASC, "id"));
            logger.info("Message Source: Async Initializing message source for " + (locales.size() - 1) + " locales");
            locales.stream().forEach(l -> {
                if (!l.getLocaleCode().toUpperCase().equals(I18NUtils.DEFAULT_LOCALE.toString().toUpperCase())) {
                    i18nUtils.asyncInit18nDataByLocale(l);
                }
            });
        }
    }
}
