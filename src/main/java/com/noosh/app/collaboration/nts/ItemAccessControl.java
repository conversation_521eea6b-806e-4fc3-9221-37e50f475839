package com.noosh.app.collaboration.nts;

import com.noosh.app.collaboration.nfc.security.*;
import com.noosh.app.collaboration.nfc.util.ListMap;
import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.PreferredRoleTokenID;
import com.noosh.app.commons.vo.message.MessageCreateVO;
import com.noosh.app.commons.vo.team.TeamMemberVO;
import com.noosh.app.commons.vo.team.TeamVO;

import java.util.*;

/**
 * Capture the access control information about an item being
 * posted or sent to a parent object
 */
public class ItemAccessControl implements java.io.Serializable {

    private Set users = new HashSet();
    private Set groups = new HashSet();
    private Set teams = new HashSet();

    private boolean isPublic = false;
    protected ListMap permissions = new ListMap();
    private long slaveProjectClassId;
    private long preferredRoleTokenId;


    public ItemAccessControl() {
        this(ObjectClassID.PROJECT_CLASS_SUPPLIER, PreferredRoleTokenID.PROJECT_SUPPLIER);
    }

    public ItemAccessControl(long slaveProjectClassId, long preferredRoleTokenId) {
        this.slaveProjectClassId = slaveProjectClassId;
        this.preferredRoleTokenId = preferredRoleTokenId;
    }

    public ItemAccessControl(User[] userArray) {
        this();
        addUsers(userArray);
    }


    public boolean isEmpty() {
        return users.isEmpty() && groups.isEmpty() && teams.isEmpty() && permissions.isEmpty();
    }


    public ItemAccessControl addUsers(User[] users) {
        for (int i = 0; i < users.length; i++) {
            this.users.add(users[i]);
            // if the user does not have a group object set,
            // then do _not_ add an empty group with id = 0.
            // Other modules depend on this being the case.
            if (users[i].getGroupId() > 0) {
                addGroup(users[i].getGroupId());
            }
        }
        return this;
    }

    public ItemAccessControl addUser(User user) {
        return addUsers(new User[]{user});
    }

    public ItemAccessControl addGroup(long groupId) {
        this.groups.add(new Group(groupId));
        return this;
    }

    public ItemAccessControl addTeam(Team team) {
        this.teams.add(team);
        return this;
    }

    public boolean containsUser(long userId) {
        return this.users.contains(new User(userId));
    }

    public boolean containsGroup(long groupId) {
        return this.groups.contains(new Group(groupId));
    }

    public boolean containsTeam(Team team) {
        return this.teams.contains(team);
    }

    public Group[] getGroups() {
        return (Group[]) groups.toArray(new Group[groups.size()]);
    }

    public User[] getUsers() {
        return (User[]) users.toArray(new User[users.size()]);
    }

    public Team[] getTeams() {
        return (Team[]) teams.toArray(new TeamBean[teams.size()]);
    }

    public User[] forGroup(long groupId) {
        List result = new ArrayList();
        Iterator iterator = users.iterator();
        while (iterator.hasNext()) {
            User user = (User) iterator.next();
            if (user.getGroupId() == groupId) result.add(user);
        }
        return (User[]) result.toArray(new User[result.size()]);
    }


    //permission handling

    public Permission[] getPermissionsFor(Object grantee) {
        List perms = permissions.getList(grantee);
        return (Permission[]) perms.toArray(new Permission[perms.size()]);
    }

    public void addPermission(User user, Permission perm) {
        permissions.add(user, perm);
        addUsers(new User[]{user});
    }

    public void addPermission(Group group, Permission perm) {
        permissions.add(group, perm);
        addGroup(group.getGroupId());
    }

    public void addPermission(Team team, Permission perm) {
        permissions.add(team, perm);
        addTeam(team);
    }

    public void addPermission(Role role, Permission perm) {
        permissions.add(role, perm);
    }

    public Object[] getGrantees() {
        return permissions.keySet().toArray();
    }

    public long getSlaveProjectClassId() {
        return this.slaveProjectClassId;
    }

    /**
     * Preferred role token - assume SUPPLIER if not explicitly set
     */
    public long getPreferredRoleTokenId() {
        if (this.preferredRoleTokenId > 0) {
            return this.preferredRoleTokenId;
        } else {
            return PreferredRoleTokenID.PROJECT_SUPPLIER;
        }
    }

    public void setAttributes(MessageCreateVO createVO, Permission perm) {

        if (createVO.isAllProjectTeams()) {
            setIsPublic(true);
        }

        List<TeamVO> teamVOList = createVO.getSendTo();
        Set<Long> userIdSet = new HashSet<Long>();
        for (TeamVO teamVO : teamVOList) {
            //adding teams
            if (teamVO.getIsAllTeamMembersChecked()) {
                TeamBean team = new TeamBean();
                team.setId(teamVO.getTeamId());
                this.addPermission(team, perm);

                continue;
            }

            List<TeamMemberVO> teamMemberVOList = teamVO.getTeamMembers();
            for (TeamMemberVO teamMemberVO : teamMemberVOList) {
                if (teamMemberVO.getUserId() != null && !userIdSet.contains(teamMemberVO.getUserId())) {
                    userIdSet.add(teamMemberVO.getUserId());
                    this.addPermission(new User(teamMemberVO.getUserId()), perm);
                }
            }
        }
    }

    public boolean isPublic() {
        return this.isPublic;
    }

    public void setIsPublic(boolean isPublic) {
        this.isPublic = isPublic;
    }


}

