package com.noosh.app.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "webresource")
public interface WebOpenFeignClient {

    @GetMapping(value = "/api/feign/workgroup/logo")
    String getWorkgroupLogo(@RequestParam(value = "workgroupId") Long workgroupId);
}
