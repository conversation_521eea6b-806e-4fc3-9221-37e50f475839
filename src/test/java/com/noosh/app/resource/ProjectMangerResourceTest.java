package com.noosh.app.resource;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.service.project.ProjectManagerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.mock;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * <AUTHOR> Shan
 * @since 0.0.1
 */
@ExtendWith(MockitoExtension.class)
public class ProjectMangerResourceTest {

    private MockMvc mockMvc;

    @Mock
    private ProjectManagerService projectManagerService;

    @InjectMocks
    private ProjectManagerResource projectManagerResource;


    private List<DropdownVO<Long>> mockShowOptions;
    private List<DropdownVO<Long>> mockShowOptionsWithoutWorkgroups;

    @BeforeEach
    public void init() {

        mockMvc = MockMvcBuilders.standaloneSetup(projectManagerResource).build();

        DropdownVO<Long> myProjectOption = new DropdownVO<>(ObjectClassID.PERSONAL_PROJECTS,
                "My projects");

        DropdownVO<Long> hotProjectOption = new DropdownVO<>(ObjectClassID.ALL_HOT_PROJECTS,
                "Hot projects");

        DropdownVO<Long> allProjectOption = new DropdownVO<>(ObjectClassID.WORKGROUP_PROJECTS,
                "All accessible projects");

        mockShowOptions = new ArrayList<>();
        mockShowOptions.add(myProjectOption);
        mockShowOptions.add(hotProjectOption);
        mockShowOptions.add(allProjectOption);

        mockShowOptionsWithoutWorkgroups = new ArrayList<>();
        mockShowOptionsWithoutWorkgroups.add(myProjectOption);
        mockShowOptionsWithoutWorkgroups.add(hotProjectOption);

    }

    @Test
    public void given_Id_when_getShowOptions_then_expectShowOptions() throws Exception {
        // given
        Map<String, Object> headers = new HashMap<>();
        headers.put("alg", "RS256");
        headers.put("typ", "JWT");

        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", 1L);
        claims.put("workgroupId", 1L);

        Jwt jwt = new Jwt("mockToken", Instant.now(), Instant.now().plusMillis(5000), headers, claims);
        Authentication auth = new JwtAuthenticationToken(jwt);
        SecurityContext securityContext = mock(SecurityContext.class);
        given(securityContext.getAuthentication()).willReturn(auth);
        SecurityContextHolder.setContext(securityContext);

        given(projectManagerService.getShowOptions(1L, 1L))
                .willReturn(mockShowOptions);

        // when
        mockMvc.perform(
                get("/project/showOptions"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[2].id").value(ObjectClassID.WORKGROUP_PROJECTS));

        // then
        then(projectManagerService).should().getShowOptions(1L, 1L);

    }
}
