package com.noosh.app.service;

import com.noosh.app.commons.dto.project.ProjectOwnerDTO;
import com.noosh.app.repository.jpa.project.ProjectRepository;
import com.noosh.app.repository.mybatis.project.ProjectMyBatisMapper;
import com.noosh.app.service.project.ProjectService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;

/**
 * Project Service Test
 *
 * <AUTHOR>
 * @since 0.0.1
 */
@ExtendWith(MockitoExtension.class)
public class ProjectServiceTest {

    @InjectMocks
    private ProjectService projectService;

    @Mock
    private ProjectMyBatisMapper projectMyBatisMapper;
    @Mock
    private ProjectRepository projectRepository;

    List<ProjectOwnerDTO> mockProjectOwnerDTOS;

    @BeforeEach
    public void init() {
        mockProjectOwnerDTOS = new ArrayList<>();
        ProjectOwnerDTO owner1 = new ProjectOwnerDTO();
        owner1.setProjectId(1L);
        owner1.setProjectOwner("Owner 1");
        mockProjectOwnerDTOS.add(owner1);

    }

    @Test
    public void test_findProjectOwnerMapByIds_withResult() {
        // given
        given(projectMyBatisMapper.findAllProjectOwnersByIds(Collections.singletonList(1L)))
                .willReturn(mockProjectOwnerDTOS);

        // when
        Map<Long, List<String>> projectOwnerMap = projectService.findProjectOwnerMapByIds(
                Collections.singletonList(1L));

        // then
        assertThat(projectOwnerMap).hasSize(1);
        then(projectMyBatisMapper).should().findAllProjectOwnersByIds(anyList());
    }

    @Test
    public void test_findProjectOwnerMapByIds_withEmptyResult() {
        // given
        // nothing

        // when
        Map<Long, List<String>> projectOwnerMap = projectService.findProjectOwnerMapByIds(
                Collections.emptyList());

        // then
        assertThat(projectOwnerMap).hasSize(0);
    }

}
