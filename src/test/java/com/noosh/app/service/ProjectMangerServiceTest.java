package com.noosh.app.service;

import com.noosh.app.commons.constant.ObjectClassID;
import com.noosh.app.commons.constant.PermissionID;
import com.noosh.app.commons.constant.StringID;
import com.noosh.app.commons.vo.component.DropdownVO;
import com.noosh.app.service.project.ProjectManagerService;
import com.noosh.app.service.security.PermissionService;
import com.noosh.app.service.util.I18NUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;

/**
 * Project Manger Service Test
 *
 * <AUTHOR>
 * @since 0.0.1
 */
@ExtendWith(MockitoExtension.class)
public class ProjectMangerServiceTest {

    @InjectMocks
    private ProjectManagerService projectManagerService;

    @Mock
    private PermissionService permissionService;
    @Mock
    private I18NUtils i18NUtils;

    private List<DropdownVO<Long>> mockShowOptions;
    private String mock_i18n_my_projects = "My projects";
    private String mock_i18n_hot_projects = "Hot projects";
    private String mock_i18n_all_accessible_projects = "All accessible projects";

    @BeforeEach
    public void init() {
        projectManagerService.init();

        DropdownVO<Long> myProjectOption = new DropdownVO<>(ObjectClassID.PERSONAL_PROJECTS,
                mock_i18n_my_projects);

        DropdownVO<Long> hotProjectOption = new DropdownVO<>(ObjectClassID.ALL_HOT_PROJECTS,
                mock_i18n_hot_projects);

        DropdownVO<Long> allProjectOption = new DropdownVO<>(ObjectClassID.WORKGROUP_PROJECTS,
                mock_i18n_all_accessible_projects);

        mockShowOptions = new ArrayList<>();
        mockShowOptions.add(myProjectOption);
        mockShowOptions.add(hotProjectOption);
        mockShowOptions.add(allProjectOption);

    }

    @Test
    public void given_Id_when_getShowOptions_then_expectShowOptions() {
        // given
        given(permissionService.checkAll(PermissionID.VIEW_WORKGROUP_PROJECTS, 1L, 1L, Long.valueOf(-1)))
                .willReturn(true);

//        given(i18NUtils.getMessage(StringID.PROJECT_MGR_MY_JOBS))
//                .willReturn(mock_i18n_my_projects);
//        given(i18NUtils.getMessage(StringID.ALL_HOT_PROJECTS))
//                .willReturn(mock_i18n_hot_projects);
//        given(i18NUtils.getMessage(StringID.PROJECT_MGR_ALL_JOBS))
//                .willReturn(mock_i18n_all_accessible_projects);

        // when
        List<DropdownVO<Long>> dropdownVOS = projectManagerService.getShowOptions(1L, 1L);

        // then
        assertThat(dropdownVOS).hasSize(3);
        then(permissionService).should().checkAll(any(long.class), any(long.class), any(long.class), any(long.class));
    }

    @Test
    public void test_isSortFieldExist() {
        boolean uppercaseTrue = projectManagerService.isSortFieldExist("PRJ_NAME");
        assertTrue(uppercaseTrue);

        boolean lowercaseTrue = projectManagerService.isSortFieldExist("prj_name");
        assertTrue(lowercaseTrue);

        boolean invalidFalse = projectManagerService.isSortFieldExist("invalid");
        assertFalse(invalidFalse);

    }
}
