package com.noosh.app.service;

import com.noosh.app.service.estimate.EstimateManagerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Estimate manager service test
 *
 * <AUTHOR>
 * @since 0.0.1
 */
@ExtendWith(MockitoExtension.class)
public class EstimateMangerServiceTest {

    @InjectMocks
    private EstimateManagerService estimateManagerService;


    @BeforeEach
    public void init() {
        this.estimateManagerService.init();
    }

    @Test
    public void test1_isSortFieldExist_true() {
        boolean res = estimateManagerService.isSortFieldExist("ref_no");
        assertTrue(res);
    }

    @Test
    public void test1_isSortFieldExist_false() {
        boolean res = estimateManagerService.isSortFieldExist("ref_no1");
        assertTrue(!res);
    }
}
