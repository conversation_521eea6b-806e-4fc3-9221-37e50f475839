package com.noosh.app.service.util;

import com.noosh.app.commons.dto.PageDTO;
import com.noosh.app.commons.vo.PageVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PageUtil test
 *
 * <AUTHOR>
 * @date 3/7/2021
 */
@ExtendWith(SpringExtension.class)
public class PageUtilTest {

    @Test
    public void test1_isOrderExist_true() {
        boolean res;
        res = PageUtil.isOrderExist("asc");
        assertTrue(res);

        res = PageUtil.isOrderExist("ASC");
        assertTrue(res);

        res = PageUtil.isOrderExist("desc");
        assertTrue(res);

        res = PageUtil.isOrderExist("DESC");
        assertTrue(res);
    }

    @Test
    public void test2_isOrderExist_false() {
        boolean res;
        res = PageUtil.isOrderExist("some");
        assertFalse(res);
    }

    @Test
    public void test3_toPageDTO() {
        PageVO pageVO = new PageVO(1, 10, "Prj_name", "Asc");
        Map<String, String> sortMap = new HashMap<>();
        sortMap.put("prj_name", "prj_name, prj_number");

        PageDTO pageDTO = PageUtil.toPageDTO(pageVO, sortMap);

        assertEquals(0, pageDTO.getStartRow());
        assertEquals(10, pageDTO.getEndRow());
        assertEquals("prj_name asc NULLS LAST, prj_number asc NULLS LAST".toLowerCase(),
                pageDTO.getOrderBy().toLowerCase());
    }

}
