package com.noosh.app.service.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * QueryUtil test
 *
 * <AUTHOR>
 * @date 4/26/2021
 */
@ExtendWith(SpringExtension.class)
public class QueryUtilTest {

    @Test
    public void test_formatQueryString() {
        String input1 = "*search by someone";
        String expected1 = "search%by%someone";
        String actual1 = QueryUtil.formatQueryString(input1);
        assertEquals(expected1,
                actual1,
                "formatted string should equal expected string");

        String input2 = "* search by  someone  ";
        String expected2 = "search%by%someone";
        String actual2 = QueryUtil.formatQueryString(input2);
        assertEquals(expected2,
                actual2,
                "formatted string should equal expected string");
    }

}
