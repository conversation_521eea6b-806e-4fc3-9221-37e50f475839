package com.noosh.app.service;

import com.noosh.app.commons.entity.invoice.InvoiceItem;
import com.noosh.app.repository.jpa.invoice.InvoiceItemRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;

/**
 * <AUTHOR>
 * @since 0.0.1
 */
@ExtendWith(MockitoExtension.class)
public class InvoiceServiceTest {

    @InjectMocks
    private InvoiceService invoiceService;

    @Mock
    private InvoiceItemRepository invoiceItemRepository;

    private List<InvoiceItem> invoiceItems;

    @BeforeEach
    public void init() {
        invoiceItems = new ArrayList<>();
        InvoiceItem invoiceItem = new InvoiceItem();
        invoiceItem.setAmount(new BigDecimal("180"));
        invoiceItem.setDiscountOrSurcharge(new BigDecimal("20"));
        invoiceItems.add(invoiceItem);
    }


    @Test
    public void given_invoiceIdAndIsFinal_when_getInvoiceItemTotal_then_expectCorrectInvoiceItemTotal() {
        // given
        //given(invoiceItemRepository.findByInvoiceId(1L)).willReturn(invoiceItems);

        // when
        BigDecimal invoiceItemTotal = invoiceService.getInvoiceItemTotal(true, invoiceItems);

        // then
        //then(invoiceItemRepository).should().findByInvoiceId(any(long.class));
        assertThat(invoiceItemTotal).isEqualTo("200");
    }

    @Test
    public void given_taxAndNull_when_getInvoiceGrandTotal_then_expectCorrectInvoiceGrandTotal() {
        // given
        //given(invoiceItemRepository.findByInvoiceId(1L)).willReturn(invoiceItems);

        // when
        BigDecimal invoiceGrandTotal = invoiceService.getGrandTotal(true,
                new BigDecimal("10"), null, null, null, invoiceItems);

        // then
        //then(invoiceItemRepository).should().findByInvoiceId(any(long.class));
        assertThat(invoiceGrandTotal).isEqualTo("210");
    }
}
