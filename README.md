# The orderresource Repository:

- Uses oauth2jwtauth

- Connects to an oracle database

- Uses My<PERSON><PERSON>

- Has Swagger API

- Ability to run with Maven or Docker swarm

# Requirements:
1. Requires Java 21+ on Classpath

2. Requires Maven 3.9.8+ on Classpath

3. Requires connection to remote config service

4. Requires connection to remote(dev) or local Vault service

5. Requires running local eureka1 service, eureka2 is optional for local development

6. Requires running local cloudgateway service

7. Requires running local oauth2jwtauth service

# Local development /etc/hosts file:
**********  qaappconfig.sjc.noosh.com

**********  hashicorp-vault-agent

127.0.0.1	eureka1

127.0.0.1	eureka2

127.0.0.1	cloudgateway

127.0.0.1	oauth2jwtauth

127.0.0.1	orderresource

127.0.0.1	one.dist.noosh.com

# To build:
mvn clean install

# To start:
mvn spring-boot:run

# Check that the service is running correctly:
https://one.dist.noosh.com:9443/orderresource/actuator/health