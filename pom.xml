<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.5</version>
        <relativePath></relativePath>
    </parent>

    <groupId>com.noosh.app</groupId>
    <artifactId>accountresource</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <java.version>21</java.version>
        <json.version>********</json.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <mybatis-plugin-pagehelper.version>6.1.0</mybatis-plugin-pagehelper.version>
        <mybatis-spring.version>3.0.3</mybatis-spring.version>
        <ojdbc8.version>23.6.0.24.10</ojdbc8.version>
        <sortpom-maven-plugin.version>4.0.0</sortpom-maven-plugin.version>
        <spring-cloud-dependencies.version>2023.0.3</spring-cloud-dependencies.version>
        <springdoc-openapi-starter-webmvc-ui.version>2.6.0</springdoc-openapi-starter-webmvc-ui.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--CVE-2022-40152-->
            <dependency>
                <groupId>com.fasterxml.woodstox</groupId>
                <artifactId>woodstox-core</artifactId>
                <version>6.4.0</version>
                <scope>compile</scope>
            </dependency>
            <!--CVE-2024-47072-->
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.21</version>
                <scope>compile</scope>
            </dependency>
            <!--CVE-2024-47554-->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.17.0</version>
                <scope>compile</scope>
            </dependency>
            <!--CVE-2025-25193-->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>4.1.118.Final</version>
                <scope>compile</scope>
            </dependency>
            <!--CVE-2020-13956-->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.14</version>
                <scope>compile</scope>
            </dependency>
            <!--CVE-2025-24813-->
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-core</artifactId>
                <version>10.1.35</version>
                <scope>compile</scope>
            </dependency>
            <!--CVE-2025-24813-->
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-el</artifactId>
                <version>10.1.35</version>
                <scope>compile</scope>
            </dependency>
            <!--CVE-2025-24813-->
            <dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>10.1.35</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud-dependencies.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>${mybatis-plugin-pagehelper.version}</version>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>${ojdbc8.version}</version>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-micrometer</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-tracing</artifactId>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>${json.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${mybatis-spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>${springdoc-openapi-starter-webmvc-ui.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-vault-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-jose</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-oauth2-resource-server</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.github.ekryd.sortpom</groupId>
                <artifactId>sortpom-maven-plugin</artifactId>
                <version>${sortpom-maven-plugin.version}</version>
                <configuration>
                    <createBackupFile>false</createBackupFile>
                    <lineSeparator>\n</lineSeparator>
                    <nrOfIndentSpace>4</nrOfIndentSpace>
                    <sortDependencies>groupId,artifactId</sortDependencies>
                    <sortPlugins>groupId,artifactId</sortPlugins>
                    <sortProperties>true</sortProperties>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>sort</goal>
                        </goals>
                        <phase>verify</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <release>${java.version}</release>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <addResources>true</addResources>
                    <profiles>
                        <profile>dev-mvn</profile>
                    </profiles>
                    <jvmArguments>-Duser.language=en -Duser.country=US -Duser.timezone=GMT -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:18304</jvmArguments>
                    <arguments>
                        <argument>--SPRING.CONFIG.IMPORT=optional:vault://,configserver:http://qaappconfig.sjc.noosh.com:8888</argument>
                        <argument>--SPRING.CLOUD.CONFIG.URI=http://qaappconfig.sjc.noosh.com:8888</argument>
                        <argument>--SPRING.CLOUD.CONFIG.USERNAME=qaconfig_user</argument>
                        <argument>--SPRING.CLOUD.CONFIG.PASSWORD=LTSy8TiCRKXU</argument>
                        <argument>--SPRING.CLOUD.CONFIG.LABEL=main</argument>
                        <argument>--SPRING.CLOUD.VAULT.ENABLED=true</argument>
                    </arguments>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
